import assert from 'node:assert';
import { test } from 'node:test';

import { formatNumberWithCommas, capitalizeFirstLetter } from '../test_build/src/utils/utils.js';
import { subtractDates } from '../test_build/src/utils/subtract-dates.js';

test('formatNumberWithCommas formats numbers correctly', () => {
  assert.strictEqual(formatNumberWithCommas(1000), '1,000');
  assert.strictEqual(formatNumberWithCommas(1234567), '1,234,567');
});

test('capitalizeFirstLetter capitalizes and lowercases correctly', () => {
  assert.strictEqual(capitalizeFirstLetter('hello'), 'Hello');
  assert.strictEqual(capitalizeFirstLetter('WORLD'), 'World');
});

test('subtractDates returns 0 when dates are equal', async () => {
  const now = new Date();
  const diff = await subtractDates(now);
  assert.strictEqual(diff, 0);
});
