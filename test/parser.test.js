import assert from 'node:assert';
import { test } from 'node:test';

import { MessageParserService } from '../test_build/public/client/services/messageParserService.js';

test('MessageParserService parses brief tags', () => {
  const svc = new MessageParserService();
  const message = '<brief>{"subjectLine":"Test","previewText":"Prev","briefText":"Body"}</brief>';
  const segments = svc.processCompleteMessage(message);
  assert.deepStrictEqual(segments[0].type, 'tool_start');
  assert.deepStrictEqual(segments[1].type, 'tool_content');
  assert.deepStrictEqual(segments[2].type, 'tool_end');
  assert.strictEqual(segments.length, 3);
});
