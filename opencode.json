{"$schema": "https://opencode.ai/config.json", "provider": {"moonshot": {"npm": "@ai-sdk/openai-compatible", "options": {"baseURL": "https://api.moonshot.ai/v1", "apiKey": "{env:MOONSHOT_API_KEY}"}, "models": {"kimi-k2-0711-preview": {}}}, "openrouter_adam": {"npm": "@ai-sdk/openai-compatible", "options": {"baseURL": "https://openrouter.ai/api/v1", "apiKey": "{env:OPENROUTER_API_KEY}"}, "models": {"mistralai/devstral-medium": {}, "moonshotai/kimi-k2:nitro": {}}}, "chutes": {"npm": "@ai-sdk/openai-compatible", "options": {"baseURL": "https://llm.chutes.ai/v1", "apiKey": "{env:MOONSHOT_API_KEY}"}, "models": {"moonshotai/Kimi-K2-Instruct": {}}}, "lmstudio": {"npm": "@ai-sdk/openai-compatible", "options": {"baseURL": "http://192.168.4.50:1234/v1", "apiKey": ""}, "models": {"mistralai/devstral-small-2507": {}}}}}