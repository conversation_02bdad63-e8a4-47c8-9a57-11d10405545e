## Quick Video Tutorial
<a href="https://www.loom.com/share/37691143d2e24c8a98807ea2288e0152">👀 Loom</a>

## Install Dependencies

```bash
$ npm i
```

## Open the Cypress Application
```bash
$ npm start # Run the webapp locally
$ npx cypress open  # Launch the Cypress application
```
This will launch the cypress application, allowing you to see a live preview while the tests are running. <br />

**Ensure raleon-webapp is running locally on port 3000**

## Run tests headlessly
```bash
npx cypress run --headless
```

## Check the results of CI tests runs
Navigate to <a href="https://cloud.cypress.io/projects/z8ng39">Cypress Cloud</a>
to view the latest CI test runs. Click on any failed tests to view a screenshot
or video of the failure.

## Best Practices
- Use `data-cy="some-identifer"` for element targeting instead of an id or class. This will help developers identify things that are being used in automated tests. Then use the custom `cy.getBySel('some-identifier')` function in your test.
- <a href="https://docs.cypress.io/guides/references/best-practices">Cypress Best Practices</a>

## Cypress Documentation
<a href="https://docs.cypress.io/api/commands/and">Cypress API Docs</a>


