before(() => {
	cy.login();

	cy.intercept('GET', '/api/v1/app-info?network=ETH', {
		fixture: 'audiences/app-info.json'
	}); 

	cy.intercept('POST', '/api/v1/segment/preview', {
		fixture: 'audiences/preview-segment.json'
	});

	cy.intercept('GET', '/api/v1/segment/event', {
		fixture: 'audiences/custom-events.json'
	});

	cy.intercept('GET', '/api/v1/metric/utm/campaigns', {
		fixture: 'audiences/utm-campaigns.json'
	});
});

describe('audiences', () => {

	it('creates a new audience', () => {
		const newAudienceName: string = 'Cypress Test Audience';

		cy.visit('/overview-segment');

		//Add Audience Button Click
		cy.getBySel('add-audience-button').click();
		cy.url().should('include', '/build-segment');

		//Create Audience
		cy.getBySel('segment-add-instruction-button').click();

		//Rename Audience
		cy.getBySel('segment-edit-segment').click({timeout: 20000});
		cy.getBySel('segment-open-edit-segment').click();
		cy.getBySel('segment-name-input').clear().type(newAudienceName);
		cy.getBySel('segment-save-changes').click();

		//Put in instruction details
		cy.getBySel('segment-menu-event-type').click();
		cy.getBySel('segment-menu-event-type-TransactionCountSegment').click();

		cy.getBySel('segment-menu-operator').click();
		cy.getBySel(`'segment-menu-operator->'`).click();

		cy.getBySel('segment-menu-amount').type('1', {force: true});

		cy.getBySel('segment-menu-subject-type').click();
		cy.getBySel('segment-menu-subject-type-CATEGORY').click();
		cy.getBySel('segment-menu-subject-value').click();
		cy.getBySel('segment-menu-subject-value-item').first().click({force: true});

		//save and preview mock
		cy.getBySel('segment-preview-save-and-preview').click();

		//navigate back to segment overview and verify audience is there
		cy.visit('/overview-segment');
		cy.get('table[id="segment-table"] > tbody > tr')
			.last()
			.should('contain', newAudienceName)
			.should('contain', 'Draft')
			.should('contain', '3');

		//delete audience
		//@ts-ignore
		cy.getBySel(`${newAudienceName.replaceAll(' ', '')}-delete`).last().click();
		cy.getBySel('confirm-delete-segment').click();
	});
});
