// beforeEach(() => {
// 	cy.login();
// });

// const newCampaignName: string = 'Cypress Test Campaign';

// describe('campaigns', () => {

// 	it('create, edit, and delete a new campaign', () => {
// 		cy.visit('/campaign-overview');
// 		cy.getBySel('create-new-campaign').click();

// 		//Create Campaign
// 		cy.getBySel('name-input').type(newCampaignName);
// 		cy.getBySel('audience-select').select(1)
// 		cy.getBySel('content-header-input').type('Cypress Test Content Header');
// 		cy.getBySel('content-header-img-input').type('https://www.cypress.io/img/cypress-logo.svg');
// 		cy.getBySel('content-message-input').type('Cypress Test Content Message');
// 		cy.getBySel('content-close-message-input').type('Cypress Test Close Message');
// 		cy.getBySel('content-button-text-input').type('Cypress Test Button Text');
// 		cy.getBySel('content-button-url-input').type('https://www.cypress.io/');
// 		cy.getBySel('create-campaign-button').click();

// 		cy.get('table[id="raleon-table"] > tbody > tr')
// 			.last()
// 			.as('newRow');

// 		cy.get('@newRow')
// 			.should('contain', newCampaignName)
// 			.and('contain', 'Running');

// 		cy.get('@newRow').children().as('tdsInLastRow');

// 		cy.get('@tdsInLastRow').first().click();

// 		cy.getBySel('campaign-name').should('contain', newCampaignName);

// 		cy.getBySel('edit-campaign').click();
// 		cy.wait(1000);
// 		cy.getBySel('start-date-input').clear().type('3000-01-01');
// 		cy.getBySel('end-date-input').clear().type('3000-01-14');
// 		cy.getBySel('create-campaign-button').click();
// 		cy.wait(1000);

// 		cy.visit('/campaign-overview');

// 		cy.get('@tdsInLastRow')
// 			.should('contain', newCampaignName)
// 			.and('contain', 'Draft');

// 		cy.get('@tdsInLastRow').last().click();
// 		cy.getBySel('delete-button').click();
// 	});
// })
