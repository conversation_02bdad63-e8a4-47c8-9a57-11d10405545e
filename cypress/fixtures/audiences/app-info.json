{"statusCode": 200, "body": [{"Reported Name": "benji-bananas", "Address": "0xe6f1d4eef6ea4cf5ae53eb13549ef52849d5dca3", "Name": "<PERSON><PERSON>", "Category": "Gaming", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "\r"}, {"Reported Name": "benji-bananas", "Address": "0xd251fa25bc15ab30746302845e9dab1a5b9adbef", "Name": "<PERSON><PERSON>", "Category": "Gaming", "Network": "POLY", "Has Logs": "No", "Decoded": "No", "Tx Count": "39k\r"}, {"Reported Name": "arc8-by-gamee-1", "Address": "0xcf32822ff397ef82425153a9dcb726e5ff61dca7", "Name": "Arc8 by GAMEE", "Category": "Gaming", "Network": "POLY", "Has Logs": "Yes", "Decoded": "Yes", "Tx Count": "\r"}, {"Reported Name": "arc8-by-gamee-1", "Address": "0x2cc01d79d504f63eb53ac15023b3d937267fd752", "Name": "Arc8 by GAMEE", "Category": "Gaming", "Network": "POLY", "Has Logs": "Yes", "Decoded": "Yes", "Tx Count": "\r"}, {"Reported Name": "arc8-by-gamee-1", "Address": "0x45eba2209fe46a46231f9c24d94a58bcd6d4625d", "Name": "Arc8 by GAMEE", "Category": "Gaming", "Network": "POLY", "Has Logs": "Yes", "Decoded": "Yes", "Tx Count": "\r"}, {"Reported Name": "arc8-by-gamee-1", "Address": "0x4f3b253c3890076d8762e72bc79088a52511de5d", "Name": "Arc8 by GAMEE", "Category": "Gaming", "Network": "POLY", "Has Logs": "Yes", "Decoded": "Yes", "Tx Count": "\r"}, {"Reported Name": "quickswap", "Address": "0x8e062b8d411780217fe1be51f1a288b5dee44378", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x9e20839495dcdc23dde9320f2f894a553c7e2097", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "Yes", "Decoded": "Yes", "Tx Count": "\r"}, {"Reported Name": "quickswap", "Address": "0x30f4c644dc3f0ba9996b1ca6cb9a202b77fb6c65", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xcf234fd225bedb8a5012b885d70c2fa074a395a3", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x3d09d279d3463157b704652aa98c6886006638cf", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xe80aacbbd31cd7042d849c22b3e01a7e5b6f9751", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x88dcda019a6ab0604b2da002191b89eb64eeeaf4", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x2e0920a9c6131be4b24d8bf3cb68d37cc12f9fbc", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xfa6028419eb457f5728f1940c16f6d7e6ae430d5", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x2a0f5812f0c4b7cfb47cf7987d660436e3230a00", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x74463ef415ef5f4395a652b55ad58c748bafcd6a", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x8db447cba0061f95268366951f33fffd67d6e78c", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "Yes", "Decoded": "Yes", "Tx Count": "\r"}, {"Reported Name": "quickswap", "Address": "0xa3b1571238a2a30d2fc1de246f1afc28479958b6", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "Yes", "Decoded": "Yes", "Tx Count": "\r"}, {"Reported Name": "quickswap", "Address": "0x0a561b4e1a0f97aac4edcc0b3602fbd62c6db377", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x1ed068f13031602669993948ec9f17d94c3e9773", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x888fe714f5cb9729f3c4c4d4dbcf556940076841", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "Yes", "Decoded": "Yes", "Tx Count": "\r"}, {"Reported Name": "quickswap", "Address": "0xe8c825c23f7d7cb64322dd3bbb6a9dba9707645e", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x030cd002b56f2c9f9eaacccc8d136508060e71b2", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x5223b2f033dce7d9de567a260c915e37f7d300a7", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "Yes", "Decoded": "Yes", "Tx Count": "34\r"}, {"Reported Name": "quickswap", "Address": "0x9d582d4ba86b2b6a43dfadb4b9c94f1c7efb6a30", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x5d84e230cee5da2599e06bf5c9d594de30514960", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2\r"}, {"Reported Name": "quickswap", "Address": "0xa7cd5af91c92eb9821ac173c7dea065a1ef57de1", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xd7f2af899c2e5f0b196a5d2f5275133de331a2f8", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x130253e2f7c72bb9b34a98a27e851b65a46cea6e", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xb87af287bb616aae80dd8bb62af998327c30b917", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x62268e5b396ca5a5b1fd232e80485c721d2af8ed", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2\r"}, {"Reported Name": "quickswap", "Address": "0x9dcfca0b7f4124341f17aa795b3a259de88b3920", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "\r"}, {"Reported Name": "quickswap", "Address": "0x293d1bfb7031e4bd84a83f9656604e3dbf479ad5", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1\r"}, {"Reported Name": "quickswap", "Address": "0xbb13b5adea42c01a041abc903cd7b4f107805b5b", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xddc1ed82adf388a7b152a49df901b37ab21357c7", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x343399586ad958fafaa5d8fc83351e1b52d123f5", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2\r"}, {"Reported Name": "quickswap", "Address": "0xe779d1be5fa737db7f0bd1dd9917e41dc6f924b1", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1\r"}, {"Reported Name": "quickswap", "Address": "0xb70a6c0224239a848d76f108e2dfa78f701994fc", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x5a1c64b9abb1893d1f29873dd7c1affeaa2f6bfa", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x45e489af73ff726900114293faa5390219c9c4ef", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x7be14ead8c52444cab89be1e84db5f07e061eaa3", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1\r"}, {"Reported Name": "quickswap", "Address": "0x0033ce8c6d55a127d1d9f4ed5215b772726deb7e", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x1fe505bef907a0a073dbb484044fabf0f9649e8a", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xf60657794c62507614cb848092ad4c65efd494ec", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x9f3565fc644a3473cce32715f573acbce6dc9aee", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x2f0d1b64222e7e2f0e75cca67fd93873540c5c89", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1\r"}, {"Reported Name": "quickswap", "Address": "0x6c2ac9c2c42d610dbefe8ca9494785a6cf0b6ce7", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xdaec9ea34dd9ebab71562dce6fb60c11b48bd1fb", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x20bfc954b2fdecbf9be4eb7ec4db88e5ac6a7392", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x7f86ad58c1c031350783476130cfb9d891ddc17e", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x35bdb68c8bd197e62a517b37632c0b7583082b48", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1\r"}, {"Reported Name": "quickswap", "Address": "0xb3f1de5ca4532626d55cdcfae9e0da4295c1990e", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xf6dc6573993e9e7eb2fef4119fa1a92bbe448490", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x52d5af10084fa8d46d137126f92c1beb0ea21a36", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x7f7bf0af806667bb15c12f1e49f2a5e5c0c38e58", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xe033101a08ca86cac298ddab78aae0d8eed6e963", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xbc054e3f4ee234d382dd550160025b870c1892e7", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xb7fe7e8595ce68fd5fb19dd68295386246a6a356", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xfdffe16554d84399ac1dce46e0be666a176ef1fd", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x6649f12e210862e0045b3dfe7e6ea1f8f0565049", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "1,232"}, {"Reported Name": "quickswap", "Address": "0xe06cbe28f7c05ec68613b5b8735175ac21c7e1d7", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "3\r"}, {"Reported Name": "quickswap", "Address": "0xd06dfbbddf8d4347317fb8e4389a7a2ae37ab2c7", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x729af27ff01bf19772f1756866021a15bc07ce2f", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xee753ef64b48a1574918c020abcd7496e7ac02bf", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x7d4f9b0efddf27e65e5f1586d7f5b7c74c67dbc5", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x15b2e08a47f50f7dddd2524cffb879d4a51bc1d8", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2\r"}, {"Reported Name": "quickswap", "Address": "0x0cc3fbb91472243536d29393c0397e2a29e9b70a", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xf7b568aabaac88e7dcb525e74f6ed817e949b044", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x92c8ae7d8a32328b82dc96ad134cd4d8e2d0914d", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x8b617de0ca8d0d6182e26bec2f5f0ffe39f30be1", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xc2ac2998e5b4ecc182ca1d441aff1d9a15c89957", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x3ce20d7e0de8e192c0b643c2262da8c7634c7fdb", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "10\r"}, {"Reported Name": "Uniswap", "Address": "0x844f329e1006c21ba0ea372e9c963a673b7e4b42", "Name": "Uniswap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "\r"}, {"Reported Name": "quickswap", "Address": "0x469d2a07c5bedfed1627784cdc79acabfbd85247", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x6919c36096f897a270f0f5a95c21812ff8a137a1", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x0866d2aa0dc8f21bf876c0a8966ee9fe7784f64f", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xdf9eba5d8d9dde3d794fb072898422a16c1e5a86", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xd361b0407580dcf393d7c656ef1585b0d5852617", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x53ef4d335f2e817b66701ac7ff297a84a7dd7763", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1\r"}, {"Reported Name": "quickswap", "Address": "0x668ea6b2fb87147a43c24e5000a9d0c664de4045", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x892063eccfde3ebd2c3212d5d29dd1e7cca3a6ba", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x013618df313b817b87f4542717743639ce5985e9", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x28c463e29d29eaca069c62ac52c0d41888a84e45", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x9a7b0789dc5e1f09e1fc2305f686017267e60ba6", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2\r"}, {"Reported Name": "quickswap", "Address": "0xd59460f89a9d4f92758db935721119d0f474fbd8", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xcc19fa7f8eedf01b5072fd5177cc92018bacd1f5", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x970db456b4d621ba48107263d32ea431c08929c0", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x6e38c574557d13784b0127413e13277cd8e209e6", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1\r"}, {"Reported Name": "quickswap", "Address": "0x4533bac8dbe88b9028c6214ad602615e27d9c1fb", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xad645df9b2eaf1286f5106deed5f544a34d2a6a9", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x2b061c6ff02a34d717f6e3708972c0f91b41499a", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x2e74a8ff3dbe902346ce0e21caf1df335cba12b1", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xc5880e452e30d36f622b01835713fea7ed87fea7", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xccbe6a309365c8cb1918f1f7a73f63ab0fbd09c7", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xa2c82e2c559c3605884cc5506bc9447f900ac9b2", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x113ce9a774aabbbf23eb42d77d1081b1526e8264", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x8e8d903309e86ef9996cfc602ea2cc1ae0a55586", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x1e877575b2436d63de655e9af972502b285fd1b7", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xc2460a08ccab061960d622bb466ddf994dc5ca03", "Name": "QuickSwap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1\r"}, {"Reported Name": "quickswap", "Address": "0x5e3ee9ca2e965f5d82bc123e9001524f308432f8", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x5bacbe1e859a8d838b69a4d415757b323fc64491", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xc29d3c47b914889badaf315dbf890b93db02aca5", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x8587a9ab976fd28ff22cf9340d807275f3a21bac", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0xfa9fbe692ed473c41d6607daa8d2b50b372620dc", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "quickswap", "Address": "0x15108ae3875da023ab4b64a41ccb032f6feab739", "Name": "QuickSwap", "Category": "Exchange", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "galxe", "Address": "******************************************", "Name": "Galxe", "Category": "Utility", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "\r"}, {"Reported Name": "galxe", "Address": "******************************************", "Name": "Galxe", "Category": "Utility", "Network": "ARB", "Has Logs": "", "Decoded": "", "Tx Count": "\r"}, {"Reported Name": "galxe", "Address": "******************************************", "Name": "Galxe", "Category": "Utility", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "\r"}, {"Reported Name": "sushi", "Address": "******************************************", "Name": "<PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "\r"}, {"Reported Name": "sushi", "Address": "******************************************", "Name": "<PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "ARB", "Has Logs": "", "Decoded": "", "Tx Count": "\r"}, {"Reported Name": "sushi", "Address": "******************************************", "Name": "<PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "\r"}, {"Reported Name": "sushi", "Address": "******************************************", "Name": "<PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "\r"}, {"Reported Name": "sushi", "Address": "******************************************", "Name": "<PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "\r"}, {"Reported Name": "sushi", "Address": "******************************************", "Name": "<PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "800k\r"}, {"Reported Name": "planet-ix", "Address": "0x206b83fc7fd1250fc30a6a1e51e13277843cda79", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "12\r"}, {"Reported Name": "planet-ix", "Address": "0xb2435253c71fca27be41206eb2793e44e1df6b6d", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "1M\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "6\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "8M\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "51\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "no/partial", "Tx Count": "1M\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "273K\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "4\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "17\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "370\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "4\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "16\r"}, {"Reported Name": "planet-ix", "Address": "0xaef349e1736b8a4b1b243a369106293c3a0b9d09", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "7\r"}, {"Reported Name": "planet-ix", "Address": "0x8f2c3a5e7d3bb12ae43ab7377ea627e1db3aa23c", "Name": "Planet IX", "Category": "Gaming", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "planet-ix", "Address": "0x7d0495d8a918fb9aa02fea8d23c970d5933bd793", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "78K\r"}, {"Reported Name": "planet-ix", "Address": "0xc4c3956732a5d8eda6c0163c174e413cb1cad1dc", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "3,874"}, {"Reported Name": "planet-ix", "Address": "0x0b8738735c72be95fe5f5636b1807cab8c9dddd8", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "3,203"}, {"Reported Name": "planet-ix", "Address": "0x24cff55d808fd10a1e730b86037760e57ecaf549", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "8,190"}, {"Reported Name": "planet-ix", "Address": "0x08971219534e1f8b6e4afb2bf4a5cf3929a141b1", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "19K\r"}, {"Reported Name": "planet-ix", "Address": "0xcb985163ca943fe6382a4165a762b38809ea7ff8", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "12K\r"}, {"Reported Name": "planet-ix", "Address": "0x87277d6676cd567dfb5536e30146cd54815f0c53", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "9K\r"}, {"Reported Name": "planet-ix", "Address": "0x5cb9fcd65cfe3eefde88a7084caf2625d516bdd0", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "149K\r"}, {"Reported Name": "planet-ix", "Address": "0xb030da155b6f3e67921157ec7793edf3e407a55b", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "22K\r"}, {"Reported Name": "planet-ix", "Address": "0xe23b62ead7cf4334058159734f76719bbccbc858", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "2K\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "19K\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "ETH", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "2K\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "553K\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "88K\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "2\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "130K\r"}, {"Reported Name": "planet-ix", "Address": "******************************************", "Name": "Planet IX", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "20K\r"}, {"Reported Name": "1Inch-network", "Address": "******************************************", "Name": "1Inch", "Category": "<PERSON><PERSON><PERSON>", "Network": "ETH", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "2M\r"}, {"Reported Name": "1Inch-network", "Address": "******************************************", "Name": "1Inch", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "4M\r"}, {"Reported Name": "1Inch-network", "Address": "******************************************", "Name": "1Inch", "Category": "<PERSON><PERSON><PERSON>", "Network": "ARB", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "20K\r"}, {"Reported Name": "1Inch-network", "Address": "******************************************", "Name": "1Inch", "Category": "<PERSON><PERSON><PERSON>", "Network": "ETH", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "2M\r"}, {"Reported Name": "1Inch-network", "Address": "******************************************", "Name": "1Inch", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "4M\r"}, {"Reported Name": "1Inch-network", "Address": "******************************************", "Name": "1Inch", "Category": "<PERSON><PERSON><PERSON>", "Network": "ARB", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "606K\r"}, {"Reported Name": "qidao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "69K\r"}, {"Reported Name": "qidao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "7\r"}, {"Reported Name": "qidao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "280K\r"}, {"Reported Name": "qidao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "ARB", "Has Logs": "", "Decoded": "", "Tx Count": "5\r"}, {"Reported Name": "qidao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "20K\r"}, {"Reported Name": "qidao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "2K\r"}, {"Reported Name": "qidao", "Address": "0xe6c23289ba5a9f0ef31b8eb36241d5c800889b7b", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "5243\r"}, {"Reported Name": "qidao", "Address": "0x0470cd31c8fcc42671465880ba81d631f0b76c1d", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "16K\r"}, {"Reported Name": "qidao", "Address": "0xb3911259f435b28ec072e4ff6ff5a2c604fea0fb", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2,800"}, {"Reported Name": "qidao", "Address": "0x7068ea5255cb05931efa8026bd04b18f3deb8b0b", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "29K\r"}, {"Reported Name": "qidao", "Address": "0xea4040b21cb68afb94889cb60834b13427cfc4eb", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "4K\r"}, {"Reported Name": "qidao", "Address": "0xba6273a78a23169e01317bd0f6338547f869e8df", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "4K\r"}, {"Reported Name": "qidao", "Address": "0x3fd939b017b31eaadf9ae50c7ff7fa5c0661d47c", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "75K\r"}, {"Reported Name": "qidao", "Address": "0x61167073e31b1dad85a3e531211c7b8f1e5cae72", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "17K\r"}, {"Reported Name": "qidao", "Address": "0x87ee36f780ae843a78d5735867bc1c13792b7b11", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "8K\r"}, {"Reported Name": "qidao", "Address": "0x98b5f32dd9670191568b661a3e847ed764943875", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "27K\r"}, {"Reported Name": "qidao", "Address": "0x3feacf904b152b1880bde8bf04ac9eb636fee4d8", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "499\r"}, {"Reported Name": "qidao", "Address": "0x574fe4e8120c4da1741b5fd45584de7a5b521f0f", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "181K\r"}, {"Reported Name": "qidao", "Address": "0x880decade22ad9c58a8a4202ef143c4f305100b3", "Name": "<PERSON><PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "38K\r"}, {"Reported Name": "klimadao", "Address": "0x7de627c56d26529145a5f9d85948ecbeaf9a4b34", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "42K\r"}, {"Reported Name": "klimadao", "Address": "0x00da51bc22edf9c5a643da7e232e5a811d10b8a3", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "7K\r"}, {"Reported Name": "klimadao", "Address": "0x285a6054ddc2980c62e716086b065e1e770fffb3", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "759\r"}, {"Reported Name": "klimadao", "Address": "0x08ee531979b730dbb63469bc56e1d6cd9f43b8d4", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "3K\r"}, {"Reported Name": "klimadao", "Address": "0x1e0dd93c81ac7af2974cdb326c85b87dd879389b", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "27K\r"}, {"Reported Name": "klimadao", "Address": "0xbf2a35efcd85e790f02458db4a3e2f29818521c5", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "15K\r"}, {"Reported Name": "klimadao", "Address": "0xb5af101742ecae095944f60c384d09453006bfde", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "4K\r"}, {"Reported Name": "klimadao", "Address": "0x18c3713d523f91fbd26e65c8babab63a0f31b9a6", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "720\r"}, {"Reported Name": "klimadao", "Address": "0x0b8d6d6611ed7cce01bbcc57826548c6107b0478", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "klimadao", "Address": "0x65a5076c0ba74e5f3e069995dc3dab9d197d995c", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "231\r"}, {"Reported Name": "klimadao", "Address": "0xd2f4a710b7db5c0a05f17b68fd5da3c4c6b63be1", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "149\r"}, {"Reported Name": "klimadao", "Address": "0xede3bd57a04960e6469b70b4863ce1c9d9363cb8", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1,196"}, {"Reported Name": "klimadao", "Address": "0x933af8c652c696fb0969eb85ddd111edb2b4e057", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "10\r"}, {"Reported Name": "klimadao", "Address": "0xa35f62dbdb93e4b772784e89b7b35736a4aeacc5", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "5\r"}, {"Reported Name": "klimadao", "Address": "0xcefb61af5325c0c100cbd77eb4c9f51d17b189ca", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "7\r"}, {"Reported Name": "klimadao", "Address": "0xac298cd34559b9acfaedea8344a977eceff1c0fd", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "5\r"}, {"Reported Name": "klimadao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "123K\r"}, {"Reported Name": "klimadao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "12\r"}, {"Reported Name": "klimadao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "373K\r"}, {"Reported Name": "klimadao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "klimadao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "27\r"}, {"Reported Name": "klimadao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "264K\r"}, {"Reported Name": "klimadao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "ARB", "Has Logs": "", "Decoded": "", "Tx Count": "4\r"}, {"Reported Name": "klimadao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "4\r"}, {"Reported Name": "klimadao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "75K\r"}, {"Reported Name": "klimadao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "22K\r"}, {"Reported Name": "klimadao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "6K\r"}, {"Reported Name": "klimadao", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2K\r"}, {"Reported Name": "klimadao", "Address": "0x0af5dee6678869201924930d924a435f6e4839c9", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "240\r"}, {"Reported Name": "klimadao", "Address": "0x7dd4f0b986f032a44f913bf92c9e8b7c17d77ad7", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category": "Social", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "2\r"}, {"Reported Name": "earntube", "Address": "0x11388d05554eb091eddafaf4b3b83da5119826f9", "Name": "EarnTube", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "422K\r"}, {"Reported Name": "earntube", "Address": "0xcf2de6939d8b70ec38bec771dd932972fffa37f2", "Name": "EarnTube", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1\r"}, {"Reported Name": "opensea", "Address": "******************************************", "Name": "OpenSea", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "100K\r"}, {"Reported Name": "opensea", "Address": "******************************************", "Name": "OpenSea", "Category": "NFT Marketplace", "Network": "ETH", "Has Logs": "yes", "Decoded": "no/small percent", "Tx Count": "11M\r"}, {"Reported Name": "opensea", "Address": "******************************************", "Name": "OpenSea", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "yes", "Decoded": "no/small percent", "Tx Count": "3M\r"}, {"Reported Name": "opensea", "Address": "******************************************", "Name": "OpenSea", "Category": "NFT Marketplace", "Network": "ARB", "Has Logs": "", "Decoded": "", "Tx Count": "133K\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ARB", "Has Logs": "", "Decoded": "", "Tx Count": "35\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "83\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "55\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ETH", "Has Logs": "No", "Decoded": "No", "Tx Count": "3\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ETH", "Has Logs": "No", "Decoded": "No", "Tx Count": "2\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ETH", "Has Logs": "no", "Decoded": "no", "Tx Count": "126\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "92\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ARB", "Has Logs": "no", "Decoded": "no", "Tx Count": "15\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ETH", "Has Logs": "no", "Decoded": "no", "Tx Count": "5M\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "1M\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ARB", "Has Logs": "no", "Decoded": "no", "Tx Count": "1M\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ETH", "Has Logs": "No", "Decoded": "No", "Tx Count": "2\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ETH", "Has Logs": "No", "Decoded": "No", "Tx Count": "2\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ETH", "Has Logs": "No", "Decoded": "No", "Tx Count": "2\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ETH", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "1M\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "2M\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ARB", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "570K\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "586\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "7\r"}, {"Reported Name": "uniswap-v3", "Address": "******************************************", "Name": "Uniswap", "Category": "Exchange", "Network": "ARB", "Has Logs": "", "Decoded": "", "Tx Count": "3\r"}, {"Reported Name": "sunflower-land", "Address": "******************************************", "Name": "Sunflower Land", "Category": "Gaming", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "1M\r"}, {"Reported Name": "sunflower-land", "Address": "******************************************", "Name": "Sunflower Land", "Category": "Gaming", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "4\r"}, {"Reported Name": "sunflower-land", "Address": "******************************************", "Name": "Sunflower Land", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "134K\r"}, {"Reported Name": "sunflower-land", "Address": "******************************************", "Name": "Sunflower Land", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "677K\r"}, {"Reported Name": "sunflower-land", "Address": "******************************************", "Name": "Sunflower Land", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "300K\r"}, {"Reported Name": "sunflower-land", "Address": "******************************************", "Name": "Sunflower Land", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "3K\r"}, {"Reported Name": "sunflower-land", "Address": "0x9c28ebff68400b29d6f7b440cdc07cfd9214996f", "Name": "Sunflower Land", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "6K\r"}, {"Reported Name": "sunflower-land", "Address": "0x205a5499c12ae4ad82a308ab2d3572b702e54299", "Name": "Sunflower Land", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "42K\r"}, {"Reported Name": "sunflower-land", "Address": "0x4bb5b2461e9ef782152c3a96698b2a4cf55b6162", "Name": "Sunflower Land", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "4K\r"}, {"Reported Name": "sunflower-land", "Address": "0x624e4fa6980afcf8ea27bfe08e2fb5979b64df1c", "Name": "Sunflower Land", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "27K\r"}, {"Reported Name": "the-dustland", "Address": "0xc65fd3945e26c15e03176810d35506956b036f39", "Name": "The Dustland", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "194K\r"}, {"Reported Name": "the-dustland", "Address": "0x41286fa21830f7df40550ff29357e0a5d7e67be0", "Name": "The Dustland", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "34\r"}, {"Reported Name": "the-dustland", "Address": "0x22bdee4a9f8ea83a3519e498120fb91fee20a72d", "Name": "The Dustland", "Category": "Gaming", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "the-dustland", "Address": "0x197a3de3eeeb714c9d75b128e95486106fec3c20", "Name": "The Dustland", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "503K\r"}, {"Reported Name": "the-dustland", "Address": "******************************************", "Name": "The Dustland", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2K\r"}, {"Reported Name": "0x-protocol", "Address": "******************************************", "Name": "0x", "Category": "Exchange Aggregator", "Network": "ETH", "Has Logs": "yes", "Decoded": "no", "Tx Count": "2M\r"}, {"Reported Name": "0x-protocol", "Address": "******************************************", "Name": "0x", "Category": "Exchange Aggregator", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "11M\r"}, {"Reported Name": "0x-protocol", "Address": "******************************************", "Name": "0x", "Category": "Exchange Aggregator", "Network": "ARB", "Has Logs": "yes", "Decoded": "no", "Tx Count": "123K\r"}, {"Reported Name": "0x-protocol", "Address": "******************************************", "Name": "0x", "Category": "Exchange Aggregator", "Network": "ARB", "Has Logs": "", "Decoded": "", "Tx Count": "2\r"}, {"Reported Name": "0x-protocol", "Address": "******************************************", "Name": "0x", "Category": "Exchange Aggregator", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "47\r"}, {"Reported Name": "0x-protocol", "Address": "******************************************", "Name": "0x", "Category": "Exchange Aggregator", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "0x-protocol", "Address": "0xe6d9207df11c55bce2f7a189ae95e3222d5484d3", "Name": "0x", "Category": "Exchange Aggregator", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "19\r"}, {"Reported Name": "crazy-defense-heroes", "Address": "0xe57dad9c809c5ff0162b17d220917089d4cc7075", "Name": "Crazy Defense Heroes", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "11M\r"}, {"Reported Name": "crazy-defense-heroes", "Address": "0x0a76d8e685e999a0818cb88e97a61e9ee1062bc3", "Name": "Crazy Defense Heroes", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "516K\r"}, {"Reported Name": "crazy-defense-heroes", "Address": "0x011c1a01012f952cff47d050ca23704612ce0ae8", "Name": "Crazy Defense Heroes", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2K\r"}, {"Reported Name": "crazy-defense-heroes", "Address": "******************************************", "Name": "Crazy Defense Heroes", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "219K\r"}, {"Reported Name": "crazy-defense-heroes", "Address": "******************************************", "Name": "Crazy Defense Heroes", "Category": "Gaming", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "4\r"}, {"Reported Name": "crazy-defense-heroes", "Address": "******************************************", "Name": "Crazy Defense Heroes", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "crazy-defense-heroes", "Address": "******************************************", "Name": "Crazy Defense Heroes", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "36K\r"}, {"Reported Name": "crazy-defense-heroes", "Address": "******************************************", "Name": "Crazy Defense Heroes", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "8K\r"}, {"Reported Name": "crazy-defense-heroes", "Address": "******************************************", "Name": "Crazy Defense Heroes", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "187K\r"}, {"Reported Name": "crazy-defense-heroes", "Address": "0x2b88ce7b01e6bdbb18f9703e01286608cf77e805", "Name": "Crazy Defense Heroes", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "3K\r"}, {"Reported Name": "crazy-defense-heroes", "Address": "0xa69924d2f8d37279304d465c0af05a19f7c2ddee", "Name": "Crazy Defense Heroes", "Category": "Gaming", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "crazy-defense-heroes", "Address": "0x3c6d0e41deab0dfb3c46c7f2f2313e4e7615c164", "Name": "Crazy Defense Heroes", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "4\r"}, {"Reported Name": "crazy-defense-heroes", "Address": "0xfd8440a81ebfe9d7c3a2a10031754e62e51c04f2", "Name": "Crazy Defense Heroes", "Category": "Gaming", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "ultimate-champions", "Address": "0xed755dba6ec1eb520076cec051a582a6d81a8253", "Name": "Ultimate Champions", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "450K\r"}, {"Reported Name": "ultimate-champions", "Address": "0x2a9154ec2ee34e5c5003db31cba5778709d767a8", "Name": "Ultimate Champions", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "490K\r"}, {"Reported Name": "ultimate-champions", "Address": "0x7f61345bdd61b4192324d612fcecd795ce4b60bd", "Name": "Ultimate Champions", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "594K\r"}, {"Reported Name": "ultimate-champions", "Address": "0xc3f7ac8623a7bd7bbae3389efb0307b2df5ec3a0", "Name": "Ultimate Champions", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "412K\r"}, {"Reported Name": "aes-finance", "Address": "0x5ac3ceee2c3e6790cadd6707deb2e87ea83b0631", "Name": "AES Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "aes-finance", "Address": "0x40eaa37ebb78ce30de4fe2feb6f50501162b88bf", "Name": "AES Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "aes-finance", "Address": "0x3bf3b700538674bf8cd4fd41437ee11203162c58", "Name": "AES Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "53K\r"}, {"Reported Name": "aes-finance", "Address": "0x7aa3ae7798e145097f0a0e5bedefee2a166a5956", "Name": "AES Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "91K\r"}, {"Reported Name": "aes-finance", "Address": "0xb27e6cf73b2b364c46a687b7bdd474ed3c87db2f", "Name": "AES Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "aes-finance", "Address": "0xd0602612d039017d559cc3532c304f7ee2f9dfd5", "Name": "AES Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "11K\r"}, {"Reported Name": "aes-finance", "Address": "******************************************", "Name": "AES Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "75M\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "51\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "54K\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "15\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "85K\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "ARB", "Has Logs": "", "Decoded": "", "Tx Count": "7\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "ETH", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "372K\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1M\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "ARB", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "848K\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "ETH", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "1\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "44\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "72\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "ARB", "Has Logs": "no", "Decoded": "no", "Tx Count": "6K\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "20\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "77\r"}, {"Reported Name": "balancer", "Address": "******************************************", "Name": "Balancer", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "382\r"}, {"Reported Name": "lens-protocol", "Address": "******************************************", "Name": "Lens Protocol", "Category": "Social", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "154\r"}, {"Reported Name": "lens-protocol", "Address": "******************************************", "Name": "Lens Protocol", "Category": "Social", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "8M\r"}, {"Reported Name": "lens-protocol", "Address": "******************************************", "Name": "Lens Protocol", "Category": "Social", "Network": "ARB", "Has Logs": "", "Decoded": "", "Tx Count": "9\r"}, {"Reported Name": "lens-protocol", "Address": "******************************************", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "******************************************", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0xb05bae098d2b0e3048de27f1931e50b0200a043b", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0x3fa902a571e941dcac6081d57917994ddb0f9a9d", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0x1292e6df9a4697daafddbd61d5a7545a634af33d", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0xef13efa565fb29cd55ecf3de2beb6c69bd988212", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0xbf4e6c28d7f37c867ce62cf6ccb9efa4c7676f7f", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0x7b94f57652cc1e5631532904a4a038435694636b", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0xa31ff85e840ed117e172bc9ad89e55128a999205", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0x23b9467334beb345aaa6fd1545538f3d54436e96", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0x80ae0e6048d6e295ee6520b07eb6ec4485193fd6", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0x057ccdf5153be1081830a6c3d507c9dfe1ac8e4e", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0x6640e4fb3fd56a6d7dff3c351dfd9ab7e57fb769", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0x17317f96f0c7a845ffe78c60b10ab15789b57aaa", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0xb0298c5540f4cfb3840c25d290be3ef3fe09fa8c", "Name": "Lens Protocol", "Category": "Social", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "8\r"}, {"Reported Name": "lens-protocol", "Address": "0xec9d9e482ce7fb715605e18add72b5a696880357", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0xeff187b4190e551fc25a7fa4dfc6cf7fdef7194f", "Name": "Lens Protocol", "Category": "Social", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "180K\r"}, {"Reported Name": "lens-protocol", "Address": "0x3df697ff746a60cbe9ee8d47555c88cb66f03bb9", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "lens-protocol", "Address": "0x1eec6eccaa4625da3fa6cd6339dbcc2418710e8a", "Name": "Lens Protocol", "Category": "Social", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "110K\r"}, {"Reported Name": "lens-protocol", "Address": "0x8b0a28a8de1de77668260a876c6dcf0330183742", "Name": "Lens Protocol", "Category": "Social", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x8494d2ed08c85a6b9014fb298f4373f2e5911b87", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "38K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x047c2fab5e150b5c47bf636fdd2f87e462f42d11", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x192b1bb9120ef03c23a8f2edc712015cbd3ac9ce", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "48K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x5268ba85beada5891203aa7f836269741127e31d", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "600K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x8765f05adce126d70bcdf1b0a48db573316662eb", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "452K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x7653ba65f7e231d3008f64f90e0fb5af2819fa94", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "143K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0xb0e8a27682ca2cc4c4086f50386391e7c589d078", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "58\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0xc58c5a1c29fcc53e3041d5f889dea9ce683b3a3c", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "87K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0xc9c9e9acd4dee1099060f0360f852b1aae896447", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "12K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x1a6d76b60018f77132d354a498bcd3b06f5e5980", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "236K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x0cbdd41c9d6f9506538206841a55c13f72fd4222", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "29K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x96a20d21861aa775294ebc507a72ecad5e438881", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x222fc0abeceda5213d1e07d0694bdef71fb1e5ef", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "786\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x9e006e230bb4fd1fa52d6c7f25d31073536d35ce", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "624\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0xacf20a217bfa91a6531cfe04861be58e2a644c31", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "479\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0xa807830be660f2f36ce85cf001f5721f297970a2", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "9\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0xe501bd0e7e4bb86d28f793e91adfe5b6d0093d99", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "156\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0xc96669da20fcc98b3ad8b44a6f22891a8a52fc81", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "5K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x77874890e357f9d3207332d905188cc012fdcd20", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "10K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x7546d34fc15b25471072493e25150e0a8901cd52", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "18K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x231b0d272d5ff3a9b47d971fb6b1cac2f149a8ae", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "7K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x37887e13eea33fe683baaeecfc6fab7551efe90b", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "8K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x29f389c498c515aee795667cecb7b61e82b21cbc", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "789\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x564c5bea7cf74cc641b039e5c9449d875f2c5714", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2\r"}, {"Reported Name": "playdapp-marketplace", "Address": "0x616db65c22b16e7f08c66b4b592ebe88fad92e16", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2K\r"}, {"Reported Name": "playdapp-marketplace", "Address": "******************************************", "Name": "PlayDapp Marketplace", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "495\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "88\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "7M\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "ARB", "Has Logs": "", "Decoded": "", "Tx Count": "47\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "49\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "432K\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "20K\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "336K\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "6\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "4M\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "182K\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "176K\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "******************************************", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "179K\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "0x42e5e06ef5b90fe15f853f59299fc96259209c5c", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "176K\r"}, {"Reported Name": "<PERSON><PERSON><PERSON><PERSON>", "Address": "0x3801c3b3b5c98f88a9c9005966aa96aa440b9afc", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "8K\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "8\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "214\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "3M\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "ARB", "Has Logs": "", "Decoded": "", "Tx Count": "35\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "237K\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "8\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "34K\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "45K\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "30K\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "kyberswap-classic", "Address": "0x37e6449b0e99befd2a708ea048d970f4ff4dc65d", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "kyberswap-classic", "Address": "0x3add3034fcf921f20c74c6149fb44921709595b1", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "30K\r"}, {"Reported Name": "kyberswap-classic", "Address": "0x3904ac366d348636694cb6720aa1540e76441b1b", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "kyberswap-classic", "Address": "0x7018c0bd73255c8966d0b26634e0bc0c7595d255", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2K\r"}, {"Reported Name": "kyberswap-classic", "Address": "0x95d708e9ee04b0136b98579141624d19c89b9d68", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "802\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "651\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "100\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2K\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "276\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "6K\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "kyberswap-classic", "Address": "0xa1219dbe76eecbf7571fed6b020dd9154396b70e", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "5K\r"}, {"Reported Name": "kyberswap-classic", "Address": "0xbb2d00675b775e0f8acd590e08da081b2a36d3a6", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "kyberswap-classic", "Address": "******************************************", "Name": "KyberSwap Classic", "Category": "Exchange", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "473\r"}, {"Reported Name": "bomb-crypto-2", "Address": "******************************************", "Name": "Bomb Crypto 2", "Category": "Gaming", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "\r"}, {"Reported Name": "bomb-crypto-2", "Address": "******************************************", "Name": "Bomb Crypto 2", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "400K\r"}, {"Reported Name": "bomb-crypto-2", "Address": "******************************************", "Name": "Bomb Crypto 2", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "3K\r"}, {"Reported Name": "bomb-crypto-2", "Address": "******************************************", "Name": "Bomb Crypto 2", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "bomb-crypto-2", "Address": "******************************************", "Name": "Bomb Crypto 2", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "773K\r"}, {"Reported Name": "go2e", "Address": "******************************************", "Name": "GO2E", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "10K\r"}, {"Reported Name": "go2e", "Address": "******************************************", "Name": "GO2E", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "18K\r"}, {"Reported Name": "go2e", "Address": "******************************************", "Name": "GO2E", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "20K\r"}, {"Reported Name": "go2e", "Address": "******************************************", "Name": "GO2E", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "go2e", "Address": "******************************************", "Name": "GO2E", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "go2e", "Address": "0x3bfa9cb604fb6e28c128dc4c97575abae9e77f70", "Name": "GO2E", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "550\r"}, {"Reported Name": "go2e", "Address": "0x8d6664e35772737c1caad6a12b05dfd658df104f", "Name": "GO2E", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2K\r"}, {"Reported Name": "go2e", "Address": "0xe7eb03af8e3398d96955057be6484e4f83cc8b60", "Name": "GO2E", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "153\r"}, {"Reported Name": "go2e", "Address": "0x15bf349c635f34a9f9c2f5f1bb145574c0cee8b7", "Name": "GO2E", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "39\r"}, {"Reported Name": "go2e", "Address": "******************************************", "Name": "GO2E", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "20K\r"}, {"Reported Name": "go2e", "Address": "******************************************", "Name": "GO2E", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "crypto-unicorns", "Address": "******************************************", "Name": "Crypto Unicorns", "Category": "Gaming", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "380K\r"}, {"Reported Name": "crypto-unicorns", "Address": "******************************************", "Name": "Crypto Unicorns", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "93K\r"}, {"Reported Name": "crypto-unicorns", "Address": "******************************************", "Name": "Crypto Unicorns", "Category": "Gaming", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "44\r"}, {"Reported Name": "crypto-unicorns", "Address": "******************************************", "Name": "Crypto Unicorns", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "109K\r"}, {"Reported Name": "crypto-unicorns", "Address": "******************************************", "Name": "Crypto Unicorns", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "1M\r"}, {"Reported Name": "crypto-unicorns", "Address": "******************************************", "Name": "Crypto Unicorns", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "180K\r"}, {"Reported Name": "crypto-unicorns", "Address": "******************************************", "Name": "Crypto Unicorns", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "31K\r"}, {"Reported Name": "crypto-unicorns", "Address": "******************************************", "Name": "Crypto Unicorns", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "383K\r"}, {"Reported Name": "crypto-unicorns", "Address": "******************************************", "Name": "Crypto Unicorns", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "5K\r"}, {"Reported Name": "clipper-dex", "Address": "******************************************", "Name": "Clipper DEX", "Category": "Exchange Aggregator", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "304K\r"}, {"Reported Name": "clipper-dex", "Address": "******************************************", "Name": "Clipper DEX", "Category": "Exchange Aggregator", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "475K\r"}, {"Reported Name": "clipper-dex", "Address": "******************************************", "Name": "Clipper DEX", "Category": "Exchange Aggregator", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "125K\r"}, {"Reported Name": "mafiafoot", "Address": "******************************************", "Name": "MAFIAFOOT", "Category": "NFT Marketplace", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "2K\r"}, {"Reported Name": "mafiafoot", "Address": "******************************************", "Name": "MAFIAFOOT", "Category": "NFT Marketplace", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "32M\r"}, {"Reported Name": "world-war", "Address": "******************************************", "Name": "World War", "Category": "Gaming", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "world-war", "Address": "******************************************", "Name": "World War", "Category": "Gaming", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "3K\r"}, {"Reported Name": "world-war", "Address": "******************************************", "Name": "World War", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "35K\r"}, {"Reported Name": "world-war", "Address": "******************************************", "Name": "World War", "Category": "Gaming", "Network": "ARB", "Has Logs": "", "Decoded": "", "Tx Count": "3K\r"}, {"Reported Name": "world-war", "Address": "******************************************", "Name": "World War", "Category": "Gaming", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "160\r"}, {"Reported Name": "world-war", "Address": "******************************************", "Name": "World War", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "6M\r"}, {"Reported Name": "world-war", "Address": "******************************************", "Name": "World War", "Category": "Gaming", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "bungee", "Address": "******************************************", "Name": "Bungee", "Category": "<PERSON><PERSON><PERSON>", "Network": "ETH", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "107K\r"}, {"Reported Name": "bungee", "Address": "******************************************", "Name": "Bungee", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "378K\r"}, {"Reported Name": "bungee", "Address": "******************************************", "Name": "Bungee", "Category": "<PERSON><PERSON><PERSON>", "Network": "ARB", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "322K\r"}, {"Reported Name": "stargate-finance", "Address": "******************************************", "Name": "Stargate Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "169K\r"}, {"Reported Name": "stargate-finance", "Address": "******************************************", "Name": "Stargate Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "97K\r"}, {"Reported Name": "stargate-finance", "Address": "******************************************", "Name": "Stargate Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "ETH", "Has Logs": "", "Decoded": "", "Tx Count": "2\r"}, {"Reported Name": "stargate-finance", "Address": "******************************************", "Name": "Stargate Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "12K\r"}, {"Reported Name": "stargate-finance", "Address": "******************************************", "Name": "Stargate Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "25K\r"}, {"Reported Name": "stargate-finance", "Address": "******************************************", "Name": "Stargate Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "ETH", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "51K\r"}, {"Reported Name": "stargate-finance", "Address": "******************************************", "Name": "Stargate Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "63K\r"}, {"Reported Name": "stargate-finance", "Address": "******************************************", "Name": "Stargate Finance", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "38\r"}, {"Reported Name": "snook", "Address": "******************************************", "Name": "Snook", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "18K\r"}, {"Reported Name": "snook", "Address": "******************************************", "Name": "Snook", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "198K\r"}, {"Reported Name": "snook", "Address": "0xf9c79145f8314311814da0de2767e8fd6b30c24f", "Name": "Snook", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "112\r"}, {"Reported Name": "snook", "Address": "0xd6588e9c555d35b0274f47dc6b9d4d7917602347", "Name": "Snook", "Category": "Gaming", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "12K\r"}, {"Reported Name": "snook", "Address": "0x86c1a81e411fbb463242c0f6514973d69a615bf0", "Name": "Snook", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "272\r"}, {"Reported Name": "snook", "Address": "0x73f45fa6f81535596600b7c2c93f4b5a71cb55e8", "Name": "Snook", "Category": "Gaming", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "snook", "Address": "0x6bc8f0128e4aaa0b3678b67bb93715fa70faf3d3", "Name": "Snook", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "46K\r"}, {"Reported Name": "snook", "Address": "0x1d9da4c160a34dda9c808a49d54112a5913c216c", "Name": "Snook", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "2.7M\r"}, {"Reported Name": "snook", "Address": "0x31f528c6c193b27d77b874d1c28d113103af04bf", "Name": "Snook", "Category": "Gaming", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "snook", "Address": "0x3473fd176c8f552a1283165cf586ee44bd5b168a", "Name": "Snook", "Category": "Gaming", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "snook", "Address": "0x45541fe85c750cf891ad751859fe5ce6786bbabb", "Name": "Snook", "Category": "Gaming", "Network": "POLY", "Has Logs": "yes", "Decoded": "no", "Tx Count": "455K\r"}, {"Reported Name": "snook", "Address": "0xe9ae27c02fada3a19bfc63447215eba838a700bb", "Name": "Snook", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "6K\r"}, {"Reported Name": "snook", "Address": "0xfb88fbbe32f01639cf2dafab20439d8ec595dda5", "Name": "Snook", "Category": "Gaming", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "snook", "Address": "0xbc0d2e182eb1d1b0b73e9f5fa9bf1ee93c30ef38", "Name": "Snook", "Category": "Gaming", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "534\r"}, {"Reported Name": "snook", "Address": "0x2344afe6c5729e12ab7ae34af5f3ef2174d5be4b", "Name": "Snook", "Category": "Gaming", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0xc0788a3ad43d79aa53b09c2eacc313a787d1d607", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "no", "Decoded": "no", "Tx Count": "1M\r"}, {"Reported Name": "apeswap", "Address": "0x1f234b1b83e21cb5e2b99b4e498fe70ef2d6e3bf", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "25\r"}, {"Reported Name": "apeswap", "Address": "0x54aff400858dcac39797a81894d9920f16972d1d", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "318K\r"}, {"Reported Name": "apeswap", "Address": "0x019011032a7ac3a87ee885b6c08467ac46ad11cd", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "331\r"}, {"Reported Name": "apeswap", "Address": "0xb7586140f2f14582e5b6be98ddd89fa243c53ce2", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0xd32f3139a214034a0f9777c87ee0a064c1ff6ae2", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "5K\r"}, {"Reported Name": "apeswap", "Address": "0x65d43b64e3b31965cd5ea367d4c2b94c03084797", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "8K\r"}, {"Reported Name": "apeswap", "Address": "0x034293f21f1cce5908bc605ce5850df2b1059ac0", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "27K\r"}, {"Reported Name": "apeswap", "Address": "0x6cf8654e85ab489ca7e70189046d507eba233613", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "10K\r"}, {"Reported Name": "apeswap", "Address": "0xe82635a105c520fd58e597181cbf754961d51e3e", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "4K\r"}, {"Reported Name": "apeswap", "Address": "0x5b13b583d4317ab15186ed660a1e4c65c10da659", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "10K\r"}, {"Reported Name": "apeswap", "Address": "0x0359001070cf696d5993e0697335157a6f7db289", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "5K\r"}, {"Reported Name": "apeswap", "Address": "0x16dd633e4904673b7e9638568df4c090098989e5", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x44b82c02f404ed004201fb23602cc0667b1d011e", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "8K\r"}, {"Reported Name": "apeswap", "Address": "0xa8eca6cc6fb9f8cfa9d3b17d4997cce79e5110cf", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "1K\r"}, {"Reported Name": "apeswap", "Address": "0x42ed6d85ccf43859cbc46f6efa1f21e21cc24030", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "757\r"}, {"Reported Name": "apeswap", "Address": "0x5d9d66ac0db91ec463fb3e9e5b1513dbff02fd0f", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "5K\r"}, {"Reported Name": "apeswap", "Address": "0x7433afe84df37d0954ff87d7f5788f124f8597f8", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "463\r"}, {"Reported Name": "apeswap", "Address": "0xb01baf15079ee93590a862df37234e8f7c9825bf", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "1.8K\r"}, {"Reported Name": "apeswap", "Address": "0x2d28d3c989c6c0717a49531f0de17f3ca81d12db", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x9ec257c1862f1bdf0603a6c20ed6f3d6bae6deb0", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "2K\r"}, {"Reported Name": "apeswap", "Address": "0xb8e54c9ea1616beebe11505a419dd8df1000e02a", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "28K\r"}, {"Reported Name": "apeswap", "Address": "0xd7b3132b9aedc86e6cd28ba3b29da6c30c049327", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "91\r"}, {"Reported Name": "apeswap", "Address": "0x3469a9de3e8148568776d4776ae719562e961b85", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "24\r"}, {"Reported Name": "apeswap", "Address": "0xcfdcb934657c1fb627db8f2b690ebbafd7a46e31", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "161\r"}, {"Reported Name": "apeswap", "Address": "0x443062b6dd5f2ab899172b0f620733726cea4cfd", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x7fb0aa677789b92808943c5be8d853e0865aaa5d", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "9\r"}, {"Reported Name": "apeswap", "Address": "0xadeb59dfc43f10f09e6a29588676cb6b14c1313a", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x82d947f710878eb357a9f68e42a39461595e2e30", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "\r"}, {"Reported Name": "apeswap", "Address": "0x64d1393b53e3c4a8b04fb1dde2c7b40bc0897222", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "2K\r"}, {"Reported Name": "apeswap", "Address": "0xb94f216e781edd465ff3725171bb30fb5b18edea", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x84964d9f9480a1db644c2b2d1022765179a40f68", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "600\r"}, {"Reported Name": "apeswap", "Address": "0x04fedb0e01fd7cce28f408e48821b30d027bf0c4", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "2\r"}, {"Reported Name": "apeswap", "Address": "0x4e45459daf7f10b3b825fca477d8b5a19671150a", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "10\r"}, {"Reported Name": "apeswap", "Address": "0x4b3bf679858da92c7443b3f2715cd1638899f3a3", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "3\r"}, {"Reported Name": "apeswap", "Address": "0x51baa4ee5881e49e598176998b35244aad384845", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0xede04e0cd393a076c49deb95d3686a52ccc49c71", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "859\r"}, {"Reported Name": "apeswap", "Address": "0x90498b6a4b9adf226b5041f36cdab1104a501750", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "17\r"}, {"Reported Name": "apeswap", "Address": "0xcdbcc1ac4d4e18fb1f2f2d604144fd33e77cda52", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "443\r"}, {"Reported Name": "apeswap", "Address": "0xc1dd63ca154837ac4356d888f7c10fbbe442407e", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "yes", "Decoded": "yes", "Tx Count": "5K\r"}, {"Reported Name": "apeswap", "Address": "0x2f87bfdb5074069a246d26d0c590512a10c980ae", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x600cc185c9883ac9113b376086d427f70b909894", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x6d9528ccf392e4f2369197510c9412b2e7027ba2", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "1\r"}, {"Reported Name": "apeswap", "Address": "0x58e75f54e5ac4038934e70914c0227a9905607eb", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "1\r"}, {"Reported Name": "apeswap", "Address": "0xed4c3c81df68feb16697c046d316f69fceb6b0cc", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x423d1df2b4092e43e630ad012baf0b5236375e7e", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0xd338f9463703f3f400231a8449cb591e9aa09ca7", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x88f3d1a86cb51007807f7ef11d89bc6c2ef8f8fb", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x64eb322399091fbb7bd655f787afbd9bc163e4e4", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x0bcc110c071c66b00437ff3acfc50851b3b21a02", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x0d9fb8f92256037632b39e8e87e84d3d03a63093", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x7b2dd4bab4487a303f716070b192543ea171d3b2", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "", "Decoded": "", "Tx Count": "489\r"}, {"Reported Name": "apeswap", "Address": "0xbff538ad7c1fd067f5529bbc7aa3b403f66d70cf", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "298\r"}, {"Reported Name": "apeswap", "Address": "0xab291e39cd0f8235ba42a20a86f996a3f8b934e1", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "335\r"}, {"Reported Name": "apeswap", "Address": "0x8bf34ff5945d519cf3eb4360e97585605c99daa6", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "POLY", "Has Logs": "Yes", "Decoded": "No", "Tx Count": "336\r"}, {"Reported Name": "apeswap", "Address": "0xa4b3a16a18c6006524e8ef84b2bc3fe3c30bb238", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}, {"Reported Name": "apeswap", "Address": "0x5084d626202e2eabd08fd2025b49745aa830a0ed", "Name": "ApeSwap", "Category": "<PERSON><PERSON><PERSON>", "Network": "UNKNOWN", "Has Logs": "No", "Decoded": "No", "Tx Count": "0\r"}]}