/// <reference types="cypress" />
// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
Cypress.Commands.add('login', () => {
	cy.session('<EMAIL>', () => {
		cy.visit('/')

		cy.get('input[id="email"]').type('<EMAIL>');
		cy.get('input[id="password"]').type(`peanuts!`);
		cy.get('#login-button').click();

		cy.url().should('include', '/home')
	});
});

Cypress.Commands.add('getBySel', (selector, ...args) => {
	return cy.get(`[data-cy=${selector}]`, ...args);
});
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })
//
declare namespace Cypress {
	interface Chainable {
		login(): Chainable<void>
		getBySel(selector: string, ...args: any[]): Chainable<any>
	}
}
