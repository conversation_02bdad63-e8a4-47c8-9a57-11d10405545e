# Tag-Based Tools Documentation

This document provides a comprehensive guide for understanding and implementing tag-based tools in the Raleon Web App. Tag-based tools allow the AI to generate structured content that is then processed and rendered with special UI components.

## Overview

Tag-based tools follow a consistent pattern where the AI can output content wrapped in specific XML-like tags. The frontend then parses these tags and renders them with specialized components instead of plain text.

## Architecture

The tag-based tool system consists of several key components:

1. **Tool Handlers** - Process streaming content and emit events
2. **Message Parser** - Parses XML tags from AI responses
3. **Message Artifact Service** - Converts parsed segments to display components
4. **Vue Components** - Render the specialized UI for each tool type
5. **Chat Streaming Composable** - Manages streaming state and tool coordination

## Existing Tools

### Core Content Tools
- **`brief`** - Email briefs with subject, preview, and content
- **`email`** - Email designs with component JSON
- **`plan`** - Marketing plans with structured campaign data

### Action Tools
- **`buildplan`** - Triggers plan building workflow

### Media Tools
- **`image`** - Single image display
- **`multiimage`** - Multiple image gallery display
- **`ig`** - Image generation parameters
- **`ie`** - Image editing parameters
- **`upload`** - Base64 image upload handling

### Utility Tools
- **`memory`** - Brand memory storage
- **`tool_message`** - Tool execution messages
- **`switch_mode`** - Mode switching with navigation

## Implementation Guide

To add a new tag-based tool, you need to update the following files:

### 1. Create Tool Handler (`/public/client/services/tools/[ToolName]ToolHandler.ts`)

```typescript
import { BaseToolHandler } from '../toolHandlerService';
import { Emitter } from 'mitt';

interface [ToolName]Data {
  // Define your tool's data structure
  field1: string;
  field2: string;
}

export class [ToolName]ToolHandler extends BaseToolHandler {
  public readonly tag = 'your_tag_name'; // Unique tag for this tool

  constructor(emitter: Emitter<any>) {
    super(emitter);
    this.register(); // Explicitly register after tag is initialized
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId;
    this.streamingContent = initialContent;
    console.log(`${this.tag} tool started. PlaceholderId: ${this.placeholderId}`);
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) return;
    console.log(`${this.tag} tool received chunk: "${contentChunk}"`);
    this.streamingContent += contentChunk;
  }

  onEnd(): void {
    if (!this.placeholderId) {
      console.warn(`${this.tag} handler received end signal but was not started or already ended.`);
      return;
    }

    const rawContent = this.streamingContent.trim();
    console.log(`${this.tag} tool: Processing raw content`);

    // Parse your tool's data
    let toolData: [ToolName]Data | null = null;
    try {
      // Add your parsing logic here
      const parsedData = JSON.parse(rawContent);
      toolData = {
        field1: parsedData.field1 || '',
        field2: parsedData.field2 || ''
      };
    } catch (error) {
      console.error(`${this.tag} tool: Error parsing data:`, error);
    }

    // Emit events
    if (toolData) {
      this.emitter.emit('chat:update-segment', {
        type: 'your_tag_name',
        sender: 'ai',
        toolData: toolData,
        timestamp: new Date(),
        placeholderId: this.placeholderId,
      });
    }

    // Clean up
    this.emitter.emit(`${this.tag}:complete`, {
      placeholderId: this.placeholderId,
      finalContent: toolData,
      success: toolData !== null
    });

    this.placeholderId = null;
    this.streamingContent = '';
  }
}
```

### 2. Register Tool Handler (`/public/client/services/tools/index.ts`)

```typescript
// Add import
import { [ToolName]ToolHandler } from './[ToolName]ToolHandler';

// Add to initializeToolHandlers function
export function initializeToolHandlers() {
  // ... existing handlers
  new [ToolName]ToolHandler(emitter);
  
  console.log('All tool handlers initialized and registered, including [ToolName].');
  return emitter;
}
```

### 3. Update Message Artifact Service (`/public/client/services/messageArtifactService.ts`)

```typescript
// Add to Artifact type union
export interface Artifact {
  type:
    | 'brief_artifact'
    | 'email_artifact'
    // ... existing types
    | 'your_tag_name';
  // ... rest of interface
}

// Add to ChatMessageSegment type union
export interface ChatMessageSegment {
  id: string;
  type:
    | 'text'
    | 'brief_placeholder'
    // ... existing types
    | 'your_tag_name';
  // ... existing properties
  yourTagData?: {
    field1: string;
    field2: string;
  };
}

// Add tag finding in findAllArtifacts method
private findAllArtifacts(content: string, artifacts: Artifact[]): void {
  const findArtifacts = (pattern: RegExp, type: Artifact['type']) => {
    // ... existing logic
  };

  // ... existing findArtifacts calls
  findArtifacts(/<your_tag_name>([\s\S]*?)<\/your_tag_name>/gm, 'your_tag_name');
}

// Add processing in convertToChatSegment method
private convertToChatSegment(segment: Artifact, generateId: () => string, timestamp: string | Date): ChatMessageSegment | null {
  // ... existing cases
  
  } else if (segment.type === 'your_tag_name' && segment.rawContent) {
    try {
      const toolContent = jsonrepair(segment.rawContent.trim());
      const toolData = JSON.parse(toolContent);
      return {
        id: generateId(),
        type: 'your_tag_name',
        sender: 'ai',
        yourTagData: {
          field1: toolData.field1 || '',
          field2: toolData.field2 || '',
        },
        timestamp: new Date(timestamp),
      };
    } catch (e) {
      console.error('Error processing your_tag_name content:', e);
      return {
        id: generateId(),
        type: 'text',
        sender: 'ai',
        content: `Error processing tool data: ${e.message}`,
        timestamp: new Date(timestamp),
      };
    }
  }
  
  return null;
}
```

### 4. Update Message Parser Service (`/public/client/services/messageParserService.ts`)

```typescript
// Add to ToolStartSegment type union
export interface ToolStartSegment {
  type: 'tool_start';
  tag: 'brief' | 'email' | /* ... existing tags */ | 'your_tag_name';
  placeholderId?: string;
}

// Add to ToolContentSegment type union
export interface ToolContentSegment {
  type: 'tool_content';
  tag: 'brief' | 'email' | /* ... existing tags */ | 'your_tag_name';
  content: string;
}

// Add to ToolEndSegment type union
export interface ToolEndSegment {
  type: 'tool_end';
  tag: 'brief' | 'email' | /* ... existing tags */ | 'your_tag_name';
  rawContent?: string;
  placeholderId?: string;
  finalPayload?: any;
}

// Add to state union
private state: 'outside' | 'in_brief' | /* ... existing states */ | 'in_your_tag_name' = 'outside';

// Add to currentTag union
private currentTag: 'brief' | 'email' | /* ... existing tags */ | 'your_tag_name' | null = null;

// Add tag detection in processChunk method
const yourTagStartIndex = this.buffer.indexOf('<your_tag_name>', currentIndex);

// Add to first tag finding logic
} else if (yourTagStartIndex !== -1) {
  console.log('Found <your_tag_name> tag at position', yourTagStartIndex);
  firstTagIndex = yourTagStartIndex;
  tag = 'your_tag_name';
  tagLength = '<your_tag_name>'.length;
}

// Add state handling
} else if (tag === 'your_tag_name') {
  console.log('Entering your_tag_name state with placeholderId:', this.activeTagPlaceholderId);
  this.state = 'in_your_tag_name';
}

// Add to state check condition
} else if (this.state === 'in_brief' || /* ... existing states */ || this.state === 'in_your_tag_name') {

// Add to tagsToScan array
const tagsToScan = ['<brief>', '<email>', /* ... existing tags */, '<your_tag_name>'];
```

### 5. Update Chat Streaming Composable (`/public/client/composables/useChatStreaming.ts`)

```typescript
// Add state variables
const currentYourTagPlaceholderId = ref<string | null>(null);
const yourTagBuffer = ref('');

// Add to tagsToClean array
const tagsToClean = ['ig', 'ie', /* ... existing tags */, 'your_tag_name'];

// Add tool_start case
} else if (toolStart.tag === 'your_tag_name') {
  const yourTagId = toolStart.placeholderId || generateUniqueId();
  chatSegments.value.push({
    id: yourTagId, type: 'your_tag_name', sender: 'ai', isGenerating: true, timestamp: new Date()
  });
  currentYourTagPlaceholderId.value = yourTagId;
  yourTagBuffer.value = '';
}

// Add tool_content case
} else if (toolContent.tag === 'your_tag_name' && currentYourTagPlaceholderId.value) {
  yourTagBuffer.value += toolContent.content;
}

// Add tool_end case
} else if (toolEnd.tag === 'your_tag_name' && currentYourTagPlaceholderId.value) {
  const placeholderId = currentYourTagPlaceholderId.value;
  const contentToParse = toolEnd.rawContent || yourTagBuffer.value;
  try {
    const parsedData = parseJsonSafely(contentToParse);
    const segmentIndex = chatSegments.value.findIndex(s => s.id === placeholderId);
    if (segmentIndex !== -1) {
      const finalSegment = {
        ...chatSegments.value[segmentIndex],
        type: 'your_tag_name' as const,
        isGenerating: false,
        yourTagData: {
          field1: parsedData.field1 || '',
          field2: parsedData.field2 || ''
        },
      };
      chatSegments.value[segmentIndex] = finalSegment;
      toolEvents.emit('your_tag_name:add', finalSegment);
    }
  } catch (error) {
    console.error('Failed to parse your_tag_name JSON:', error);
    // Handle error...
  } finally {
    currentYourTagPlaceholderId.value = null;
    yourTagBuffer.value = '';
  }
}
```

### 6. Add UI Components

Add rendering logic to your Vue components (Chat.ts.vue, BriefChat.ts.vue, etc.):

```vue
<template>
  <!-- Add after existing segment renderers -->
  <div
    v-if="segment.type === 'your_tag_name' && segment.yourTagData"
    class="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-xl p-4 shadow-sm max-w-md"
  >
    <div class="flex items-center gap-2 mb-2">
      <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
        <!-- Your icon path -->
      </svg>
      <div class="text-sm font-semibold text-green-800">
        Your Tool Name
      </div>
    </div>
    <div class="text-sm text-gray-700">
      {{ segment.yourTagData.field1 }}
    </div>
    <div class="text-xs text-green-600">
      {{ segment.yourTagData.field2 }}
    </div>
  </div>
</template>
```

## Tool Handler Event System

Tool handlers communicate through an event system using mitt. Common events:

- **`chat:update-segment`** - Update or add a chat segment
- **`[tool]:add`** - Tool-specific completion event
- **`[tool]:complete`** - Generic completion event
- **`[tool]:error`** - Error handling event

## Best Practices

1. **Error Handling**: Always wrap JSON parsing in try-catch blocks
2. **State Management**: Clean up placeholderIds and buffers in finally blocks  
3. **Debugging**: Add comprehensive console.log statements for debugging
4. **Type Safety**: Define TypeScript interfaces for your tool's data structure
5. **Fallback**: Provide fallback text segments when parsing fails
6. **Event Naming**: Use consistent event naming conventions
7. **Content Validation**: Validate required fields before processing

## Testing Your Tool

1. **Backend**: Ensure your AI can output the correct tag format
2. **Parsing**: Test with malformed JSON to ensure graceful error handling
3. **UI**: Verify the component renders correctly in both Chat and BriefChat
4. **Events**: Check that tool events are emitted and handled properly
5. **Streaming**: Test with streamed content to ensure proper assembly

## Common Issues

1. **Tag Not Recognized**: Check that the tag is added to all relevant type unions
2. **Parsing Errors**: Ensure JSON repair and validation logic is robust
3. **UI Not Rendering**: Verify the segment type is included in v-if conditions
4. **State Leaks**: Always clean up state in finally blocks
5. **Missing Events**: Ensure events are emitted and listeners are registered

## Example: Switch Mode Tool

The switch_mode tool is a complete example of a tag-based tool implementation. It demonstrates:

- JSON parsing with validation
- Navigation functionality
- Clean error handling
- Proper state management
- Event emission and handling

Refer to the switch_mode implementation files for a working example of all the patterns described in this guide.