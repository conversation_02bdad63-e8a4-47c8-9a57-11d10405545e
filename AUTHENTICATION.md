# Authentication & Authorization Guide

## Overview

The Raleon Web App uses a comprehensive authentication and authorization system built on LoopBack 4's security framework. This document details the decorators, roles, and guard checks used throughout the application.

## Authentication Strategies

### JWT Authentication

The primary authentication method uses JWT tokens:

```typescript
@authenticate('jwt')
```

This decorator is applied to most controller methods to ensure users are authenticated before accessing endpoints.

### Other Authentication Strategies

The application also supports:
- **API Key Authentication** - For external integrations
- **Shopify Customer Auth** - For Shopify-specific operations  
- **User Access Token** - For specific user-scoped operations

## Authorization System

### @authorize Decorator

The `@authorize` decorator provides role-based access control:

```typescript
@authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
})
```

#### Parameters:
- **allowedRoles**: Array of role strings that can access the endpoint
- **voters**: Authorization functions that determine access (typically `basicAuthorization`)

### Available Roles

The system supports the following roles with different privilege levels:

#### Core Roles:
- **`admin`** - Full system access, bypasses most restrictions
- **`support`** - Support team access, bypasses ID verification  
- **`customer`** - Standard customer/organization user
- **`customer-admin`** - Customer organization administrator
- **`raleon-admin`** - Raleon internal administrator

#### Role Hierarchy:
- `admin` and `support` roles bypass most authorization checks
- `raleon-admin` has elevated privileges for internal operations
- `customer-admin` has administrative rights within their organization
- `customer` has standard user access within their organization

### Basic Authorization Voter

The `basicAuthorization` function (`src/services/basic.authorizor.ts:9`) implements the core authorization logic:

1. **Authentication Check**: Verifies user is authenticated
2. **Role Validation**: Checks if user has required roles
3. **Admin/Support Bypass**: Allows admin/support to bypass restrictions
4. **Organization Scoping**: Restricts access to user's organization (planned)

## Guard System

### Guard Checks

The application uses a guard system to restrict data access based on user context and organization ownership.

#### @skipGuardCheck Decorator

Use this decorator to bypass guard restrictions:

```typescript
@skipGuardCheck()
async findByCampaignId(
    @param.path.string('campaignId') campaignId: string,
    @injectUserOrgId() orgId: number
): Promise<any> {
```

**Common use cases for @skipGuardCheck:**
- Admin endpoints that need to access data across organizations
- Public endpoints that don't require organization scoping
- Endpoints where manual organization checking is implemented

### Guard Strategies

Guard strategies are defined for models to automatically filter data:

```typescript
@guardStrategy({
    restrictReadsWithGuard: true,
    restrictCreatesWithGuard: true,
    restrictUpdatesWithGuard: true,
    restrictDeletesWithGuard: true
})
```

### Organization Injection

The `@injectUserOrgId()` decorator automatically injects the user's organization ID:

```typescript
async findByCampaignId(
    @param.path.string('campaignId') campaignId: string,
    @injectUserOrgId() orgId: number
): Promise<any> {
```

This ensures endpoints automatically scope data to the user's organization.

## Common Patterns

### Standard Controller Method

```typescript
@authenticate('jwt')
@authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
})
@get('/campaigns/{id}')
async findById(
    @param.path.string('id') id: string,
    @injectUserOrgId() orgId: number
): Promise<Campaign> {
    // Implementation automatically scoped to orgId
}
```

### Admin-Only Endpoint

```typescript
@authenticate('jwt')
@authorize({
    allowedRoles: ['admin'],
    voters: [basicAuthorization],
})
@skipGuardCheck()
@get('/admin/all-campaigns')
async findAllCampaigns(): Promise<Campaign[]> {
    // Admin can access all campaigns across organizations
}
```

### Public Endpoint (No Auth Required)

```typescript
// No @authenticate decorator
@skipGuardCheck()
@get('/public/status')
async getStatus(): Promise<object> {
    return {status: 'ok'};
}
```

### Extended Permissions

```typescript
@authenticate('jwt')
@authorize({
    allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
    voters: [basicAuthorization],
})
@get('/plans/{id}')
async findPlan(
    @param.path.string('id') id: string,
    @injectUserOrgId() orgId: number
): Promise<Plan> {
    // Multiple roles can access this endpoint
}
```

## Security Best Practices

### 1. Always Use Authentication
Most endpoints should have `@authenticate('jwt')` unless they are intentionally public.

### 2. Define Appropriate Roles
- Use minimal required roles for each endpoint
- Consider if customers need admin-level access
- Include support role for customer service scenarios

### 3. Organization Scoping
- Use `@injectUserOrgId()` to automatically scope data
- Only use `@skipGuardCheck()` when organization scoping is not needed
- Admin/support roles automatically bypass organization restrictions

### 4. Consistent Authorization
- Always include `voters: [basicAuthorization]` in authorize decorators
- Follow existing role patterns throughout the codebase

## Default CRUD Controller Behavior

Auto-generated CRUD controllers use these defaults:

```typescript
@authenticate('jwt')
@authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
})
```

Custom endpoints can override these defaults as needed.

## Troubleshooting

### Common Issues:

1. **403 Forbidden**: User lacks required role for endpoint
2. **401 Unauthorized**: JWT token missing or invalid  
3. **Empty Results**: Guard system filtering data based on organization
4. **Guard Errors**: Missing `@skipGuardCheck()` on cross-organization endpoints

### Debugging Tips:

- Check user roles in JWT token payload
- Verify organization ID is being injected correctly
- Use `@skipGuardCheck()` to test if guards are causing issues
- Admin/support roles bypass most restrictions for testing

## File Locations

- **Basic Authorization**: `src/services/basic.authorizor.ts`
- **Guard Interceptor**: `src/interceptors/crud-guard.interceptor.ts`
- **CRUD Defaults**: `src/crud-rest-default.ts`
- **Example Controllers**: `src/controllers/*.ts`

This authentication and authorization system provides secure, role-based access control while maintaining organization data isolation and supporting various user types and access patterns.