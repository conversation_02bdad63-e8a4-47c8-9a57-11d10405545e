const plugin = require('tailwindcss/plugin');

module.exports = {
  content: [
    './index.html',
    './public/**/*.{vue,js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        'ralpurple': {
          300: '#9259EB',
          400: '#A183F0',
          500: '#6635E6',
          600: '#4C14DF',
          700: '#370AAE',
        },
        'ralocean': {
          300: '#6CBBE9',
          400: '#42A7E3',
          500: '#1B97DF',
          600: '#068AD9',
          700: '#045F94',
        },
        'ralgranite': {
          300: '#4C596D',
          400: '#323C4C',
          500: '#1E293B',
          600: '#121D2F',
          700: '#071120',
        },
        'ralcloud': {
          300: '#FFFFFF',
          400: '#FFFFFF',
          500: '#C7D2FE',
          600: '#9AADFB',
          700: '#738CF1',
        },
        'ralpumpkin': {
          300: '#FFC86A',
          400: '#FDB741',
          500: '#F59E0B',
          600: '#BF7902',
          700: '#965F00',
        },
        'ralmidnight': {
          300: '#7F7E7E',
          400: '#464646',
          500: '#000000',
          600: '#030101',
        },
        'ralapple': {
          300: '#5AD1AA',
          400: '#33C293',
          500: '#10B981',
          600: '#009B68',
          700: '#007851',
        },
        'ralseafoam': {
          300: '#69F8E0',
          400: '#3AFADA',
          500: '#0BFAD4',
          600: '#00F7CE',
          700: '#00B99A',
        },
        'ralcottoncandy': {
          400: '#FDEEFE',
          500: '#F1C0F6',
          600: '#DE92E6',
          700: '#C165CB',
        },

		'ralprimary': {
			'main': '#5A16C9',
			'light': '#9254F7',
			'dark': '#400F92',
			'ultralight': '#C2BEEA',
			'highlighted': '#5E47F8'
		},
		'ralsecondary': {
			'start': '#202041',
			'end': '#2A263F',
		},
		'raltable': {
			'ring': {
				'primary': 'rgba(215, 195, 249, 0.12)',
				'secondary': 'rgba(146, 84, 247, 0.5)',
				'tertiary': 'rgba(37, 99, 235, 0.5)',
			},
		},
		'ralbackground': {
			'dark': {
				'base': 'rgba(32, 32, 32, 0.80)',
				'primary': '#2B2D30',
				'secondary': '#3C3C42',
				'line': '#3F444D',
			},
			'light': {
				'base': '#FFFFFF',
				'primary': '#F8F9FC',
				'secondary': '#F1F3F9',
				'tertiary': '#BFDBFE',
				'line': 'E1E6EF',
			}
		},
		'ralbutton': {
			'primary': {
				'dark': {
					'selected': 'rgba(225, 182, 252, 0.2)'
				},
				'light': {
					'active': 'linear-gradient(225deg, #4F3EAC 0%, #5E48F8 100%)',
					'selected': '#E3E3F8',
					'deactivated': '#D1D5DB',
					'active-active': '#4F3EAC',
				},
			},
			'secondary': {
				'light': '#958F9D'
			},
		},
		'ralwhite': {
			'line': '#E1E6EF',
			'secondary': '#F1F3F9',
		},
		'ralblack': {
			'primary': '#202020',
			'secondary': '#4D4D4D',
		},
		'ralsuccess': {
			'dark': '#15803D',
			'light': 'rgba(134, 239, 172, 0.5)'
		},
		'ralinfo': {
			'dark': '#2563Eb',
			'light': '#BFDBFE'
		},
		'ralwarning': {
			'dark': '#CB9200',
			'light': 'rgba(255, 237, 142, 0.5)'
		},
		'ralerror': {
			'dark': '#DC2626',
		},
		'ralgray': {
			'light': 'rgba(32, 32, 32, 0.25)',
			'main': '#9CA3AF',
			'dark': '#4B5563'
		},
	},
	backgroundImage: theme => ({
		'gradient-button-primary': `linear-gradient(225deg, #4F3EAC 0%, #5E48F8 100%)`,
		'gradient-action-prompt-bg': `linear-gradient(140.19deg, rgba(146, 84, 247, 0.2) 8.22%, rgba(90, 22, 201, 0.2) 87.25%)`,
		'sticky-background' : 'linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent)',
	}),
      boxShadow: {
        DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.08), 0 1px 2px 0 rgba(0, 0, 0, 0.02)',
        md: '0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.02)',
        lg: '0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.01)',
        xl: '0 20px 25px -5px rgba(0, 0, 0, 0.08), 0 10px 10px -5px rgba(0, 0, 0, 0.01)',
      },
      outline: {
        blue: '2px solid rgba(0, 112, 244, 0.5)',
      },
      fontFamily: {
        inter: ['Inter', 'sans-serif'],
      },
      fontSize: {
        xs: ['0.75rem', { lineHeight: '1.5' }],
        sm: ['0.875rem', { lineHeight: '1.5715' }],
        base: ['1rem', { lineHeight: '1.5', letterSpacing: '-0.01em' }],
        lg: ['1.125rem', { lineHeight: '1.5', letterSpacing: '-0.01em' }],
        xl: ['1.25rem', { lineHeight: '1.5', letterSpacing: '-0.01em' }],
        '2xl': ['1.5rem', { lineHeight: '1.33', letterSpacing: '-0.01em' }],
        '3xl': ['1.88rem', { lineHeight: '1.33', letterSpacing: '-0.01em' }],
        '4xl': ['2.25rem', { lineHeight: '1.25', letterSpacing: '-0.02em' }],
        '5xl': ['3rem', { lineHeight: '1.25', letterSpacing: '-0.02em' }],
        '6xl': ['3.75rem', { lineHeight: '1.2', letterSpacing: '-0.02em' }],
		  overline: ['0.75rem', { lineHeight: '1.5', letterSpacing: '0.1em', transform: 'uppercase' }],
      },
      screens: {
        xs: '480px',
      },
      borderWidth: {
        3: '3px',
		  16: '16px',
      },
	  borderRadius: {
		'button': '100px',
	  },
      minWidth: {
        36: '9rem',
        44: '11rem',
        56: '14rem',
        60: '15rem',
        72: '18rem',
        80: '20rem',
      },
      maxWidth: {
        '8xl': '88rem',
        '9xl': '96rem',
      },
      zIndex: {
        60: '60',
      },
      animation: {
        'waving-hand': 'wave 2s linear infinite',
        'fadein-content': 'fadein 500ms',
		'pulse': 'pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: theme => ({
        wave: {
          '0%': { transform: 'rotate(0.0deg)' },
          '10%': { transform: 'rotate(14deg)' },
          '20%': { transform: 'rotate(-8deg)' },
          '30%': { transform: 'rotate(14deg)' },
          '40%': { transform: 'rotate(-4deg)' },
          '50%': { transform: 'rotate(10.0deg)' },
          '60%': { transform: 'rotate(0.0deg)' },
          '100%': { transform: 'rotate(0.0deg)' },
        },
        fadein: {
          '0%': { opacity: 0 },
          '50%': { opacity: 0.25 },
          '100%': { opacity: 1 },
        },
        pulse: {
			'0%, 100%': { opacity: 1 },
			'50%': { opacity: 0.5 },
		  },
      }),
    },
  },
  plugins: [
    // eslint-disable-next-line global-require
    require('@tailwindcss/forms'),
    // eslint-disable-next-line global-require
    require('@tailwindcss/typography'),
    // add custom variant for expanding sidebar
    plugin(({ addVariant, e }) => {
      addVariant('sidebar-expanded', ({ modifySelectors, separator }) => {
        modifySelectors(({ className }) => `.sidebar-expanded .${e(`sidebar-expanded${separator}${className}`)}`);
      });
    }),
  ],
};
