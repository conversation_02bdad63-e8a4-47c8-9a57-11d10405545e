import { defineCustomElement, h, nextTick } from 'vue';
import { getEmailBrandingData } from '../email-templates/email-branding-data';

const EmailTemplateComponent = defineCustomElement({
	props: [
		'template',
		...Object.keys(getEmailBrandingData()),
	],
	data() {
		return {
			templateLoading: false,
			stencilTemplate: this.getStencil(),
		}
	},
	methods: {
		updateTemplate() {
			this.templateLoading = true;

			nextTick(() => {
				setTimeout(() => {
					this.templateLoading = false;
				}, 2000);
			});
		},
		getStencil() {
			return `<style>
				.skeleton {
					background-color: #f2f2f2;
					border-radius: 4px;
					width: 100%;
					height: 100%;
				}

				.skeleton-logo-img {
					background-color: #f2f2f2;
					width: 20%;
					height: 50px;
					max-width: 50px;
					margin: 30px auto;
					display: block;
				}

				.skeleton-img {
					background-color: #f2f2f2;
					width: 100%;
					height: auto;
					padding-top: 56.25%;
					border-radius: 8px;
					max-width: 600px;
					margin: 0 auto;
				}

				.skeleton-header {
					background-color: #f2f2f2;
					width: 60%;
					height: 30px;
					margin: 20px auto;
					border-radius: 6px;
				}

				.skeleton-subheader {
					background-color: #f2f2f2;
					width: 50%;
					height: 20px;
					margin: 10px auto;
					border-radius: 6px;
				}

				.skeleton-button {
					background-color: #f2f2f2;
					width: 30%;
					height: 40px;
					margin: 30px auto;
					border-radius: 4px;
				}

				.skeleton-footer {
					background-color: #f2f2f2;
					width: 80%;
					height: 20px;
					margin: 40px auto;
					border-radius: 6px;
				}

				@media (max-width: 768px) {
					.skeleton-logo-img {
						width: 40%;
						max-width: 40px;
					}
					.skeleton-img {
						width: 50%;
					}
					.skeleton-header {
						width: 80%;
					}
					.skeleton-subheader {
						width: 70%;
					}
					.skeleton-button {
						width: 60%;
					}
					.skeleton-footer {
						width: 90%;
					}
				}

				@media (max-width: 480px) {
					.skeleton-logo-img {
						width: 50%;
						max-width: 30px;
					}
					.skeleton-img {
						width: 50%;
					}
					.skeleton-header {
						width: 90%;
					}
					.skeleton-subheader {
						width: 80%;
					}
					.skeleton-button {
						width: 80%;
					}
					.skeleton-footer {
						width: 100%;
					}
				}
				</style>

				<div class="email-template-skeleton">
				<div class="skeleton skeleton-logo-img"></div>
				<div class="skeleton skeleton-img"></div>
				<div class="skeleton skeleton-header"></div>
				<div class="skeleton skeleton-subheader"></div>
				<div class="skeleton skeleton-button"></div>
				<div class="skeleton skeleton-footer"></div>
			</div>`;
		}
	},
	created() {
		const ignoredProps = ['template', 'titleTextColor', 'subtitleTextColor', 'buttonTextColor', 'buttonBackgroundColor'];
		for (const prop in this.$props) {
			if (!ignoredProps.includes(prop)) {
				this.$watch(
				  prop,
				  () => { this.updateTemplate(); },
				  { immediate: true }
				);
			  }
			}
	},
	computed: {
		brandedTemplate() {
			let newTemplate = this.template;
			newTemplate.replace('{{subdomain}}', location.hostname.includes('app.raleon.io') ? 'app' : 'dev');
			Object.keys(getEmailBrandingData()).forEach(prop => {
				const value = this[prop];
				if (value) {
					newTemplate = newTemplate.replace(new RegExp(`{{${prop}}}`, 'g'), value);
				}
			});

			return newTemplate;
		}
	},
	render() {
		return h('div', {
			innerHTML: this.templateLoading ? this.stencilTemplate : this.brandedTemplate,
		});
	},
});

customElements.define('loyalty-email-template', EmailTemplateComponent);
