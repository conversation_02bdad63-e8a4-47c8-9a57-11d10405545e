/**
 * Constants for task types used throughout the agent task components
 */

// Task type enum
export const TaskType = {
  SCHEDULE: 1,
  SEGMENT: 2,
  CONTENT: 3,
  PROMOTION: 4,
  REVIEW_EMAIL_DESIGN: 12,
  KLAVIYO_CAMPAIGN: 13
};

// Human-readable labels for each task type
export const TaskTypeLabels = {
  [TaskType.SCHEDULE]: 'Schedule',
  [TaskType.SEGMENT]: 'Segment',
  [TaskType.CONTENT]: 'Content',
  [TaskType.PROMOTION]: 'Promotion',
  [TaskType.REVIEW_EMAIL_DESIGN]: 'Review Email',
  [TaskType.KLAVIYO_CAMPAIGN]: 'Finalize Campaign'
};

// Helper function to check if a task type is valid
export function isValidTaskType(type) {
  return Object.values(TaskType).includes(type);
}
