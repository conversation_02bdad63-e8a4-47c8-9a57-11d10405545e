import {QuickAction} from '../components/QuickChatPrompts.ts.vue';
import {
  CalendarIcon,
  MegaphoneIcon,
  EnvelopeIcon,
  LightBulbIcon,
} from '@heroicons/vue/24/outline';

// Define initial prompts for each action type
export const INITIAL_PROMPTS: Record<
  string,
  Array<{
    title: string;
    description: string;
    promptText: string;
    iconComponent: any;
  }>
> = {
  planMonth: [
    {
      title: 'Help me plan next month',
      description: 'Build a plan of campaign ideas for the upcoming month.',
      promptText:
        'Help me plan my marketing campaigns for next month using your best assumptions.',
      iconComponent: CalendarIcon,
    },
    {
      title: 'Give me a Plan for emails next week',
      description: 'Create a plan for emails to send next week.',
      promptText: 'Help me plan my emails for next week.',
      iconComponent: EnvelopeIcon,
    },
    {
      title: 'Generate a plan for a new product launch',
      description: 'Develop a plan for launching a new product.',
      promptText: 'Help me plan a new product launch.',
      iconComponent: MegaphoneIcon,
    },
    {
      title: 'Help me plan emails for an upcoming holiday',
      description: 'Create a plan for emails to send during the next holiday.',
      promptText: 'Help me plan my emails for the upcoming holiday.',
      iconComponent: LightBulbIcon,
    },
  ],

  brainstorm: [
    {
      title: 'Brainstorm marketing ideas',
      description: 'Generate creative marketing ideas for your brand.',
      promptText: 'Help me brainstorm marketing ideas for my brand.',
      iconComponent: LightBulbIcon,
    },
    {
      title: 'Brainstorm email content',
      description: 'Generate content ideas for your email campaigns.',
      promptText: 'Help me brainstorm content for my email campaigns.',
      iconComponent: EnvelopeIcon,
    },
    {
      title: 'Brainstorm promotional offers',
      description: 'Generate ideas for promotions and special offers.',
      promptText: 'Help me brainstorm promotional offers for my customers.',
      iconComponent: MegaphoneIcon,
    },
    {
      title: 'Brainstorm seasonal campaigns',
      description: 'Generate ideas for seasonal marketing campaigns.',
      promptText: 'Help me brainstorm seasonal campaign ideas for my store.',
      iconComponent: CalendarIcon,
    },
  ],

  planCampaigns: [
    {
      title: 'Give me 5 ideas for emails I can send',
      description:
        'Get email campaign ideas based on best assumptions for your brand.',
      promptText:
        'Give me 5 ideas for emails I can send using your best assumptions.',
      iconComponent: EnvelopeIcon,
    },
    {
      title: 'Give me a brief for an email I can send',
      description: 'Request an email brief using AI analysis of your brand.',
      promptText:
        'Give me a brief that you think will work using your best analysis.',
      iconComponent: EnvelopeIcon,
    },
    {
      title: 'Generate a Promotional Brief',
      description: 'Create a promotional email brief tailored to your brand.',
      promptText:
        'Generate a Brief for a Promotional Email using your best estimate on what would work for my brand.',
      iconComponent: MegaphoneIcon,
    },
    {
      title: 'Give me 5 educational ideas for emails',
      description: 'Get educational content ideas based on store analysis.',
      promptText:
        'Give me 5 educational ideas for emails using your analysis of my store.',
      iconComponent: LightBulbIcon,
    },
  ],

  flows: [
    {
      title: 'Analyze customer journey',
      description: 'Analyze your customer journey and identify optimization opportunities.',
      promptText: 'Analyze my customer journey and identify opportunities for optimization.',
      iconComponent: LightBulbIcon,
    },
    {
      title: 'Workflow automation ideas',
      description: 'Get suggestions for automating your marketing workflows.',
      promptText: 'What marketing workflows can I automate to improve efficiency?',
      iconComponent: MegaphoneIcon,
    },
    {
      title: 'Process optimization',
      description: 'Identify bottlenecks and improve your current processes.',
      promptText: 'Help me identify bottlenecks in my marketing processes and suggest improvements.',
      iconComponent: CalendarIcon,
    },
    {
      title: 'Flow mapping',
      description: 'Create visual maps of your marketing and sales processes.',
      promptText: 'Help me map out my marketing and sales processes for better visibility.',
      iconComponent: EnvelopeIcon,
    },
  ],
};

// Helper function to get initial prompts for a specific action type
export function getInitialPromptsForType(actionType: string) {
  return INITIAL_PROMPTS[actionType] || INITIAL_PROMPTS.planMonth;
}

// Define quick action prompts for each action type
export const QUICK_ACTIONS: Record<string, QuickAction[]> = {
  planMonth: [
    {
      title: 'Generate plan',
      description:
        'Generate a plan based on what we have discussed.',
      promptText:
        'Generate a plan based on what we have discussed.',
    },
    {
      title: 'Add a campaign',
      description: 'Add a campaign to your marketing plan',
      promptText: 'Add a campaign to your marketing plan',
    },
    {
      title: 'Ideas',
      description: 'Give me 5 campaign ideas using your analysis',
      promptText:
        'Give me 5 campaign ideas using your best analysis of my store',
    },
    {
      title: 'Analysis',
      description: 'What type of campaigns work best for my store?',
      promptText: 'What type of campaigns work best for my store?',
    },
  ],

  brainstorm: [
    {
      title: 'More ideas',
      description: 'Generate more creative ideas based on our discussion',
      promptText: 'Can you generate more ideas based on what we have discussed?',
    },
    {
      title: 'Refine ideas',
      description: 'Help refine and improve the existing ideas',
      promptText: 'Can you help me refine these ideas further?',
    },
    {
      title: 'Combine ideas',
      description: 'Suggest ways to combine different ideas',
      promptText: 'How could we combine some of these ideas for better results?',
    },
    {
      title: 'What campaigns work best',
      description: 'Talk about what campaigns work best for my store',
      promptText: 'What campaigns work best for my store?',
    },
  ],

  planCampaigns: [
    {
      title: 'Generate brief',
      description: 'Generate an email brief',
      promptText: 'Generate an email brief',
    },
    {
      title: 'Campaign ideas',
      description: 'Get ideas for email campaigns',
      promptText: 'Give me 5 campaign ideas I could implement',
    },
    {
      title: 'Subject line help',
      description: 'Get help with email subject lines',
      promptText: 'Help me write effective subject lines for my emails',
    },
    {
      title: 'Hero images',
      description: 'Show your available hero images',
      promptText: 'Can you show me my hero images?',
    },
  ],

  flows: [
    {
      title: 'Map current flow',
      description: 'Analyze and map your current customer flow',
      promptText: 'Can you help me map out my current customer flow?',
    },
    {
      title: 'Optimize processes',
      description: 'Suggest process improvements',
      promptText: 'What processes can I optimize in my marketing workflow?',
    },
    {
      title: 'Automation opportunities',
      description: 'Identify what can be automated',
      promptText: 'What parts of my workflow can be automated?',
    },
    {
      title: 'Flow analysis',
      description: 'Deep dive into flow performance',
      promptText: 'Analyze the performance of my current flows and suggest improvements',
    },
  ],

  default: [
    {
      title: 'Generate Email',
      description: 'Generate an Email based on the brief, do not edit or create any variations of images',
      promptText: 'Generate an email I can send as part of my marketing plan',
    },
    {
      title: 'Generate Email + Image Edit',
      description: 'Generate an Email based on the brief, use image_edit tool to modify my Hero image with the appropriate text if possible',
      promptText: 'Generate an email I can send as part of my marketing plan, and use the image_edit tool to modify my Hero image with appropriate text if possible',
    },
    {
      title: 'Generate brief',
      description: 'Create a brief for your marketing campaign',
      promptText: 'Create a brief for my next marketing campaign',
    },
  ],
};

// Helper function to get quick actions for a specific action type
export function getQuickActionsForType(actionType: string): QuickAction[] {
  return QUICK_ACTIONS[actionType] || QUICK_ACTIONS.default;
}
