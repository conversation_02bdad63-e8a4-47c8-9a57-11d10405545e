<template>
  <div class="flex justify-start mb-3">
    <div class="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-xl px-4 py-3 shadow-sm max-w-md">
      <div class="flex items-center gap-3">
        <div class="flex-shrink-0 p-2 bg-purple-100 rounded-lg">
          <svg class="h-4 w-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.532 1.532 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div class="flex-1 min-w-0">
          <span class="text-sm font-semibold text-purple-800 block">Agent Working:</span>
          <span class="text-sm text-gray-700">{{ friendlyMessage }}</span>
        </div>
        <div v-if="isActive" class="flex-shrink-0">
          <div class="animate-spin rounded-full h-4 w-4 border-2 border-purple-300 border-t-purple-600"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from '@vue/runtime-core';

export default defineComponent({
  name: 'ToolMessage',
  props: {
    message: {
      type: String,
      required: true
    },
    isActive: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const friendlyMessage = computed(() => {
      // Remove the *** wrapping and return clean message
      return props.message.replace(/^\*\*\*/, '').replace(/\*\*\*$/, '').trim();
    });

    return {
      friendlyMessage
    };
  }
});
</script>
