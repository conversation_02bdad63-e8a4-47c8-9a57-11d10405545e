<template>
	<div class="mt-6 relative">

		<div className="max-w-7xl mx-auto">
        <div className="flex gap-6 min-h-[60vh]">
          <div className="w-1/2 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Available Signals</h3>
			<div>
				<signal-section
					class="min-h-[300px]"
					title="All Signals"
					section="all"
					:signals="signals?.all || []"
					:is-dragging-over="isDraggingOver"
					@drop="handleDrop"
					@dragenter="handleDragEnter"
					@dragleave="handleDragLeave"
					@signal-click="handleSignalClick"
				/>
			</div>
          </div>

          <div className="w-1/2 space-y-6">
            <div
              className="bg-white p-6 rounded-lg shadow-sm border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">Include Signals</h3>
			  <signal-section
				title="Include Signals"
				section="positive"
				:signals="signals?.positive || []"
				:is-dragging-over="isDraggingOver"
				@drop="handleDrop"
				@dragenter="handleDragEnter"
				@dragleave="handleDragLeave"
				@signal-click="handleSignalClick"
				class=""
			/>
            </div>

            <div
              className="bg-white p-6 rounded-lg shadow-sm border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">Exclude Signals</h3>
			  <signal-section
					title="Exclude Signals"
					section="negative"
					:signals="signals?.negative || []"
					:is-dragging-over="isDraggingOver"
					@drop="handleDrop"
					@dragenter="handleDragEnter"
					@dragleave="handleDragLeave"
					@signal-click="handleSignalClick"
				/>
            </div>
          </div>
        </div>

	  <SignalEditor
      v-if="activeSignal"
      :signal="activeSignal"
      :position="editorPosition"
      @close="closeEditor"
      @update="handleSignalUpdate"
    />
		</div>
	</div>
  </template>

  <script>
  import SignalSection from './SignalSectionX.ts.vue'
  import SignalEditor from './SignalEditor.ts.vue'

  export default {
	name: 'SignalManager',
	components: {
	  SignalSection,
	  SignalEditor
	},
	emits: ['update:audience-size', 'calculating'],
	data() {
	  return {
		isCalculating: false,
		signals: {
		  all: [
			{ id: 1, label: "At risk to churn", type: "raleon", size: 14831, desc: "Customers most likely predicted to churn based on the Raleon Churn Score.", adjuster: "Risk Score", thresholdLabel: " / 100"},
			{ id: 2, label: "Replenishment desire", type: "model", size: 1849, desc: "Customers who are likely to rebuy a product they have before in the next ~30 days.", adjuster: "Replenishment Score", thresholdLabel: " / 100" },
			{ id: 3, label: "Subscription upsell", type: "model", size: 931 },
			{ id: 4, label: "Subscription downsell", type: "model", size: 823 },
			{ id: 5, label: "Likely for repeat purchase", type: "model", size: 2344 },
			{ id: 6, label: "Likely for cross-sell", type: "model", size: 843 },
			{ id: 7, label: "Email Engaged", type: "rule", size: 21932 },
			{ id: 8, label: "Losing engagement", type: "raleon", size: 843 },
			{ id: 9, label: "Single-category buyer", type: "rule", size: 843 },
			{ id: 10, label: "Multi-category buyer", type: "rule", size: 843 },
			{ id: 11, label: "Discounter", type: "raleon", size: 843 },
			{ id: 12, label: "Heavy discounter", type: "raleon", size: 843 },
			{ id: 13, label: "Not price sensitive", type: "model", size: 843 },
			{ id: 14, label: "Shipping sensitive", type: "model", size: 843 },
			{ id: 15, label: "Seasonal", type: "model", size: 843 },
			{ id: 16, label: "Returner", type: "model", size: 843 },
			{ id: 17, label: "Abandoned Cart Recently", type: "rule", size: 843 },
		  ],
		  positive: [],
		  negative: []
		},
		isDraggingOver: null,
		activeSignal: null,
		editorPosition: { top: 0, left: 0 },
		calculationDebounce: null
	  }
	},
	computed: {
	  estimatedSize() {
		if (!this.signals?.positive?.length) {
		  return null;
		}

		// Calculate base size from positive signals
		const baseSize = this.signals.positive.reduce((total, signal) => {
		  return total + signal.size;
		}, 0);

		// Calculate negative impact
		const negativeImpact = this.signals.negative.reduce((total, signal) => {
		  return total + signal.size;
		}, 0);

		// Adjust base size considering negative signals
		const adjustedSize = Math.max(0, baseSize - negativeImpact);

		// Calculate range (±15% for variation)
		const minSize = Math.floor(adjustedSize * 0.85);
		const maxSize = Math.ceil(adjustedSize * 1.15);

		// Format numbers with commas
		return `${minSize.toLocaleString()} - ${maxSize.toLocaleString()}`;
	  }
	},
	watch: {
		'signals.positive': {
		deep: true,
		handler() {
			this.triggerSizeCalculation()
		}
		},
		'signals.negative': {
		deep: true,
		handler() {
			this.triggerSizeCalculation()
		}
		}
	},
	methods: {
		triggerSizeCalculation() {
		// Clear any existing calculation timeout
		if (this.calculationDebounce) {
			clearTimeout(this.calculationDebounce)
		}

		// Emit calculating state
		this.$emit('calculating', true)
		this.isCalculating = true

		// Debounce the calculation
		this.calculationDebounce = setTimeout(() => {
			this.calculateAndEmitSize()
		}, 600)
		},

		calculateAndEmitSize() {
		if (!this.signals?.positive?.length) {
			this.$emit('update:audience-size', null)
			this.$emit('calculating', false)
			this.isCalculating = false
			return
		}

		// Calculate base size from positive signals
		const baseSize = this.signals.positive.reduce((total, signal) => {
			return total + signal.size
		}, 0)

		// Calculate negative impact
		const negativeImpact = this.signals.negative.reduce((total, signal) => {
			return total + signal.size
		}, 0)

		// Adjust base size considering negative signals
		const adjustedSize = Math.max(0, baseSize - negativeImpact)

		// Calculate range (±15% for variation)
		const minSize = Math.floor(adjustedSize * 0.85)
		const maxSize = Math.ceil(adjustedSize * 1.15)

		// Emit the new size range
		this.$emit('update:audience-size', {
			min: minSize,
			max: maxSize,
			exact: adjustedSize
		})

		// Reset calculation state
		this.$emit('calculating', false)
		this.isCalculating = false
		},
	handleDrop({ signalId, sourceSection, targetSection }) {
      if (sourceSection === targetSection) return

      const sourceSignals = this.signals[sourceSection]
      const signal = sourceSignals.find(s => s.id === signalId)

      if (signal) {
        this.signals[sourceSection] = sourceSignals.filter(s => s.id !== signalId)
        this.signals[targetSection] = [...this.signals[targetSection], signal]
      }

      this.isDraggingOver = null
      this.triggerSizeCalculation()
    },
	  handleDragEnter(section) {
		this.isDraggingOver = section;
	  },
	  handleDragLeave() {
		this.isDraggingOver = null;
	  },
	  handleSignalClick({ signal, event }) {
		if (!signal) return;

		const EDITOR_WIDTH = 320;
		const EDITOR_HEIGHT = 300;
		const TOP_SPACING = 70;
		const BOTTOM_SPACING = 8;

		const badge = event.target.closest('[role="button"]') || event.target;
		const rect = badge.getBoundingClientRect();
		const viewportHeight = window.innerHeight;

		// Set left position to align with badge's left edge
		let left = rect.left;
		let top;

		// Check if editor would go below viewport
		if ((rect.bottom + BOTTOM_SPACING + EDITOR_HEIGHT) > viewportHeight) {
			// Position above the badge
			top = rect.top - EDITOR_HEIGHT + TOP_SPACING;
		} else {
			// Position below the badge
			top = rect.bottom + BOTTOM_SPACING;
		}

		this.editorPosition = {
			top: top + window.scrollY,
			left: Math.max(BOTTOM_SPACING, left)
		};

		this.activeSignal = this.activeSignal?.id === signal.id ? null : signal;
		},

		closeEditor() {
			this.activeSignal = null;
		},

	handleSignalUpdate(signal, updates) {
      const sections = ['all', 'positive', 'negative']
      sections.forEach(section => {
        const signalIndex = this.signals[section].findIndex(s => s.id === signal.id)
        if (signalIndex !== -1) {
          this.signals[section][signalIndex] = { ...signal, ...updates }
        }
      })

      this.triggerSizeCalculation()
    },

	}
  }
  </script>
