<template>
	<div v-if="!hasData">
		<div class="ml-4 text-zinc-400 text-lg font-normal font-['Inter']">{{ chartTitle }}</div>
		<div class="flex justify-center w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2">
			<div role="status" class="max-w-sm w-[100%] p-4 md:p-6 dark:border-gray-700">
				<span class="text-gray-500">More data is needed to display the chart.</span>
				<div class="h-2.5 bg-gray-200 rounded-full light:bg-gray-700 w-32 mb-2.5"></div>
				<div class="w-48 h-2 mb-10 bg-gray-200 rounded-full light:bg-gray-700"></div>
				<div class="flex items-baseline mt-4">
					<div class="w-full bg-gray-200 rounded-t-lg h-72 light:bg-gray-700"></div>
					<div class="w-full h-56 ms-6 bg-gray-200 rounded-t-lg light:bg-gray-700"></div>
					<div class="w-full bg-gray-200 rounded-t-lg h-72 ms-6 light:bg-gray-700"></div>
					<div class="w-full h-64 ms-6 bg-gray-200 rounded-t-lg light:bg-gray-700"></div>
					<div class="w-full bg-gray-200 rounded-t-lg h-80 ms-6 light:bg-gray-700"></div>
					<div class="w-full bg-gray-200 rounded-t-lg h-72 ms-6 light:bg-gray-700"></div>
					<div class="w-full bg-gray-200 rounded-t-lg h-80 ms-6 light:bg-gray-700"></div>
				</div>
			</div>
		</div>
	</div>

	<div v-if="isLoading"
		class="flex justify-center w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2">

		<div role="status" class="max-w-sm w-[100%] p-4 animate-pulse md:p-6 dark:border-gray-700">
			<div class="h-2.5 bg-gray-200 rounded-full dark:bg-gray-700 w-32 mb-2.5"></div>
			<div class="w-48 h-2 mb-10 bg-gray-200 rounded-full dark:bg-gray-700"></div>
			<div class="flex items-baseline mt-4">
				<div class="w-full bg-gray-200 rounded-t-lg h-72 dark:bg-gray-700"></div>
				<div class="w-full h-56 ms-6 bg-gray-200 rounded-t-lg dark:bg-gray-700"></div>
				<div class="w-full bg-gray-200 rounded-t-lg h-72 ms-6 dark:bg-gray-700"></div>
				<div class="w-full h-64 ms-6 bg-gray-200 rounded-t-lg dark:bg-gray-700"></div>
				<div class="w-full bg-gray-200 rounded-t-lg h-80 ms-6 dark:bg-gray-700"></div>
				<div class="w-full bg-gray-200 rounded-t-lg h-72 ms-6 dark:bg-gray-700"></div>
				<div class="w-full bg-gray-200 rounded-t-lg h-80 ms-6 dark:bg-gray-700"></div>
			</div>
			<span class="sr-only">Loading...</span>
		</div>
	</div>
	<div v-if="!isLoading && hasData">
			<div class="ml-4 flex">
				<div class="flex-col">
					<div class="text-zinc-400 text-lg font-normal font-['Inter']">{{ chartTitle }}</div>
				</div>
				<div class="flex-grow"></div>
				<div v-if="type === 'time'">
					<div class="flex flex-col items-end">
						<div class="flex">
							<div class="text-slate-800 text-base font-bold font-['Inter'] mr-2"> {{ percentChange }} %</div>
							<div class="w-6 h-5 relative">
								<div v-if="percentChange > 0"
									class="w-6 h-5 left-0 top-0 absolute bg-green-300 rounded-full">
									<svg width="26" height="22" viewBox="0 0 26 22" fill="none"
										xmlns="http://www.w3.org/2000/svg">
										<ellipse cx="12.8253" cy="11.1217" rx="12.5844" ry="10.8781"
											fill="#9DD098" />
										<path
											d="M15.6491 9.96284L15.3114 10.2547C13.5045 11.8167 11.6974 13.3787 9.8901 14.941C9.5245 15.257 9.09252 15.3262 8.66107 15.1536C8.24542 14.9847 8.01732 14.6755 8.0779 14.3016C8.13233 14.0315 8.27976 13.7819 8.50092 13.5853C10.2483 12.0475 12.0184 10.5293 13.7816 9.00519L14.1156 8.71648C14.0078 8.70056 13.8991 8.68961 13.79 8.6837C13.0831 8.68096 12.3756 8.68688 11.6691 8.67914C11.1097 8.67368 10.7235 8.3408 10.7161 7.87814C10.7088 7.37359 11.0591 7.03525 11.6444 7.03206C13.2704 7.02295 14.8956 7.02189 16.5199 7.02887C17.1768 7.03206 17.5467 7.33898 17.5588 7.90046C17.5886 9.2942 17.6064 10.6881 17.612 12.0821C17.6141 12.5931 17.2295 12.8936 16.6421 12.8895C16.0879 12.885 15.7234 12.5771 15.7065 12.0808C15.6886 11.4915 15.687 10.9018 15.677 10.3121C15.6749 10.2192 15.6612 10.1281 15.6491 9.96284Z"
											fill="black" />
									</svg>
								</div>
								<div v-if="percentChange < 0"
									class="w-6 h-5 left-0 top-0 absolute bg-red-300 rounded-full">
									<svg width="26" height="22" viewBox="0 0 26 22" fill="none"
										xmlns="http://www.w3.org/2000/svg" style="transform: rotate(180deg);">
										<ellipse cx="12.8253" cy="11.1217" rx="12.5844" ry="10.8781" fill="" />
										<path
											d="M15.6491 9.96284L15.3114 10.2547C13.5045 11.8167 11.6974 13.3787 9.8901 14.941C9.5245 15.257 9.09252 15.3262 8.66107 15.1536C8.24542 14.9847 8.01732 14.6755 8.0779 14.3016C8.13233 14.0315 8.27976 13.7819 8.50092 13.5853C10.2483 12.0475 12.0184 10.5293 13.7816 9.00519L14.1156 8.71648C14.0078 8.70056 13.8991 8.68961 13.79 8.6837C13.0831 8.68096 12.3756 8.68688 11.6691 8.67914C11.1097 8.67368 10.7235 8.3408 10.7161 7.87814C10.7088 7.37359 11.0591 7.03525 11.6444 7.03206C13.2704 7.02295 14.8956 7.02189 16.5199 7.02887C17.1768 7.03206 17.5467 7.33898 17.5588 7.90046C17.5886 9.2942 17.6064 10.6881 17.612 12.0821C17.6141 12.5931 17.2295 12.8936 16.6421 12.8895C16.0879 12.885 15.7234 12.5771 15.7065 12.0808C15.6886 11.4915 15.687 10.9018 15.677 10.3121C15.6749 10.2192 15.6612 10.1281 15.6491 9.96284Z"
											fill="black" />
									</svg>
								</div>
							</div>
						</div>
						<div
							class="text-right text-gray-500 text-sm font-normal font-['Inter'] uppercase tracking-wide">
							DAILY % CHANGE</div>
					</div>
				</div>
			</div>

			<highcharts :options="chartOptions"></highcharts>

	</div>

</template>

<script>
import { ref } from "vue";
import ChartMixin from "./mixins/chart-mixin.ts";
import * as Utils from '../../../client-old/utils/Utils';

export default {
	name: 'LineChart',
	mixins: [ChartMixin],

	props: {
		metricName: String,
		keyField: String,
		keyFieldArray: Array,
		keyLabel: String,
		xAxisLabel: String,
		valueField: String,
		value2Field: String,
		value3Field: String,
		valueLabel: String,
		value2Label: String,
		value3Label: String,
		startDate: Date,
		endDate: Date,
		chartTitle: String,
		type: String,
		groupBy: String,
		calculation: String
	},

	data() {
		const self = this;
		return {
			chartOptions: {
				colors: ['#37049f', '#ffa3df', '#760c6d', '#6a0045'],
				chart: {
					type: 'line',
					zoomType: 'x'
				},
				title: {
					text: undefined,
					align: 'left',
					style: {
						color: 'rgb(161,161,170)',
						'font-weight': '400',
						'font-size': '1.125rem',
						'line-height': '1.5rem'
					}
				},
				subtitle: {
					text: undefined,
					align: 'left'
				},
				xAxis: {
					categories: [],
					gridLineWidth: 1,
					title: {
						text: this.xAxisLabel
					},
					labels: {
						formatter: function() {
							return Utils.formatNumberWithCommas(this.value);
						}
					}
				},
				yAxis: {
					min: 0,
					title: {
						text: this.keyLabel
					},
					gridLineWidth: 2,
					gridLineDashStyle: 'Dash',
					labels: {
						formatter: function() {
							return Utils.formatNumberWithCommas(this.value);
						}
					}
				},
				legend: {
					enabled: true
				},
				plotOptions: {
					area: {
						color: '#9254F6',
						fillColor: {
							linearGradient: {
								x1: 0,
								y1: 0,
								x2: 0,
								y2: 1
							},
							stops: [
								[0, '#D3DDFFFF'],
								[1, '#C6D2FD00'],
							]
						},
						marker: {
							radius: 0
						},
						lineWidth: 3,
						states: {
							hover: {
								lineWidth: 3,
								marker: {
									radius: 6
								}
							}
						},
						threshold: null
					}
				},
				tooltip: {
					// Customizing the tooltip format
					formatter: function () {
						let formattedValue = Utils.formatNumberWithCommas(this.y);

						if (self.valuePrefix === '$') {
							formattedValue = Utils.formatNumberWithCommas(parseFloat(this.y).toFixed(2));
						}

						return `${this.series.name}: ${self.valuePrefix}${formattedValue}${self.valueSuffix}`;
					}
				},
				credits: {
					enabled: false
				},
				series: []
			},
			valuePrefix: '',
			valueSuffix: '',
			chartType: 'line',
			isLoading: true,
			hasData: true
		};
	},
	computed: {
		lastValue() {
			if (this.chartOptions.series.length > 0) {
				const lastSeries = this.chartOptions.series[0];
				if (lastSeries.data && lastSeries.data.length > 0) {
					return lastSeries.data[lastSeries.data.length - 1];
				}
			}
			return 0;
		},
		lastValueLabel() {
			if (this.chartOptions.series.length > 0) {
				const lastSeries = this.chartOptions.series[0];
				if (lastSeries.data && lastSeries.data.length > 0) {
					return lastSeries.name;
				}
			}
			return 0;
		},
		previousValue() {
			if (this.chartOptions.series.length > 0) {
				const lastSeries = this.chartOptions.series[0];
				if (lastSeries.data && lastSeries.data.length > 1) {
					return lastSeries.data[lastSeries.data.length - 2];
				}
			}
			return null;
		},
		percentChange() {
			const last = this.lastValue;
			const previous = this.previousValue;
			if (last !== null && previous !== null) {
			if (previous === 0) {
				return last === 0 ? 0 : 'Infinity';
			}
			const change = ((last - previous) / previous) * 100;
			return change.toFixed(0);
			}
			return 'N/A';
		},
		formattedLastValue() {
			const value = this.lastValue;
			if (value !== null) {
				if (value >= 1000000) {
					return Utils.formatNumberWithCommas((value / 1000000).toFixed(1)) + 'M';
				} else if (value >= 1000) {
					return Utils.formatNumberWithCommas((value / 1000).toFixed(1)) + 'k';
				} else {
					return Utils.formatNumberWithCommas(value);
				}
			}
			return 'N/A';
		}
	},

	async mounted() {
		if(location.hostname === 'localhost') {
			this.isLoading = true;
			if (this.type === 'time') {
				await this.fetchMetricFakeAndUpdateTime();
			} else if (this.type === 'category') {
				await this.fetchMetricFakeAndUpdateCategory();
			} else if (this.type == 'burndown') {
				await this.fetchMetricFakeAndUpdateBurndown();
			}
			this.isLoading = false;
			return;
		}


		this.isLoading = true;
		if (this.type === 'time') {
			await this.fetchMetricAndUpdateTime();
		} else if (this.type === 'category') {
			await this.fetchMetricAndUpdateCategory();
		} else if (this.type == 'burndown') {
			await this.fetchMetricAndUpdateBurndown();
		}
		this.isLoading = false;
	},

	methods: {

	}
};
</script>
