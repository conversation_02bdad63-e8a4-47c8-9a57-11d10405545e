<template>
	<div v-if="!hasData">
		<div class="ml-4 text-zinc-400 text-lg font-normal font-['Inter']">{{ chartTitle }}</div>
		<div class="flex justify-center w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2">
			<div role="status" class="max-w-sm w-[100%] p-4 md:p-6 dark:border-gray-700">
				<span class="text-gray-500">More data is needed to display the chart.</span>
				<div class="h-2.5 bg-gray-200 rounded-full light:bg-gray-700 w-32 mb-2.5"></div>
				<div class="w-48 h-2 mb-10 bg-gray-200 rounded-full light:bg-gray-700"></div>
				<div class="flex items-baseline mt-4">
					<div class="w-full bg-gray-200 rounded-t-lg h-72 light:bg-gray-700"></div>
					<div class="w-full h-56 ms-6 bg-gray-200 rounded-t-lg light:bg-gray-700"></div>
					<div class="w-full bg-gray-200 rounded-t-lg h-72 ms-6 light:bg-gray-700"></div>
					<div class="w-full h-64 ms-6 bg-gray-200 rounded-t-lg light:bg-gray-700"></div>
					<div class="w-full bg-gray-200 rounded-t-lg h-80 ms-6 light:bg-gray-700"></div>
					<div class="w-full bg-gray-200 rounded-t-lg h-72 ms-6 light:bg-gray-700"></div>
					<div class="w-full bg-gray-200 rounded-t-lg h-80 ms-6 light:bg-gray-700"></div>
				</div>
			</div>
		</div>
	</div>
	<div v-if="isLoading"
		class="flex justify-center w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2">

		<div role="status" class="max-w-sm w-[100%] p-4 animate-pulse md:p-6 dark:border-gray-700">
			<div class="h-2.5 bg-gray-200 rounded-full dark:bg-gray-700 w-32 mb-2.5"></div>
			<div class="w-48 h-2 mb-10 bg-gray-200 rounded-full dark:bg-gray-700"></div>
			<div class="flex items-baseline mt-4">
				<div class="w-full bg-gray-200 rounded-t-lg h-72 dark:bg-gray-700"></div>
				<div class="w-full h-56 ms-6 bg-gray-200 rounded-t-lg dark:bg-gray-700"></div>
				<div class="w-full bg-gray-200 rounded-t-lg h-72 ms-6 dark:bg-gray-700"></div>
				<div class="w-full h-64 ms-6 bg-gray-200 rounded-t-lg dark:bg-gray-700"></div>
				<div class="w-full bg-gray-200 rounded-t-lg h-80 ms-6 dark:bg-gray-700"></div>
				<div class="w-full bg-gray-200 rounded-t-lg h-72 ms-6 dark:bg-gray-700"></div>
				<div class="w-full bg-gray-200 rounded-t-lg h-80 ms-6 dark:bg-gray-700"></div>
			</div>
			<span class="sr-only">Loading...</span>
		</div>
	</div>
	<div v-if="!isLoading && hasData">
		<highcharts :options="chartOptions"></highcharts>
	</div>
</template>

<script>
import { ref } from "vue";
import ChartMixin from "./mixins/chart-mixin.ts";
import * as Utils from '../../../client-old/utils/Utils';

export default {
	name: 'PieChart',
	mixins: [ChartMixin],

	props: {
		metricName: String,
		keyField: String,
		keyFieldArray: Array,
		valueField: String,
		chartTitle: String,
		type: String,
		customLabelsArray: Array,
		groupBy: String,
		calculation: String
	},

	data() {
		const self = this;
		return {
			chartOptions: {
				colors: ['#37049f', '#ffa3df', '#760c6d'],
				chart: {
					type: 'pie'
				},
				title: {
					text: this.chartTitle,
					align: 'left',
					style: {
						color: 'rgb(161,161,170)',
						'font-weight': '400',
						'font-size': '1.125rem',
						'line-height': '1.5rem'
					}
				},
				tooltip: {
					pointFormatter: function () {
						return `<b>${Utils.formatNumberWithCommas(this.y)}</b>`;
					}
				},
				plotOptions: {
					pie: {
						allowPointSelect: true,
						cursor: 'pointer',
						dataLabels: {
							enabled: true,
							formatter: function () {
								return `<b>${this.point.name}</b>: ${this.point.percentage.toFixed(1)}% (${Utils.formatNumberWithCommas(this.point.y)})`;
							}
						}
					}
				},
				credits: {
					enabled: false
				},
				series: []
			},
			valuePrefix: '',
			valueSuffix: '',
			chartType: 'pie',
			isLoading: true,
			hasData: true
		};
	},

	async mounted() {

		if (location.hostname === 'localhost') {
			this.isLoading = true;
			if (this.type === 'time') {
				await this.fetchMetricFakeAndUpdateTime();
			} else if (this.type === 'category') {
				await this.fetchMetricFakeAndUpdateCategory();
			} else if (this.type === 'multirowcategory') {
				await this.fetchMetricAndUpdateMultiRowCategory();
			} else if (this.type == 'burndown') {
				await this.fetchMetricFakeAndUpdateBurndown();
			}
			this.isLoading = false;
			return;
		}


		this.isLoading = true;
		if (this.type === 'time') {
			await this.fetchMetricAndUpdateTime();
		} else if (this.type === 'category') {
			await this.fetchMetricAndUpdateCategory();
		} else if (this.type === 'multirowcategory') {
			await this.fetchMetricAndUpdateMultiRowCategory();
		} else {
			console.error('Invalid chart type');
		}
		this.isLoading = false;
	},

	methods: {
		updateChartData(data) {
			this.chartOptions.series = [{
				data: data.map(item => ({
					name: item.name,
					y: item.value,
					formattedValue: Utils.formatNumberWithCommas(item.value)
				}))
			}];
		}
	}
};
</script>
