<template>
  <div v-if="!hasData">
    <div class="ml-4 text-zinc-400 text-lg font-normal font-['Inter']">{{ chartTitle }}</div>
    <div class="flex justify-center w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2">
      <div role="status" class="max-w-sm w-[100%] p-4 md:p-6 dark:border-gray-700">
        <span class="text-gray-500">More data is needed to display the chart.</span>
      </div>
    </div>
  </div>

  <div v-if="isLoading"
    class="flex justify-center w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2">
    <div role="status" class="max-w-sm w-[100%] p-4 animate-pulse md:p-6 dark:border-gray-700">
      <div class="h-2.5 bg-gray-200 rounded-full dark:bg-gray-700 w-32 mb-2.5"></div>
      <div class="w-48 h-2 mb-10 bg-gray-200 rounded-full dark:bg-gray-700"></div>
      <span class="sr-only">Loading...</span>
    </div>
  </div>

  <div v-if="!isLoading && hasData" class="chart-container">
    <div class="ml-4 text-zinc-400 text-lg font-normal font-['Inter']">{{ chartTitle }}</div>
    <div class="ml-4 mr-4 bg-white rounded-2xl shadow border border-violet-200 p-6">
      <div ref="chartRef" class="dual-axis-container"></div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as d3 from 'd3';

interface DataPoint {
  date: string;
  newRevenue: number;
  repeatRevenue: number;
  activeCustomers: number;
}

export default defineComponent({
  name: 'DualAxisChart',
  props: {
    chartTitle: {
      type: String,
      default: 'Rolling 12-Month Average: Revenue and Active Customers'
    },
    data: {
      type: Array as () => DataPoint[],
      required: true,
      default: () => []
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },

  setup(props) {
    const chartRef = ref<HTMLElement | null>(null);
    const hasData = ref(true);
    let svg: d3.Selection<SVGGElement, unknown, null, undefined>;

    const formatNumber = (value: number) => {
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)}M`;
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}K`;
      }
      return value.toFixed(0);
    };

    const formatCurrency = (value: number) => {
      return `$${formatNumber(value)}`;
    };

    const formatCustomers = (value: number) => {
      return formatNumber(value);
    };

    const createChart = () => {
      if (!chartRef.value || !props.data.length) return;

      // Clear previous chart
      d3.select(chartRef.value).selectAll('*').remove();

      // Chart dimensions
      const margin = { top: 40, right: 80, bottom: 60, left: 80 };
      const width = chartRef.value.clientWidth - margin.left - margin.right;
      const height = 400 - margin.top - margin.bottom;

      // Create SVG
      const svgElement = d3.select(chartRef.value)
        .append('svg')
        .attr('width', width + margin.left + margin.right)
        .attr('height', height + margin.top + margin.bottom);

      svg = svgElement
        .append('g')
        .attr('transform', `translate(${margin.left},${margin.top})`);

      // Scales with proper domains
      const xScale = d3.scaleBand()
        .domain(props.data.map(d => d.date))
        .range([0, width])
        .padding(0.1);

      // Use nice() with count to ensure reasonable tick values
      const yScaleRevenue = d3.scaleLinear()
        .domain([0, d3.max(props.data, d => d.newRevenue + d.repeatRevenue) || 0])
        .range([height, 0])
        .nice(5);

      const yScaleCustomers = d3.scaleLinear()
        .domain([0, d3.max(props.data, d => d.activeCustomers) || 0])
        .range([height, 0])
        .nice(5);

      // Draw bars
      props.data.forEach((d) => {
        const x = xScale(d.date) || 0;
        const barWidth = xScale.bandwidth();
        const total = d.newRevenue + d.repeatRevenue;

        // New Revenue bar (bottom)
        svg.append('rect')
          .attr('x', x)
          .attr('y', yScaleRevenue(d.newRevenue))
          .attr('width', barWidth)
          .attr('height', height - yScaleRevenue(d.newRevenue))
          .attr('fill', '#3B82F6')
          .append('title')
          .text(`New Revenue: ${formatCurrency(d.newRevenue)}\nTotal: ${formatCurrency(total)}`);

        // Repeat Revenue bar (top)
        svg.append('rect')
          .attr('x', x)
          .attr('y', yScaleRevenue(total))
          .attr('width', barWidth)
          .attr('height', yScaleRevenue(d.newRevenue) - yScaleRevenue(total))
          .attr('fill', '#8B5CF6')
          .append('title')
          .text(`Repeat Revenue: ${formatCurrency(d.repeatRevenue)}\nTotal: ${formatCurrency(total)}`);
      });

      // Draw line with proper scaling
      const line = d3.line<DataPoint>()
        .x(d => (xScale(d.date) || 0) + xScale.bandwidth() / 2)
        .y(d => yScaleCustomers(d.activeCustomers));

      svg.append('path')
        .datum(props.data)
        .attr('fill', 'none')
        .attr('stroke', '#EC4899')
        .attr('stroke-width', 2)
        .attr('d', line);

      // Add dots with tooltips
      svg.selectAll('.dot')
        .data(props.data)
        .enter()
        .append('circle')
        .attr('class', 'dot')
        .attr('cx', d => (xScale(d.date) || 0) + xScale.bandwidth() / 2)
        .attr('cy', d => yScaleCustomers(d.activeCustomers))
        .attr('r', 4)
        .attr('fill', '#EC4899')
        .append('title')
        .text(d => `Active Customers: ${formatCustomers(d.activeCustomers)}\nDate: ${d.date}`);

      // Configure axes with proper tick formatting
      const xAxis = d3.axisBottom(xScale);
      const yAxisRevenue = d3.axisLeft(yScaleRevenue)
        .ticks(5)
        .tickFormat(d => formatCurrency(+d));
      const yAxisCustomers = d3.axisRight(yScaleCustomers)
        .ticks(5)
        .tickFormat(d => formatCustomers(+d));

      // X-axis
      svg.append('g')
        .attr('transform', `translate(0,${height})`)
        .call(xAxis)
        .selectAll('text')
        .style('text-anchor', 'end')
        .attr('dx', '-.8em')
        .attr('dy', '.15em')
        .attr('transform', 'rotate(-45)');

      // Left Y-axis (Revenue)
      svg.append('g')
        .call(yAxisRevenue)
        .append('text')
        .attr('fill', '#666')
        .attr('transform', 'rotate(-90)')
        .attr('y', -60)
        .attr('text-anchor', 'middle')
        .text('Revenue ($)');

      // Right Y-axis (Customers)
      svg.append('g')
        .attr('transform', `translate(${width},0)`)
        .call(yAxisCustomers)
        .append('text')
        .attr('fill', '#666')
        .attr('transform', 'rotate(-90)')
        .attr('y', 50)
        .attr('text-anchor', 'middle')
        .text('Active Customers');

      // Legend
      const legend = svg.append('g')
        .attr('transform', `translate(${width / 2 - 250},${-20})`);

      // New Revenue
      legend.append('rect')
        .attr('x', 0)
        .attr('width', 15)
        .attr('height', 15)
        .attr('fill', '#3B82F6');
      legend.append('text')
        .attr('x', 25)
        .attr('y', 12)
        .text('New Revenue');

      // Repeat Revenue
      legend.append('rect')
        .attr('x', 180)
        .attr('width', 15)
        .attr('height', 15)
        .attr('fill', '#8B5CF6');
      legend.append('text')
        .attr('x', 205)
        .attr('y', 12)
        .text('Repeat Revenue');

      // Active Customers
      legend.append('circle')
        .attr('cx', 380)
        .attr('cy', 7)
        .attr('r', 4)
        .attr('fill', '#EC4899');
      legend.append('text')
        .attr('x', 395)
        .attr('y', 12)
        .text('Active Customers');
    };

    // Watch for data changes and redraw
    watch(() => props.data, () => {
      hasData.value = props.data.length > 0;
      createChart();
    }, { deep: true });

    // Initial draw
    onMounted(() => {
      hasData.value = props.data.length > 0;
      createChart();

      // Handle window resize
      const handleResize = () => {
        createChart();
      };
      window.addEventListener('resize', handleResize);
    });

    return {
      chartRef,
      hasData
    };
  }
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  min-height: 500px;
}

.dual-axis-container {
  width: 100%;
  height: 400px;
}
</style>
