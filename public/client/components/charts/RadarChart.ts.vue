<template>
  <div class="relative h-[400px] w-full">
    <canvas ref="canvas"></canvas>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { Chart, registerables } from 'chart.js';
Chart.register(...registerables);

export default defineComponent({
  name: '<PERSON><PERSON><PERSON>',
  props: {
    data: {
      type: Object,
      required: true
    },
    chartTitle: {
      type: String,
      default: ''
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    data: {
      deep: true,
      handler() {
        if (this.chart) {
          this.chart.destroy();
        }
        this.createChart();
      }
    },
    chartTitle() {
      if (this.chart) {
        this.chart.options.plugins.title.text = this.chartTitle;
        this.chart.update();
      }
    }
  },
  mounted() {
    this.createChart();
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.destroy();
    }
  },
  methods: {
    createChart() {
      const ctx = this.$refs.canvas.getContext('2d');

      const defaultOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: this.chartTitle,
            padding: 20,
            font: {
              size: 16,
              weight: 'bold'
            }
          },
          legend: {
            position: 'bottom'
          }
        },
        scales: {
          r: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return value + '%';
              }
            }
          }
        }
      };

      this.chart = new Chart(ctx, {
        type: 'radar',
        data: this.data,
        options: { ...defaultOptions, ...this.options }
      });
    }
  }
});
</script>
