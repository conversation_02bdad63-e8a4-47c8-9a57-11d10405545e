
import {getMetric} from '../../../services/metrics.js';
import * as CurrencyUtils from '../../../services/currency.js';

interface FakeTimeDataItem {
	group_label: string;
	value: number;
}

interface FakeCategoryDataItem {
	name: string;
	value: number;
	color: string;
}

interface FakeTopDataItem {
	name: string;
	value: number;
	color: string;
}

interface FakeBurndownDataItem {
	group_label: string;
	metrics: {
		[key: string]: {value: number};
	};
}

export default {
	methods: {
		async fetchMetricAndUpdateTime() {
			const currentDate = new Date();
			currentDate.setDate(currentDate.getDate() + 1);
			const date30DaysAgo = new Date(currentDate.getTime() - (30 * 24 * 60 * 60 * 1000));
			const colors = ['#8349fa', '#a73fcb', '#da6fd1', '#ffa3df'];
			let startDateStr = date30DaysAgo.toISOString().split('T')[0];
			let endDateStr = currentDate.toISOString().split('T')[0];

			if (this.startDate && this.startDate != 'latest') {
				startDateStr = this.startDate.toISOString().split('T')[0];
			}
			if (this.endDate) {
				endDateStr = this.endDate.toISOString().split('T')[0];
			}
			if(!this.groupBy)	{
				this.groupBy = 'month';
			}
			let data = await getMetric(this.metricName, startDateStr, endDateStr, this.groupBy, 'trend');
			console.log('data', data);
			if (data == "Metrics still loading...") {
							this.$emit('loading', false);
							this.hasData = false;
							return console.log('No data for metric', this.metricName);
						}
			if (data.body.data.length > 0) {
			// Sort data by date
			let items = data.body.data.sort((a, b) => new Date(b.group_label).getTime() - new Date(a.group_label).getTime());

			// Take only the most recent 12 months
			items = items.slice(0, 12);

			// Reverse the array so dates are in ascending order
			items = items.reverse();

				this.chartOptions.series = [{name: this.valueLabel, data: [], color: colors[0]}];
				// Extracting x-axis and y-axis data from items
				this.chartOptions.xAxis.categories = items.map(item => {
					const [year, month, day] = item.group_label.split('-');
					return `${month}-${day}`; // Format as MM-DD
				});

				this.chartOptions.series[0].data = items.map(item => Math.round(item.metrics[this.valueField]?.value * 100) / 100 || 0);

				// Check if value2Field is provided and update the second series
				if (this.value2Field) {
					if (this.chartOptions.series.length < 2) {
						this.chartOptions.series.push({name: this.value2Label, data: [], color: colors[1]});
					}
					this.chartOptions.series[1].data = items.map(item => Math.round(item.metrics[this.value2Field]?.value * 100) / 100 || 0);
				}

				if (this.value3Field) {
					if (this.chartOptions.series.length < 3) {
						this.chartOptions.series.push({name: this.value3Label, data: [], color: colors[3]});
					}
					this.chartOptions.series[2].data = items.map(item => Math.round(item.metrics[this.value3Field]?.value * 100) / 100 || 0);
				}

				if (!this.valueLabel) {
					this.chartOptions.series[0].name = items[0].metrics[this.valueField]?.label;
				} else {
					this.chartOptions.series[0].name = this.valueLabel;
				}

				if (this.value2Field && !this.value2Label) {
					this.chartOptions.series[1].name = items[0].metrics[this.value2Field]?.label;
				}
				if (this.value3Field && !this.value3Label) {
					this.chartOptions.series[2].name = items[0].metrics[this.value3Field]?.label;
				}
				this.valueSuffix = items[0].metrics[this.valueField]?.suffix || '';
				this.valuePrefix = items[0].metrics[this.valueField]?.prefix || '';
				if (this.valuePrefix == '$') {
					let primaryCurrency = await CurrencyUtils.returnPrimaryCurrency();
					this.valueSuffix = primaryCurrency.suffix || items[0].metrics[this.valueField]?.suffix || '';
					this.valuePrefix = primaryCurrency.prefix || items[0].metrics[this.valueField]?.prefix || '';
				}
				console.log("LOOK AT ME", this.chartOptions);

			} else {
				this.$emit('loading', false);
				this.hasData = false;
				return console.log('No data for metric', this.metricName);
			}
		},
		async fetchMetricAndUpdateCategory() {
			console.log('fetchMetricAndUpdateCategory', this.metricName);
			const startDateStr = 'latest';
			const endDateStr = '';
			const colors = ['#8349fa', '#a73fcb', '#da6fd1', '#ffa3df'];

			let data = await getMetric(this.metricName, startDateStr, endDateStr, this.groupBy, this.calculation);
			if (data == "Metrics still loading...") {
				this.$emit('loading', false);
				this.hasData = false;
				return console.log('No data for metric', this.metricName);
			}
			//console.log(this.metricName, 'data', data)
			if (data.body.data.length > 0) {
				const lastDataItem = data.body.data[data.body.data.length - 1];

				let seriesData: any[] = [];
				let categories: any[] = [];

				for (let i = 0; i < this.keyFieldArray.length; i++) {
					const metric = lastDataItem.metrics[this.keyFieldArray[i]];
					const value = Number(metric.value);
					const label = metric.label;

					if (this.chartType === 'pie') {
						if (this.customLabelsArray && this.customLabelsArray.length >= i) {
							seriesData.push({name: this.customLabelsArray[i], y: value, color: colors[i % colors.length]});
						} else {
							seriesData.push({name: label, y: value, color: colors[i % colors.length]});
						}
					} else if (this.chartType === 'bar' || this.chartType === 'line') {
						seriesData.push(value);

						if (this.customLabelsArray && this.customLabelsArray.length >= i) {
							categories.push(this.customLabelsArray[i]);
						} else {
							categories.push(label);
						}
					}
				}

				if (this.chartType === 'pie') {
					this.chartOptions.series = [{data: seriesData}];
				} else if (this.chartType === 'bar' || this.chartType === 'line') {
					this.chartOptions.series = [{data: seriesData, name: this.valueLabel}];
					this.chartOptions.xAxis.categories = categories;
				}
				if (this.chartType === 'bar' || this.chartType === 'line') {
					this.chartOptions.series = [];
					seriesData.forEach((value, index) => {
						this.chartOptions.series.push({
							name: categories[index],
							data: [value],
							color: colors[index % colors.length] // Assign color to each series
						});
					});

					this.chartOptions.xAxis.categories = categories;
				}

				this.valueSuffix = lastDataItem.metrics[this.keyFieldArray[0]]?.suffix || '';
				this.valuePrefix = lastDataItem.metrics[this.keyFieldArray[0]]?.prefix || '';
				console.log("LOOK AT ME", this.chartOptions);
			} else {
				this.$emit('loading', false);
				this.hasData = false;
				return console.log('No data for metric', this.metricName);
			}
		},
		async fetchMetricAndUpdateGroups() {
			console.log('fetchMetricAndUpdateGroups', this.metricName);
			const startDateStr = 'latest';
			const endDateStr = '';
			const colors = ['#8349fa', '#a73fcb', '#da6fd1', '#ffa3df'];

			let data = await getMetric(this.metricName, startDateStr, endDateStr, this.groupBy, this.calculation);
			if (data == "Metrics still loading...") {
				this.$emit('loading', false);
				this.hasData = false;
				return console.log('No data for metric', this.metricName);
			}
			//console.log(this.metricName, 'data', data)
			if (data.body.data.length > 0) {
				const lastDataItem = data.body.data[data.body.data.length - 1];

				let seriesData: any[] = [];
				let categories: any[] = [];
				for (let x = 0; x < data.body.data.length; x++) {
					for (let i = 0; i < this.keyFieldArray.length; i++) {
						const metric = data.body.data[x].metrics[this.keyFieldArray[i]];
						const value = Number(metric.value);
						const label = data.body.data[x].group_label;

						if (this.chartType === 'bar') {
							seriesData.push(value);
							categories.push(label);
						}
					}
				}

				if (this.chartType === 'bar') {
					this.chartOptions.series = [{data: seriesData, name: this.valueLabel}];
					this.chartOptions.xAxis.categories = categories;
					this.chartOptions.series = [];
					seriesData.forEach((value, index) => {
						this.chartOptions.series.push({
							name: categories[index],
							data: [value],
							color: colors[index % colors.length] // Assign color to each series
						});
					});

					this.chartOptions.xAxis.categories = categories;
				}

				this.valueSuffix = lastDataItem.metrics[this.keyFieldArray[0]]?.suffix || '';
				this.valuePrefix = lastDataItem.metrics[this.keyFieldArray[0]]?.prefix || '';
				console.log("LOOK AT ME", this.chartOptions);
			} else {
				this.$emit('loading', false);
				this.hasData = false;
				return console.log('No data for metric', this.metricName);
			}
		},
		async fetchMetricAndUpdateMultiRowCategory() {
			console.log('fetchMetricAndUpdateCategory', this.metricName);
			const startDateStr = 'latest';
			const endDateStr = '';
			const colors = ['#8349fa', '#a73fcb', '#da6fd1', '#ffa3df'];

			let data = await getMetric(this.metricName, startDateStr, endDateStr, this.groupBy, this.calculation);
			if (data == "Metrics still loading...") {
				this.$emit('loading', false);
				this.hasData = false;
				return console.log('No data for metric', this.metricName);
			}
			//console.log(this.metricName, 'data', data)
			if (data.body.data.length > 0) {
				const lastDataItem = data.body.data[data.body.data.length - 1];

				let seriesData: any[] = [];
				let categories: any[] = [];

				for (let i = 0; i < data.body.data.length; i++) {
					const metric = data.body.data[i];
					const value = metric.metrics[this.keyFieldArray[0]].value;
					const label = metric.group_label;

					if (this.chartType === 'pie') {
						if (this.customLabelsArray && this.customLabelsArray.length >= i) {
							seriesData.push({name: this.customLabelsArray[i], y: value, color: colors[i % colors.length]});
						} else {
							seriesData.push({name: label, y: value, color: colors[i % colors.length]});
						}
					} else if (this.chartType === 'bar' || this.chartType === 'line') {
						seriesData.push(value);

						if (this.customLabelsArray && this.customLabelsArray.length >= i) {
							categories.push(this.customLabelsArray[i]);
						} else {
							categories.push(label);
						}
					}
				}

				if (this.chartType === 'pie') {
					this.chartOptions.series = [{data: seriesData}];
				} else if (this.chartType === 'bar' || this.chartType === 'line') {
					this.chartOptions.series = [{data: seriesData, name: this.valueLabel}];
					this.chartOptions.xAxis.categories = categories;
				}
				if (this.chartType === 'bar' || this.chartType === 'line') {
					this.chartOptions.series = [];
					seriesData.forEach((value, index) => {
						this.chartOptions.series.push({
							name: categories[index],
							data: [value],
							color: colors[index % colors.length] // Assign color to each series
						});
					});

					this.chartOptions.xAxis.categories = categories;
				}

				this.valueSuffix = lastDataItem.metrics[this.keyFieldArray[0]]?.suffix || '';
				this.valuePrefix = lastDataItem.metrics[this.keyFieldArray[0]]?.prefix || '';
				console.log("LOOK AT ME", this.chartOptions);
			} else {
				this.$emit('loading', false);
				this.hasData = false;
				return console.log('No data for metric', this.metricName);
			}
		},
		async fetchMetricAndUpdateTop(): Promise<void> {
			console.log('fetchMetricAndUpdateCategory', this.metricName);
			const startDateStr = 'latest';
			const endDateStr = '';
			const colors = ['#8349fa', '#a73fcb', '#da6fd1', '#ffa3df', '#6a2efb', '#b24acc', '#c755e5', '#ff8ee0', '#ffb3e8', '#e47fff'];

			let data: any = await getMetric(this.metricName, startDateStr, endDateStr, this.groupBy, this.calculation);
			if (data === "Metrics still loading...") {
				this.$emit('loading', false);
				this.hasData = false;
				return console.log('No data for metric', this.metricName);
			}

			if (data.body.data.length > 0) {
				let seriesData: {name: string, data: number[], color: string}[] = [];
				let categories: string[] = [];
				await CurrencyUtils.returnPrimaryCurrency();
				data.body.data.forEach((item: any, index: number) => {
					let groupLabel: string = CurrencyUtils.replaceCurrencyTagsSync(item.group_label);
					groupLabel = CurrencyUtils.convertCurrencyPlaceholdersToValues(groupLabel);
					const usedCount: number = item.metrics[this.keyFieldArray[0]].value;
					categories.push(groupLabel);
					seriesData.push({
						name: groupLabel,
						data: [usedCount],
						color: colors[index % colors.length] // Assign color to each series
					});
				});

				this.chartOptions.series = seriesData;
				this.chartOptions.xAxis.categories = categories;
				console.log("LOOK AT ME", this.chartOptions);
			} else {
				this.$emit('loading', false);
				this.hasData = false;
				return console.log('No data for metric', this.metricName);
			}
		},
		async fetchMetricAndUpdateBurndown() {
			//console.log('fetchMetricAndUpdateCategory', this.metricName);
			const startDateStr = 'latest';
			const endDateStr = '';

			let data = await getMetric(this.metricName, startDateStr, endDateStr, this.groupBy, this.calculation);
			if (data === "Metrics still loading...") {
				this.hasData = false;
				return console.log('No data for metric', this.metricName);
			}
			//console.log(this.metricName, 'data', data);

			if (data.body.data.length > 0) {
				let categories: any[] = [];
				let seriesData: {name: any; data: any[]}[] = [];

				// Initialize series data array for each key field
				this.keyFieldArray.forEach((keyField, index) => {
					seriesData.push({
						name: this.customLabelsArray && this.customLabelsArray.length > index ? this.customLabelsArray[index] : keyField,
						data: []
					});
				});

				// Populate series data and categories
				data.body.data.forEach((item: any) => {
					this.keyFieldArray.forEach((keyField: any, index: number) => {
						seriesData[index].data.push(Number(item.metrics[keyField].value));
					});
					categories.push(item.group_label);
				});

				this.chartOptions.series = seriesData;
				this.chartOptions.xAxis.categories = categories;

				// Set chart type based on the component's type prop
				this.chartOptions.chart.type = this.chartType;

				//console.log("Updated Chart Options", this.chartOptions);
			} else {
				this.$emit('loading', false);
				this.hasData = false;
				return console.log('No data for metric', this.metricName);
			}
		},

		async fetchMetricFakeAndUpdateTop() {
			const colors = ['#8349fa', '#a73fcb', '#da6fd1', '#ffa3df', '#6a2efb', '#b24acc', '#c755e5', '#ff8ee0', '#ffb3e8', '#e47fff'];
			const itemCount = 10;
			const fakeData: FakeTopDataItem[] = [];

			for (let i = 0; i < itemCount; i++) {
				fakeData.push({
					name: `Item ${i + 1}`,
					value: Math.floor(Math.random() * 1000) + 100,
					color: colors[i % colors.length]
				});
			}

			this.chartOptions.series = fakeData.map(item => ({
				name: item.name,
				data: [item.value],
				color: item.color
			}));
			this.chartOptions.xAxis.categories = fakeData.map(item => item.name);

			this.hasData = true;
		},
		async fetchMetricFakeAndUpdateTime() {
			const colors: string[] = ['#8349fa', '#a73fcb', '#da6fd1', '#ffa3df', '#EC4899'];
			const daysCount: number = 30;
			const fakeData: FakeTimeDataItem[] = [];

			for (let i = 0; i < daysCount; i++) {
				const date = new Date();
				date.setDate(date.getDate() - i);
				const formattedDate = `${date.getMonth() + 1}-${date.getDate()}`;
				const value = Math.floor(Math.random() * 1000) + 500; // Random value between 500 and 1500
				fakeData.unshift({group_label: formattedDate, value: value});
			}

			this.chartOptions.xAxis.categories = fakeData.map(item => item.group_label);
			this.chartOptions.series = [{
				name: this.valueLabel || 'Value',
				data: fakeData.map(item => item.value),
				color: colors[0]
			}];

			if (this.value2Field) {
				const value2Data = fakeData.map(item => Math.floor(item.value * 0.7 + Math.random() * 300));
				this.chartOptions.series.push({
					name: this.value2Label || 'Value 2',
					data: value2Data,
					color: colors[1]
				});
			}
			if (this.value3Field) {
				const value3Data = fakeData.map(item => Math.floor(item.value * 0.7 + Math.random() * 300));
				this.chartOptions.series.push({
					name: this.value3Label || 'Value 3',
					data: value3Data,
					color: colors[3]
				});
			}

			this.valueSuffix = '';
			this.valuePrefix = '$';
			this.hasData = true;
		},
		async fetchMetricFakeAndUpdateCategory() {
			const colors: string[] = ['#8349fa', '#a73fcb', '#da6fd1', '#ffa3df'];
			const fakeData: FakeCategoryDataItem[] = this.keyFieldArray.map((field, index) => ({
				name: this.customLabelsArray?.[index] || field,
				value: Math.floor(Math.random() * 1000) + 100,
				color: colors[index % colors.length]
			}));

			if (this.chartType === 'pie') {
				this.chartOptions.series = [{
					data: fakeData.map(item => ({
						name: item.name,
						y: item.value,
						color: item.color
					}))
				}];
			} else if (this.chartType === 'bar' || this.chartType === 'line') {
				this.chartOptions.series = fakeData.map(item => ({
					name: item.name,
					data: [item.value],
					color: item.color
				}));
				this.chartOptions.xAxis.categories = fakeData.map(item => item.name);
			}

			this.valueSuffix = '';
			this.valuePrefix = '$';
			this.hasData = true;
		},
		async fetchMetricFakeAndUpdateBurndown() {
			const purchaseCount: number = 20;
			const fakeData: FakeBurndownDataItem[] = [];

			for (let i = 1; i <= purchaseCount; i++) {
				fakeData.push({
					group_label: `Purchase ${i}`,
					metrics: {
						[this.keyFieldArray[0]]: {value: Math.floor(1000 * Math.pow(0.95, i - 1))},
						[this.keyFieldArray[1]]: {value: Math.floor(2000 * Math.pow(0.85, i - 1))}
					}
				});
			}

			this.chartOptions.xAxis.categories = fakeData.map(item => item.group_label);
			this.chartOptions.series = this.keyFieldArray.map((field, index) => ({
				name: this.customLabelsArray?.[index] || field,
				data: fakeData.map(item => item.metrics[field].value)
			}));

			this.hasData = true;
		},

	}
};
