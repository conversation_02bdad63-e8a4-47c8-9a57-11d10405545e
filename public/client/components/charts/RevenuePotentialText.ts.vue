<template>
	<div v-if="isLoading">
		Loading...
	</div>
	<div v-else-if="showRevenueMessage">
		With a conversion of {{ conversionRate }}% of Growth Users to Loyal, you could increase your yearly revenue by ${{ revenueIncrease }}.
	</div>
</template>

<script>
import { ref, computed } from "vue";
import ChartMixin from "./mixins/chart-mixin.ts";
import { getMetric } from '../../services/metrics.js';

export default {
	name: 'RevenuePotentialText',
	mixins: [ChartMixin],

	data() {
		return {
			isLoading: true,
			conversionRate: 0, // Variable for conversion rate
			revenueIncrease: 0, // Variable for revenue increase
		};
	},

	computed: {
		// Computed property to determine if revenue message should be shown
		showRevenueMessage() {
			return this.revenueIncrease && this.revenueIncrease > 0;
		}
	},

	async mounted() {
		console.log("RevenuePotentialText mounted")
		try {
			let data = await getMetric('segment_to_very_loyal', 'latest', '', '', '');
			if(data && data.body && data.body.data) {
				console.log("RevenuePotentialText data:", data)
				this.conversionRate = 5; // Assuming this is a static value
				if(data.body.data.length > 0) {
					const lastDataItem = data.body.data[data.body.data.length - 1];
					this.revenueIncrease = lastDataItem.potential_revenue_opportunity * this.conversionRate;
					if(this.revenueIncrease < 0) {
						this.revenueIncrease = 0;
					}
				}
			}

			this.isLoading = false;
		} catch (error) {
			console.error("Error fetching metric:", error);
			this.isLoading = false;
		}
	}
};
</script>
