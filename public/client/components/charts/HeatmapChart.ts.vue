<template>
  <div v-if="!hasData">
    <div class="ml-4 text-zinc-400 text-lg font-normal font-['Inter']">{{ chartTitle }}</div>
    <div class="flex justify-center w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2">
      <div role="status" class="max-w-sm w-[100%] p-4 md:p-6 dark:border-gray-700">
        <span class="text-gray-500">More data is needed to display the chart.</span>
      </div>
    </div>
  </div>

  <div v-if="isLoading"
    class="flex justify-center w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2">
    <div role="status" class="max-w-sm w-[100%] p-4 animate-pulse md:p-6 dark:border-gray-700">
      <div class="h-2.5 bg-gray-200 rounded-full dark:bg-gray-700 w-32 mb-2.5"></div>
      <div class="w-48 h-2 mb-10 bg-gray-200 rounded-full dark:bg-gray-700"></div>
      <span class="sr-only">Loading...</span>
    </div>
  </div>

  <div v-if="!isLoading && hasData" class="chart-container">
    <div class="ml-4 text-zinc-400 text-lg font-normal font-['Inter']">{{ chartTitle }}</div>
    <div class="ml-4 mr-4 bg-white rounded-2xl shadow border border-violet-200 p-6">
      <div ref="chartRef" class="heatmap-container"></div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as d3 from 'd3';

interface DataPoint {
  cohort: string;
  month: number;
  rate: number;
  isCustomerCount?: boolean;
}

export default defineComponent({
  name: 'HeatmapChart',
  props: {
    chartTitle: {
      type: String,
      default: 'Cumulative Repeat Purchase Rate by Cohort'
    },
    data: {
      type: Array as () => DataPoint[],
      required: true,
      default: () => []
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },

  setup(props) {
    const chartRef = ref<HTMLElement | null>(null);
    const hasData = ref(true);
    let svg: d3.Selection<SVGSVGElement, unknown, null, undefined>;

    const createChart = () => {
      if (!chartRef.value || !props.data.length) return;

      // Clear previous chart
      d3.select(chartRef.value).selectAll('*').remove();

      // Chart dimensions
      const margin = { top: 40, right: 100, bottom: 60, left: 100 };
      const width = chartRef.value.clientWidth - margin.left - margin.right;
      const height = 600 - margin.top - margin.bottom;

      // Create SVG
      svg = d3.select(chartRef.value)
        .append('svg')
        .attr('width', width + margin.left + margin.right)
        .attr('height', height + margin.top + margin.bottom)
        .append('g')
        .attr('transform', `translate(${margin.left},${margin.top})`);

      // Get unique cohorts and months
      const cohorts = Array.from(new Set(props.data.map(d => d.cohort)));
      const months = Array.from(new Set(props.data.map(d => d.month))).sort((a, b) => a - b);

      // Scales
      const xScale = d3.scaleBand()
        .domain(['Customers', ...months.filter(m => m > 0).map(String)])
        .range([0, width])
        .padding(0.05);

      const yScale = d3.scaleBand()
        .domain(cohorts)
        .range([0, height])
        .padding(0.05);

      // Create two color scales - one for rates and one for customer counts
      const maxRate = d3.max(props.data.filter(d => !d.isCustomerCount), d => d.rate) || 0;
      const rateColorScale = d3.scaleSequential()
        .domain([0, maxRate])
        .interpolator(d3.interpolatePurples);

      const maxCustomers = d3.max(props.data.filter(d => d.isCustomerCount), d => d.rate) || 0;
      const customerColorScale = d3.scaleSequential()
        .domain([0, maxCustomers])
        .interpolator(d3.interpolateBlues);

      // Draw cells
      const cells = svg.selectAll('g')
        .data(props.data)
        .enter()
        .append('g');

      cells.append('rect')
        .attr('x', d => xScale(String(d.month)) || 0)
        .attr('y', d => yScale(d.cohort) || 0)
        .attr('width', xScale.bandwidth())
        .attr('height', yScale.bandwidth())
        .attr('fill', d => d.isCustomerCount ? customerColorScale(d.rate) : rateColorScale(d.rate))
        .append('title')
        .text(d => {
          if (d.isCustomerCount) {
            return `${d.cohort}\nCustomers: ${d.rate.toLocaleString()}`;
          }
          return `${d.cohort}\nMonth ${d.month}\nRate: ${(d.rate * 100).toFixed(2)}%`;
        });

      // Add text labels in cells
      cells.append('text')
        .attr('x', d => (xScale(String(d.month)) || 0) + xScale.bandwidth() / 2)
        .attr('y', d => (yScale(d.cohort) || 0) + yScale.bandwidth() / 2)
        .attr('text-anchor', 'middle')
        .attr('dominant-baseline', 'middle')
        .style('font-size', '12px')
        .style('fill', d => {
          if (d.isCustomerCount) {
            return d.rate > maxCustomers / 2 ? 'white' : 'black';
          }
          return d.rate > maxRate / 2 ? 'white' : 'black';
        })
        .text(d => {
          if (d.isCustomerCount) {
            return d.rate.toLocaleString();
          }
          return `${(d.rate * 100).toFixed(2)}%`;
        });

      // Add axes
      const xAxis = d3.axisBottom(xScale);
      // Format cohort dates as "month - year"
      const formatCohortDate = (date: string) => {
        const [year, month] = date.split('-');
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return `${monthNames[parseInt(month) - 1]} - ${year}`;
      };

      const yAxis = d3.axisLeft(yScale)
        .tickFormat(formatCohortDate);

      svg.append('g')
        .attr('transform', `translate(0,${height})`)
        .call(xAxis)
        .append('text')
        .attr('x', width / 2)
        .attr('y', 40)
        .attr('fill', 'black')
        .style('text-anchor', 'middle')
        .text('Months Since Cohort Start');

      svg.append('g')
        .call(yAxis)
        .append('text')
        .attr('transform', 'rotate(-90)')
        .attr('x', -height / 2)
        .attr('y', -60)
        .attr('fill', 'black')
        .style('text-anchor', 'middle')
        .text('Cohort Month');

      // Add two color legends
      const addLegend = (colorScale: d3.ScaleSequential<string, never>, title: string, domain: [number, number], format: (d: number) => string, xOffset: number) => {
        const legendWidth = 20;
        const legendHeight = height;
        const legend = svg.append('g')
          .attr('transform', `translate(${width + xOffset},0)`);

        const legendScale = d3.scaleLinear()
          .domain(domain)
          .range([legendHeight, 0]);

        const legendAxis = d3.axisRight(legendScale)
          .tickFormat(format)
          .ticks(6);

        const gradientId = `gradient-${title.toLowerCase().replace(/\s+/g, '-')}`;
        const gradient = svg.append('defs')
          .append('linearGradient')
          .attr('id', gradientId)
          .attr('x1', '0%')
          .attr('y1', '100%')
          .attr('x2', '0%')
          .attr('y2', '0%');

        gradient.selectAll('stop')
          .data(d3.range(0, 1.1, 0.1))
          .enter()
          .append('stop')
          .attr('offset', d => `${d * 100}%`)
          .attr('stop-color', d => colorScale(domain[0] + d * (domain[1] - domain[0])));

        legend.append('rect')
          .attr('width', legendWidth)
          .attr('height', legendHeight)
          .style('fill', `url(#${gradientId})`);

        legend.append('g')
          .attr('transform', `translate(${legendWidth},0)`)
          .call(legendAxis);

        legend.append('text')
          .attr('transform', 'rotate(-90)')
          .attr('x', -height / 2)
          .attr('y', -40)
          .attr('fill', 'black')
          .style('text-anchor', 'middle')
          .text(title);
      };

      // Add rate legend
      addLegend(
        rateColorScale,
        'Repeat Purchase Rate',
        [0, maxRate],
        d => `${(Number(d) * 100).toFixed(0)}%`,
        20
      );

      // Add customer count legend
      addLegend(
        customerColorScale,
        'Cohort Size',
        [0, maxCustomers],
        d => d.toLocaleString(),
        80
      );
    };

    // Watch for data changes and redraw
    watch(() => props.data, () => {
      hasData.value = props.data.length > 0;
      createChart();
    }, { deep: true });

    // Initial draw
    onMounted(() => {
      hasData.value = props.data.length > 0;
      createChart();

      // Handle window resize
      const handleResize = () => {
        createChart();
      };
      window.addEventListener('resize', handleResize);
    });

    return {
      chartRef,
      hasData
    };
  }
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  min-height: 700px;
}

.heatmap-container {
  width: 100%;
  height: 600px;
}
</style>
