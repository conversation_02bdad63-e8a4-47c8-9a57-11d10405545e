<template>
  <div v-if="!hasData">
    <div class="ml-4 text-zinc-400 text-lg font-normal font-['Inter']">{{ chartTitle }}</div>
    <div class="flex justify-center w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2">
      <div role="status" class="max-w-sm w-[100%] p-4 md:p-6 dark:border-gray-700">
        <span class="text-gray-500">More data is needed to display the chart.</span>
      </div>
    </div>
  </div>

  <div v-if="isLoading"
    class="flex justify-center w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2">
    <div role="status" class="max-w-sm w-[100%] p-4 animate-pulse md:p-6 dark:border-gray-700">
      <div class="h-2.5 bg-gray-200 rounded-full dark:bg-gray-700 w-32 mb-2.5"></div>
      <div class="w-48 h-2 mb-10 bg-gray-200 rounded-full dark:bg-gray-700"></div>
      <span class="sr-only">Loading...</span>
    </div>
  </div>

  <div v-if="!isLoading && hasData" class="chart-container">
    <div class="ml-4 text-zinc-400 text-lg font-normal font-['Inter']">{{ chartTitle }}</div>
    <div class="ml-4 mr-4 bg-white rounded-2xl shadow border border-violet-200 p-6">
      <canvas ref="chartRef" style="height: 350px !important;"></canvas>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, watch } from 'vue';
import Chart from 'chart.js/auto';

export default defineComponent({
  name: 'StackedBarChart',
  props: {
    chartTitle: {
      type: String,
      default: 'Discount Usage by Purchase Order'
    },
    data: {
      type: Object,
      required: true,
      default: () => ({
        welcome10: [],
        signup20: [],
        first15: [],
        other: []
      })
    },
    isLoading: {
      type: Boolean,
      default: false
    },
	xAxisLabels: {
	  type: Array,
	  default: () => ['1st Purchase', '2nd Purchase', '3rd Purchase']
	},
	includePercent: {
	  type: Boolean,
	  default: true
	}
  },

  setup(props) {
    const chartRef = ref<HTMLCanvasElement | null>(null);
    let chart: Chart | null = null;
    const hasData = ref(true);

    const createChart = () => {
      if (!chartRef.value) return;
      const ctx = chartRef.value.getContext('2d');
      if (!ctx) return;

      if (chart) {
        chart.destroy();
      }

		const maxValue = Math.max(
			...Object.values(props.data as number[][])
				.reduce((acc, arr) => {
					arr.forEach((value, index) => {
						acc[index] = (acc[index] || 0) + value;
					});
					return acc;
				}, [])
		);
		const yAxisMax = Math.ceil(maxValue * 1.1);
		console.log('Y Axis Max:', yAxisMax);

      const datasets = Object.entries(props.data).map(([group, values], index) => {
        const colors = ['#3B82F6', '#8B5CF6', '#EC4899', '#9CA3AF', '#14B8A6', '#F59E0B', '#EF4444', '#6366F1'];
        return {
          label: group,
          data: values,
          backgroundColor: colors[index % colors.length],
          stack: 'Stack 0'
        };
      });

      chart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: props.xAxisLabels,
          datasets
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          layout: {
            padding: {
              top: 20,
              bottom: 20
            }
          },
          scales: {
            x: {
              stacked: true,
              grid: {
                display: false
              }
            },
            y: {
              stacked: true,
              beginAtZero: true,
              max: yAxisMax,
              ticks: {
                callback: (value) => `${value}${props.includePercent ? '%' : ''}`
              },
              grid: {
                color: '#E5E7EB',
                drawBorder: false
              }
            }
          },
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                usePointStyle: true,
                padding: 20
              }
            },
            title: {
              display: false
            }
          }
        }
      });
    };

    watch(() => props.data, () => {
      createChart();
    }, { deep: true });

    onMounted(() => {
      createChart();
    });

    return {
      chartRef,
      hasData
    };
  }
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  min-height: 450px;
}
</style>
