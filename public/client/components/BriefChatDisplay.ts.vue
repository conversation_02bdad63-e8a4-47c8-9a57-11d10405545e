<template>
  <button
    class="flex items-center justify-between w-full p-4 rounded-lg bg-white text-[#202020] border border-gray-200 cursor-pointer transition-colors duration-200 ease-in-out hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#9254F7] focus:ring-opacity-75"
    :disabled="segment.isGenerating"
    :aria-label="`View generated ${artifactName}`"
  >
    <!-- Left Side: Text Content -->
    <div class="flex flex-col items-start mr-4">
      <span class="font-semibold text-base mb-1 text-[#202020]">{{ artifactName }}</span>
      <!-- TODO: Replace with dynamic description and version if available -->
      <span class="text-sm text-gray-400">{{ artifactTypeDetails }}</span>
    </div>

    <!-- Right Side: Icon or Spinner -->
    <div class="flex-shrink-0">
      <!-- Generating State -->
      <div v-if="segment.isGenerating" class="flex items-center justify-center h-10 w-10">
        <svg class="animate-spin h-5 w-5 text-[#5A16C9]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"> {/* Using primary purple for spinner */}
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>

      <!-- Generated State -->
      <div v-else class="flex items-center justify-center p-2 rounded-md border border-gray-200 bg-gray-50 h-10 w-10 relative">
        <!-- Brief Icon -->
        <svg v-if="artifactType === 'brief'" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#5A16C9]" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 17.25v-.001M9.75 14.25v-.001M9.75 11.25v-.001M12 17.25v-.001M12 14.25v-.001M12 11.25v-.001M14.25 17.25v-.001M14.25 14.25v-.001M14.25 11.25v-.001M4.5 20.25h15A2.25 2.25 0 0021.75 18V6A2.25 2.25 0 0019.5 3.75H4.5A2.25 2.25 0 002.25 6v12A2.25 2.25 0 004.5 20.25z" />
        </svg>

        <!-- Email Icon -->
        <svg v-else-if="artifactType === 'email'" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#5A16C9]" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
        </svg>

        <!-- Plan Icon -->
        <svg v-else-if="artifactType === 'plan'" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#5A16C9]" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
        </svg>

        <!-- Default Icon (fallback) -->
        <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#5A16C9]" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 17.25v-.001M9.75 14.25v-.001M9.75 11.25v-.001M12 17.25v-.001M12 14.25v-.001M12 11.25v-.001M14.25 17.25v-.001M14.25 14.25v-.001M14.25 11.25v-.001M4.5 20.25h15A2.25 2.25 0 0021.75 18V6A2.25 2.25 0 0019.5 3.75H4.5A2.25 2.25 0 002.25 6v12A2.25 2.25 0 004.5 20.25z" />
        </svg>

        <!-- Icon Overlay Slot -->
        <slot name="icon-overlay"></slot>
      </div>
    </div>
  </button>
</template>

<script lang="ts">
import { defineComponent, PropType } from '@vue/runtime-core';

// Define a more generic interface for the segment prop
interface ArtifactSegment {
  // type: 'brief_placeholder' | 'brief_artifact' | 'email_placeholder' | 'email_artifact'; // Type might still be useful
  isGenerating: boolean;
  // Add any other common fields needed for display logic
}

export default defineComponent({
  name: 'BriefChatDisplay',
  props: {
    segment: {
      type: Object as PropType<ArtifactSegment>, // Use the more generic interface
      required: true,
    },
    artifactType: { // Add prop to specify artifact type
      type: String as PropType<'brief' | 'email' | 'plan'>,
      default: 'brief',
    },
  },
  computed: {
    artifactName(): string {
      // TODO: This might need to be derived from a dedicated title prop later
      // For now, keep capitalizing the artifactType
      return this.artifactType.charAt(0).toUpperCase() + this.artifactType.slice(1);
    },
    artifactTypeDetails(): string {
      // TODO: Replace with dynamic description and version props when available
      // Example placeholder combining type and a static version
      const typeDisplay = this.artifactType.charAt(0).toUpperCase() + this.artifactType.slice(1);
      return `${typeDisplay} artifact`; // Placeholder version
    }
  },
  setup() {
    // No specific setup logic needed yet
    return {};
  },
});
</script>

<style scoped>
/* Add component-specific styles here if needed */
button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Ensure icon size is consistent */
.h-10 { height: 2.5rem; }
.w-10 { width: 2.5rem; }
</style>
