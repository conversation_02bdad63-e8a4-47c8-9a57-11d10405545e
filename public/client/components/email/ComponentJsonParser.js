/**
 * Utility functions for parsing and manipulating email component JSON
 */

/**
 * Extract all paths from a JSON object
 * @param {Object} obj - The JSON object to extract paths from
 * @param {String} parentPath - The parent path (used for recursion)
 * @returns {Array} - Array of paths
 */
export function extractPaths(obj, parentPath = '') {
  let paths = [];

  if (obj !== null && typeof obj === 'object') {
    // Check if this is a specific component type with custom field filtering
    try {
      if (parentPath === '' && obj.body && obj.body.rows) {
        // Process Unlayer email template structure
        obj.body.rows.forEach((row, rowIndex) => {
          if (row.columns) {
            row.columns.forEach((column, colIndex) => {
              if (column.contents) {
                column.contents.forEach((content, contentIndex) => {
                  if (content.type && content.id) {
                    // Generate paths for all relevant fields in this component
                    const componentBasePath = `body.rows[${rowIndex}].columns[${colIndex}].contents[${contentIndex}]`;
                    const componentPaths = getComponentPaths(content, componentBasePath, content.type);
                    paths = paths.concat(componentPaths);
                  }
                });
              }
            });
          }
        });

        return paths;
      }
    } catch (e) {
      console.error('Error while processing component structure:', e);
    }

    // Default path extraction if not a special component structure
    Object.keys(obj).forEach(key => {
      const currentPath = parentPath ? `${parentPath}.${key}` : key;

      if (obj[key] !== null && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
        // For nested objects, recursively extract paths
        paths = paths.concat(extractPaths(obj[key], currentPath));
      } else if (Array.isArray(obj[key])) {
        // For arrays, add the array path and then paths for each element
        paths.push(currentPath);

        // Add paths for array elements if they are objects
        obj[key].forEach((item, index) => {
          if (item !== null && typeof item === 'object') {
            paths = paths.concat(extractPaths(item, `${currentPath}[${index}]`));
          }
        });
      } else {
        // For primitive values
        paths.push(currentPath);
      }
    });
  }

  return paths;
}

/**
 * Get component-specific paths based on component type
 * @param {Object} component - The component object
 * @param {String} basePath - The base path for this component
 * @param {String} componentType - The type of component
 * @returns {Array} - Array of paths
 */
function getComponentPaths(component, basePath, componentType) {
  const paths = [];

  if (!component.values) return paths;

  switch (componentType) {
    case 'heading':
      if (component.values.text) {
        paths.push(`${basePath}.values.text`);
      }
      break;

    case 'text':
      if (component.values.text) {
        paths.push(`${basePath}.values.text`);
      }
      break;

    case 'button':
      if (component.values.text) {
        paths.push(`${basePath}.values.text`);
      }
      if (component.values.href && component.values.href.values && component.values.href.values.href !== undefined) {
        paths.push(`${basePath}.values.href.values.href`);
      }
      break;

    case 'image':
      if (component.values.src && component.values.src.url) {
        paths.push(`${basePath}.values.src.url`);
      }
      if ('altText' in component.values) { // Check existence, even if empty
        paths.push(`${basePath}.values.altText`);
      }
	  if (component.values.action && component.values.action.values && component.values.action.values.href !== undefined) {
        paths.push(`${basePath}.values.action.values.href`);
      }

      break;

    default:
      // Only include text fields for unknown types
      if (component.values.text) {
        paths.push(`${basePath}.values.text`);
      }
      break;
  }

  return paths;
}

/**
 * Parse component JSON and validate its structure
 * @param {String} jsonString - The JSON string to parse
 * @returns {Object} - The parsed JSON object or null if invalid
 */
export function parseComponentJson(jsonString) {
  try {
    return JSON.parse(jsonString);
  } catch (e) {
    console.error('Error parsing component JSON:', e);
    return null;
  }
}

/**
 * Format JSON string with proper indentation
 * @param {String} json - The JSON string to format
 * @returns {String} - The formatted JSON string
 */
export function formatJson(json) {
  try {
    return JSON.stringify(JSON.parse(json), null, 2);
  } catch {
    return json;
  }
}

export default {
  extractPaths,
  parseComponentJson,
  formatJson
};
