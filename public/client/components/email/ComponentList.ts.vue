<template>
  <div v-if="loading" class="p-6 text-center">
    <div class="animate-spin h-10 w-10 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
    <p class="text-gray-600">Loading components...</p>
  </div>

  <div v-else class="grid grid-cols-1 gap-6">
    <!-- Components section -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="p-4 bg-gray-50 border-b flex justify-between items-center">
        <div class="flex items-center">
          <h3 class="text-lg font-medium mr-4">Email Sections</h3>
          <ToggleItem
            :state="showInactive"
            @toggleChange="$emit('update:showInactive', $event)"
            :showLabel="true"
            onLabel="Showing Inactive"
            offLabel="Hiding Inactive"
          />
        </div>
        <div class="flex space-x-2">
          <button
            @click="$emit('open-modal', 'create')"
            class="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700"
          >
            Create Section
          </button>
          <button
            @click="$emit('open-modal', 'override')"
            class="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded-lg hover:bg-gray-300"
          >
            Override Section
          </button>
        </div>
      </div>

      <div v-if="components.length === 0" class="p-6 text-gray-500 text-center">
        No components available
      </div>

      <div v-else class="divide-y">
        <div
          v-for="component in components"
          :key="component.id"
          class="p-4 hover:bg-gray-50 transition-colors"
        >
          <div class="flex justify-between items-start">
            <div>
              <div class="flex items-center gap-2">
                <h4 class="font-medium">{{ component.name }}</h4>
                <span
                  v-if="component.orgId === orgId && component.overrideId"
                  class="text-xs px-2 py-0.5 bg-purple-100 text-purple-800 rounded"
                >
                  Override
                </span>
                <span
                  v-else-if="component.orgId === orgId"
                  class="text-xs px-2 py-0.5 bg-blue-100 text-blue-800 rounded"
                >
                  Organization
                </span>
                <span v-else class="text-xs px-2 py-0.5 bg-gray-100 text-gray-800 rounded">
                  Global
                </span>
              </div>

              <!-- Show information about editable fields -->
              <div v-if="component.editableFields" class="mt-1 text-sm text-gray-500">
                <span v-if="Object.keys(getComponentEditableFields(component)).length > 0" class="flex items-center text-xs text-green-600">
                  <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                  {{ Object.keys(getComponentEditableFields(component)).length }} AI-editable fields
                </span>

                <!-- Show the first few editable fields with descriptions -->
                <div class="ml-4 mt-1 text-xs max-w-md">
                  <div v-for="(description, field, index) in getComponentEditableFields(component)" :key="field"
                       v-show="index < 2" class="flex items-start mb-0.5">
                    <span class="font-mono bg-gray-100 px-1 rounded mr-1 flex-shrink-0">{{ field.split('.').pop() }}</span>
                    <span v-if="description" class="text-gray-600 truncate">{{ description }}</span>
                  </div>

                  <div v-if="Object.keys(getComponentEditableFields(component)).length > 2" class="text-xs text-gray-400 italic">
                    ...and {{ Object.keys(getComponentEditableFields(component)).length - 2 }} more
                  </div>
                </div>
              </div>
            </div>

            <div class="flex space-x-2 items-center">
              <!-- Active toggle for all components -->
              <div class="mr-2">
                <ToggleItem
                  :state="component.active !== false"
                  @toggleChange="$emit('toggle-active', component, $event)"
                  :showLabel="false"
                />
              </div>
              <button
                @click="$emit('open-modal', 'override', component)"
                class="text-gray-500 hover:text-blue-600"
                title="Edit component"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
              </button>
              <button
                v-if="component.orgId === orgId"
                @click="$emit('delete-component', component.id)"
                class="text-gray-500 hover:text-red-600"
                title="Delete custom component"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
              <button
                @click="$emit('debug-component', component)"
                class="text-gray-500 hover:text-green-600"
                title="Debug component"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ToggleItem from '../ToggleItem.ts.vue';

export default {
  name: 'ComponentList',
  components: {
    ToggleItem
  },
  props: {
    components: {
      type: Array,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    showInactive: {
      type: Boolean,
      default: false
    },
    orgId: {
      type: Number,
      required: true
    }
  },
  emits: [
    'update:showInactive',
    'open-modal',
    'toggle-active',
    'delete-component',
    'debug-component'
  ],
  methods: {
    getComponentEditableFields(component) {
      if (!component.editableFields) return {};

      // If it's a string, parse it
      if (typeof component.editableFields === 'string') {
        try {
          return JSON.parse(component.editableFields);
        } catch (e) {
          console.error('Error parsing editable fields:', e);
          return {};
        }
      }

      // If it's already an object
      return component.editableFields;
    }
  }
};
</script>
