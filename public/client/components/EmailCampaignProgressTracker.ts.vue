<template>
  <div class="flex justify-center pb-6">
    <div class="w-full lg:w-3/5">
      <div class="flex items-center justify-between">
        <div class="flex items-center w-full">
          <!-- Step 1 -->
          <div class="relative flex flex-col items-center">
            <div class="rounded-full h-10 w-10 flex items-center justify-center z-10"
              :class="{'bg-indigo-600 text-white': currentStep >= 1, 'bg-gray-200 text-gray-600': currentStep < 1}">
              1
            </div>
            <p class="text-sm mt-2" :class="{'text-indigo-600 font-medium': currentStep === 1, 'text-gray-500': currentStep !== 1}">Planning</p>
          </div>
          <!-- Line between Step 1 and 2 -->
          <div class="flex-1 h-1 mx-2 mb-8" :class="{'bg-indigo-600': currentStep >= 2, 'bg-gray-200': currentStep < 2}"></div>
          <!-- Step 2 -->
          <div class="relative flex flex-col items-center">
            <div class="rounded-full h-10 w-10 flex items-center justify-center z-10"
              :class="{'bg-indigo-600 text-white': currentStep >= 2, 'bg-gray-200 text-gray-600': currentStep < 2}">
              2
            </div>
            <p class="text-sm mt-2" :class="{'text-indigo-600 font-medium': currentStep === 2, 'text-gray-500': currentStep !== 2}">Generating</p>
          </div>
          <!-- Line between Step 2 and 3 -->
          <div class="flex-1 h-1 mx-2 mb-8" :class="{'bg-indigo-600': currentStep >= 3, 'bg-gray-200': currentStep < 3}"></div>
          <!-- Step 3 -->
          <div class="relative flex flex-col items-center">
            <div class="rounded-full h-10 w-10 flex items-center justify-center z-10"
              :class="{'bg-indigo-600 text-white': currentStep >= 3, 'bg-gray-200 text-gray-600': currentStep < 3}">
              3
            </div>
            <p class="text-sm mt-2" :class="{'text-indigo-600 font-medium': currentStep === 3, 'text-gray-500': currentStep !== 3}">In Progress</p>
          </div>
          <!-- Line between Step 3 and 4 -->
          <div class="flex-1 h-1 mx-2 mb-8" :class="{'bg-indigo-600': currentStep >= 4, 'bg-gray-200': currentStep < 4}"></div>
          <!-- Step 4 -->
          <div class="relative flex flex-col items-center">
            <div class="rounded-full h-10 w-10 flex items-center justify-center z-10"
              :class="{'bg-indigo-600 text-white': currentStep === 4, 'bg-gray-200 text-gray-600': currentStep < 4}">
              4
            </div>
            <p class="text-sm mt-2" :class="{'text-indigo-600 font-medium': currentStep === 4, 'text-gray-500': currentStep !== 4}">Done</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmailCampaignProgressTracker',

  props: {
    currentStep: {
      type: Number,
      required: true,
      validator: function(value) {
        return value >= 1 && value <= 4;
      }
    }
  }
}
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
