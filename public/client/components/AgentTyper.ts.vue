<template>
	<span v-if="isVisible">{{ displayedContent }}</span>
  </template>

  <script>
  export default {
	name: 'TypeWriter',
	props: {
	  text: {
		type: String,
		required: true
	  },
	  delay: {
		type: Number,
		default: 0
	  }
	},
	data() {
	  return {
		displayedContent: '',
		isVisible: false
	  }
	},
	mounted() {
	  setTimeout(() => {
		this.isVisible = true;
		this.animateText();
	  }, this.delay);
	},
	methods: {
	  async animateText() {
		for (let i = 0; i <= this.text.length; i++) {
		  this.displayedContent = this.text.slice(0, i);
		  await new Promise(r => setTimeout(r, 20));
		}
	  }
	}
  }
  </script>
