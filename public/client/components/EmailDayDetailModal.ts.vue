<template>
  <div v-if="day" class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] flex flex-col">
      <!-- Modal Header -->
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">
          Emails for {{ formatDateHeader(day.date) }}
        </h3>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="flex-1 overflow-y-auto p-6">
        <div class="space-y-3">
          <div
            v-for="email in day.emails"
            :key="email.id"
            @click="$emit('navigate', email.id); $emit('close');"
            class="p-4 bg-white border border-gray-200 hover:border-purple-300 hover:bg-purple-50 rounded-lg cursor-pointer transition-colors flex items-start"
          >
            <!-- Email Icon -->
            <div class="mr-4 mt-1">
              <div class="p-2 bg-purple-50 rounded-lg">
                <svg viewBox="0 0 24 24" class="w-5 h-5 text-purple-600">
                  <path fill="currentColor" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z"/>
                </svg>
              </div>
            </div>

            <!-- Content -->
            <div class="flex-1">
              <div class="flex items-center justify-between">
                <h4 class="font-medium text-gray-900 mr-2">{{ email.title }}</h4>
                <span
                  class="px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getStatusColor(email.status)"
                >
                  {{ getDisplayStatus(email.status) }}
                </span>
              </div>
              <p class="text-sm text-gray-500 mt-1">{{ email.description }}</p>
              <div class="flex items-center space-x-3 mt-3 text-xs text-gray-500">
                <span>{{ formatScheduledTime(email.scheduledFor) }}</span>
                <span v-if="email.planName" class="text-purple-600">{{ email.planName }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="px-6 py-4 border-t border-gray-200">
        <button
          @click="$emit('close')"
          class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmailDayDetailModal',

  props: {
    day: {
      type: Object,
      default: null
    }
  },

  methods: {
    getDisplayStatus(status) {
      const statusMap = {
        'Ready': 'Not Started',
        'In Progress': 'In Progress',
        'In Review': 'In Review',
        'Not Started': 'Not Started',
        'Complete': 'Complete',
        'Processing': 'Processing'
      };
      return statusMap[status] || status;
    },

    formatScheduledTime(dateString) {
      // Create date object from the UTC string
      const date = new Date(dateString);

      // Format with UTC timezone
      const options = {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
        timeZone: 'UTC'  // Important: Treat the date as UTC
      };

      return new Intl.DateTimeFormat('en-US', options).format(date);
    },

    formatDateHeader(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        timeZone: 'UTC'
      });
    },

    getStatusColor(status) {
      if (status === 'Processing') return null;
      const colors = {
        'Ready': 'bg-green-100 text-green-800',
        'In Progress': 'bg-blue-100 text-blue-800',
        'In Review': 'bg-yellow-100 text-yellow-800',
        'Not Started': 'bg-gray-100 text-gray-800',
        'Complete': 'bg-purple-100 text-purple-800'
      };
      return colors[status] || 'bg-gray-100 text-gray-800';
    }
  },

  emits: ['close', 'navigate']
}
</script>
