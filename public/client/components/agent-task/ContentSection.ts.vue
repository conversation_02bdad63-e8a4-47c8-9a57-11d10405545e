<template>
  <div class="pb-3 last:pb-0">
    <template v-if="section.title">
      <h1 class="text-base font-semibold text-gray-900 mb-2">{{ section.title }}</h1>
    </template>
    <div class="space-y-3">
      <template v-for="(block, blockIndex) in section.blocks" :key="blockIndex">
        <template v-if="block.type === 'design'">
          <div class="border-l-4 border-blue-400 pl-3">
            <div class="uppercase text-xs font-medium tracking-wider text-blue-600 mb-1">Design Brief</div>
            <div class="bg-blue-50 p-2 rounded-r">
              <div v-for="line in processedContent(block.content)">{{ line }}</div>
            </div>
          </div>
        </template>
        <template v-else-if="block.type === 'copy'">
          <div class="border-l-4 border-green-400 pl-3">
            <div class="uppercase text-xs font-medium tracking-wider text-green-600 mb-1">Copy</div>
            <div class="bg-green-50 p-2 rounded-r">
              <div v-for="line in processedContent(block.content)">{{ line }}</div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="text-gray-600 pl-3 border-l-2 border-gray-200">
            <div v-for="line in processedContent(block.content)">{{ line }}</div>
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ContentSection',
  props: {
    section: {
      type: Object,
      required: true
    }
  },
  methods: {
    processedContent(content) {
      // Process escape sequences in each line of content
      if (!content) return [];
      return content.map(line => {
        if (typeof line === 'string') {
          return line.replace(/\\n/g, '\n')
                     .replace(/\\t/g, '\t')
                     .replace(/\\r/g, '\r');
        }
        return line;
      });
    }
  }
}
</script>
