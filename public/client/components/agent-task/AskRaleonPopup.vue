<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from '@vue/runtime-core';

const props = defineProps({
  isVisible: {
    type: Boolean,
    required: true,
  },
  position: {
    type: Object,
    required: true,
    default: () => ({ top: 0, left: 0 }),
  },
  selectedText: {
    type: String,
    required: true,
  },
  // Add a new prop for the restore function
  onRestoreSelection: {
    type: Function,
    required: false,
    default: () => {}
  }
});

const emit = defineEmits(['submitQuery', 'closePopup', 'restoreSelection', 'setHighlight', 'removeHighlight', 'preventClose']);

const userQuery = ref('');
const textareaRef = ref<HTMLTextAreaElement | null>(null);

// Function to auto-resize the textarea as content grows
const autoResize = () => {
  if (textareaRef.value) {
    // Reset height to auto to get the correct scrollHeight
    textareaRef.value.style.height = 'auto';
    // Set the height to match the content (with a max of 8 rows instead of 4)
    const newHeight = Math.min(textareaRef.value.scrollHeight, 200);
    textareaRef.value.style.height = `${newHeight}px`;
    
    // Prevent any events from closing the popup while typing
    // This will create a small delay to allow input without closing
    emit('preventClose');
  }
};

// Function to handle query submission
const handleSubmit = (query: string) => {
  // Emit the query
  emit('submitQuery', query);
  // Clear the input field
  userQuery.value = '';
  // Emit event to restore selection
  emit('restoreSelection');
  // Emit event to remove highlight
  emit('removeHighlight');
  // Close the popup
  emit('closePopup');
};

// Clear input field when popup opens and initialize textarea height
watch(() => props.isVisible, (isVisible) => {
  if (isVisible) {
    userQuery.value = '';
    // Initialize textarea height after it's rendered
    setTimeout(() => {
      if (textareaRef.value) {
        textareaRef.value.style.height = 'auto';
        textareaRef.value.style.height = `${textareaRef.value.scrollHeight}px`;
      }
    }, 0);
  }
});
</script>

<template>
  <div
    v-if="props.isVisible"
    class="absolute z-[1000] bg-white border border-purple-200 rounded-lg shadow-md p-2 w-80 max-w-[90vw] ask-raleon-popup-root"
    :style="{
      top: props.position.top + 'px',
      left: props.position.left + 'px',
      transform: 'translateX(-50%)', /* Center horizontally */
      maxWidth: '300px'
    }"
  >
    <!-- Small display of selected text -->
    <div class="text-xs text-gray-500 mb-1 truncate">
      <span class="font-medium">Selected:</span> "{{ props.selectedText }}"
    </div>

    <!-- Query input as textarea that can handle longer text -->
    <textarea
      v-model="userQuery"
      placeholder="What changes would you like to make to the selected text?"
      class="w-full bg-white border border-gray-200 rounded-lg p-2 text-sm text-gray-600 placeholder-gray-400 focus:ring-2 focus:ring-purple-200 focus:border-purple-300 focus:outline-none resize-none overflow-hidden"
      rows="2"
      ref="textareaRef"
      @keydown.enter.prevent="userQuery.trim() && handleSubmit(userQuery)"
      @focus="emit('setHighlight')"
      @blur="emit('removeHighlight')"
      @input="autoResize"
      @scroll.stop="() => {}"
    ></textarea>
  </div>
</template>

<style scoped>
/* Additional custom styles */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ask-raleon-popup-root {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: opacity 0.2s ease-in-out;
}

/* Custom scrollbar for the popup */
.ask-raleon-popup-root::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.ask-raleon-popup-root::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.ask-raleon-popup-root::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.ask-raleon-popup-root::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}
</style>
