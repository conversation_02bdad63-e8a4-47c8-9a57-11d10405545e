# filepath: /home/<USER>/Documents/Code/raleon_webapp/public/client/components/agent-task/PromotionTask.ts.vue
<template>
  <div class="h-full bg-gray-50">
    <!-- Version Navigation -->
    <div class="px-6 py-3 border-b bg-white">
      <div class="flex items-center gap-2">
        <svg class="w-4 h-4 text-gray-400" viewBox="0 0 24 24">
          <path fill="currentColor"
            d="M21.41 11.58l-9-9C12.05 2.22 11.55 2 11 2H4c-1.1 0-2 .9-2 2v7c0 .55.22 1.05.59 1.42l9 9c.36.36.86.58 1.41.58.55 0 1.05-.22 1.41-.59l7-7c.37-.36.59-.86.59-1.41 0-.55-.23-1.06-.59-1.42zM5.5 7C4.67 7 4 6.33 4 5.5S4.67 4 5.5 4 7 4.67 7 5.5 6.33 7 5.5 7z" />
        </svg>
        <span class="text-lg font-medium">Promotion Setup</span>
      </div>
    </div>

    <div class="p-6">
      <div class="max-w-3xl space-y-6">
        <!-- Suggested Promotion - Percentage discount -->
        <div class="bg-white border rounded-lg">
          <div class="flex items-center p-4 border-b">
            <div class="flex items-center gap-2">
              <!-- Percentage discount icon -->
              <svg v-if="task?.campaign?.promotionType === 'Percentage Discount'"
                class="w-4 h-4" viewBox="0 0 24 24">
                <path fill="currentColor"
                  d="M7.5 11C6.67 11 6 10.33 6 9.5S6.67 8 7.5 8 9 8.67 9 9.5 8.33 11 7.5 11zm0-2C7.22 9 7 9.22 7 9.5S7.22 10 7.5 10 8 9.78 8 9.5 7.78 9 7.5 9zM16.5 13c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm0-2c-.28 0-.5.22-.5.5s.********.5-.22.5-.5-.22-.5-.5-.5zm3.09 6.91L4.91 3.23c-.39-.39-1.02-.39-1.41 0-.39.39-.39 1.02 0 1.41L17.16 18.3c.39.39 1.02.39 1.41 0 .4-.39.4-1.02.02-1.39z" />
              </svg>
              <!-- Free shipping icon -->
              <svg v-else-if="task?.campaign?.promotionType === 'Free Shipping'"
                class="w-4 h-4" viewBox="0 0 24 24">
                <path fill="currentColor"
                  d="M20 8h-3V4H3c-1.1 0-2 .9-2 2v11h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-3-4zM6 18.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm13.5-9l1.96 2.5H17V9.5h2.5zm-1.5 9c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z" />
              </svg>
              <!-- Gift icon -->
              <svg v-else-if="task?.campaign?.promotionType === 'Free Gift'"
                class="w-4 h-4" viewBox="0 0 24 24">
                <path fill="currentColor"
                  d="M20 6h-2.18c.11-.31.18-.65.18-1 0-1.66-1.34-3-3-3-1.05 0-1.96.54-2.5 1.35l-.5.67-.5-.68C10.96 2.54 10.05 2 9 2 7.34 2 6 3.34 6 5c0 .*********** 1H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM9 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm11 15H4v-2h16v2zm0-5H4V8h5.08L7 10.83 8.62 12 11 8.76l1-1.36 1 1.36L15.38 12 17 10.83 14.92 8H20v6z" />
              </svg>
              <!-- Default promotion icon -->
              <svg v-else class="w-4 h-4" viewBox="0 0 24 24">
                <path fill="currentColor"
                  d="M21.41 11.58l-9-9C12.05 2.22 11.55 2 11 2H4c-1.1 0-2 .9-2 2v7c0 .55.22 1.05.59 1.42l9 9c.36.36.86.58 1.41.58.55 0 1.05-.22 1.41-.59l7-7c.37-.36.59-.86.59-1.41 0-.55-.23-1.06-.59-1.42zM5.5 7C4.67 7 4 6.33 4 5.5S4.67 4 5.5 4 7 4.67 7 5.5 6.33 7 5.5 7z" />
              </svg>
              <h3 class="text-sm font-medium">{{ task?.campaign?.promotionType }}</h3>
            </div>
          </div>

          <!-- Promotion details -->
          <div class="p-6 bg-gradient-to-r from-purple-50 to-white border-b">
            <div class="flex gap-4">
              <div class="flex-1">
                <div class="text-2xl font-semibold">{{ task?.campaign?.promotionTitle }}</div>
                <div class="text-base text-gray-600 mt-2">{{ task?.campaign?.promotionDescription }}</div>
                <div class="mt-4 flex gap-2 flex-wrap">
                  <span v-for="(badge, index) in promotionBadges" :key="index"
                    :class="['inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium', badge.colorClasses]">
                    {{ badge.text }}
                  </span>
                </div>
              </div>
              <div class="bg-white bg-opacity-50 rounded-lg p-4 flex items-center">
                <!-- Percentage discount icon -->
                <svg v-if="task?.campaign?.promotionType === 'Percentage Discount'"
                  class="w-12 h-12 text-purple-600" viewBox="0 0 24 24">
                  <path fill="currentColor"
                    d="M7.5 11C6.67 11 6 10.33 6 9.5S6.67 8 7.5 8 9 8.67 9 9.5 8.33 11 7.5 11zm0-2C7.22 9 7 9.22 7 9.5S7.22 10 7.5 10 8 9.78 8 9.5 7.78 9 7.5 9zM16.5 13c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm0-2c-.28 0-.5.22-.5.5s.********.5-.22.5-.5-.22-.5-.5-.5zm3.09 6.91L4.91 3.23c-.39-.39-1.02-.39-1.41 0-.39.39-.39 1.02 0 1.41L17.16 18.3c.39.39 1.02.39 1.41 0 .4-.39.4-1.02.02-1.39z" />
                </svg>
                <!-- Free shipping icon -->
                <svg v-else-if="task?.campaign?.promotionType === 'Free Shipping'"
                  class="w-12 h-12 text-purple-600" viewBox="0 0 24 24">
                  <path fill="currentColor"
                    d="M20 8h-3V4H3c-1.1 0-2 .9-2 2v11h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-3-4zM6 18.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm13.5-9l1.96 2.5H17V9.5h2.5zm-1.5 9c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z" />
                </svg>
                <!-- Gift icon -->
                <svg v-else-if="task?.campaign?.promotionType === 'Free Gift'"
                  class="w-12 h-12 text-purple-600" viewBox="0 0 24 24">
                  <path fill="currentColor"
                    d="M20 6h-2.18c.11-.31.18-.65.18-1 0-1.66-1.34-3-3-3-1.05 0-1.96.54-2.5 1.35l-.5.67-.5-.68C10.96 2.54 10.05 2 9 2 7.34 2 6 3.34 6 5c0 .*********** 1H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM9 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm11 15H4v-2h16v2zm0-5H4V8h5.08L7 10.83 8.62 12 11 8.76l1-1.36 1 1.36L15.38 12 17 10.83 14.92 8H20v6z" />
                </svg>
                <!-- Default promotion icon -->
                <svg v-else class="w-12 h-12 text-purple-600" viewBox="0 0 24 24">
                  <path fill="currentColor"
                    d="M21.41 11.58l-9-9C12.05 2.22 11.55 2 11 2H4c-1.1 0-2 .9-2 2v7c0 .55.22 1.05.59 1.42l9 9c.36.36.86.58 1.41.58.55 0 1.05-.22 1.41-.59l7-7c.37-.36.59-.86.59-1.41 0-.55-.23-1.06-.59-1.42zM5.5 7C4.67 7 4 6.33 4 5.5S4.67 4 5.5 4 7 4.67 7 5.5 6.33 7 5.5 7z" />
                </svg>
              </div>
            </div>
          </div>

          <!-- Insights -->
          <div v-if="task?.campaign?.promotionSuggestionReason" class="p-4">
            <div class="space-y-3">
              <div class="text-sm font-medium">Why we suggest this:</div>
              <ul class="space-y-2">
                <li class="flex items-start gap-2">
                  <svg class="w-4 h-4 text-green-500 mt-0.5" viewBox="0 0 24 24">
                    <path fill="currentColor"
                      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                  </svg>
                  <span class="text-sm text-gray-600">
                    {{ task?.campaign?.promotionSuggestionReason }}
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PromotionTask',
  props: {
    task: {
      type: Object,
      required: true
    }
  },
  computed: {
    promotionBadges() {
      if (!this.task?.campaign) return [];

      const badges = [];

      if (this.task.campaign.startDate) {
        badges.push({
          text: `Starts ${this.task.campaign.startDate}`,
          colorClasses: 'bg-blue-100 text-blue-800'
        });
      }

      if (this.task.campaign.endDate) {
        badges.push({
          text: `Ends ${this.task.campaign.endDate}`,
          colorClasses: 'bg-orange-100 text-orange-800'
        });
      }

      if (this.task.campaign.discountAmount) {
        badges.push({
          text: `${this.task.campaign.discountAmount}% Off`,
          colorClasses: 'bg-green-100 text-green-800'
        });
      }

      if (this.task.campaign.minimumPurchase) {
        badges.push({
          text: `Min. Purchase $${this.task.campaign.minimumPurchase}`,
          colorClasses: 'bg-purple-100 text-purple-800'
        });
      }

      return badges;
    }
  }
}
</script>
