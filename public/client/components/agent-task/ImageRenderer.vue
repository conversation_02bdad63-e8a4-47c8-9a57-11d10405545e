<template>
  <div>
    <!-- Base component template - will be overridden by child components -->
    <slot></slot>
  </div>
</template>

<script lang="ts">
import { defineComponent } from '@vue/runtime-core';

export default defineComponent({
  name: 'ImageRenderer',
  props: {
    // Common props for all image renderers
    imageId: {
      type: String,
      required: true
    }
  },
  emits: ['image-loaded', 'image-error'],
  setup(props, { emit }) {
    // Common methods for image handling
    const handleImageLoad = () => {
      emit('image-loaded', props.imageId);
    };

    const handleImageError = (event: Event) => {
      emit('image-error', event, props.imageId);
    };

    const openImageInNewTab = (url: string) => {
      window.open(url, '_blank');
    };

    return {
      handleImageLoad,
      handleImageError,
      openImageInNewTab
    };
  }
});
</script>
