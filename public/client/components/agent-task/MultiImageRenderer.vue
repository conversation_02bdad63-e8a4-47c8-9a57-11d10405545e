<template>
  <div class="rounded-lg overflow-hidden bg-white border border-gray-200 shadow-sm inline-block max-w-full">
    <div class="p-3 border-b border-gray-100">
      <h3 class="text-sm font-medium text-gray-700">{{ title }}</h3>
    </div>
    <div class="p-3">
      <div class="grid gap-3"
        :class="{
          'grid-cols-1': images.length === 1,
          'grid-cols-2': images.length === 2,
          'grid-cols-3': images.length === 3,
          'grid-cols-4': images.length >= 4
        }">
        <div v-for="(image, index) in images" :key="index" class="relative">
          <!-- Loading placeholder -->
          <div v-if="image.loaded !== true" class="w-full h-[150px] bg-gray-100 flex items-center justify-center rounded-lg">
            <div class="flex flex-col items-center">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500 mb-2"></div>
              <span class="text-xs text-gray-500">Loading...</span>
            </div>
          </div>

          <!-- Hidden image preloader -->
          <img
            :src="image.url"
            alt=""
            class="hidden"
            @load="handleMultiImageLoad(index)"
            @error="handleMultiImageError($event, index)" />

          <!-- Actual image (only shown after loading) -->
          <div v-if="image.loaded === true" class="relative">
            <div class="w-full h-[150px] flex items-center justify-center overflow-hidden bg-gray-50 rounded-lg">
              <img
                :src="image.url"
                :alt="image.name || 'Image ' + (index + 1)"
                class="max-w-full max-h-full object-contain"
                @click="openImageInNewTab(image.url)"
                style="cursor: pointer;" />
            </div>
            <div class="mt-1">
              <div class="text-center">
                <span class="text-xs text-gray-700">{{ image.name || 'Image ' + (index + 1) }}</span>
              </div>
              <div class="text-center mt-1">
                <a :href="image.url" target="_blank" class="text-purple-600 hover:text-purple-800 text-xs">
                  Open in new tab
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from '@vue/runtime-core';
import ImageRenderer from './ImageRenderer.vue';

interface ImageItem {
  url: string;
  name?: string;
  loaded?: boolean;
}

export default defineComponent({
  name: 'MultiImageRenderer',
  extends: ImageRenderer,
  props: {
    images: {
      type: Array as () => ImageItem[],
      required: true
    },
    title: {
      type: String,
      default: 'Image Collection'
    }
  },
  setup(props, { emit }) {
    // Handle loading of individual images in the collection
    const handleMultiImageLoad = (imageIndex: number) => {
      // Create a new array with the updated image
      const updatedImages = [...props.images];
      updatedImages[imageIndex] = {
        ...updatedImages[imageIndex],
        loaded: true
      };

      // Emit event with the updated images array
      emit('multi-image-loaded', props.imageId, imageIndex, updatedImages);
    };

    // Handle errors for individual images
    const handleMultiImageError = (event: Event, imageIndex: number) => {
      // Create a new array with the error state for the failed image
      const updatedImages = [...props.images];
      updatedImages[imageIndex] = {
        ...updatedImages[imageIndex],
        loaded: true,
        name: 'Error: Unable to load image'
      };

      // Emit event with the updated images array and error info
      emit('multi-image-error', event, props.imageId, imageIndex, updatedImages);
    };

    return {
      handleMultiImageLoad,
      handleMultiImageError
    };
  }
});
</script>
