<template>
  <div>
    <div>
      <!-- Status Notification - Only show for non-loading states or errors -->
      <div v-if="status && status !== 'loading-segment'" class="mb-4">
        <div v-if="status === 'loading'" class="p-3 bg-blue-50 border border-blue-200 rounded-md flex items-center">
          <svg class="animate-spin h-5 w-5 mr-3 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-blue-800">{{ statusText }}</span>
        </div>
        <div v-else-if="status === 'success'" class="p-3 bg-green-50 border border-green-200 rounded-md flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-green-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <span class="text-green-800">{{ statusText }}</span>
        </div>
        <div v-else-if="status === 'fail'" class="p-3 bg-red-50 border border-red-200 rounded-md flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-red-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          <span class="text-red-800">{{ statusText }}</span>
        </div>
      </div>

      <!-- Segment Settings Section -->
      <div class="h-full">
        <div class="px-6 py-3 border-b bg-white rounded-t-lg">
          <h2 class="text-lg font-medium">Segment Settings</h2>
        </div>
        <div class="bg-gray-50 rounded-b-lg">
          <div class="p-6">
            <div class="max-w-3xl space-y-6">
              <!-- Task Description -->
              <div v-if="taskData" class="bg-white rounded-lg p-4">
                <div class="text-sm text-gray-600" v-html="formattedDescription"></div>
              </div>

              <!-- Segment Overview -->
              <div class="bg-white border rounded-lg overflow-hidden">
                <div class="p-4 border-b">
                  <div class="flex flex-col md:flex-row md:items-start">
                    <div class="flex-1 min-w-0 mr-4">
                      <!-- Display Mode: Segment Name with Edit Icon -->
                      <div v-if="!isEditingSegment" class="flex items-center">
                        <div class="flex flex-col">
                          <h3 class="font-medium">
                            <!-- Skeleton loader for segment name when loading -->
                            <span v-if="status === 'loading-segment'" class="bg-gray-200 animate-pulse rounded h-5 w-40 inline-block"></span>
                            <span v-else>{{ activeSegment?.name }}</span>
                          </h3>
                          <span v-if="!status || status !== 'loading-segment'" class="text-xs text-gray-500">
                            {{ segmentType === 'klaviyo' ? 'Klaviyo Segment' : 'Raleon Segment' }}
                          </span>
                        </div>
                        <button
                          @click="toggleSegmentEdit"
                          class="ml-2 text-gray-400 hover:text-blue-500 focus:outline-none"
                          title="Edit segment"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                          </svg>
                        </button>
                      </div>

                      <!-- Edit Mode: Segment Type Toggle and Dropdown -->
                      <div v-else class="flex flex-col space-y-3">
                        <!-- Segment Type Toggle -->
                        <div class="flex items-center space-x-4">
                          <span class="text-sm font-medium text-gray-700">Segment Type:</span>
                          <div class="flex items-center space-x-2">
                            <label class="flex items-center">
                              <input
                                type="radio"
                                v-model="segmentType"
                                value="raleon"
                                @change="handleSegmentTypeChange"
                                class="form-radio h-4 w-4 text-blue-600"
                              />
                              <span class="ml-2 text-sm text-gray-700">Raleon Segments</span>
                            </label>
                            <label class="flex items-center">
                              <input
                                type="radio"
                                v-model="segmentType"
                                value="klaviyo"
                                @change="handleSegmentTypeChange"
                                class="form-radio h-4 w-4 text-blue-600"
                              />
                              <span class="ml-2 text-sm text-gray-700">Klaviyo Segments</span>
                            </label>
                          </div>
                        </div>

                        <!-- Raleon Segment Dropdown -->
                        <div v-if="segmentType === 'raleon'" class="relative" style="min-width: 200px;">
                          <select
                            id="segment-select"
                            v-model="selectedSegmentId"
                            @change="handleSegmentChange"
                            class="block w-full py-1 pl-3 pr-8 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm appearance-none"
                          >
                            <option v-for="seg in allSegments" :key="seg.id" :value="seg.id">{{ seg.name }}</option>
                          </select>
                          <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2 text-gray-500">
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                          </div>
                        </div>

                        <!-- Klaviyo Segment Dropdown -->
                        <div v-if="segmentType === 'klaviyo'" class="relative" style="min-width: 200px;">
                          <select
                            id="klaviyo-segment-select"
                            v-model="selectedKlaviyoSegmentId"
                            @change="handleKlaviyoSegmentChange"
                            class="block w-full py-1 pl-3 pr-8 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm appearance-none"
                          >
                            <option value="">Select a Klaviyo segment...</option>
                            <option v-for="seg in allKlaviyoSegments" :key="seg.id" :value="seg.id">{{ seg.name }}</option>
                          </select>
                          <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2 text-gray-500">
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                          </div>
                        </div>

                        <!-- Cancel Button -->
                        <div class="flex justify-end">
                          <button
                            @click="toggleSegmentEdit"
                            class="text-gray-400 hover:text-red-500 focus:outline-none"
                            title="Cancel editing"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                          </button>
                        </div>
                      </div>

                      <p class="text-sm text-gray-500 mt-1 break-words pr-2">
                        <!-- Skeleton loader for description when loading -->
                        <span v-if="status === 'loading-segment'" class="bg-gray-200 animate-pulse rounded h-4 w-64 inline-block"></span>
                        <span v-else>{{ activeSegment?.description }}</span>
                      </p>
                    </div>

                    <!-- Klaviyo Sync Status & Button -->
                    <div class="flex-shrink-0 mt-3 md:mt-0 flex space-x-2">
                      <!-- Klaviyo Segment Status - Show when using Klaviyo segment -->
                      <div v-if="segmentType === 'klaviyo' && activeSegment?.klaviyoSegmentId"
                        class="flex items-center border rounded-md px-3 py-1.5 bg-purple-50 border-purple-200">
                        <svg xmlns="http://www.w3.org/2000/svg" height="16px" viewBox="0 -960 960 960"
                          width="16px" fill="#7c3aed" class="mr-2">
                          <path
                            d="m382-354 339-339q12-12 28-12t28 12q12 12 12 28.5T777-636L410-268q-12 12-28 12t-28-12L182-440q-12-12-11.5-28.5T183-497q12-12 28.5-12t28.5 12l142 143Z" />
                        </svg>
                        <div class="flex flex-col">
                          <span class="text-sm font-medium text-purple-800">Klaviyo Segment</span>
                          <span class="text-xs text-purple-600">Ready to use</span>
                        </div>
                      </div>

                      <!-- Sync Button - Show when not synced and not currently syncing (Raleon segments only) -->
                      <div v-if="segmentType === 'raleon' && activeSegment?.id && !activeSegment?.externalId && klaviyoMetricCompleted && status !== 'loading'">
                        <PrimaryButton cta="Sync to Klaviyo" size="xs" @click="handleKlaviyoSync" />
                      </div>

                      <!-- Animated Sync Button - Show when syncing -->
                      <div v-if="status === 'loading'"
                        class="flex items-center border rounded-md px-3 py-1.5 bg-blue-50 border-blue-200">
                        <svg class="animate-spin h-4 w-4 mr-2 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span class="text-sm font-medium text-blue-800">Syncing to Klaviyo...</span>
                      </div>

                      <!-- Synced Status - Show when synced or when just finished syncing with success status (Raleon segments only) -->
                      <div v-if="segmentType === 'raleon' && ((activeSegment?.externalId && activeSegment?.externalSyncDate) || status === 'success')"
                        class="flex items-center border rounded-md px-3 py-1.5 bg-green-50 border-green-200">
                        <svg xmlns="http://www.w3.org/2000/svg" height="16px" viewBox="0 -960 960 960"
                          width="16px" fill="#16a34a" class="mr-2">
                          <path
                            d="m382-354 339-339q12-12 28-12t28 12q12 12 12 28.5T777-636L410-268q-12 12-28 12t-28-12L182-440q-12-12-11.5-28.5T183-497q12-12 28.5-12t28.5 12l142 143Z" />
                        </svg>
                        <div class="flex flex-col">
                          <span class="text-sm font-medium text-green-800">Synced to Klaviyo</span>
                          <span class="text-xs text-green-600">First sync: {{ formattedDate }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Quick Stats -->
                <div class="grid grid-cols-2 border-b">
                  <div class="p-4 border-r">
                    <div class="text-sm text-gray-500">Estimated Size</div>
                    <div class="text-xl font-semibold mt-1">
                      <!-- Skeleton loader for customer count when loading -->
                      <span v-if="status === 'loading-segment'" class="bg-gray-200 animate-pulse rounded h-7 w-16 inline-block"></span>
                      <span v-else-if="segmentType === 'klaviyo'">N/A</span>
                      <span v-else>{{ activeSegment?.aggregates?.totalCount || activeSegment?.customers?.length || 0 }}</span>
                    </div>
                    <div class="text-sm text-gray-500">profiles</div>
                  </div>
                  <div class="p-4">
                    <div class="text-sm text-gray-500">LTV</div>
                    <div class="text-xl font-semibold mt-1">
                      <!-- Skeleton loader for LTV when loading -->
                      <span v-if="status === 'loading-segment'" class="bg-gray-200 animate-pulse rounded h-7 w-20 inline-block"></span>
                      <span v-else-if="segmentType === 'klaviyo'">N/A</span>
                      <span v-else>${{ Math.round(activeSegment?.aggregates?.averageLtv || 0).toLocaleString() }}</span>
                    </div>
                    <div class="text-sm text-gray-500">All time</div>
                  </div>
                </div>
                <!-- Segment Signals -->
                <div class="p-4">
                  <h4 class="text-sm font-medium mb-3">
                    {{ segmentType === 'klaviyo' ? 'Segment Details' : 'Included Signals' }}
                  </h4>

                  <!-- Skeleton loader for signals when loading -->
                  <div v-if="status === 'loading-segment'" class="space-y-4">
                    <div>
                      <div class="text-xs font-medium text-gray-500 mb-2">LOADING...</div>
                      <div class="flex flex-wrap gap-2">
                        <div v-for="n in 3" :key="n" class="bg-gray-200 animate-pulse rounded h-8 w-28"></div>
                      </div>
                    </div>
                  </div>

                  <!-- Klaviyo segment info -->
                  <div v-else-if="segmentType === 'klaviyo'" class="space-y-4">
                    <div class="text-sm text-gray-600">
                      This is a Klaviyo segment that will be used directly from your Klaviyo account.
                      Segment conditions and targeting are managed within Klaviyo.
                    </div>
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-3">
                      <div class="text-xs font-medium text-purple-800 mb-1">KLAVIYO SEGMENT</div>
                      <div class="text-sm text-purple-700">{{ activeSegment?.description || 'External segment from Klaviyo' }}</div>
                    </div>
                  </div>

                  <!-- Raleon segment signals when loaded -->
                  <div v-else-if="activeSegment?.organizationSegmentDetails" class="space-y-4">
                    <!-- Positive Signals -->
                    <div v-if="positiveSignals.length > 0">
                      <div class="text-xs font-medium text-gray-500 mb-2">POSITIVE SIGNALS</div>
                      <div class="flex flex-wrap gap-2">
                        <SignalBadge
                          v-for="signal in positiveSignals"
                          :key="signal.id"
                          :signal="signal"
                          :section="'segment'"
                          :showNub="false"
                          :margin="false"
                        />
                      </div>
                    </div>
                    <!-- Negative Signals -->
                    <div v-if="negativeSignals.length > 0">
                      <div class="text-xs font-medium text-gray-500 mb-2">NEGATIVE SIGNALS</div>
                      <div class="flex flex-wrap gap-2">
                        <SignalBadge
                          v-for="signal in negativeSignals"
                          :key="signal.id"
                          :signal="signal"
                          :section="'segment'"
                          :showNub="false"
                          :margin="false"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SignalBadge from '../../components/SignalBadge.ts.vue';
import PrimaryButton from '../../components/PrimaryButton.ts.vue';
import * as Utils from '../../../client-old/utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;


export default {
  name: 'SegmentTask',
  components: {
    SignalBadge,
    PrimaryButton
  },
  props: {
    segment: {
      type: Object,
      default: () => ({})
    },
    taskData: {
      type: String,
      default: ''
    },
    campaignId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      klaviyoMetricCompleted: true,
      status: '',
      statusText: '',
      allSegments: [],
      allKlaviyoSegments: [],
      selectedSegmentId: null,
      selectedKlaviyoSegmentId: null,
      localSegment: null,
      isEditingSegment: false,
      segmentType: 'raleon' // 'raleon' or 'klaviyo'
    };
  },
  computed: {
    formattedDescription() {
      return this.taskData?.replace(/\n/g, '<br>') || '';
    },
    positiveSignals() {
      if (!this.activeSegment?.organizationSegmentDetails) return [];
      return this.activeSegment.organizationSegmentDetails
        .filter(detail => detail.include)
        .map(detail => detail.metricSegment);
    },
    negativeSignals() {
      if (!this.activeSegment?.organizationSegmentDetails) return [];
      return this.activeSegment.organizationSegmentDetails
        .filter(detail => !detail.include)
        .map(detail => detail.metricSegment);
    },
    formattedDate() {
      if (!this.activeSegment?.externalSyncDate) return '';
      const date = new Date(this.activeSegment.externalSyncDate);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    // Use computed property to get the active segment (either local or prop)
    activeSegment() {
      return this.localSegment || this.segment;
    }
  },
  watch: {
    segment: {
      immediate: true,
      handler(newSegment) {
        if (newSegment && newSegment.id) {
          // Check if this is a Klaviyo segment
          if (newSegment.segmentType === 'klaviyo' || newSegment.klaviyoSegmentId) {
            this.segmentType = 'klaviyo';
            this.selectedKlaviyoSegmentId = newSegment.klaviyoSegmentId;
          } else {
            this.segmentType = 'raleon';
            this.selectedSegmentId = newSegment.id;
          }
          this.localSegment = JSON.parse(JSON.stringify(newSegment));
        }
      }
    }
  },
  created() {
    console.log('Segment data:', this.segment); // Debug log
    console.log('API URL being used:', URL_DOMAIN); // Debug the API URL

    // Check Klaviyo metric completion status
    this.checkKlaviyoSyncProgress();

    // Fetch all segments
    this.fetchAllSegments();
    this.fetchAllKlaviyoSegments();
  },
  methods: {
    // Toggle between edit mode and display mode for segment name
    toggleSegmentEdit() {
      this.isEditingSegment = !this.isEditingSegment;

      // When entering edit mode, make sure the selected segment ID is set to the current segment
      if (this.isEditingSegment && this.activeSegment?.id) {
        this.selectedSegmentId = this.activeSegment.id;
      }
    },

    // Helper method to validate auth token
    validateToken() {
      const token = localStorage.getItem('token');
      if (!token) {
        const errorMsg = 'No authentication token found. Please log in again.';
        console.error(errorMsg);
        this.status = 'fail';
        this.statusText = errorMsg;
        throw new Error(errorMsg);
      }
      return token;
    },

    // Helper method to check if response is HTML (not JSON)
    isHtmlResponse(text) {
      return text.trim().startsWith('<!DOCTYPE') || text.trim().startsWith('<html');
    },

    // Helper method to safely parse JSON
    async safelyParseJson(response) {
      try {
        const text = await response.text();
        if (this.isHtmlResponse(text)) {
          console.error('Received HTML response instead of JSON:', text.substring(0, 150) + '...');
          throw new Error('Received HTML response instead of JSON. Check your authentication and API endpoint.');
        }
        return JSON.parse(text);
      } catch (error) {
        console.error('Error parsing response:', error);
        throw error;
      }
    },

    async fetchAllSegments() {
      try {
        const token = this.validateToken();

        // Use a separate loading state for segments that won't show the top notification
        this.status = 'loading-segment';
        this.statusText = 'Loading segments...';

        const response = await fetch(`${URL_DOMAIN}/organization-segment`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          }
        });

        if (!response.ok) {
          console.error('Failed to fetch segments:', response.status, response.statusText);
          this.status = 'fail';
          this.statusText = `Failed to load segments: ${response.status} ${response.statusText}`;
          throw new Error(`Failed to fetch segments: ${response.status} ${response.statusText}`);
        }

        const data = await this.safelyParseJson(response);
        this.allSegments = data;

        this.status = '';
        this.statusText = '';
      } catch (error) {
        console.error('Error fetching segments:', error);
        this.status = 'fail';
        this.statusText = error.message || 'Error loading segments';
      }
    },

    async fetchAllKlaviyoSegments() {
      try {
        const token = this.validateToken();

        const response = await fetch(`${URL_DOMAIN}/organization-segment/klaviyo-segments`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          }
        });

        if (!response.ok) {
          console.error('Failed to fetch Klaviyo segments:', response.status, response.statusText);
          // Don't set error status for Klaviyo segments as it's optional
          return;
        }

        const data = await this.safelyParseJson(response);
        this.allKlaviyoSegments = data;
      } catch (error) {
        console.error('Error fetching Klaviyo segments:', error);
        // Don't set error status for Klaviyo segments as it's optional
      }
    },

    handleSegmentTypeChange() {
      // Clear selections when switching types
      this.selectedSegmentId = null;
      this.selectedKlaviyoSegmentId = null;
      this.localSegment = null;

      // If switching to Klaviyo and we haven't loaded segments yet, load them
      if (this.segmentType === 'klaviyo' && this.allKlaviyoSegments.length === 0) {
        this.fetchAllKlaviyoSegments();
      }
    },

    async handleSegmentChange() {
      // Find the selected segment from the list
      const selectedSegment = this.allSegments.find(seg => seg.id === this.selectedSegmentId);
      console.log('handleSegmentChange called with selectedSegmentId:', this.selectedSegmentId);

      if (!selectedSegment) {
        console.error('Selected segment not found in allSegments list');
        return;
      }

      try {
        // Fetch the complete segment details
        await this.fetchSegmentDetails(this.selectedSegmentId);

        // Notify parent component about segment change
        console.log('Emitting update:segment event with segment data:', this.localSegment);
        this.$emit('update:segment', this.localSegment);
      } catch (error) {
        console.error('Error changing segment:', error);
        this.status = 'fail';
        this.statusText = error.message || 'Error changing segment';
      }
    },

    async handleKlaviyoSegmentChange() {
      // Find the selected Klaviyo segment from the list
      const selectedKlaviyoSegment = this.allKlaviyoSegments.find(seg => seg.id === this.selectedKlaviyoSegmentId);
      console.log('handleKlaviyoSegmentChange called with selectedKlaviyoSegmentId:', this.selectedKlaviyoSegmentId);

      if (!selectedKlaviyoSegment) {
        console.error('Selected Klaviyo segment not found in allKlaviyoSegments list');
        return;
      }

      try {
        // Create a mock segment object for Klaviyo segments
        this.localSegment = {
          id: selectedKlaviyoSegment.id,
          name: selectedKlaviyoSegment.name,
          description: selectedKlaviyoSegment.description,
          segmentType: 'klaviyo',
          klaviyoSegmentId: selectedKlaviyoSegment.id,
          // Mock some basic data for display
          aggregates: {
            totalCount: 'N/A',
            averageLtv: 0
          },
          organizationSegmentDetails: []
        };

        // Toggle out of edit mode
        this.isEditingSegment = false;

        // Notify parent component about segment change
        console.log('Emitting update:segment event with Klaviyo segment data:', this.localSegment);
        this.$emit('update:segment', this.localSegment);
      } catch (error) {
        console.error('Error changing Klaviyo segment:', error);
        this.status = 'fail';
        this.statusText = error.message || 'Error changing Klaviyo segment';
      }
    },

    async fetchSegmentDetails(segmentId) {
      try {
        const token = this.validateToken();

        // Use a separate loading state for segments that won't show the top notification
        this.status = 'loading-segment';
        this.statusText = 'Loading segment details...';

        const response = await fetch(`${URL_DOMAIN}/organization-segment/${segmentId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          }
        });

        if (!response.ok) {
          console.error('Failed to fetch segment details:', response.status, response.statusText);
          this.status = 'fail';
          this.statusText = `Failed to load segment details: ${response.status} ${response.statusText}`;
          throw new Error(`Failed to fetch segment details: ${response.status} ${response.statusText}`);
        }

        const segmentData = await this.safelyParseJson(response);

        // Update the local segment data
        this.localSegment = segmentData;

        // Clear the status immediately instead of showing success message
        this.status = '';
        this.statusText = '';

        // Toggle out of edit mode when new segment is loaded
        this.isEditingSegment = false;

        // Emit the updated segment to the parent
        console.log('Emitting update:segment event with segment data:', this.localSegment);
        this.$emit('update:segment', this.localSegment);
      } catch (error) {
        console.error('Error fetching segment details:', error);
        this.status = 'fail';
        this.statusText = error.message || 'Error loading segment details';
        throw error;
      }
    },

    async handleKlaviyoSync() {
      try {
        if (!this.activeSegment?.id) {
          this.status = 'fail';
          this.statusText = 'Cannot sync: Segment ID is missing';
          this.clearStatusAfterDelay();
          return;
        }

        this.status = 'loading';
        this.statusText = 'Syncing with Klaviyo...';
        await this.klaviyoSync();
        this.clearStatusAfterDelay();
        this.$emit('update:segment', this.localSegment);
      } catch (error) {
        console.error('Failed to sync with Klaviyo:', error);
        // Status is already set in the other methods if they fail
        if (!this.status) {
          this.status = 'fail';
          this.statusText = error.message || 'Failed to sync with Klaviyo';
        }
        this.clearStatusAfterDelay();
      }
    },

    async klaviyoSync() {
      try {
        const connectionResponse = await this.checkKlaviyoConnection();
        if (connectionResponse.connected) {
          await this.syncOrganizationSegment();
        } else {
          console.error('Klaviyo integration is not connected');
          // Optionally, you could emit an event to notify parent component
          this.$emit('klaviyo-not-connected');
        }
      } catch (error) {
        console.error('An error occurred:', error);
      }
    },

    async checkKlaviyoConnection() {
      try {
        const token = this.validateToken();

        const response = await fetch(`${URL_DOMAIN}/integration/klaviyo/connected`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          }
        });

        if (!response.ok) {
          console.error('Klaviyo connection check failed:', response.status, response.statusText);
          throw new Error(`Failed to check Klaviyo connection: ${response.status} ${response.statusText}`);
        }

        return await this.safelyParseJson(response);
      } catch (error) {
        console.error('Klaviyo connection error:', error);
        throw new Error('Failed to check Klaviyo connection');
      }
    },

    async syncOrganizationSegment() {
      try {
        const token = this.validateToken();

        const response = await fetch(`${URL_DOMAIN}/organization-segment/${this.activeSegment.id}/integration-sync`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          }
        });

        // Check if response is ok before parsing as JSON
        if (!response.ok) {
          this.status = 'fail';
          this.statusText = `Failed to sync: ${response.status} ${response.statusText}`;
          console.error('Sync error:', response.status, response.statusText);
          throw new Error(`Klaviyo Error: ${response.status} ${response.statusText}`);
        }

        // Only parse JSON if the response was successful
        const data = await this.safelyParseJson(response);

        this.status = 'success';
        this.statusText = 'Successfully synced to Klaviyo';

        // Update the local segment data instead of the prop
        if (this.localSegment) {
          // Update externalId and externalSyncDate to ensure the synced message appears
          this.localSegment.externalId = data.externalId || this.localSegment.externalId || 'synced';
          this.localSegment.externalSyncDate = data.externalSyncDate || new Date().toISOString();
        }

        // Update the segment data to reflect changes
        this.$emit('update:segment', this.localSegment);
      } catch (error) {
        console.error('Sync error:', error);
        this.status = 'fail';
        this.statusText = error.message || 'Error syncing with Klaviyo';
        throw error;
      }
    },

    async checkKlaviyoSyncProgress() {
      // Skip the check if no token or there's no point checking
      if (!localStorage.getItem('token')) {
        console.log('Skipping Klaviyo sync progress check: No token available');
        this.klaviyoMetricCompleted = true;
        return;
      }

      try {
        const token = this.validateToken();

        console.log('Checking Klaviyo sync progress, API URL:', `${URL_DOMAIN}/organization-segment/klaviyo-sync-progress`);

        const response = await fetch(`${URL_DOMAIN}/organization-segment/klaviyo-sync-progress`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          // Add cache control to prevent cached responses
          cache: 'no-cache'
        });

        if (!response.ok) {
          console.error("Klaviyo sync progress check failed:", response.status, response.statusText);
          // Set to true by default if we can't check properly
          this.klaviyoMetricCompleted = true;
          return;
        }

        const progressData = await this.safelyParseJson(response);
        console.log('Klaviyo sync progress data:', progressData);
        this.klaviyoMetricCompleted = progressData.progress === 100;
      } catch (error) {
        console.error("Error checking Klaviyo sync progress:", error);
        // Set to true by default if we can't check properly
        this.klaviyoMetricCompleted = true;
      }
    },

    clearStatusAfterDelay(delay = 5000) {
      setTimeout(() => {
        this.status = '';
        this.statusText = '';
      }, delay);
    }
  }
}
</script>
