<template>
  <div class="h-full flex flex-col bg-white">
    <!-- Header -->
    <div class="border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold text-gray-900">Email Generation Jobs</h2>
        <div class="flex items-center space-x-3">
          <button
            @click="refreshJobs"
            :disabled="isRefreshing"
            class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            <svg
              class="w-4 h-4 mr-2"
              :class="{ 'animate-spin': isRefreshing }"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Refresh
          </button>
          <button
            @click="openCreateJobModal"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Create Job
          </button>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="flex-1 overflow-auto">
      <!-- Loading state -->
      <div v-if="isLoading" class="flex items-center justify-center h-64">
        <div class="flex items-center space-x-2">
          <div class="w-4 h-4 bg-purple-500 rounded-full animate-bounce"></div>
          <div class="w-4 h-4 bg-purple-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
          <div class="w-4 h-4 bg-purple-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
          <span class="text-gray-500 ml-2">Loading jobs...</span>
        </div>
      </div>

      <!-- Empty state -->
      <div v-else-if="workflows.length === 0" class="flex flex-col items-center justify-center h-64 text-gray-500">
        <svg class="w-16 h-16 mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4-4-4m0 0L8 8l4-4 4 4z" />
        </svg>
        <p class="text-lg font-medium mb-2">No email generation jobs yet</p>
        <p class="text-sm">Create your first automated email generation job to get started</p>
      </div>

      <!-- Jobs list -->
      <div v-else class="p-6">
        <div class="space-y-4">
          <div
            v-for="workflow in workflows"
            :key="workflow.id"
            class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow"
          >
            <div class="p-4">
              <!-- Job header -->
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                      <div
                        class="w-3 h-3 rounded-full"
                        :class="getStatusColor(workflow.status)"
                      ></div>
                    </div>
                    <div>
                      <h3 class="text-sm font-medium text-gray-900">
                        Email Generation Job #{{ workflow.id }}
                      </h3>
                      <p class="text-xs text-gray-500">
                        {{ formatDate(workflow.createdAt) }}
                      </p>
                    </div>
                  </div>

                  <!-- Brief preview -->
                  <div class="mt-3 bg-gray-50 p-3 rounded-md">
                    <p class="text-sm font-medium text-gray-700">Subject: {{ workflow.briefData.subjectLine }}</p>
                    <p class="text-xs text-gray-500 mt-1">Preview: {{ workflow.briefData.previewText }}</p>
                  </div>
                </div>

                <!-- Actions -->
                <div class="flex items-center space-x-2 ml-4">
                  <button
                    @click="viewLogs(workflow)"
                    class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Logs
                  </button>
                  <button
                    v-if="workflow.status === 'failed' || workflow.status === 'timeout'"
                    @click="retryJob(workflow)"
                    class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Retry
                  </button>
                  <button
                    v-if="workflow.status === 'processing' || workflow.status === 'pending'"
                    @click="cancelJob(workflow)"
                    class="inline-flex items-center px-3 py-1.5 border border-red-300 text-xs font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
                  >
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Cancel
                  </button>
                </div>
              </div>

              <!-- Progress bar -->
              <div v-if="workflow.status === 'processing'" class="mt-4">
                <div class="flex justify-between items-center text-xs text-gray-500 mb-1">
                  <span>Progress: {{ workflow.completedIterations || 0 }}/{{ workflow.iterationCount }}</span>
                  <span v-if="workflow.status === 'processing'">
                    {{ getTimeElapsed(workflow) }}
                  </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    class="bg-purple-600 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${getProgressPercentage(workflow)}%` }"
                  ></div>
                </div>
              </div>

              <!-- Status -->
              <div class="mt-4 flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <span class="text-xs font-medium text-gray-500">Status:</span>
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="getStatusBadgeClass(workflow.status)"
                  >
                    {{ getStatusText(workflow.status) }}
                  </span>
                </div>
                <div v-if="workflow.status === 'completed'" class="flex items-center space-x-2">
                  <span class="text-xs text-gray-500">{{ workflow.completedIterations || 0 }} variations</span>
                  <button
                    @click="viewVariations(workflow)"
                    class="text-xs text-purple-600 hover:text-purple-700 font-medium"
                  >
                    View Results
                  </button>
                </div>
              </div>

              <!-- Error message -->
              <div v-if="workflow.error" class="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                <p class="text-sm text-red-700">{{ workflow.error }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Job Modal -->
    <div
      v-if="showCreateJobModal"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="showCreateJobModal = false"
    >
      <div
        class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"
        @click.stop
      >
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Create Email Generation Job</h3>

          <div class="space-y-4">
            <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h4 class="text-sm font-medium text-blue-800 mb-2">Generate Email Variations</h4>
              <p class="text-xs text-blue-600">
                This will automatically use the current task brief to generate multiple email variations with different tones and styles.
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Number of Variations</label>
              <select
                v-model="newJob.iterationCount"
                @change="updateTasteProfiles"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="1">1 variation</option>
                <option value="2">2 variations</option>
                <option value="3">3 variations</option>
                <option value="4">4 variations</option>
                <option value="5">5 variations</option>
              </select>
            </div>

            <!-- Taste Profile Selection -->
            <div v-if="newJob.iterationCount > 0" class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Taste Profiles</label>
              <div class="space-y-4">
                <div
                  v-for="(profile, index) in newJob.tasteProfiles"
                  :key="index"
                  class="p-3 border border-gray-200 rounded-md bg-gray-50"
                >
                  <div class="flex items-center space-x-3 mb-2">
                    <div class="flex-shrink-0">
                      <span class="text-sm font-medium text-gray-600">Variation {{ index + 1 }}:</span>
                    </div>
                    <div class="flex-1">
                      <select
                        v-model="profile.name"
                        class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm min-w-0"
                      >
                        <option value="Safe">Safe</option>
                        <option value="Variety">Variety</option>
                        <option value="Unique">Unique</option>
                        <!-- <option value="Wild">Wild</option> -->
                      </select>
                    </div>
                  </div>
                  <div class="text-xs text-gray-500 leading-relaxed">
                    {{ getTasteProfileDescription(profile.name) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="flex justify-end space-x-3 mt-6">
            <button
              @click="showCreateJobModal = false"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              Cancel
            </button>
            <button
              @click="createJob"
              :disabled="isCreatingJob"
              class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
            >
              {{ isCreatingJob ? 'Creating...' : 'Create Job' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Logs Modal -->
    <div
      v-if="showLogsModal"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="showLogsModal = false"
    >
      <div
        class="relative top-10 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white"
        @click.stop
      >
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-900">
            Job Logs - #{{ selectedWorkflow?.id }}
          </h3>
          <button
            @click="showLogsModal = false"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="max-h-96 overflow-y-auto bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm">
          <div v-for="log in selectedLogs" :key="log.id" class="mb-2">
            <span class="text-gray-500">{{ formatLogTime(log.timestamp) }}</span>
            <span :class="getLogLevelColor(log.level)" class="ml-2 font-medium">[{{ log.level.toUpperCase() }}]</span>
            <span class="ml-2">{{ log.message }}</span>
            <div v-if="log.data" class="ml-4 text-gray-400 text-xs">
              {{ JSON.stringify(log.data, null, 2) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Variations Modal -->
    <div
      v-if="showVariationsModal"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="showVariationsModal = false"
    >
      <div
        class="relative top-4 mx-auto border w-11/12 max-w-7xl shadow-lg rounded-md bg-white h-5/6"
        @click.stop
      >
        <!-- Header -->
        <div class="flex justify-between items-center p-4 border-b">
          <div class="flex items-center space-x-4">
            <h3 class="text-lg font-medium text-gray-900">
              Email Variations - Job #{{ selectedWorkflow?.id }}
            </h3>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500">
                {{ currentVariationIndex + 1 }} of {{ selectedVariations.length }}
              </span>
              <div class="flex space-x-1">
                <button
                  @click="previousVariation"
                  :disabled="currentVariationIndex === 0"
                  class="p-1 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  @click="nextVariation"
                  :disabled="currentVariationIndex === selectedVariations.length - 1"
                  class="p-1 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <button
              v-if="currentVariation"
              @click="selectVariation(currentVariation)"
              :disabled="isSelectingVariation"
              class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 flex items-center space-x-2"
            >
              <svg v-if="isSelectingVariation" class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>{{ isSelectingVariation ? 'Selecting...' : 'Select This Variation' }}</span>
            </button>
            <button
              @click="showVariationsModal = false"
              class="text-gray-400 hover:text-gray-600"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Content -->
        <div class="flex h-full" style="height: calc(100% - 73px);">
          <!-- Variation Info Sidebar -->
          <div class="w-80 bg-gray-50 border-r p-4 overflow-y-auto">
            <div v-if="currentVariation">
              <h4 class="text-lg font-medium text-gray-900 mb-4">
                Variation {{ currentVariation.iterationNumber }}
              </h4>

              <!-- Variation Details -->
              <div class="space-y-4">
                <div v-if="currentVariation.variationPrompt" class="bg-white p-3 rounded-md border">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">Variation Settings</h5>
                  <div class="space-y-2 text-xs text-gray-600">
                    <div v-if="currentVariation.variationPrompt.tasteProfile">
                      <span class="font-medium">Taste Profile:</span>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 ml-1">
                        {{ currentVariation.variationPrompt.tasteProfile }}
                      </span>
                    </div>
                    <div v-if="currentVariation.variationPrompt.tone">
                      <span class="font-medium">Tone:</span> {{ currentVariation.variationPrompt.tone }}
                    </div>
                    <div v-if="currentVariation.variationPrompt.style">
                      <span class="font-medium">Style:</span> {{ currentVariation.variationPrompt.style }}
                    </div>
                    <div v-if="currentVariation.variationPrompt.focus">
                      <span class="font-medium">Focus:</span> {{ currentVariation.variationPrompt.focus }}
                    </div>
                    <div v-if="currentVariation.variationPrompt.customInstructions">
                      <span class="font-medium">Instructions:</span> {{ currentVariation.variationPrompt.customInstructions }}
                    </div>
                  </div>
                </div>

                <div class="bg-white p-3 rounded-md border">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">Status</h5>
                  <div class="text-xs text-gray-600">
                    <div>Status: {{ currentVariation.status }}</div>
                    <div v-if="currentVariation.completedAt">
                      Completed: {{ formatDate(currentVariation.completedAt) }}
                    </div>
                  </div>
                </div>

                <!-- Loading State -->
                <div v-if="isProcessingVariation" class="bg-white p-3 rounded-md border">
                  <div class="flex items-center space-x-2">
                    <svg class="animate-spin h-4 w-4 text-purple-600" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-sm text-gray-600">Processing email design...</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Email Preview -->
          <div class="flex-1 bg-white overflow-hidden relative">
            <div v-if="isProcessingVariation" class="flex items-center justify-center h-full">
              <div class="text-center">
                <svg class="w-8 h-8 mx-auto text-purple-600 animate-spin mb-4" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <p class="text-gray-600">Processing email design...</p>
              </div>
            </div>

            <div v-else-if="emailPreviewHtml" class="h-full overflow-auto p-4">
              <div class="max-w-2xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
                <div class="bg-gray-100 px-4 py-2 border-b">
                  <div class="text-xs text-gray-500">Email Preview</div>
                </div>
                <div class="email-preview-content" v-html="emailPreviewHtml"></div>
              </div>
            </div>

            <div v-else class="flex items-center justify-center h-full text-gray-500">
              <div class="text-center">
                <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <p>No preview available</p>
              </div>
            </div>

            <!-- Hidden Unlayer Editor -->
            <div v-if="showUnlayerEditor" class="hidden">
              <EmailEditor
                ref="unlayerEditor"
                :project-id="projectId"
                :tools="{}"
                :options="unlayerOptions"
                @load="onUnlayerEditorLoaded"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as Utils from '../../../client-old/utils/Utils';
import { EmailEditor } from 'vue-email-editor';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
  name: 'EmailJobsView',
  components: {
    EmailEditor
  },
  props: {
    task: {
      type: Object,
      required: true
    },
    projectId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      workflows: [],
      isLoading: false,
      isRefreshing: false,
      isCreatingJob: false,
      showCreateJobModal: false,
      showLogsModal: false,
      showVariationsModal: false,
      selectedWorkflow: null,
      selectedLogs: [],
      selectedVariations: [],
      autoRefreshInterval: null,
      newJob: {
        subjectLine: '',
        previewText: '',
        briefText: '',
        iterationCount: 3,
        tasteProfiles: [
          { name: 'Safe' },
          { name: 'Variety' },
          { name: 'Unique' }
        ]
      },
      // New variation preview functionality
      currentVariationIndex: 0,
      isProcessingVariation: false,
      isSelectingVariation: false,
      emailPreviewHtml: '',
      showUnlayerEditor: false,
      unlayerEditorReady: false
    };
  },
  computed: {
    canCreateJob() {
      // Always return true since we extract brief data from task
      return true;
    },
    currentVariation() {
      if (this.selectedVariations.length === 0) return null;
      return this.selectedVariations[this.currentVariationIndex] || null;
    },
    unlayerOptions() {
      return {
        displayMode: 'email',
        appearance: {
          theme: 'light'
        }
      };
    }
  },
  mounted() {
    this.loadJobs();
    this.startAutoRefresh();
  },
  beforeUnmount() {
    this.stopAutoRefresh();
  },
  methods: {
    openCreateJobModal() {
      // Extract brief data from the current task
      const briefData = this.extractBriefFromTask();

      if (briefData) {
        this.newJob.subjectLine = briefData.subjectLine || '';
        this.newJob.previewText = briefData.previewText || '';
        this.newJob.briefText = briefData.briefText || '';
      }

      this.showCreateJobModal = true;
    },

    extractBriefFromTask() {
      // The task object itself contains the brief data
      if (!this.task) return null;

      // First check if the task has taskSteps
      if (this.task.taskSteps && this.task.taskSteps.length > 0) {
        // Find the content task step
        const contentStep = this.task.taskSteps.find(step =>
          step.taskTypeId === 'CONTENT' || step.name === 'Content'
        );

        if (!contentStep && this.task.taskSteps.length > 0) {
          // Fallback to first step
          const firstStep = this.task.taskSteps[0];
          if (firstStep?.data) {
            try {
              let stepData;
              if (typeof firstStep.data === 'string') {
                // Try to parse as JSON first
                try {
                  stepData = JSON.parse(firstStep.data);
                } catch (jsonError) {
                  // If JSON parsing fails, treat as plain text brief
                  console.warn('Task step data is not JSON, treating as plain text brief:', firstStep.data.substring(0, 100) + '...');
                  stepData = {
                    briefText: firstStep.data,
                    subjectLine: '',
                    previewText: ''
                  };
                }
              } else {
                stepData = firstStep.data;
              }

              return {
                subjectLine: stepData.subjectLine || '',
                previewText: stepData.previewText || stepData.previewLine || '',
                briefText: stepData.briefText || stepData.brief || stepData.content || ''
              };
            } catch (error) {
              console.error('Error parsing task step data:', error);
            }
          }
        } else if (contentStep?.data) {
          try {
            let dataToProcess = contentStep.data;

            // Check if the data contains <brief> tags and extract the content
            if (typeof dataToProcess === 'string' && dataToProcess.includes('<brief>')) {
              const briefTagRegex = /<brief>\s*([\s\S]*?)\s*<\/brief>/i;
              const match = dataToProcess.match(briefTagRegex);

              if (match && match[1]) {
                // Found content between <brief> tags, use that for parsing
                dataToProcess = match[1].trim();
              }
            }

            let stepData;
            if (typeof dataToProcess === 'string') {
              // Try to parse as JSON first
              try {
                stepData = JSON.parse(dataToProcess);
              } catch (jsonError) {
                // If JSON parsing fails, treat as plain text brief
                console.warn('Task step data is not JSON, treating as plain text brief:', dataToProcess.substring(0, 100) + '...');
                stepData = {
                  briefText: dataToProcess,
                  subjectLine: '',
                  previewText: ''
                };
              }
            } else {
              stepData = dataToProcess;
            }

            return {
              subjectLine: stepData.subjectLine || '',
              previewText: stepData.previewText || stepData.previewLine || '',
              briefText: stepData.briefText || stepData.brief || stepData.content || ''
            };
          } catch (error) {
            console.error('Error parsing task step data:', error);
          }
        }
      }

      // If no taskSteps, check if the brief data is directly on the task
      if (this.task.briefData) {
        return {
          subjectLine: this.task.briefData.subjectLine || '',
          previewText: this.task.briefData.previewText || this.task.briefData.previewLine || '',
          briefText: this.task.briefData.briefText || this.task.briefData.brief || ''
        };
      }

      // Check if the task has the data directly as properties
      if (this.task.subjectLine || this.task.previewText || this.task.briefText) {
        return {
          subjectLine: this.task.subjectLine || '',
          previewText: this.task.previewText || this.task.previewLine || '',
          briefText: this.task.briefText || this.task.brief || ''
        };
      }

      // Check if there's a data property on the task itself
      if (this.task.data) {
        try {
          let taskData;
          if (typeof this.task.data === 'string') {
            // Try to parse as JSON first
            try {
              taskData = JSON.parse(this.task.data);
            } catch (jsonError) {
              // If JSON parsing fails, treat as plain text brief
              console.warn('Task data is not JSON, treating as plain text brief:', this.task.data.substring(0, 100) + '...');
              taskData = {
                briefText: this.task.data,
                subjectLine: '',
                previewText: ''
              };
            }
          } else {
            taskData = this.task.data;
          }

          return {
            subjectLine: taskData.subjectLine || '',
            previewText: taskData.previewText || taskData.previewLine || '',
            briefText: taskData.briefText || taskData.brief || taskData.content || ''
          };
        } catch (error) {
          console.error('Error parsing task data:', error);
        }
      }

      console.warn('No brief data found in task', this.task);
      return null;
    },

    async loadJobs(silent = false) {
      if (!this.task?.id) return;

      if (!silent) {
        this.isLoading = true;
      }

      try {
        const response = await fetch(`${URL_DOMAIN}/workflows/task/${this.task.id}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          const newWorkflows = await response.json();

          // Only update if data has actually changed to avoid flickering
          if (this.hasWorkflowDataChanged(newWorkflows)) {
            this.workflows = newWorkflows;
          }
        } else {
          console.error('Failed to load workflows:', response.statusText);
        }
      } catch (error) {
        console.error('Error loading workflows:', error);
      } finally {
        if (!silent) {
          this.isLoading = false;
        }
      }
    },

    async refreshJobs() {
      this.isRefreshing = true;
      await this.loadJobs();
      this.isRefreshing = false;
    },

    async createJob() {
      this.isCreatingJob = true;

      // Extract brief data from the current task
      const briefData = this.extractBriefFromTask();

      if (!briefData) {
        console.error('No brief data found in task. Task structure:', this.task);
        alert('Unable to find brief data. Please ensure you have created a brief in the Brief tab.');
        this.isCreatingJob = false;
        return;
      }

      // Check if we have minimum required data
      if (!briefData.subjectLine && !briefData.previewText && !briefData.briefText) {
        console.error('Brief data is empty:', briefData);
        alert('Please create a brief with subject line, preview text, and content before generating email variations.');
        this.isCreatingJob = false;
        return;
      }

      try {
        const requestData = {
          taskId: this.task.id,
          briefData: {
            subjectLine: briefData.subjectLine || 'Subject Line',
            previewText: briefData.previewText || 'Preview Text',
            briefText: briefData.briefText || 'Email content'
          },
          options: {
            iterationCount: parseInt(this.newJob.iterationCount),
            tasteProfiles: this.newJob.tasteProfiles.slice(0, parseInt(this.newJob.iterationCount))
          }
        };

        console.log('Creating email workflow with data:', requestData);

        const response = await fetch(`${URL_DOMAIN}/workflows/email`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(requestData)
        });

        if (response.ok) {
          this.showCreateJobModal = false;
          this.resetNewJob();
          await this.loadJobs();
        } else {
          const errorText = await response.text();
          console.error('Failed to create job:', response.statusText, errorText);
          alert(`Failed to create email generation job: ${response.statusText}`);
        }
      } catch (error) {
        console.error('Error creating job:', error);
        alert('An error occurred while creating the email generation job. Please try again.');
      } finally {
        this.isCreatingJob = false;
      }
    },

    async viewLogs(workflow) {
      this.selectedWorkflow = workflow;
      try {
        const response = await fetch(`${URL_DOMAIN}/workflows/${workflow.id}/logs`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          this.selectedLogs = await response.json();
          this.showLogsModal = true;
        } else {
          console.error('Failed to load logs:', response.statusText);
        }
      } catch (error) {
        console.error('Error loading logs:', error);
      }
    },

    async viewVariations(workflow) {
      this.selectedWorkflow = workflow;
      try {
        const response = await fetch(`${URL_DOMAIN}/workflows/${workflow.id}/variations`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          this.selectedVariations = await response.json();
          this.currentVariationIndex = 0;
          this.emailPreviewHtml = '';
          this.showVariationsModal = true;

          // Initialize Unlayer editor for preview processing
          this.initializeUnlayerEditor();

          // Process the first variation
          if (this.selectedVariations.length > 0) {
            await this.processCurrentVariation();
          }
        } else {
          console.error('Failed to load variations:', response.statusText);
        }
      } catch (error) {
        console.error('Error loading variations:', error);
      }
    },

    // New variation preview methods
    initializeUnlayerEditor() {
      this.showUnlayerEditor = true;
      this.$nextTick(() => {
        // Editor will load and call onUnlayerEditorLoaded
      });
    },

    onUnlayerEditorLoaded() {
      console.log('Unlayer editor loaded for variation preview');
      this.unlayerEditorReady = true;
    },

    async processCurrentVariation() {
      if (!this.currentVariation || !this.currentVariation.emailDesignJson) {
        this.emailPreviewHtml = '';
        return;
      }

      this.isProcessingVariation = true;

      try {
        const emailDesignJson = this.currentVariation.emailDesignJson;

        // Detect format: check if it's raw Unlayer format (has counters + body) or component-based format
        const isUnlayerFormat = emailDesignJson.counters && emailDesignJson.body &&
                               typeof emailDesignJson.counters === 'object' &&
                               typeof emailDesignJson.body === 'object';

        console.log('Email design format detected:', isUnlayerFormat ? 'unlayer' : 'components');

        if (isUnlayerFormat) {
          // Raw Unlayer format - use directly without API call
          console.log('Using raw Unlayer format directly');
          await this.generatePreviewHtml(emailDesignJson);
        } else {
          // Component-based format - needs to be processed via API
          console.log('Processing component-based format via API');
          const response = await fetch(`${URL_DOMAIN}/planner/generate-email-from-component-list`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(emailDesignJson)
          });

          if (response.ok) {
            const unlayerDesign = await response.json();
            await this.generatePreviewHtml(unlayerDesign);
          } else {
            console.error('Failed to process email design:', response.statusText);
            this.emailPreviewHtml = '<p>Failed to process email design</p>';
          }
        }
      } catch (error) {
        console.error('Error processing variation:', error);
        this.emailPreviewHtml = '<p>Error processing email design</p>';
      } finally {
        this.isProcessingVariation = false;
      }
    },


    async generatePreviewHtml(emailDesign) {
      if (!this.unlayerEditorReady || !this.$refs.unlayerEditor?.editor) {
        // Fallback: use the design JSON directly
        this.emailPreviewHtml = `<pre>${JSON.stringify(emailDesign, null, 2)}</pre>`;
        return;
      }

      try {
        // Load design into Unlayer editor
        this.$refs.unlayerEditor.editor.loadDesign(emailDesign);

        // Export HTML
        this.$refs.unlayerEditor.editor.exportHtml((data) => {
          this.emailPreviewHtml = data.html;
        });
      } catch (error) {
        console.error('Error generating preview HTML:', error);
        this.emailPreviewHtml = `<pre>${JSON.stringify(emailDesign, null, 2)}</pre>`;
      }
    },

    previousVariation() {
      if (this.currentVariationIndex > 0) {
        this.currentVariationIndex--;
        this.processCurrentVariation();
      }
    },

    nextVariation() {
      if (this.currentVariationIndex < this.selectedVariations.length - 1) {
        this.currentVariationIndex++;
        this.processCurrentVariation();
      }
    },

    async selectVariation(variation) {
      this.isSelectingVariation = true;

      try {
        const emailDesignJson = variation.emailDesignJson;

        // Send chat message with unhydrated JSON wrapped in <email></email> tags
        try {
          // Create the chat message with the unhydrated JSON
          const chatMessage = `Do not reply at all, this is just to inform you of an update. This is the current email design: <email>${JSON.stringify(emailDesignJson, null, 2)}</email>`;

          // Send the message to the chat via API
          await fetch(`${URL_DOMAIN}/chat/conversations/${this.task.conversationId}/message`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
              message: chatMessage,
              stream: false
            })
          });

          console.log('Chat message sent with email design JSON');
        } catch (chatError) {
          console.error('Failed to send chat message:', chatError);
          // Continue with the selection process even if chat message fails
        }

        // Detect format and get the hydrated Unlayer JSON
        const isUnlayerFormat = emailDesignJson.counters && emailDesignJson.body &&
                               typeof emailDesignJson.counters === 'object' &&
                               typeof emailDesignJson.body === 'object';

        let hydratedUnlayerJson;

        if (isUnlayerFormat) {
          // Already in Unlayer format - use directly
          console.log('Using raw Unlayer format for task update');
          hydratedUnlayerJson = emailDesignJson;
        } else {
          // Component-based format - need to convert to Unlayer format via API
          console.log('Converting component-based format to Unlayer format for task update');
          const response = await fetch(`${URL_DOMAIN}/planner/generate-email-from-component-list`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(emailDesignJson)
          });

          if (response.ok) {
            hydratedUnlayerJson = await response.json();
          } else {
            console.error('Failed to convert email design to Unlayer format:', response.statusText);
            alert('Failed to process email design. Please try again.');
            return;
          }
        }

        // Update the task with the hydrated Unlayer JSON
        const taskUpdateData = {
          emailDesign: hydratedUnlayerJson,
          emailHtml: this.emailPreviewHtml
        };

        const updateResponse = await fetch(`${URL_DOMAIN}/planner/task/${this.task.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(taskUpdateData)
        });

        if (updateResponse.ok) {
          // Close the modal and refresh the task
          this.showVariationsModal = false;

          // Emit event to parent to refresh task data
          this.$emit('task-updated');

          // Show success message
          console.log('Variation selected and task updated successfully with hydrated Unlayer JSON');
        } else {
          console.error('Failed to update task:', updateResponse.statusText);
          alert('Failed to select variation. Please try again.');
        }
      } catch (error) {
        console.error('Error selecting variation:', error);
        alert('An error occurred while selecting the variation. Please try again.');
      } finally {
        this.isSelectingVariation = false;
      }
    },

    async retryJob(workflow) {
      try {
        const response = await fetch(`${URL_DOMAIN}/workflows/${workflow.id}/retry`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          await this.loadJobs();
        } else {
          console.error('Failed to retry job:', response.statusText);
        }
      } catch (error) {
        console.error('Error retrying job:', error);
      }
    },

    async cancelJob(workflow) {
      try {
        const response = await fetch(`${URL_DOMAIN}/workflows/${workflow.id}/cancel`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          await this.loadJobs();
        } else {
          console.error('Failed to cancel job:', response.statusText);
        }
      } catch (error) {
        console.error('Error cancelling job:', error);
      }
    },

    previewVariation(variation) {
      if (variation.finalHtml) {
        // Open HTML in new window
        const newWindow = window.open('', '_blank');
        newWindow.document.write(variation.finalHtml);
        newWindow.document.close();
      } else if (variation.emailDesignJson) {
        // Show JSON in a modal or new tab
        const jsonStr = JSON.stringify(variation.emailDesignJson, null, 2);
        const newWindow = window.open('', '_blank');
        newWindow.document.write(`<pre>${jsonStr}</pre>`);
        newWindow.document.close();
      }
    },

    hasWorkflowDataChanged(newWorkflows) {
      if (this.workflows.length !== newWorkflows.length) {
        return true;
      }

      // Check if any workflow has changed status, progress, or error
      // BUT always update if there are processing workflows (for timer countdown)
      const hasProcessingWorkflows = newWorkflows.some(w => w.status === 'processing');

      for (let i = 0; i < this.workflows.length; i++) {
        const oldWorkflow = this.workflows[i];
        const newWorkflow = newWorkflows[i];

        if (oldWorkflow.id !== newWorkflow.id ||
            oldWorkflow.status !== newWorkflow.status ||
            oldWorkflow.completedIterations !== newWorkflow.completedIterations ||
            oldWorkflow.error !== newWorkflow.error ||
            oldWorkflow.completedAt !== newWorkflow.completedAt) {
          return true;
        }
      }

      // Always update if there are processing workflows to keep timers ticking
      return hasProcessingWorkflows;
    },

    startAutoRefresh() {
      this.autoRefreshInterval = setInterval(() => {
        // Only refresh if there are active jobs and not showing modals
        const hasActiveJobs = this.workflows.some(w =>
          w.status === 'processing' || w.status === 'pending'
        );
        const hasOpenModals = this.showCreateJobModal || this.showLogsModal || this.showVariationsModal;

        if (hasActiveJobs && !hasOpenModals) {
          this.loadJobs(true); // Silent refresh to avoid flickering
        }
      }, 3000); // Refresh every 3 seconds
    },

    stopAutoRefresh() {
      if (this.autoRefreshInterval) {
        clearInterval(this.autoRefreshInterval);
        this.autoRefreshInterval = null;
      }
    },

    resetNewJob() {
      this.newJob = {
        subjectLine: '',
        previewText: '',
        briefText: '',
        iterationCount: 3,
        tasteProfiles: [
          { name: 'Safe' },
          { name: 'Variety' },
          { name: 'Unique' }
        ]
      };
    },

    updateTasteProfiles() {
      const count = parseInt(this.newJob.iterationCount);
      const defaultProfiles = ['Safe', 'Variety', 'Unique', 'Wild'];

      // Resize the taste profiles array to match the iteration count
      this.newJob.tasteProfiles = [];
      for (let i = 0; i < count; i++) {
        this.newJob.tasteProfiles.push({
          name: defaultProfiles[i] || 'Safe'
        });
      }
    },

    getTasteProfileDescription(profileName) {
      const descriptions = {
        'Safe': 'Conservative, proven approaches',
        'Variety': 'Balanced mix of styles',
        'Unique': 'Creative, distinctive content',
        'Wild': 'Bold, experimental approaches'
      };
      return descriptions[profileName] || '';
    },

    getStatusColor(status) {
      switch (status) {
        case 'pending': return 'bg-yellow-400';
        case 'processing': return 'bg-blue-400';
        case 'completed': return 'bg-green-400';
        case 'failed': return 'bg-red-400';
        case 'timeout': return 'bg-orange-400';
        default: return 'bg-gray-400';
      }
    },

    getStatusBadgeClass(status) {
      switch (status) {
        case 'pending': return 'bg-yellow-100 text-yellow-800';
        case 'processing': return 'bg-blue-100 text-blue-800';
        case 'completed': return 'bg-green-100 text-green-800';
        case 'failed': return 'bg-red-100 text-red-800';
        case 'timeout': return 'bg-orange-100 text-orange-800';
        default: return 'bg-gray-100 text-gray-800';
      }
    },

    getStatusText(status) {
      switch (status) {
        case 'pending': return 'Pending';
        case 'processing': return 'Processing';
        case 'completed': return 'Completed';
        case 'failed': return 'Failed';
        case 'timeout': return 'Timed Out';
        default: return 'Unknown';
      }
    },

    getLogLevelColor(level) {
      switch (level) {
        case 'info': return 'text-blue-400';
        case 'warn': return 'text-yellow-400';
        case 'error': return 'text-red-400';
        case 'debug': return 'text-gray-400';
        default: return 'text-gray-400';
      }
    },

    getProgressPercentage(workflow) {
      if (!workflow.iterationCount) return 0;
      return Math.round((workflow.completedIterations || 0) / workflow.iterationCount * 100);
    },

    getTimeElapsed(workflow) {
      if (!workflow.startedAt) return 'Starting...';

      const elapsed = new Date() - new Date(workflow.startedAt);
      const minutes = Math.floor(elapsed / 60000);
      const seconds = Math.floor((elapsed % 60000) / 1000);

      if (minutes > 0) {
        return `${minutes}m ${seconds}s elapsed`;
      } else {
        return `${seconds}s elapsed`;
      }
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleString();
    },

    formatLogTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString();
    }
  }
};
</script>

<style scoped>
.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-10px);
  }
  70% {
    transform: translateY(-5px);
  }
  90% {
    transform: translateY(-2px);
  }
}
</style>
