<template>
  <div class="flex flex-col w-full max-w-[80%] p-4 rounded-lg bg-white border-2 border-red-300 shadow-md">
    <div class="flex items-center justify-between mb-3">
      <span class="font-semibold text-base text-[#202020]">{{ errorType === 'brief' ? 'Brief Generation Error' : 'Email Generation Error' }}</span>
      <div class="flex-shrink-0">
        <div class="flex items-center justify-center p-2 rounded-md border border-red-200 bg-red-50 h-8 w-8">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        </div>
      </div>
    </div>
    <div class="text-sm text-gray-700 mb-3">
      <p class="font-medium text-red-600">{{ errorMessage }}</p>
      <p class="text-xs text-gray-600 mt-1">{{ errorDetails }}</p>
    </div>
    <button
      @click="handleRetry"
      class="mt-2 px-4 py-2 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 transition-colors duration-200 flex items-center justify-center font-medium border border-purple-200"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
        <path stroke-linecap="round" stroke-linejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
      </svg>
      Retry with Improved Format
    </button>
  </div>
</template>

<script lang="ts">
import { defineComponent } from '@vue/runtime-core';

export default defineComponent({
  name: 'BriefParseError',
  props: {
    errorType: {
      type: String,
      required: true,
      validator: (value: string) => ['brief', 'email'].includes(value)
    },
    errorMessage: {
      type: String,
      default: 'The AI generated content in an invalid format'
    },
    errorDetails: {
      type: String,
      default: 'This occasionally happens with AI-generated content. Please try again by clicking the button below.'
    }
  },
  emits: ['retry'],
  setup(props, { emit }) {
    const handleRetry = () => {
      const message = props.errorType === 'brief'
        ? "The brief you generated failed to parse as valid JSON. Please generate a new brief with a properly formatted JSON object containing 'subjectLine', 'previewText', and 'briefText' fields. Make sure all property names have quotes and all string values are properly escaped."
        : "The email design you generated failed to parse as valid JSON. Please generate a new email design with a properly formatted JSON object. Make sure all property names have quotes and all string values are properly escaped.";

      emit('retry', message);
    };

    return {
      handleRetry
    };
  }
});
</script>
