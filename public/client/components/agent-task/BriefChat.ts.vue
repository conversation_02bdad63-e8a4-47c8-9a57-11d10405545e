<template>
  <div class="relative flex flex-col h-full bg-[#F5F5F5]">
    <!-- Confirm Modal -->
    <ConfirmModal
      v-model="showConfirmModal"
      title="Switching Briefs"
      message="Loading this brief will overwrite your current brief. Are you sure you want to continue?"
      confirm-text="Load Brief"
      cancel-text="Cancel"
      @confirm="confirmBriefLoad"
    />
    <div class="flex flex-1 overflow-hidden relative">
      <div class="flex flex-col flex-1 w-full h-full">
        <div
          ref="chatHistoryContainer"
          class="p-4 space-y-4 overflow-y-auto flex-1 w-full max-w-full chat-scrollbar"
          @scroll="handleScroll"
        >
          <div
            v-if="chatSegments.length === 0"
            class="flex flex-col items-center justify-center h-full pt-10"
          >
            <div class="flex flex-col space-y-4 w-full max-w-xl mx-auto py-6">
              <div
                class="bg-white rounded-xl border border-gray-200 shadow-md overflow-hidden p-6"
              >
                <div class="divide-y divide-gray-200">
                  <div
                    v-for="(prompt, index) in briefPrompts"
                    :key="index"
                    @click="handlePromptSelected(prompt.promptText)"
                    class="p-4 hover:bg-gray-100 transition-colors duration-150 ease-in-out cursor-pointer group"
                  >
                    <div class="flex items-start space-x-4">
                      <div class="flex-shrink-0 p-2 bg-purple-100 rounded-lg">
                        <component
                          :is="prompt.iconComponent"
                          class="h-6 w-6 text-purple-700"
                          aria-hidden="true"
                        />
                      </div>
                      <div class="flex-1 min-w-0">
                        <h3 class="font-medium text-[#202020] mb-1 truncate">
                          {{ prompt.title }}
                        </h3>
                        <p class="text-sm text-[#202020] line-clamp-2">
                          {{ prompt.description }}
                        </p>
                      </div>
                      <div
                        class="flex-shrink-0 text-purple-500 group-hover:translate-x-1 transition-transform duration-200 ease-in-out self-center"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            v-for="segment in chatSegments"
            :key="segment.id"
            :class="[
              'flex w-full',
              (segment.type === 'user' || segment.sender === 'user') ? 'justify-end' : 'justify-start',
            ]"
          >
            <div
              v-if="segment.type === 'user' || (segment.type === 'text' && segment.sender === 'user')"
              class="bg-purple-50 border border-purple-100 rounded-2xl p-4 text-gray-600 shadow-sm max-w-[80%]"
            >
              <div
                v-if="segment.content && containsFormatting(segment.content)"
                v-html="formatText(segment.content)"
              ></div>
              <template v-else>{{ segment.content }}</template>
            </div>

            <div
              v-if="
                segment.type === 'text' &&
                segment.sender === 'ai' &&
                segment.content &&
                segment.content.trim() !== ''
              "
              class="rounded-lg px-4 py-2 whitespace-pre-wrap bg-[#F5F5F5] text-[#202020] max-w-[80%]"
            >
              <div v-if="segment.isHtml" v-html="segment.content.trim()"></div>
              <div
                v-else-if="
                  segment.content && containsFormatting(segment.content.trim())
                "
                v-html="formatText(segment.content.trim())"
              ></div>
              <template v-else>{{ segment.content.trim() }}</template>
            </div>

            <!-- Tool Message Display -->
            <ToolMessage
              v-if="segment.type === 'tool_message'"
              :message="segment.content || ''"
              :is-active="segment.isGenerating || false"
            />

            <div
              v-if="segment.type === 'brief_content'"
              class="flex flex-col w-full max-w-[80%] p-4 rounded-lg bg-white border border-purple-200 shadow-sm"
            >
              <div class="flex items-center justify-between mb-3">
                <span class="font-semibold text-base text-[#202020]"
                  >Campaign Brief</span
                >
                <div class="flex-shrink-0">
                  <div
                    class="flex items-center justify-center p-2 rounded-md border border-gray-200 bg-gray-50 h-8 w-8"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-[#5A16C9]"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M9.75 17.25v-.001M9.75 14.25v-.001M9.75 11.25v-.001M12 17.25v-.001M12 14.25v-.001M12 11.25v-.001M14.25 17.25v-.001M14.25 14.25v-.001M14.25 11.25v-.001M4.5 20.25h15A2.25 2.25 0 0021.75 18V6A2.25 2.25 0 0019.5 3.75H4.5A2.25 2.25 0 002.25 6v12A2.25 2.25 0 004.5 20.25z"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <div class="text-sm text-gray-700 whitespace-pre-wrap">
                {{ decodeBriefText(segment.content || '') }}
              </div>
            </div>

            <div
              v-if="segment.type === 'brief_placeholder'"
              class="rounded-lg px-4 py-2 bg-purple-100 text-purple-700 shadow-sm max-w-[80%] italic"
            >
              Generating brief...
            </div>

            <div
              v-if="segment.type === 'brief_error'"
              class="w-full max-w-[80%]"
            >
              <BriefParseError
                errorType="brief"
                :errorMessage="
                  segment.errorMessage || 'Failed to parse brief data'
                "
                :errorDetails="segment.errorDetails || ''"
                @retry="handleErrorRetry(segment, $event)"
              />
            </div>

            <div
              v-if="segment.type === 'email_error'"
              class="w-full max-w-[80%]"
            >
              <BriefParseError
                errorType="email"
                :errorMessage="
                  segment.errorMessage || 'Failed to parse email data'
                "
                :errorDetails="segment.errorDetails || ''"
                @retry="handleErrorRetry(segment, $event)"
              />
            </div>

            <div
              v-if="segment.type === 'historic_brief'"
              class="flex items-center justify-between w-full max-w-[80%] p-4 rounded-lg bg-white border border-purple-200 shadow-sm cursor-pointer transition-colors duration-200 ease-in-out hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-[#9254F7] focus:ring-opacity-75"
              @click="loadHistoricBrief(segment)"
              :data-segment-id="segment.id"
            >
              <div class="flex flex-col items-start mr-4">
                <span class="font-semibold text-base mb-1 text-[#202020]"
                  >Campaign Brief</span
                >
                <span class="text-sm text-gray-400"
                  >Click to load into editor</span
                >
                <span class="text-xs text-purple-600 mt-1 brief-status">
                  {{
                    segment.timestamp &&
                    new Date().getTime() -
                      new Date(segment.timestamp).getTime() <
                      60000
                      ? 'Generated just now'
                      : 'From historic chat'
                  }}
                </span>
              </div>

              <div class="flex-shrink-0">
                <div
                  class="flex items-center justify-center p-2 rounded-md border border-gray-200 bg-gray-50 h-10 w-10"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-[#5A16C9]"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M9.75 17.25v-.001M9.75 14.25v-.001M9.75 11.25v-.001M12 17.25v-.001M12 14.25v-.001M12 11.25v-.001M14.25 17.25v-.001M14.25 14.25v-.001M14.25 11.25v-.001M4.5 20.25h15A2.25 2.25 0 0021.75 18V6A2.25 2.25 0 0019.5 3.75H4.5A2.25 2.25 0 002.25 6v12A2.25 2.25 0 004.5 20.25z"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <div
              v-if="segment.type === 'email_placeholder'"
              class="rounded-lg px-4 py-2 bg-purple-100 text-purple-700 shadow-sm max-w-[80%] italic"
            >
              Generating email design...
            </div>

            <div
              v-if="segment.type === 'historic_email'"
              class="flex items-center justify-between w-full max-w-[80%] p-4 rounded-lg bg-white border border-purple-200 shadow-sm cursor-pointer transition-colors duration-200 ease-in-out hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-[#9254F7] focus:ring-opacity-75"
              @click="loadHistoricEmail(segment)"
              :data-segment-id="segment.id"
            >
              <div class="flex flex-col items-start mr-4">
                <span class="font-semibold text-base mb-1 text-[#202020]"
                  >Email Design</span
                >
                <span class="text-sm text-gray-400"
                  >Click to load into editor</span
                >
                <span class="text-xs text-purple-600 mt-1 email-status">
                  {{
                    segment.timestamp &&
                    new Date().getTime() -
                      new Date(segment.timestamp).getTime() <
                      60000
                      ? 'Generated just now'
                      : 'From historic chat'
                  }}
                </span>
              </div>

              <div class="flex-shrink-0">
                <div
                  class="flex items-center justify-center p-2 rounded-md border border-gray-200 bg-gray-50 h-10 w-10"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-[#5A16C9]"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <!-- Image Display Component (Thumbnail Style) -->
            <!-- Single Image Renderer Component -->
            <SingleImageRenderer
              v-if="segment.type === 'image' && segment.imageUrl"
              :image-id="segment.id"
              :image-url="segment.imageUrl"
              :image-name="segment.content"
              :initial-load-state="segment.imageLoaded || false"
              @image-loaded="handleImageLoad"
              @image-error="handleImageError"
            />

            <!-- Multi-Image Display Component (Grid Style) -->
            <!-- Multi Image Renderer Component -->
            <MultiImageRenderer
              v-if="
                segment.type === 'multiimage' &&
                segment.images &&
                segment.images.length > 0
              "
              :image-id="segment.id"
              :images="segment.images"
              @multi-image-loaded="handleMultiImageLoad"
              @multi-image-error="handleMultiImageError"
            />

            <div
              v-if="segment.type === 'memory' && segment.memory"
              class="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-xl p-4 shadow-sm max-w-md"
            >
              <div class="flex items-center gap-2 mb-2">
                <svg
                  class="w-4 h-4 text-purple-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <div class="text-sm font-semibold text-purple-800">
                  Capturing brand memory
                </div>
              </div>
              <div class="text-xs text-purple-600 font-medium mb-1">
                {{ segment.memory.category }}
              </div>
              <div class="text-sm text-gray-700 whitespace-pre-wrap">
                {{ segment.memory.info }}
              </div>
            </div>

            <div
              v-if="segment.type === 'switch_mode' && segment.switchModeData"
              class="bg-gradient-to-r from-blue-50 to-cyan-50 border border-blue-200 rounded-xl p-4 shadow-sm max-w-md cursor-pointer hover:shadow-md transition-shadow"
              @click="handleSwitchModeClick(segment.switchModeData)"
            >
              <div class="flex items-center gap-2 mb-2">
                <svg
                  class="w-4 h-4 text-blue-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"
                  />
                </svg>
                <div class="text-sm font-semibold text-blue-800">
                  Switch Mode
                </div>
              </div>
              <div class="text-xs text-blue-600 font-medium mb-1 uppercase">
                {{ segment.switchModeData.mode }}
              </div>
              <div class="text-sm text-gray-700 whitespace-pre-wrap mb-2">
                {{ segment.switchModeData.message }}
              </div>
              <div class="text-xs text-blue-500 font-medium">
                Click to switch →
              </div>
            </div>
          </div>

          <div
            v-if="isWaitingForAI"
            class="flex justify-start items-center space-x-2"
          >
            <div
              class="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
              style="animation-delay: -0.3s"
            ></div>
            <div
              class="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
              style="animation-delay: -0.15s"
            ></div>
            <div
              class="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
            ></div>
            <span class="text-sm text-gray-500">Thinking...</span>
          </div>

          <div
            v-if="isCompressing"
            class="flex justify-start items-center space-x-2"
          >
            <div
              class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"
              style="animation-delay: -0.3s"
            ></div>
            <div
              class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"
              style="animation-delay: -0.15s"
            ></div>
            <div
              class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"
            ></div>
            <span class="text-sm text-blue-600 font-medium">Compressing conversation...</span>
          </div>
        </div>

        <div
          class="ml-4 mr-4 p-4 px-4 border-t border-gray-200 shadow-md bg-white max-w-full rounded-xl relative"
          :class="{
            'mb-auto rounded-xl': chatSegments.length === 0,
          }"
        >
          <!-- Command Palette -->
          <CommandPalette
            :show="showCommandPalette"
            :commands="commands"
            :search-text="newMessage"
            :conversation-id="conversationId"
            :has-messages="chatSegments.length > 0"
            @execute-command="executeCommand"
          />
          <!-- Down Arrow Button -->
          <button
            v-if="!isAtBottom && chatSegments.length > 0"
            @click="scrollToBottom"
            class="absolute left-1/2 transform -translate-x-1/2 -top-6 p-1.5 rounded-full bg-purple-100 hover:bg-purple-200 transition-colors duration-200 text-purple-700 focus:outline-none z-10 shadow-sm"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
          <!-- Quick Action Buttons -->
          <div class="flex flex-wrap items-center justify-start gap-2 mb-3">
            <div
              v-for="(action, index) in quickActions"
              :key="index"
              class="relative group"
            >
              <button
                @click="handleQuickAction(action.promptText)"
                class="flex items-center px-4 py-1.5 text-xs font-medium rounded-full bg-white border border-gray-200 hover:bg-gray-50 hover:border-purple-200 transition-colors duration-150 shadow-sm"
                :class="{'opacity-70': isWaitingForAI || isCompressing}"
              >
                <span class="text-[#5A16C9]">{{ action.title }}</span>
              </button>
              <!-- Hover tooltip -->
              <div
                class="absolute left-0 bottom-full mb-2 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10"
              >
                {{ action.description }}
              </div>
            </div>
          </div>

          <div
            class="flex items-center bg-white rounded-lg p-3 border border-gray-200 mb-3 w-full"
          >
            <textarea
              ref="messageTextarea"
              v-model="newMessage"
              :placeholder="isWaitingForAI ? 'Continue typing your next message while AI generates...' : isCompressing ? 'Compressing conversation, please wait...' : 'Message your AI campaign assistant...'"
              class="flex-1 bg-transparent border-none focus:ring-0 focus:outline-none px-1 text-gray-600 placeholder-gray-400 w-full resize-none overflow-hidden leading-normal"
              :class="{'opacity-70': isWaitingForAI || isCompressing}"
              rows="1"
              @input="handleCommandInput"
              @keydown.enter="handleEnterKey"
              style="min-height: 24px; max-height: 96px; line-height: 1.5"
              @click="(e: MouseEvent) => { const target = e.target as HTMLTextAreaElement; if (target) target.focus(); }"
            ></textarea>
            <button
              @click="() => sendMessage()"
              :disabled="!newMessage.trim() || isWaitingForAI || isCompressing || !canSendMessage"
              class="ml-3 p-1.5 rounded-md bg-[#E9D5FF] text-[#5A16C9] transition-colors duration-200 relative"
              :class="
                newMessage.trim() && !isWaitingForAI && !isCompressing && canSendMessage
                  ? 'hover:bg-[#D8B4FE]'
                  : 'bg-purple-100 text-purple-300 cursor-not-allowed'
              "
              :title="isWaitingForAI ? 'AI is generating content. You can keep typing but can\'t send until it completes.' : isCompressing ? 'Compressing conversation, please wait...' : ''"
            >
              <span v-if="isWaitingForAI" class="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-green-400 animate-pulse" title="AI is generating content. You can still type but cannot send yet."></span>
              <span v-if="isCompressing" class="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-blue-400 animate-pulse" title="Compressing conversation, please wait..."></span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                height="20px"
                viewBox="0 -960 960 960"
                width="20px"
                fill="currentColor"
              >
                <path
                  d="M440-160v-487L216-423l-56-57 320-320 320 320-56 57-224-224v487h-80Z"
                />
              </svg>
            </button>
          </div>
        </div>
        <div
          class="text-center text-xs text-gray-500 py-1 w-full max-w-full mb-3"
        >
      Raleon AI can make mistakes, always double check before sending.
        </div>
      </div>
    </div>
    <MessageQuotaModal :show="showQuotaModal" @close="showQuotaModal = false" />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  nextTick,
  ref,
  onMounted,
  watch,
  computed,
  toRefs,
} from '@vue/runtime-core';
import {jsonrepair} from 'jsonrepair';
import { getConversation } from '../../services/chatService'; // Added getConversation import
import {customerIOTrackEvent} from '../../services/customerio.js';
import { useChatStreaming } from '../../composables/useChatStreaming';
import { chatCommandService, Command } from '../../services/chatCommandService';
import {
  ChatBubbleLeftRightIcon,
  LightBulbIcon,
  SparklesIcon,
  DocumentTextIcon,
} from '@heroicons/vue/24/outline';
import {messageArtifactService, ChatMessageSegment} from '../../services/messageArtifactService';
import {TextFormattingService} from '../../services/textFormattingService';
import BriefParseError from './BriefParseError.vue';
import SingleImageRenderer from './SingleImageRenderer.vue';
import MultiImageRenderer from './MultiImageRenderer.vue';
import ConfirmModal from '../common/ConfirmModal.vue';
import MessageQuotaModal from '../MessageQuotaModal.ts.vue';
import ToolMessage from '../ToolMessage.vue';
import CommandPalette from '../CommandPalette.vue';
import {fetchMessageQuota, useQuotaService} from '../../services/messageQuotaService';
import {ToolMessageService} from '../../services/toolMessageService';
import * as Utils from '../../../client-old/utils/Utils';

export default defineComponent({
  name: 'BriefChat',
  components: {
    BriefParseError,
    SingleImageRenderer,
    MultiImageRenderer,
    ConfirmModal,
    MessageQuotaModal,
    ToolMessage,
    CommandPalette,
  },
  props: {
    currentBriefText: {
      type: String,
      required: true,
    },
    campaignId: {
      type: String,
      required: true,
    },
    conversationId: {
      type: [String, Number],
      required: false,
      default: null,
    },
    forceReload: {
      type: Boolean,
      default: false,
    },
  },
  emits: [
    'update:brief',
    'update:raw-brief-stream',
    'brief-stream-complete',
    'auto-save-brief',
    'update:email',
    'update:raw-email-stream',
    'email-stream-complete',
    'auto-save-email',
    'generate-email-from-components',
    'conversation-cleared',
  ],
  setup(props, {emit}) {
    const chatHistoryContainer = ref<HTMLElement | null>(null);
    const messageTextarea = ref<HTMLTextAreaElement | null>(null);
    const newMessage = ref('');
    const chatSegments = ref<ChatMessageSegment[]>([]);
    const segmentIdCounter = ref(0);
    const conversationId = ref<string | number | null>(null);
    const pendingConversationMetadata = ref<any>(null);

    // Track if the chat is scrolled to the bottom
    const isAtBottom = ref(true);

    // Confirm modal state
    const showConfirmModal = ref(false);
    const pendingBriefSegment = ref<ChatMessageSegment | null>(null);

    const showQuotaModal = ref(false);
    const isCompressing = ref(false);

    // Use the reactive quota service
    const quotaService = useQuotaService();

    // Make TextFormattingService available in the template
    const formatText = TextFormattingService.formatText;
    const containsFormatting = TextFormattingService.containsFormatting;

    // Quick action buttons with image icons
    const quickActions = [
      {
        title: 'Generate email',
        description: 'Generate an email based on my prompt.',
        promptText: 'Generate an email based on my brief do not edit any images during the first generation, we may edit them later.',
        imageUrl: null,
      },
	  {
        title: 'Generate email + image edits',
        description: 'Generate an email based on my prompt.',
        promptText: 'Generate an email based on my brief, you can edit images with variations and edit text all using image_edit tool.',
        imageUrl: null,
      },
      {
        title: 'Generate Brief',
        description: 'Generate a new brief for me.',
        promptText: 'Generate a new brief for me.',
        imageUrl: null,
      },
      {
        title: 'Explain the Brief',
        description:
          'Tell me what data you used and why this brief is good, and what improvements we should consider.',
        promptText:
          'Tell me what data you used and why this brief is good, and what improvements we should consider.',
        imageUrl: null,
      },
      {
        title: 'Show my Images',
        description: 'Show me the images I am currently using in my email.',
        promptText: 'Show me the images I am currently using in my email.',
        imageUrl: null,
      },
    ];

    // Brief prompts for quick actions
    const briefPrompts = [
      {
        title: 'Tell me how you came up with this brief',
        description:
          'Understand the reasoning and thought process behind this brief.',
        promptText:
          'Tell me how you came up with this brief and what your thought process was.',
        iconComponent: ChatBubbleLeftRightIcon,
      },
      {
        title: 'Why do you think this brief will work',
        description:
          'Learn why this brief is effective for your target audience.',
        promptText: 'Why do you think this brief will work for my audience?',
        iconComponent: LightBulbIcon,
      },
      {
        title: 'Generate an Email from this brief',
        description:
          'Generate an email based on this brief utilizing my email components.',
        promptText: 'Generate an email from this brief.',
        iconComponent: SparklesIcon,
      },
      {
        title: 'Give me an entirely new brief',
        description:
          'Generate a completely different approach for this campaign.',
        promptText: 'Give me an entirely new brief with a different approach.',
        iconComponent: DocumentTextIcon,
      },
    ];

    // --- Slash Commands ---
    const showCommandPalette = ref(false);
    const commands = ref<Command[]>(chatCommandService.getCommands());

    const filteredCommands = computed(() => {
      if (!newMessage.value.startsWith('/')) {
        return [];
      }
      const search = newMessage.value.toLowerCase();
      return commands.value.filter(command => command.name.toLowerCase().includes(search));
    });

    const executeCommand = async (command: Command) => {
      showCommandPalette.value = false;
      const commandName = command.name;
      try {
        const args: { [key: string]: any } = {
          campaignId: props.campaignId,
        };

        if (commandName === '/compress') {
          if (!conversationId.value) {
            chatSegments.value.push({
              id: generateUniqueId(),
              type: 'text',
              sender: 'ai',
              content: 'Error: No active conversation to summarize.',
              timestamp: new Date(),
            });
            newMessage.value = '';
            scrollToBottom();
            return;
          }
          args.conversationId = conversationId.value;

          // Set compressing state and show loading message
          isCompressing.value = true;
          chatSegments.value.push({
            id: generateUniqueId(),
            type: 'text',
            sender: 'ai',
            content: 'Compressing conversation... This may take a moment while I generate a summary.',
            timestamp: new Date(),
          });
          scrollToBottom();
        }

        if (commandName === '/clear') {
          // Pass current conversation details to carry forward
          args.conversationId = conversationId.value;
          // We need to get the current conversation details to carry forward
          // This will be handled by the backend when we update it
        }

        const result = await chatCommandService.executeCommand(commandName, args);

        if (commandName === '/clear') {
          // Store the conversation metadata for later use
          pendingConversationMetadata.value = result.conversationMetadata;
          // Clear the conversation ID - new conversation will be created on first message
          conversationId.value = null;
          emit('conversation-cleared', null);
          chatSegments.value = [];
          newMessage.value = '';
          chatSegments.value.push({
            id: generateUniqueId(),
            type: 'text',
            sender: 'ai',
            content: 'Chat cleared. A new conversation will be started with your next message.',
            timestamp: new Date(),
          });
        } else if (commandName === '/compress') {
          // Handle compress command by switching to new conversation
          isCompressing.value = false; // Reset loading state
          if (result.newConversationId) {
            conversationId.value = result.newConversationId;
            emit('conversation-cleared', result.newConversationId);
            chatSegments.value = [];
            newMessage.value = '';
            // Add the summary as the first message in the new conversation
            chatSegments.value.push({
              id: generateUniqueId(),
              type: 'text',
              sender: 'user',
              content: result.summary,
              timestamp: new Date(),
            });
            // Add a confirmation message
            chatSegments.value.push({
              id: generateUniqueId(),
              type: 'text',
              sender: 'ai',
              content: 'Conversation compressed successfully! The summary above contains the key points from our previous discussion. You can now continue the conversation from here.',
              timestamp: new Date(),
            });
            // Load the new conversation to show the summary
            loadConversation(result.newConversationId);
          } else {
            // Fallback to old behavior if something went wrong
            chatSegments.value.push({
              id: generateUniqueId(),
              type: 'text',
              sender: 'ai',
              content: result.summary || result.message,
              timestamp: new Date(),
            });
          }
        } else {
          chatSegments.value.push({
            id: generateUniqueId(),
            type: 'text',
            sender: 'ai',
            content: result.message,
            timestamp: new Date(),
          });
        }
      } catch (error) {
        isCompressing.value = false; // Reset loading state on error
        chatSegments.value.push({
          id: generateUniqueId(),
          type: 'text',
          sender: 'ai',
          content: `Error: ${(error as Error).message}`,
          timestamp: new Date(),
        });
      }
      newMessage.value = '';
      scrollToBottom();
    };

    const handleCommandInput = () => {
      if (newMessage.value.startsWith('/')) {
        showCommandPalette.value = true;
      } else {
        showCommandPalette.value = false;
      }
      resizeMessageTextarea();
    };

    // --- Methods ---

    const generateUniqueId = (): string => {
      segmentIdCounter.value++;
      return `briefchat-seg-${Date.now()}-${segmentIdCounter.value}`;
    };

    const scrollToBottom = () => {
      nextTick(() => {
        if (chatHistoryContainer.value) {
          chatHistoryContainer.value.scrollTop =
            chatHistoryContainer.value.scrollHeight;
          isAtBottom.value = true;
        }
      });
    };

    // Check if the chat is scrolled to the bottom
    const handleScroll = () => {
      if (chatHistoryContainer.value) {
        const {scrollTop, scrollHeight, clientHeight} =
          chatHistoryContainer.value;
        // Consider "at bottom" if within 20px of the bottom
        const wasAtBottom = isAtBottom.value;
        isAtBottom.value = scrollHeight - scrollTop - clientHeight < 20;

        // If we just detected that we're no longer at the bottom during streaming,
        // force a re-render to show the down arrow
        if (wasAtBottom && !isAtBottom.value) {
          console.log('Content went off screen, showing down arrow');
          // Force Vue to update the DOM
          nextTick();
        }
      }
    };

    const { campaignId, currentBriefText } = toRefs(props);
    // Track if this is the first message after loading the conversation
    const isFirstMessageAfterLoad = ref(true);

    const {
      isWaitingForAI,
      sendMessageAndProcessStream
    } = useChatStreaming({
      conversationId,
      chatSegments,
      campaignId,
      currentBriefText,
      isFirstMessageAfterLoad,
      pendingConversationMetadata,
      onScrollToBottom: scrollToBottom,
      onBriefStreamComplete: () => emit('brief-stream-complete'),
      onEmailStreamComplete: () => emit('email-stream-complete'),
      onUpdateBrief: (briefData) => emit('update:brief', briefData),
      onUpdateEmail: (emailData) => emit('update:email', emailData),
      onAutoSaveBrief: (briefData) => emit('auto-save-brief', briefData),
      onAutoSaveEmail: (emailData) => emit('auto-save-email', emailData),
      onGenerateEmailFromComponents: (json) => emit('generate-email-from-components', json),
      onUpdateRawBriefStream: (rawContent) => emit('update:raw-brief-stream', rawContent),
      onUpdateRawEmailStream: (rawContent) => emit('update:raw-email-stream', rawContent),
    });





    // Modified to accept an external message parameter
    const sendMessage = (externalMessage?: string) => {
      if (!quotaService.canSendMessage.value) {
        showQuotaModal.value = true;
        return;
      }
      // Use external message if provided, otherwise use the input field
      const text = externalMessage
        ? externalMessage.trim()
        : newMessage.value.trim();
      // Don't send empty messages or if already waiting for AI response or compressing
      if (!text || isWaitingForAI.value || isCompressing.value) {
        // Re-focus the chat input if it exists
        if (messageTextarea.value) {
          nextTick(() => {
            if (messageTextarea.value) messageTextarea.value.focus();
          });
        }
        return;
      }

      // Handle slash commands first
      if (text.startsWith('/')) {
        const command = commands.value.find(cmd => cmd.name === text);
        if (command) {
          executeCommand(command);
        }
        return;
      }

      customerIOTrackEvent('Sent Campaign Chat Message');
       // Add user message visually
      chatSegments.value.push({
        id: generateUniqueId(),
        type: 'user',
        content: text,
        timestamp: new Date(),
      });
      scrollToBottom();

      // Only clear the input field if we're using it (not an external message)
      if (!externalMessage) {
        newMessage.value = ''; // Clear input immediately

        // Reset textarea height after clearing
        if (messageTextarea.value) {
          messageTextarea.value.style.height = 'auto';
        }
      }

      // Update quota immediately when sending message
      quotaService.addMessage(1);

      sendMessageAndProcessStream(text).then(() => {
        quotaService.refreshQuota();
      });

    };

    // Method to resize the textarea as user types
    const resizeMessageTextarea = () => {
      if (messageTextarea.value) {
        // Reset height to auto to properly calculate the new height
        messageTextarea.value.style.height = 'auto';

        // Set the height based on scrollHeight (content height)
        // with a maximum of 4 lines (approximately 96px)
        const newHeight = Math.min(messageTextarea.value.scrollHeight, 96);
        messageTextarea.value.style.height = `${newHeight}px`;
      }
    };

    // Initialize textarea when component is mounted
    onMounted(async () => {
      // Set initial height for textarea
      if (messageTextarea.value) {
        messageTextarea.value.style.height = 'auto';
      }
      await quotaService.initializeQuota();
    });

    // Load conversation if conversationId is provided

    const loadConversation = async (convId: string | number) => {
      console.log('BriefChat: loadConversation called with ID:', convId);
      isWaitingForAI.value = true;
      try {
        const conversation = await getConversation(convId.toString());
        if (!conversation) throw new Error('Failed to load conversation');
        conversationId.value = conversation.id.toString();
        isFirstMessageAfterLoad.value = true;
        chatSegments.value = [];
        if (conversation.messages && conversation.messages.length > 0) {
          const sorted = [...conversation.messages].sort((a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
          );
          for (const msg of sorted) {
            if (msg.role === 'user') {
              if (typeof msg.content === 'string') {
                try {
                  const parsedContent = JSON.parse(msg.content);
                  if (Array.isArray(parsedContent)) {
                    for (const item of parsedContent) {
                      if (item.type === 'text' && item.text) {
                        chatSegments.value.push({
                          id: generateUniqueId(),
                          type: 'user',
                          content: item.text,
                          timestamp: new Date(msg.createdAt),
                        });
                      } else if (item.type === 'image_url' && item.image_url?.url) {
                        chatSegments.value.push({
                          id: generateUniqueId(),
                          type: 'image',
                          imageUrl: item.image_url.url,
                          imageLoaded: false,
                          timestamp: new Date(msg.createdAt),
                        });
                      }
                    }
                  } else {
                    chatSegments.value.push({
                      id: generateUniqueId(),
                      type: 'user',
                      content: msg.content,
                      timestamp: new Date(msg.createdAt),
                    });
                  }
                } catch (e) {
                  chatSegments.value.push({
                    id: generateUniqueId(),
                    type: 'user',
                    content: msg.content,
                    timestamp: new Date(msg.createdAt),
                  });
                }
              } else if (Array.isArray(msg.content)) {
                for (const item of msg.content) {
                  if (item.type === 'text' && item.text) {
                    chatSegments.value.push({
                      id: generateUniqueId(),
                      type: 'user',
                      content: item.text,
                      timestamp: new Date(msg.createdAt),
                    });
                  } else if (item.type === 'image_url' && item.image_url?.url) {
                    chatSegments.value.push({
                      id: generateUniqueId(),
                      type: 'image',
                      imageUrl: item.image_url.url,
                      imageLoaded: false,
                      timestamp: new Date(msg.createdAt),
                    });
                  }
                }
              }
            } else if (msg.role === 'assistant') {
              const {segments} = messageArtifactService.processAssistantMessage(
                msg,
                generateUniqueId,
              );
              for (const seg of segments) {
                if (seg.type === 'brief_artifact' && seg.rawContent) {
                  try {
                    const parsed = JSON.parse(jsonrepair(seg.rawContent));
                    const briefData = {
                      subjectLine: parsed.subjectLine || '',
                      previewText: parsed.previewText || '',
                      briefText: Utils.cleanMarkdown(parsed.briefText || ''),
                    };
                    chatSegments.value.push({
                      id: seg.id,
                      type: 'historic_brief',
                      sender: 'ai',
                      content: 'Campaign Brief',
                      timestamp: new Date(msg.createdAt),
                      briefData,
                      rawContent: seg.rawContent,
                      isGenerating: false,
                      artifactType: 'brief',
                    });
                  } catch (e) {
                    chatSegments.value.push({
                      id: seg.id,
                      type: 'brief_error',
                      sender: 'ai',
                      errorMessage: 'Failed to parse brief JSON',
                      errorDetails:
                        'This sometimes happens with AI, we are working to minimize this as much as possible. Please try again!',
                      timestamp: new Date(msg.createdAt),
                    });
                  }
                } else if (seg.type === 'email_artifact' && seg.rawContent) {
                  try {
                    const emailJson = JSON.parse(jsonrepair(seg.rawContent));
                    chatSegments.value.push({
                      id: seg.id,
                      type: 'historic_email',
                      sender: 'ai',
                      content: 'Email Design',
                      timestamp: new Date(msg.createdAt),
                      design: emailJson,
                      rawContent: seg.rawContent,
                      isGenerating: false,
                      artifactType: 'email',
                    });
                  } catch (err) {
                    chatSegments.value.push({
                      id: seg.id,
                      type: 'email_error',
                      sender: 'ai',
                      errorMessage: 'Failed to parse email JSON',
                      errorDetails: (err as Error).message || '',
                      timestamp: new Date(msg.createdAt),
                    });
                  }
                } else {
                  chatSegments.value.push(seg as any);
                }
              }
            } else if (msg.role === 'tool') {
              // Process tool message to show what function was called
              processToolMessage(msg);
            }
          }
        }
      } catch (error) {
        console.error('Error loading conversation:', error);
        chatSegments.value.push({
          id: generateUniqueId(),
          type: 'brief_error',
          sender: 'ai',
          errorMessage: 'Failed to load conversation',
          errorDetails: 'The conversation could not be loaded. Please try again.',
          timestamp: new Date(),
        });
      } finally {
        isWaitingForAI.value = false;
        nextTick(() => {
          scrollToBottom();
        });
      }
    };
    // Function to load a brief when clicked (works for both historic and newly generated briefs)
    const loadHistoricBrief = (segment: ChatMessageSegment) => {
      console.log(
        'Loading brief:',
        segment.id,
        segment.briefData ? 'Data present' : 'Data MISSING',
      );

      // Only show confirmation if the brief has actually been edited
      // Get the original brief text from the segment that's going to be loaded
      const originalBriefText = segment.briefData?.briefText || '';
      const currentBriefText = props.currentBriefText || '';

      // Compare the current brief with the original brief
      // Only show confirmation if they're different and current brief isn't empty
      if (
        currentBriefText.trim() !== '' &&
        currentBriefText !== originalBriefText
      ) {
        // Store the segment to be loaded after confirmation
        pendingBriefSegment.value = segment;
        // Show confirmation modal
        showConfirmModal.value = true;
        return;
      }

      // No unsaved changes, proceed with loading
      applyHistoricBrief(segment);
    };

    // Function to apply the selected brief after confirmation or directly
    const applyHistoricBrief = (segment: ChatMessageSegment) => {
      if (segment.briefData) {
        // Create the brief data object with default values if fields are missing
        const briefData = {
          subjectLine: segment.briefData.subjectLine || '',
          previewText: segment.briefData.previewText || '',
          briefText: Utils.cleanMarkdown(segment.briefData.briefText || ''),
        };

        // Log the subject line and preview text being emitted
        console.log('Emitting subject line:', briefData.subjectLine);
        console.log('Emitting preview text:', briefData.previewText);
        console.log('Emitting brief text length:', briefData.briefText.length);

        // Emit the brief data to update the Campaign Brief area
        emit('update:brief', briefData);

        // Emit auto-save event to trigger immediate saving
        emit('auto-save-brief', briefData);

        // Reset visual indicators on all brief cards
        document.querySelectorAll('.historic_brief').forEach(card => {
          const htmlCard = card as HTMLElement;
          htmlCard.classList.remove('border-green-300', 'bg-green-50');
          htmlCard.classList.add('border-purple-200');

          const statusSpan = htmlCard.querySelector('.brief-status');
          if (statusSpan) {
            // Determine if this is a recent brief or a historic one
            const cardId = htmlCard.getAttribute('data-segment-id');
            const cardSegment = chatSegments.value.find(s => s.id === cardId);
            const isRecent =
              cardSegment?.timestamp &&
              new Date().getTime() - new Date(cardSegment.timestamp).getTime() <
                60000;

            statusSpan.textContent = isRecent
              ? 'Generated just now'
              : 'From historic chat';
            statusSpan.classList.remove('text-green-600');
            statusSpan.classList.add('text-purple-600');
          }
        });

        // Add a visual indicator to the clicked brief card
        const briefCard = document.querySelector(
          `[data-segment-id="${segment.id}"]`,
        ) as HTMLElement | null;
        if (briefCard) {
          briefCard.classList.add('border-green-300', 'bg-green-50');
          briefCard.classList.remove('border-purple-200');

          // Add a checkmark icon or text to indicate it was loaded
          const statusSpan = briefCard.querySelector('.brief-status');
          if (statusSpan) {
            statusSpan.textContent = 'Loaded ✓';
            statusSpan.classList.remove('text-purple-600');
            statusSpan.classList.add('text-green-600');
          }
        }
      } else {
        console.error(
          'Attempted to load historic brief but briefData was missing on segment:',
          segment.id,
        );

        // Show an error message to the user
        chatSegments.value.push({
          id: generateUniqueId(),
          type: 'brief_error',
          sender: 'ai',
          errorMessage: 'Failed to load brief',
          errorDetails:
            'The brief data is missing or corrupted. Please try generating a new brief.',
          timestamp: new Date(),
        });

        scrollToBottom();
      }
    }; // End of loadHistoricBrief

    // Function to load an email design when clicked
    const loadHistoricEmail = (segment: ChatMessageSegment) => {
      console.log(
        'Loading email design:',
        segment.id,
        segment.design ? 'Design present' : 'Design MISSING',
      );
      if (segment.design) {
        // Check if this is a component list that needs to be processed
        if (
          segment.design &&
          typeof segment.design === 'object' &&
          'components' in segment.design &&
          Array.isArray(segment.design.components)
        ) {
          console.log(
            'Email contains component list, emitting raw JSON for API processing',
          );

          // Convert the design to JSON string
          const componentJSONText = JSON.stringify(segment.design);

          // Emit a special event to trigger immediate API call
          emit('generate-email-from-components', componentJSONText);

          // No need to add a message - we'll just use the existing email card
          // and the ContentTask component will handle showing the loading state
        } else {
          // This is already a complete email design, emit it directly
          // Emit the email design data to update the parent component
          emit('update:email', segment.design);

          // Emit auto-save event to trigger immediate saving
          emit('auto-save-email', segment.design);
        }

        // Reset visual indicators on all email cards
        document.querySelectorAll('.historic_email').forEach(card => {
          const htmlCard = card as HTMLElement;
          htmlCard.classList.remove('border-green-300', 'bg-green-50');
          htmlCard.classList.add('border-purple-200');

          const statusSpan = htmlCard.querySelector('.email-status');
          if (statusSpan) {
            // Determine if this is a recent email or a historic one
            const cardId = htmlCard.getAttribute('data-segment-id');
            const cardSegment = chatSegments.value.find(s => s.id === cardId);
            const isRecent =
              cardSegment?.timestamp &&
              new Date().getTime() - new Date(cardSegment.timestamp).getTime() <
                60000;

            statusSpan.textContent = isRecent
              ? 'Generated just now'
              : 'From historic chat';
            statusSpan.classList.remove('text-green-600');
            statusSpan.classList.add('text-purple-600');
          }
        });

        // Add a visual indicator to the clicked email card
        const emailCard = document.querySelector(
          `[data-segment-id="${segment.id}"]`,
        ) as HTMLElement | null;
        if (emailCard) {
          emailCard.classList.add('border-green-300', 'bg-green-50');
          emailCard.classList.remove('border-purple-200');

          // Add a checkmark icon or text to indicate it was loaded
          const statusSpan = emailCard.querySelector('.email-status');
          if (statusSpan) {
            statusSpan.textContent = 'Loaded ✓';
            statusSpan.classList.remove('text-purple-600');
            statusSpan.classList.add('text-green-600');
          }
        }
      } else {
        console.error(
          'Attempted to load historic email but design was missing on segment:',
          segment.id,
        );

        // Show an error message to the user
        chatSegments.value.push({
          id: generateUniqueId(),
          type: 'email_error',
          sender: 'ai',
          errorMessage: 'Failed to load email design',
          errorDetails:
            'The email design data is missing or corrupted. Please try generating a new email design.',
          timestamp: new Date(),
        });

        scrollToBottom();
      }
    }; // End of loadHistoricEmail

    // Watch for changes to newMessage and resize textarea accordingly
    watch(newMessage, () => {
      nextTick(() => {
        resizeMessageTextarea();
      });
    });

    // Ensure chat is scrolled to bottom when first loaded
    onMounted(() => {
      nextTick(() => {
        scrollToBottom();
      });
    });

    // Watch for changes to the conversationId prop
    watch(
      () => props.conversationId,
      (newVal, oldVal) => {
        // Only load if the ID actually changes to a non-null value
        // and is different from the current internal conversation ID
        console.log('BriefChat: conversationId prop changed.', {
          newVal,
          oldVal,
          internalId: conversationId.value,
        });
        if (newVal && newVal !== conversationId.value) {
          // Make sure we have a valid conversationId
          if (
            typeof newVal === 'number' ||
            (typeof newVal === 'string' && newVal.trim() !== '')
          ) {
            loadConversation(newVal);
          } else {
            console.warn('BriefChat: Invalid conversationId prop:', newVal);
            // Clear chat if the prop becomes invalid/null
            chatSegments.value = [];
            conversationId.value = null;
            isWaitingForAI.value = false;
          }
        } else if (!newVal && conversationId.value) {
          // Handle case where prop becomes null/undefined after being set
          console.log(
            'BriefChat: conversationId prop became null/undefined. Clearing chat.',
          );
          chatSegments.value = [];
          conversationId.value = null;
          isWaitingForAI.value = false;
        }
      },
      {immediate: true},
    ); // immediate: true ensures loadConversation runs on initial mount if prop is set

    // Watch for changes to the forceReload prop
    watch(
      () => props.forceReload,
      newVal => {
        console.log('BriefChat: forceReload prop changed to:', newVal);
        if (newVal && props.conversationId) {
          // Force reload the conversation
          console.log(
            'BriefChat: Force reloading conversation with ID:',
            props.conversationId,
          );
          loadConversation(props.conversationId);
        }
      },
    );

    // Handle Enter key: Shift+Enter for newline, Enter to send
    const handleEnterKey = (event: KeyboardEvent) => {
      if (event.shiftKey) {
        // Allow default (insert newline)
        return;
      }

      // Prevent default (no newline)
      event.preventDefault();

      // If the command palette is open and there are filtered commands, execute the first one
      if (showCommandPalette.value && filteredCommands.value.length > 0) {
        executeCommand(filteredCommands.value[0]);
        return;
      }

      // Only send message if not waiting for AI response or compressing
      if (!isWaitingForAI.value && !isCompressing.value) {
        sendMessage();
      } else {
        // Ensure chat input stays focused
        if (messageTextarea.value) {
          nextTick(() => {
            if (messageTextarea.value) messageTextarea.value.focus();
          });
        }
      }
    };

    // Handle when a prompt is selected
    const handlePromptSelected = (promptText: string) => {
      newMessage.value = promptText;

      // Use the sendMessage function to handle the message
      // This will use the same logic for updating the system prompt
      if (!isWaitingForAI.value && !isCompressing.value) {
        sendMessage();
      } else if (messageTextarea.value) {
        // Keep focus on textarea if we can't send
        nextTick(() => {
          if (messageTextarea.value) messageTextarea.value.focus();
        });
      }
    };

    // Handle quick action button clicks
    const handleQuickAction = (promptText: string) => {
      // Use the same logic as handlePromptSelected
      newMessage.value = promptText;
      if (!isWaitingForAI.value && !isCompressing.value) {
        sendMessage();
      } else if (messageTextarea.value) {
        // Keep focus on textarea if we can't send
        nextTick(() => messageTextarea.value?.focus());
      }
    };

    // Public method to send a message from outside components (like AskRaleonPopup)
    const sendExternalMessage = (message: string) => {
      console.log('Sending external message:', message);
      sendMessage(message);
    };

    // Handle retry for brief/email parsing errors
    const handleErrorRetry = (segment: ChatMessageSegment, message: string) => {
      console.log(
        'Handling error retry for segment:',
        segment.id,
        'with message:',
        message,
      );
      // Send the retry message to the AI
      sendMessage(message);
    };

    // Handle opening image in a new tab
    const openImageInNewTab = (url: string) => {
      window.open(url, '_blank');
    };

    // Handle successful image loading
    const handleImageLoad = (segmentId: string) => {
      console.log('Image loaded successfully for segment:', segmentId);
      // Find the segment and mark the image as loaded
      const segmentIndex = chatSegments.value.findIndex(
        s => s.id === segmentId,
      );
      if (segmentIndex !== -1) {
        // Update the segment to mark image as loaded
        const segment = chatSegments.value[segmentIndex];
        chatSegments.value.splice(segmentIndex, 1, {
          ...segment,
          imageLoaded: true,
        });
      }
    };

    // Handle image loading errors
    const handleImageError = (_event: Event, segmentId: string) => {
      console.error('Failed to load image for segment:', segmentId);
      // Find the segment and update it to show an error message
      const segmentIndex = chatSegments.value.findIndex(
        s => s.id === segmentId,
      );
      if (segmentIndex !== -1) {
        // Replace the image segment with an error text segment
        chatSegments.value.splice(segmentIndex, 1, {
          id: segmentId,
          type: 'text',
          sender: 'ai',
          content:
            'Error: Unable to load image. The image URL may be invalid or inaccessible.',
          timestamp: new Date(),
        });
      }
    };

    // Handle multi-image loading - updated to work with the component
    const handleMultiImageLoad = (
      segmentId: string,
      imageIndex: number,
      updatedImages: any[],
    ) => {
      console.log(
        `Multi-image loaded successfully for segment: ${segmentId}, image index: ${imageIndex}`,
      );
      // Find the segment
      const segmentIndex = chatSegments.value.findIndex(
        s => s.id === segmentId,
      );
      if (segmentIndex !== -1) {
        // Update the segment with the new images array
        const updatedSegment = {
          ...chatSegments.value[segmentIndex],
          images: updatedImages,
        };

        // Replace the segment in the array
        chatSegments.value.splice(segmentIndex, 1, updatedSegment);
      }
    };

    // Handle multi-image loading errors - updated to work with the component
    const handleMultiImageError = (
      _event: Event,
      segmentId: string,
      imageIndex: number,
      updatedImages: any[],
    ) => {
      console.error(
        `Failed to load multi-image for segment: ${segmentId}, image index: ${imageIndex}`,
      );
      // Find the segment
      const segmentIndex = chatSegments.value.findIndex(
        s => s.id === segmentId,
      );
      if (segmentIndex !== -1) {
        // Update the segment with the new images array
        const updatedSegment = {
          ...chatSegments.value[segmentIndex],
          images: updatedImages,
        };

        // Replace the segment in the array
        chatSegments.value.splice(segmentIndex, 1, updatedSegment);
      }
    };

    // Confirm modal handlers
    const confirmBriefLoad = () => {
      if (pendingBriefSegment.value) {
        applyHistoricBrief(pendingBriefSegment.value);
        pendingBriefSegment.value = null;
      }
      showConfirmModal.value = false;
    };

    // Function to process tool messages and show random loading messages
    const processToolMessage = (message: any) => {
      // Extract tool name from metadata if available
      const toolName = message.llmMetadata?.name || 'tool';
      const randomMessage = ToolMessageService.getRandomMessage(toolName);

      chatSegments.value.push({
        id: generateUniqueId(),
        type: 'tool_message',
        sender: 'ai',
        content: randomMessage,
        timestamp: new Date(message.createdAt),
      });
    };

    // Handle switch mode button click
    const handleSwitchModeClick = (switchModeData: { message: string; mode: string; summary: string }) => {
      console.log('Switch mode clicked:', switchModeData);

      // Navigate to /chat with mode and summary parameters
      const baseUrl = '/chat';
      const params = new URLSearchParams();
      params.set('mode', switchModeData.mode);
      if (switchModeData.summary) {
        params.set('summary', switchModeData.summary);
      }

      const url = `${baseUrl}?${params.toString()}`;

      // Use router if available, otherwise use window.location
      if (window.location.pathname === '/chat') {
        // If already on chat page, just update the URL params
        window.location.href = url;
      } else {
        // Navigate to chat page
        window.location.href = url;
      }
    };

    return {
      newMessage,
      chatSegments,
      isWaitingForAI,
      sendMessage,
      sendExternalMessage, // Expose the public method for external components
      generateUniqueId,
      scrollToBottom,
      chatHistoryContainer,
      messageTextarea,
      resizeMessageTextarea,
      loadHistoricBrief,
      loadHistoricEmail,
      handleEnterKey,
      briefPrompts,
      quickActions, // Add quick actions array
      handlePromptSelected,
      handleQuickAction, // Add quick action handler
      isAtBottom,
      handleScroll,
      openImageInNewTab,
      handleImageLoad,
      handleImageError,
      handleMultiImageLoad,
      handleMultiImageError,
      handleErrorRetry, // Add the error retry handler
      processToolMessage, // Add the tool message handler
      // Confirm modal
      showConfirmModal,
      confirmBriefLoad,
      showQuotaModal,
      canSendMessage: quotaService.canSendMessage,
      quota: quotaService.quota,
      quotaService,
      // Text formatting functions
      formatText,
      containsFormatting,
      // Method to set the message directly
      setMessage: (message: string) => {
        newMessage.value = message;
        // Resize the textarea after setting the message
        nextTick(() => {
          if (messageTextarea.value) {
            resizeMessageTextarea();
          }
        });
      },
      // Switch mode handler
      handleSwitchModeClick,
      // Slash commands
      showCommandPalette,
      commands,
      filteredCommands,
      executeCommand,
      handleCommandInput,
      // Compression state
      isCompressing,
    };
  },
  methods: {
    decodeBriefText: (s: string) =>
      s.replace(/\\n/g, '\n').replace(/\\"/g, '"'),
  },
});
</script>

<style scoped>
.chat-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #b1b2b4 #f3f4f6;
}

.chat-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 9999px;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background-color: #8e9299;
  border-radius: 9999px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #8e9299;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* autoprefixer: off */
  line-clamp: 2;
  /* autoprefixer: on */
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.extra-smooth-gradient-text {
  /* Using more color stops for ultra-smooth transition */
  background-image: linear-gradient(
    90deg,
    #6536e2 0%,
    #6536e2 15%,
    #4c63df 30%,
    #1e90db 50%,
    #4c63df 70%,
    #6536e2 85%,
    #6536e2 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
</style>
