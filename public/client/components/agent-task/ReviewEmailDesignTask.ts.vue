<template>
  <div class="h-full">
    <div class="px-6 py-3 border-b bg-white">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-medium">Email Review</h2>
        <div class="flex items-center gap-2">
          <!-- <button
            v-if="task?.emailDesign"
            @click="$emit('regenerate-email')"
            class="px-4 py-2 bg-purple-50 border border-purple-200 text-purple-700 rounded-lg hover:bg-purple-100 hover:border-purple-300 transition-colors duration-200 flex items-center font-medium"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 10h7V3l-2.35 3.35z"/>
            </svg>
            Regenerate Email
          </button> -->
          <button
            v-if="task?.emailDesign"
            @click="$emit('show-email-modal')"
            class="px-4 py-2 bg-blue-50 border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-100 hover:border-blue-300 transition-colors duration-200 flex items-center font-medium"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
            </svg>
            Edit Email
          </button>
        </div>
      </div>
    </div>

    <div class="h-[calc(100%-65px)]"> <!-- Adjust height to account for header -->
      <!-- Content Layout (stacks on small screens, side-by-side on larger screens) -->
      <div class="flex flex-col sm:flex-row h-full">
        <!-- Left Column: Process Steps -->
        <div class="w-full sm:w-1/2 xl:w-2/5 p-6 border-b sm:border-b-0 sm:border-r overflow-y-auto">
          <!-- Step status indicators -->
          <div class="flex flex-col space-y-6">
            <h3 class="font-medium text-gray-800">Email Creation Process</h3>

            <!-- Step 1: Generate Email Template -->
            <div class="flex items-start">
              <div class="flex-shrink-0 mt-1">
                <div v-if="task?.emailDesign" class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                  </svg>
                </div>
                <div v-else class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                  <span class="text-gray-600 text-sm font-medium">1</span>
                </div>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium" :class="{'text-gray-900': !task?.emailDesign, 'text-green-700': task?.emailDesign}">
                  Generate Email
                </h3>
                <p class="mt-1 text-xs text-gray-500">
                  Create the email template design based on campaign details
                </p>
                <div v-if="!task?.emailDesign" class="mt-3">
                  <div v-if="isCheckingAccess" class="flex items-center text-gray-500 text-sm">
                    <div class="w-4 h-4 border-t-2 border-gray-400 animate-spin rounded-full mr-2"></div>
                    <span>Checking access...</span>
                  </div>
                  <div v-else-if="!hasEmailGenerationAccess" class="mt-3 p-4 bg-blue-50 border border-blue-100 rounded-lg">
                    <div class="flex items-start">
                      <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd" />
                        </svg>
                      </div>
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">Email Generation (Early Access)</h3>
                        <div class="mt-2 text-sm text-blue-700">
                          <p>Email generation is currently in early access. Please email <a href="mailto:<EMAIL>" class="underline font-medium"><EMAIL></a> to join the waiting list.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    v-else
                    @click="$emit('generate-email')"
                    :disabled="isGeneratingEmail"
                    class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center text-sm font-medium disabled:opacity-75 disabled:cursor-not-allowed min-w-[200px]"
                  >
                    <template v-if="isGeneratingEmail">
                      <div class="w-4 h-4 border-t-2 border-white animate-spin rounded-full mr-2"></div>
                      <span>Generating Email...</span>
                    </template>
                    <template v-else>
                      <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" fill="white"/>
                      </svg>
                      Generate Email Template
                    </template>
                  </button>
                  <p v-if="isGeneratingEmail" class="mt-2 text-sm text-purple-700">
                    Our AI is crafting your email template. This may take a moment...
                  </p>
                </div>
              </div>
            </div>

            <!-- Step 2: Review and Edit -->
            <div class="flex items-start">
              <div class="flex-shrink-0 mt-1">
                <div v-if="task?.emailHtml" class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                  </svg>
                </div>
                <div v-else-if="task?.emailDesign" class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <span class="text-blue-600 text-sm font-medium">2</span>
                </div>
                <div v-else class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                  <span class="text-gray-400 text-sm font-medium">2</span>
                </div>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium" :class="{
                  'text-gray-400': !task?.emailDesign,
                  'text-blue-700': task?.emailDesign && !task?.emailHtml,
                  'text-green-700': task?.emailHtml
                }">
                  Review and Edit Email
                </h3>
                <p class="mt-1 text-xs" :class="{'text-gray-400': !task?.emailDesign, 'text-gray-500': task?.emailDesign}">
                  Review the generated template and make any necessary edits
                </p>
                <div v-if="task?.emailDesign && !task?.emailHtml" class="mt-3">
                  <button
                    @click="$emit('show-email-modal')"
                    class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center text-sm font-medium"
                  >
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" fill="white"/>
                    </svg>
                    View and Edit Email
                  </button>
                </div>
              </div>
            </div>

            <!-- Step 3: Save and Send -->
            <div class="flex items-start">
              <div class="flex-shrink-0 mt-1">
                <div v-if="task?.emailHtml" class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                  </svg>
                </div>
                <div v-else class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                  <span class="text-gray-400 text-sm font-medium">3</span>
                </div>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium" :class="{'text-gray-400': !task?.emailHtml, 'text-green-700': task?.emailHtml}">
                  Email Ready to Send
                </h3>
                <p class="mt-1 text-xs" :class="{'text-gray-400': !task?.emailHtml, 'text-gray-500': task?.emailHtml}">
                  Email template is saved and ready for the campaign
                </p>
              </div>
            </div>
          </div>

          <!-- Empty state illustration when nothing is generated -->
          <div v-if="!task?.emailDesign && !isGeneratingEmail" class="text-center p-6 bg-gray-50 rounded-lg border border-gray-200 mt-8">
            <svg class="w-16 h-16 mx-auto text-gray-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z"/>
            </svg>
            <h3 class="mt-4 text-gray-700 font-medium">No Email Template Yet</h3>
            <p class="mt-2 text-sm text-gray-500 max-w-md mx-auto">
              Generate an email template to get started. The AI will create a professional design based on your campaign details.
            </p>
          </div>
        </div>

        <!-- Right Column: Email Preview -->
        <div class="w-full sm:w-1/2 xl:w-3/5 flex flex-col overflow-hidden">
          <!-- Email Preview Header -->
          <div class="p-4 bg-gray-50 border-b">
            <h3 class="font-medium text-gray-800">Email Preview</h3>
            <p v-if="emailThumbnail" class="text-sm text-gray-500 mt-1">
              This shows a snapshot of how your email will appear to recipients.
            </p>
            <p v-else-if="task?.emailDesign" class="text-sm text-gray-500 mt-1">
              Your email design is ready. For a complete preview, click the "View Email" button above.
            </p>
            <p v-else class="text-sm text-gray-500 mt-1">
              Generate an email template to preview it here.
            </p>
          </div>

          <!-- Email Preview Content -->
          <div class="flex-grow overflow-auto p-4 bg-gray-50 min-h-[400px] sm:min-h-0">
            <div v-if="task?.emailHtml" class="border rounded-lg overflow-hidden bg-white h-full flex flex-col">
              <div class="p-4 border-b bg-gray-50 flex justify-between items-center">
                <div>
                  <div class="text-sm font-medium">{{ subjectLine || 'No subject' }}</div>
                  <div class="text-xs text-gray-500">{{ previewLine || 'No preview text' }}</div>
                </div>
              </div>
              <div class="p-4 flex-grow overflow-auto">
                <div class="email-preview-thumbnail h-full">
                  <img v-if="emailThumbnail" :src="emailThumbnail" alt="Email preview" class="w-full h-auto rounded border" />
                  <div v-else-if="emailHtmlContent" class="overflow-hidden rounded border h-full flex flex-col">
                    <div class="email-html-preview flex-grow">
                      <iframe
                        :srcdoc="emailHtmlContent"
                        class="w-full border-0 h-full"
                        style="background-color: white; min-height: 400px;"
                        title="Email preview"
                      ></iframe>
                      <div class="p-3 bg-blue-50 text-center">
                        <p class="text-blue-700 text-sm">
                          This is a simplified preview. Click "View Email" for the full editor.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 2 State - Email Design Exists but not HTML -->
            <div v-else-if="task?.emailDesign" class="h-full flex items-center justify-center">
              <div class="text-center p-8 bg-blue-50 rounded border flex flex-col items-center max-w-md">
                <svg class="w-16 h-16 text-blue-400 mb-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z"/>
                </svg>
                <p class="text-blue-700 font-medium">Review and finalize your email</p>
                <p class="text-blue-600 text-sm mt-2">Click "View and Edit Email" to review and approve the email template.</p>
                <button
                  @click="$emit('show-email-modal')"
                  class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                  </svg>
                  View and Edit Email
                </button>
              </div>
            </div>

            <!-- Empty Preview State -->
            <div v-else class="h-full flex items-center justify-center">
              <div v-if="!hasEmailGenerationAccess" class="text-center p-8 bg-blue-50 rounded border flex flex-col items-center max-w-md">
                <svg class="w-16 h-16 text-blue-400 mb-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M13 7h-2v2h2V7zm0 4h-2v6h2v-6zm-1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                </svg>
                <p class="text-blue-800 font-medium">Email Generation (Early Access)</p>
                <p class="text-blue-700 text-sm mt-2">
                  Email generation is currently in early access. Please contact us to join the waiting list.
                </p>
                <a
                  href="mailto:<EMAIL>"
                  class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center"
                >
                  <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z"/>
                  </svg>
                  Contact Support
                </a>
              </div>
              <div v-else class="text-center p-8 bg-gray-100 rounded border flex flex-col items-center max-w-md">
                <svg class="w-16 h-16 text-gray-400 mb-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z"/>
                </svg>
                <p class="text-gray-700 font-medium">No Email Preview Available</p>
                <p class="text-gray-500 text-sm mt-2">Generate an email template to see a preview here.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as OrganizationSettings from '../../services/organization-settings.js';

export default {
  name: 'ReviewEmailDesignTask',
  props: {
    task: {
      type: Object,
      required: true
    },
    isGeneratingEmail: {
      type: Boolean,
      default: false
    },
    subjectLine: {
      type: String,
      default: ''
    },
    previewLine: {
      type: String,
      default: ''
    },
    emailThumbnail: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      hasEmailGenerationAccess: true,
      isCheckingAccess: true
    };
  },
  async mounted() {
    // Check if organization has email generation access
    this.isCheckingAccess = true;
    try {
      const hasEmailGeneration = await OrganizationSettings.getOrganizationSetting('hasEmailGeneration');
      this.hasEmailGenerationAccess = hasEmailGeneration === 'true';
    } catch (error) {
      console.error('Error checking email generation access:', error);
      // Default to false if there's an error
      this.hasEmailGenerationAccess = false;
    } finally {
      this.isCheckingAccess = false;
    }
  },
  computed: {
    emailHtmlContent() {
      // First check for the top-level emailHTML property
      if (this.task?.emailHTML) {
        return this.task.emailHTML;
      }

      // Then check for the alternate spelling variant
      if (this.task?.emailHtml) {
        return this.task.emailHtml;
      }

      // Then try emailDesign as a fallback, but only if it contains valid HTML content
      if (!this.task?.emailDesign) return null;

      // Try different possible locations of the HTML content
      if (typeof this.task.emailDesign === 'string') {
        return this.task.emailDesign;
      } else if (this.task.emailDesign.html) {
        return this.task.emailDesign.html;
      } else if (this.task.emailDesign.content) {
        return this.task.emailDesign.content;
      } else if (this.task.emailDesign.body) {
        return this.task.emailDesign.body;
      }

      // If we have emailDesign but can't find valid HTML content, return null
      // This prevents [object Object] from being displayed when emailDesign
      // exists but doesn't contain renderable HTML
      return null;
    }
  },
  emits: ['generate-email', 'regenerate-email', 'show-email-modal']
}
</script>

<style scoped>
.email-preview-thumbnail {
  max-width: 100%;
  margin: 0 auto;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
