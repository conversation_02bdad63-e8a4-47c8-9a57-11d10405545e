<template>
  <div class="h-full flex flex-col">
    <!-- Header -->
    <div class="px-6 py-3 border-b bg-white flex-shrink-0">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-medium">Finalize Campaign</h2>
      </div>
    </div>

    <!-- Body -->
    <div class="flex-1 overflow-y-auto">
      <div class="p-6 space-y-6 max-w-3xl">
        <CampaignChecklist
          :task="task"
          :segment="segment"
          :task-steps="taskSteps"
          :klaviyo-status="klaviyoStatus"
          :is-creating="isCreatingKlaviyoCampaign"

          @select-task="emit('select-task', $event)"
          @export-klaviyo="emit('export-to-klaviyo')"
          @export-klaviyo-html="emit('export-to-klaviyo-html-only')"
          @open-resync="onResyncVariant"
        />
      </div>
    </div>

    <ResyncModal v-if="isResyncModalOpen" @close="isResyncModalOpen = false" @resync="handleResync" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from '@vue/runtime-core';

import CampaignChecklist from './klaviyo-campaign/CampaignChecklist.ts.vue';
import ResyncModal from './klaviyo-campaign/ResyncModal.ts.vue';

interface Props {
  task: Record<string, any>;
  segment?: Record<string, any>;
  taskSteps?: Array<Record<string, any>>;
  isCreatingKlaviyoCampaign?: boolean;
  klaviyoStatus?: Record<string, any> | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'select-task', step: any): void;
  (e: 'export-to-klaviyo'): void;
  (e: 'export-to-klaviyo-html-only'): void;
  (e: 'resync-klaviyo'): void;
  (e: 'resync-klaviyo-html-only'): void;
}>();

const isResyncModalOpen = ref(false);

type ResyncVariant = 'hybrid' | 'html';
const requestedResyncVariant = ref<ResyncVariant>('hybrid');

function onResyncVariant(variant: ResyncVariant) {
  requestedResyncVariant.value = variant;
  isResyncModalOpen.value = true;
}

function handleResync(variant: ResyncVariant) {
  isResyncModalOpen.value = false;
  if (variant === 'hybrid') emit('resync-klaviyo');
  else emit('resync-klaviyo-html-only');
}

watch(isResyncModalOpen, (open) => {
  if (!open) requestedResyncVariant.value = 'hybrid';
});
</script>

<style scoped></style>
