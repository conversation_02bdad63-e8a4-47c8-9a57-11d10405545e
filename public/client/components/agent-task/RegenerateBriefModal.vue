<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-lg w-full mx-4">
      <h2 class="text-xl font-semibold mb-4">Regenerate Brief</h2>
      <p class="text-gray-600 mb-4">
        Provide additional instructions or context for regenerating the brief:
      </p>
      <textarea
        v-model="userInput"
        rows="4"
        class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4"
        placeholder="e.g., Make it more concise, focus on the new product feature..."
      ></textarea>
      <div class="flex justify-end space-x-3">
        <button
          @click="handleClose"
          class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400"
        >
          Cancel
        </button>
        <button
          @click="handleSubmit"
          :disabled="isGenerating"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ isGenerating ? 'Generating...' : 'Regenerate' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface Props {
  show: boolean;
  isGenerating?: boolean;
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  isGenerating: false,
});

// Define emits
const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'submit', userInput: string): void;
}>();

// Local state for textarea
const userInput = ref('');

// Methods
const handleClose = () => {
  emit('close');
};

const handleSubmit = () => {
  if (!props.isGenerating) {
    emit('submit', userInput.value);
    // Optionally clear input after submit, or let parent handle it
    userInput.value = '';
  }
};
</script>

<style scoped>
/* Add any additional scoped styles if needed, though Tailwind should cover most cases */
</style>
