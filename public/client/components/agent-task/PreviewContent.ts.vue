<template>
  <!-- Display one of three states: -->
  <!-- 1. Loading animation during generation -->
  <div v-if="isGeneratingEmail" class="text-center">
    <div class="bg-gray-50 border rounded-lg p-6 w-96 shadow-lg">
      <div class="animate-bounce mb-4">
        <svg class="w-8 h-8 mx-auto text-purple-600" viewBox="0 0 24 24">
          <path fill="currentColor" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z" />
        </svg>
      </div>
      <div class="h-20 overflow-hidden">
        <transition-group name="fade" tag="div" class="text-gray-700">
          <div v-for="(message, index) in currentGenerationMessages" :key="message" class="py-1"
            :class="{'font-medium text-purple-700': index === currentMessageIndex}">
            {{ message }}
          </div>
        </transition-group>
      </div>
      <div class="mt-4 text-sm text-gray-500">
        {{ showAnySecond ? 'Any second now...' : formatCountdown(countdownTime) + ' remaining' }}
      </div>
    </div>
  </div>

  <!-- 2. Generate button if no email exists -->
  <div v-else-if="!hasEmailDesign" class="text-center">
    <button @click="$emit('generate-new-email')"
      class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center gap-2">
      <svg class="w-5 h-5" viewBox="0 0 24 24">
        <path fill="currentColor" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z" />
      </svg>
      Generate Email
    </button>
    <p class="mt-3 text-sm text-gray-500">Generate a beautiful email based on your campaign brief</p>
  </div>

  <!-- 3. View Email button if email exists -->
  <button v-else class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200"
    @click="$emit('show-email-modal')">
    View Email Design
  </button>
</template>

<script>
export default {
  name: 'PreviewContent',
  props: {
    isGeneratingEmail: Boolean,
    currentGenerationMessages: {
      type: Array,
      default: () => []
    },
    currentMessageIndex: Number,
    countdownTime: Number,
    showAnySecond: Boolean,
    hasEmailDesign: Boolean
  },
  emits: ['generate-new-email', 'show-email-modal'],
  methods: {
    formatCountdown(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  }
}
</script>
