<template>
  <div class="rounded-lg overflow-hidden bg-white border border-gray-200 shadow-sm inline-block max-w-[300px]">
    <!-- Image name header -->
    <div class="p-2 bg-purple-50 border-b border-gray-200">
      <h4 class="font-medium text-sm text-gray-700 truncate">{{ imageName || altText }}</h4>
    </div>

    <div class="relative">
      <!-- Loading placeholder -->
      <div v-if="!imageLoaded" class="w-[300px] h-[200px] bg-gray-100 flex items-center justify-center">
        <div class="flex flex-col items-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mb-2"></div>
          <span class="text-sm text-gray-500">Loading image...</span>
        </div>
      </div>

      <!-- Hidden image preloader -->
      <img
        :src="imageUrl"
        alt=""
        class="hidden"
        @load="handleImageLoad"
        @error="handleImageError" />

      <!-- Actual image (only shown after loading) -->
      <div v-if="imageLoaded" class="w-[300px] h-[200px] flex items-center justify-center overflow-hidden bg-gray-50">
        <img
          :src="imageUrl"
          :alt="imageName || altText"
          class="max-w-full max-h-full object-contain"
          @click="openImageInNewTab(imageUrl)"
          style="cursor: pointer;" />
      </div>
    </div>

    <div class="p-2 bg-white border-t border-gray-100 text-center">
      <div class="flex justify-between items-center">
        <span class="text-xs text-gray-500 mr-2 flex-shrink-1">Click to view full size</span>
        <a :href="imageUrl" target="_blank" class="text-purple-600 hover:text-purple-800 text-xs whitespace-nowrap flex-shrink-0 min-w-[80px] text-right">
          Open in new tab
        </a>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from '@vue/runtime-core';
import ImageRenderer from './ImageRenderer.vue';

export default defineComponent({
  name: 'SingleImageRenderer',
  extends: ImageRenderer,
  props: {
    imageUrl: {
      type: String,
      required: true
    },
    imageName: {
      type: String,
      default: ''
    },
    altText: {
      type: String,
      default: 'AI Generated Image'
    },
    initialLoadState: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const imageLoaded = ref(props.initialLoadState);

    // Computed property to ensure the image URL is properly formatted
    const getFullImageUrl = () => {
      const url = props.imageUrl || '';

      // If it's already an absolute URL (starts with http:// or https://)
      if (url.match(/^https?:\/\//)) {
        return url;
      }

      // Check if it's a CloudFront URL that might be missing the protocol
      if (url.includes('cloudfront.net')) {
        return 'https://' + url.replace(/^[^a-zA-Z0-9]+/, '');
      }

      // If it's a relative URL that doesn't start with a slash, add one
      if (url && !url.startsWith('/')) {
        return '/' + url;
      }

      return url;
    };

    // Override base methods to update local state
    const handleImageLoad = () => {
      imageLoaded.value = true;
      emit('image-loaded', props.imageId);
    };

    const handleImageError = (event: Event) => {
      console.error(`Image load error for URL: ${props.imageUrl}`);
      emit('image-error', event, props.imageId);
    };

    return {
      imageLoaded,
      imageUrl: getFullImageUrl(),
      handleImageLoad,
      handleImageError
    };
  }
});
</script>
