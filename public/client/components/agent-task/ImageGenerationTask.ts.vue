<template>
  <div class="h-full relative" id="image-generation-container">
    <!-- Preview Image Modal -->
    <div v-if="showPreviewModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="flex justify-end">
              <button @click="closePreviewModal" class="text-gray-400 hover:text-gray-500">
                <span class="sr-only">Close</span>
                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:text-left">
              <div class="mt-2">
                <img
                  :src="previewImage?.url"
                  :alt="previewImage?.prompt"
                  class="max-w-full max-h-[70vh] object-contain mx-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Brand Image Modal -->
    <EditBrandImageModal
      :show="showEditImageModal"
      :image="imageToEdit"
      :is-edit-mode="!!imageToEdit?.id"
      :asset-type="'email'"
      :custom-categories="[]"
      @close="closeEditImageModal"
      @save="saveEditedImage"
      @status-update="handleStatusUpdate"
    />

    <!-- Select Existing Images Modal -->
    <SelectImagesModal
      :show="showSelectImagesModal"
      :asset-type="'email'"
      :custom-categories="[]"
      @close="closeSelectImagesModal"
      @select="addSelectedImages"
      @status-update="handleStatusUpdate"
    />
    <!-- Main Flex Container: Left (1/3) | Right (2/3) -->
    <div class="flex h-[calc(100%-10px)] relative">
      <!-- Left Section (1/3) -->
      <div class="w-1/3 h-full overflow-y-auto flex-shrink p-4 border-r border-gray-200">
        <div class="bg-white rounded-lg shadow-sm p-4">
          <h2 class="text-lg font-medium text-gray-800 mb-4">Image Generation Controls</h2>

          <!-- Image Generation Form -->
          <div class="space-y-4">
            <!-- Reference Images Section -->
            <div>
              <div class="flex justify-between items-center mb-1">
                <label class="block text-sm font-medium text-gray-700">Reference Images</label>
                <span class="text-xs text-gray-500">{{ referenceImages.length }} of {{ maxReferenceImages }} selected</span>
              </div>
              <div class="border border-gray-200 rounded-md bg-gray-50 p-4 flex flex-col items-center justify-center">
                <div v-if="referenceImages.length === 0" class="text-sm text-gray-500 mb-3">
                  No reference images selected
                </div>
                <div v-else class="w-full grid grid-cols-2 gap-2 mb-3">
                  <div
                    v-for="(image, index) in referenceImages"
                    :key="index"
                    class="relative"
                  >
                    <img
                      :src="image.url"
                      class="w-full h-16 object-cover rounded border border-gray-200"
                      alt="Reference image"
                    />
                    <button
                      @click="removeReferenceImage(index)"
                      class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                    >
                      ×
                    </button>
                  </div>
                </div>
                <button
                  @click="toggleReferenceSelectionMode"
                  class="text-xs flex items-center justify-center gap-1 px-3 py-2 rounded border transition-colors duration-200"
                  :class="{
                    'border-purple-500 bg-purple-50 text-purple-700': isReferenceSelectionMode,
                    'border-gray-300 bg-white text-gray-700 hover:bg-gray-50': !isReferenceSelectionMode
                  }"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z" />
                  </svg>
                  {{ isReferenceSelectionMode ? 'Done Selecting' : 'Choose Reference Images' }}
                </button>
                <input
                  type="file"
                  ref="fileInput"
                  @change="handleFileSelect"
                  accept="image/*"
                  multiple
                  class="hidden"
                />
              </div>
            </div>

            <!-- Prompt Input -->
            <div>
              <label for="prompt" class="block text-sm font-medium text-gray-700 mb-1">Prompt</label>
              <textarea
                id="prompt"
                v-model="prompt"
                rows="4"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                placeholder="Describe the image you want to generate..."
                @keydown.ctrl.enter.prevent="generateImage"
              ></textarea>
            </div>

            <!-- Generate Button -->
            <button
              @click="generateImage"
              class="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors duration-200"
              :disabled="isGenerating"
            >
              <span v-if="!isGenerating">Generate Image</span>
              <span v-else class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Generating...
              </span>
            </button>

            <!-- Size Selector -->
              <div class="mb-8">
                <label class="block text-sm font-medium text-gray-700 mb-2">Image Size</label>
                <div class="grid grid-cols-3 gap-2">
                  <!-- Portrait Option -->
                  <button
                    @click="size = 'portrait'"
                    class="relative border-2 rounded-lg p-2 flex items-center justify-center transition-all aspect-square w-full pb-8"
                    :class="size === 'portrait' ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-gray-300'"
                  >
                    <div class="w-3/5 h-4/5 bg-gray-100 rounded flex items-center justify-center border-2 border-gray-200">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div class="absolute bottom-1 text-center w-full">
                      <span class="text-xs font-medium" :class="size === 'portrait' ? 'text-purple-700' : 'text-gray-500'">Portrait</span>
                    </div>
                  </button>

                  <!-- Landscape Option -->
                  <button
                    @click="size = 'landscape'"
                    class="relative border-2 rounded-lg p-2 flex items-center justify-center transition-all aspect-square w-full pb-8"
                    :class="size === 'landscape' ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-gray-300'"
                  >
                    <div class="w-4/5 h-3/5 bg-gray-100 rounded flex items-center justify-center border-2 border-gray-200">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div class="absolute bottom-1 text-center w-full">
                      <span class="text-xs font-medium" :class="size === 'landscape' ? 'text-purple-700' : 'text-gray-500'">Landscape</span>
                    </div>
                  </button>

                  <!-- Square Option -->
                  <button
                    @click="size = 'square'"
                    class="relative border-2 rounded-lg p-2 flex items-center justify-center transition-all aspect-square w-full pb-8"
                    :class="size === 'square' ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-gray-300'"
                  >
                    <div class="w-3/4 h-3/4 bg-gray-100 rounded flex items-center justify-center border-2 border-gray-200">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div class="absolute bottom-1 text-center w-full">
                      <span class="text-xs font-medium" :class="size === 'square' ? 'text-purple-700' : 'text-gray-500'">Square</span>
                    </div>
                  </button>
                </div>
              </div>

            <!-- Style and Background Toggles (only shown when no reference images) -->
            <div v-if="referenceImages.length === 0" class="mb-4 grid grid-cols-2 gap-4">
              <!-- Style Toggle -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Style</label>
                <div class="flex items-center space-x-2">
                  <button
                    @click="style = 'vivid'"
                    class="flex-1 py-1 px-2 border-2 rounded-lg text-xs font-medium transition-all relative group"
                    :class="style === 'vivid' ? 'border-purple-500 bg-purple-50 text-purple-700' : 'border-gray-200 text-gray-700 hover:border-gray-300'"
                  >
                    Vivid
                    <span class="absolute left-1/2 -translate-x-1/2 -top-10 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                      Hyper real images
                    </span>
                  </button>
                  <button
                    @click="style = 'natural'"
                    class="flex-1 py-1 px-2 border-2 rounded-lg text-xs font-medium transition-all relative group"
                    :class="style === 'natural' ? 'border-purple-500 bg-purple-50 text-purple-700' : 'border-gray-200 text-gray-700 hover:border-gray-300'"
                  >
                    Natural
                    <span class="absolute left-1/2 -translate-x-1/2 -top-10 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                      Less dramatic images
                    </span>
                  </button>
                </div>
              </div>

              <!-- Background Toggle -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Background</label>
                <div class="flex items-center space-x-2">
                  <button
                    @click="background = 'opaque'"
                    class="flex-1 py-1 px-2 border-2 rounded-lg text-xs font-medium transition-all"
                    :class="background === 'opaque' ? 'border-purple-500 bg-purple-50 text-purple-700' : 'border-gray-200 text-gray-700 hover:border-gray-300'"
                  >
                    Opaque
                  </button>
                  <button
                    @click="background = 'transparent'"
                    class="flex-1 py-1 px-2 border-2 rounded-lg text-xs font-medium transition-all"
                    :class="background === 'transparent' ? 'border-purple-500 bg-purple-50 text-purple-700' : 'border-gray-200 text-gray-700 hover:border-gray-300'"
                  >
                    Transparent
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Section (2/3) -->
      <div class="w-2/3 h-full overflow-y-auto p-4">
        <div class="bg-white rounded-lg shadow-sm p-4 h-full flex flex-col">
          <!-- Gallery Header -->
          <div class="flex flex-col mb-4 flex-shrink-0">
            <!-- Gallery Header -->
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-medium text-gray-800">Gallery</h2>
              <div class="flex space-x-2">
                <button
                  @click="selectExistingImage"
                  class="text-xs flex items-center justify-center gap-1 px-3 py-2 rounded border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z" />
                  </svg>
                  Select Existing Image
                </button>
                <button
                  @click="openUploadDialog"
                  class="text-xs flex items-center justify-center gap-1 px-3 py-2 rounded bg-purple-600 text-white hover:bg-purple-700 transition-colors duration-200"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                  Upload
                </button>
              </div>
            </div>
          </div>

          <!-- Image Gallery Grid Container - Scrollable area -->
          <div class="flex-grow overflow-y-auto overflow-x-hidden">
            <div class="grid grid-cols-3 gap-4 p-1 pb-4">
              <!-- Loading state -->
              <div v-if="isLoading" class="col-span-3 py-10 text-center">
                <div class="mx-auto w-16 h-16 mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                  <svg class="animate-spin h-8 w-8 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
                <h3 class="text-gray-500 text-lg font-medium mb-1">Loading images...</h3>
                <p class="text-gray-400 mb-4">Please wait while we load your images</p>
              </div>

              <!-- Empty state when no images -->
              <div v-else-if="generatedImages.length === 0" class="col-span-3 py-10 text-center">
                <div class="mx-auto w-16 h-16 mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-gray-400">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                  </svg>
                </div>
                <h3 class="text-gray-500 text-lg font-medium mb-1">No images in gallery</h3>
                <p class="text-gray-400 mb-4">Generate new images or select existing ones</p>
              </div>

            <!-- Dynamic image cards -->
            <div
              v-for="(image, index) in generatedImages"
              :key="image.id"
              class="flex flex-col border border-gray-200 rounded-lg overflow-hidden h-[320px] cursor-pointer m-0.5"
              :class="{
                'ring-2 ring-purple-500': isReferenceSelectionMode && selectedGalleryImages.some(selected => selected.id === image.id),
                'ring-1 ring-blue-300': !isReferenceSelectionMode && referenceImages.some(ref => ref.url === image.url),
                'hover:border-purple-300': isReferenceSelectionMode,
                'shadow-sm': true
              }"
              @click="isReferenceSelectionMode ? toggleImageSelection(image) : null"
            >
              <div class="relative group">
                <!-- Selection overlay for reference selection mode -->
                <div
                  v-if="isReferenceSelectionMode"
                  class="absolute inset-0 bg-black bg-opacity-10 flex items-center justify-center z-10"
                  :class="{ 'bg-purple-500 bg-opacity-20': selectedGalleryImages.some(selected => selected.id === image.id) }"
                >
                  <div
                    v-if="selectedGalleryImages.some(selected => selected.id === image.id)"
                    class="bg-purple-500 rounded-full w-8 h-8 flex items-center justify-center shadow-md"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div
                    v-else
                    class="bg-white bg-opacity-70 rounded-full w-8 h-8 flex items-center justify-center shadow-md"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                  </div>
                </div>
                <!-- Image preview or placeholder -->
                <div v-if="image.url && !image.isGenerating" class="h-[200px] bg-gray-50 flex items-center justify-center">
                  <img
                    :src="image.originalUrl || image.url"
                    :alt="image.prompt"
                    class="object-contain max-w-full max-h-full"
                  />
                </div>
                <div v-else class="bg-gray-100 h-[200px] flex items-center justify-center">
                  <div v-if="image.isGenerating || image.id.startsWith('generating-') || image.imageId === -1" class="flex flex-col items-center">
                    <div class="h-24 w-24 relative mb-2">
                      <!-- Background canvas -->
                      <div class="absolute inset-0 bg-gray-100 rounded-md overflow-hidden">
                        <!-- Animated color gradient -->
                        <div class="absolute inset-0 bg-gradient-to-r from-purple-300 via-pink-300 to-blue-300 opacity-70 animate-gradient"></div>

                        <!-- Animated particles -->
                        <div class="absolute h-1 w-1 bg-white rounded-full top-1/4 left-1/3 animate-particle1"></div>
                        <div class="absolute h-1 w-1 bg-white rounded-full top-1/2 left-1/4 animate-particle2"></div>
                        <div class="absolute h-2 w-2 bg-white rounded-full top-3/4 left-1/2 animate-particle3"></div>
                        <div class="absolute h-1 w-1 bg-white rounded-full top-1/3 left-2/3 animate-particle2"></div>
                        <div class="absolute h-1.5 w-1.5 bg-white rounded-full top-2/3 left-3/4 animate-particle1"></div>

                        <!-- Animated drawing lines -->
                        <div class="absolute h-0.5 w-0 bg-white top-1/4 left-0 animate-drawLineH"></div>
                        <div class="absolute h-0 w-0.5 bg-white top-0 left-1/3 animate-drawLineV"></div>
                        <div class="absolute h-0.5 w-0 bg-white top-3/4 right-0 animate-drawLineH-reverse"></div>
                        <div class="absolute h-0 w-0.5 bg-white bottom-0 right-1/3 animate-drawLineV-reverse"></div>

                        <!-- Pulsing center -->
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-8 w-8 bg-purple-500 rounded-full opacity-75 animate-pulse"></div>
                      </div>
                    </div>
                    <span class="text-sm text-purple-500 font-medium">Creating magic...</span>
                  </div>
                  <div v-else-if="image.hasError || (image.imageId === -2 && !image.url)" class="flex flex-col items-center group relative w-full h-full bg-red-50">
                    <div class="h-full w-full flex flex-col items-center justify-center border-2 border-red-300 rounded">
                      <div class="flex items-center justify-center h-20 w-20 bg-red-100 rounded-full mb-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                      </div>
                      <span class="text-sm text-red-600 font-bold text-center">Generation Failed</span>
                      <span class="text-xs text-red-500 text-center mt-1">Hover for details</span>
                    </div>
                  </div>
                  <div v-else class="text-gray-400">
                    {{ image.size || '300 × 200' }}
                  </div>
                </div>

                <!-- Tags for image status -->
                <div class="absolute top-2 right-2 flex flex-col gap-1">
                  <!-- Draft tag (only for non-existing images) -->
                  <span v-if="!image.isExisting" class="bg-yellow-400 text-xs font-medium px-2 py-1 rounded">
                    Draft
                  </span>

                  <!-- Reference tag (when image is used as reference) -->
                  <span
                    v-if="!isReferenceSelectionMode && referenceImages.some(ref => ref.url === image.url)"
                    class="bg-blue-400 text-white text-xs font-medium px-2 py-1 rounded"
                  >
                    Reference
                  </span>

                  <!-- Error tag -->
                  <span
                    v-if="image.hasError || (image.imageId === -2 && !image.url)"
                    class="bg-red-500 text-white text-xs font-medium px-2 py-1 rounded"
                  >
                    Failed
                  </span>
                </div>

                <!-- Hover overlay - different for draft vs existing (hidden in selection mode) -->
                <div
                  v-if="!isReferenceSelectionMode && !image.hasError && image.imageId !== -2"
                  class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  :class="image.isExisting ? 'bg-blue-600 bg-opacity-70' : 'bg-purple-600 bg-opacity-70'"
                >
                  <div class="flex flex-col gap-2">
                    <button
                      v-if="!image.isExisting"
                      @click="addImageToAssets(image)"
                      class="bg-white font-medium px-4 py-2 rounded-lg shadow hover:bg-purple-50 transition-colors duration-200 text-purple-700"
                      :disabled="image.isGenerating || image.imageId === -1"
                      :class="{ 'opacity-50 cursor-not-allowed': image.isGenerating || image.imageId === -1 }"
                    >
                      Add to Assets
                    </button>
                    <button
                      v-if="image.isExisting"
                      @click="useInEmail(image)"
                      class="bg-white text-blue-700 font-medium px-4 py-2 rounded-lg shadow hover:bg-blue-50 transition-colors duration-200"
                      :disabled="image.isGenerating || image.imageId === -1"
                      :class="{ 'opacity-50 cursor-not-allowed': image.isGenerating || image.imageId === -1 }"
                    >
                      Use in Email
                    </button>
                    <button
                      @click.stop="openPreviewModal(image)"
                      class="bg-white text-gray-700 font-medium px-4 py-2 rounded-lg shadow hover:bg-gray-50 transition-colors duration-200"
                      :disabled="image.isGenerating || image.imageId === -1"
                      :class="{ 'opacity-50 cursor-not-allowed': image.isGenerating || image.imageId === -1 }"
                    >
                      Preview
                    </button>
                  </div>
                </div>

                <!-- Error state overlay - shows explanation on hover -->
                <div
                  v-if="!isReferenceSelectionMode && (image.hasError || image.imageId === -2)"
                  class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-black bg-opacity-80"
                >
                  <div class="text-white text-sm text-center max-w-xs p-4">
                    <p class="font-bold mb-2">Image Generation Failed</p>
                    <p>{{ image.errorMessage || 'The image failed to generate due to content policy restrictions. Try rephrasing your prompt with different wording.' }}</p>
                  </div>
                </div>
              </div>

              <div class="flex flex-col flex-grow">
               <div class="p-3 h-[82px]">
                 <h3 class="font-medium text-gray-800 line-clamp-2">{{ image.isExisting ? image.prompt : 'Generated Image' }}</h3>
                 <p class="text-xs text-gray-500 mt-1 truncate">{{ formatDate(image.createdAt) }}</p>
               </div>
               <div class="border-t border-gray-200 p-2 flex justify-between">
                 <div class="flex space-x-2">
                   <!-- Edit button - disabled for draft images -->
                   <button
                     @click="(image.isDraft || image.isGenerating || image.imageId === -1) ? null : openEditImageModal(image)"
                     class="text-gray-500 relative group"
                     :class="{
                       'hover:text-blue-700': image.isExisting && !image.isDraft && !image.isGenerating && image.imageId !== -1,
                       'hover:text-purple-700': !image.isExisting && !image.isDraft && !image.isGenerating && image.imageId !== -1,
                       'opacity-50 cursor-not-allowed': image.isDraft || image.isGenerating || image.imageId === -1
                     }"
                   >
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                       <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                     </svg>
                     <span class="absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-1 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                       {{ image.isGenerating || image.imageId === -1 ? 'Image is Generating' : (image.isDraft ? 'Save Image First' : 'Edit') }}
                     </span>
                   </button>

                   <!-- Download button -->
                   <button
                     @click="(image.isGenerating || image.imageId === -1) ? null : downloadImage(image)"
                     class="text-gray-500 relative group"
                     :class="{
                       'hover:text-blue-700': image.isExisting && !image.isGenerating && image.imageId !== -1,
                       'hover:text-purple-700': !image.isExisting && !image.isGenerating && image.imageId !== -1,
                       'opacity-50 cursor-not-allowed': image.isGenerating || image.imageId === -1
                     }"
                   >
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                       <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                     </svg>
                     <span class="absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-1 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                       Download
                     </span>
                   </button>

                   <!-- Add to Assets button (for Draft images) or Checkmark (for existing) -->
                   <button
                     v-if="!image.isExisting"
                     @click="(image.isGenerating || image.imageId === -1) ? null : addImageToAssets(image)"
                     class="text-gray-500 relative group"
                     :class="{
                       'hover:text-purple-700': !image.isGenerating && image.imageId !== -1,
                       'opacity-50 cursor-not-allowed': image.isGenerating || image.imageId === -1
                     }"
                     :disabled="image.isGenerating || image.imageId === -1 || image.id.startsWith('generating-')"
                   >
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                       <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                     </svg>
                     <span class="absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-1 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                       Add to Assets
                     </span>
                   </button>
                   <button
                     v-else
                     class="text-gray-500 hover:text-blue-700 relative group"
                   >
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                       <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                     </svg>
                     <span class="absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-1 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                       Added to Assets
                     </span>
                   </button>
                 </div>

                 <div class="flex space-x-2">
                   <!-- Add as Reference button -->
                   <button
                     @click="(image.isGenerating || image.imageId === -1 || image.imageId === -2 || image.hasError || (image.imageId === -2 && !image.url)) ? null : addAsReference(image)"
                     class="text-gray-500 relative group"
                     :class="{
                       'hover:text-blue-700': image.isExisting && !image.isGenerating && image.imageId !== -1 && image.imageId !== -2 && !image.hasError,
                       'hover:text-purple-700': !image.isExisting && !image.isGenerating && image.imageId !== -1 && image.imageId !== -2 && !image.hasError,
                       'opacity-50 cursor-not-allowed': image.isGenerating || image.imageId === -1 || image.imageId === -2 || image.hasError || (image.imageId === -2 && !image.url)
                     }"
                   >
                     <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4">
                       <!-- Left arrow pointing to a document/image -->
                       <path d="M9 12L5 8M5 8L9 4M5 8H13.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                       <path d="M14 14H17C18.6569 14 20 12.6569 20 11V7C20 5.34315 18.6569 4 17 4H14V14Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                       <path d="M14 14V19C14 19.5523 14.4477 20 15 20H19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                     </svg>
                     <span class="absolute bottom-full right-0 transform -translate-y-1 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                       Add as Reference
                     </span>
                   </button>

                   <!-- Delete button -->
                   <button
                     @click="(image.isGenerating && !image.hasError && image.imageId === -1) ? null : removeImage(index)"
                     class="text-gray-500 relative group"
                     :class="{
                       'hover:text-red-600': !image.isGenerating || image.hasError || image.imageId === -2,
                       'opacity-50 cursor-not-allowed': image.isGenerating && !image.hasError && image.imageId === -1
                     }"
                   >
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                       <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                     </svg>
                     <span class="absolute bottom-full right-0 transform -translate-y-1 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                       Remove
                     </span>
                   </button>
                 </div>
               </div>
             </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from '@vue/runtime-core';
import EditBrandImageModal from '../../components/EditBrandImageModal.vue';
import SelectImagesModal from '../../components/SelectImagesModal.vue';
import * as Utils from '../../../client-old/utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

interface GeneratedImage {
  id: string;
  url: string;
  prompt: string;
  style: string;
  size: string;
  createdAt: Date;
  isExisting?: boolean; // Flag for uploaded/existing images
  width?: number;
  height?: number;
  description?: string; // For AI usage description
  imageType?: string; // For AI image tag
  isDraft?: boolean; // Flag for draft images not yet saved to assets
  imageId?: string | number; // ID of the image in the assets system
  originalUrl?: string; // Original URL before processing
  isGenerating?: boolean; // Flag to show generation animation
  hasError?: boolean; // Flag to indicate generation error
  errorMessage?: string; // Error message for failed generations
}

// Helper function to get image dimensions
const getImageDimensions = (file: File): Promise<{width: number, height: number}> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height
        });
      };
      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };
      img.src = e.target?.result as string;
    };
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    reader.readAsDataURL(file);
  });
};

export default defineComponent({
  name: 'ImageGenerationTask',
  components: {
    EditBrandImageModal,
    SelectImagesModal
  },
  props: {
    taskId: {
      type: String,
      default: null
    },
    campaignId: {
      type: String,
      default: null
    }
  },
  setup(props, { emit }) {
    // Form state
    const prompt = ref('');
    const style = ref('vivid');
    const size = ref('portrait');
    const background = ref('opaque');
    const isGenerating = ref(false);
    const isCopied = ref(false);
    const showEditImageModal = ref(false);
    const showSelectImagesModal = ref(false);
    const showPreviewModal = ref(false);
    const imageToEdit = ref<any>(null);
    const previewImage = ref<GeneratedImage | null>(null);
    const isLoading = ref(false);

    // Reference selection mode state
    const isReferenceSelectionMode = ref(false);
    const selectedGalleryImages = ref<GeneratedImage[]>([]);

    const openPreviewModal = (image: GeneratedImage) => {
      previewImage.value = image;
      showPreviewModal.value = true;
    };

    const closePreviewModal = () => {
      showPreviewModal.value = false;
      previewImage.value = null;
    };

    // Reference images
    const referenceImages = ref<{url: string, file: File}[]>([]);
    const maxReferenceImages = 2;
    const fileInput = ref<HTMLInputElement | null>(null);

    // Generated images
    const generatedImages = ref<GeneratedImage[]>([]);
    const selectedImage = ref<GeneratedImage | null>(null);

    // Function to generate an image using the API
    const generateImage = async () => {
      if (!prompt.value.trim()) {
        handleStatusUpdate('error', 'Please enter a prompt');
        return;
      }

      isGenerating.value = true;

      // Create temporary placeholder
      const tempId = `generating-${Date.now()}`;
      const placeholderImage: GeneratedImage = {
        id: tempId,
        url: '', // Empty URL for placeholder
        prompt: prompt.value,
        style: style.value,
        size: size.value,
        createdAt: new Date(),
        isGenerating: true // Flag to show the animation
      };

      // Add placeholder to start of gallery
      generatedImages.value.unshift(placeholderImage);

      try {
        // Convert size value to API format
        let apiSize: '1024x1024' | '1024x1536' | '1536x1024' = '1024x1024';
        switch (size.value) {
          case 'portrait':
            apiSize = '1024x1536';
            break;
          case 'landscape':
            apiSize = '1536x1024';
            break;
          default:
            apiSize = '1024x1024';
        }

        // Call the appropriate image generation API
        const endpoint = referenceImages.value.length > 0 ?
          `${URL_DOMAIN}/imagegen/edit-image` :
          `${URL_DOMAIN}/imagegen/generate-image`;

        // Prepare the request body based on whether we're editing or generating
        const requestBody = referenceImages.value.length > 0 ? {
          prompt: prompt.value,
          images: referenceImages.value.map(img => img.url),
          size: apiSize,
          quality: 'high',
          n: 1,
          campaignId: props.campaignId // Include campaignId for edit-image too
        } : {
          prompt: prompt.value,
          quality: 'high',
          style: style.value,
          size: apiSize,
          n: 1,
          background: background.value === 'transparent' ? 'transparent' : 'opaque',
          campaignId: props.campaignId // Include campaignId if available
        };

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }

        const result = await response.json();
        console.log('Image generation response:', result);

        // Check if we got a placeholder ID (async generation)
        if (result.placeholderImageId) {
          console.log('Image generation started with placeholder ID:', result.placeholderImageId);

          // Update our local placeholder with the server-generated ID
          const index = generatedImages.value.findIndex(img => img.id === tempId);
          if (index !== -1) {
            generatedImages.value[index].id = result.placeholderImageId.toString();

            // Start polling for the image status
            pollImageStatus(result.placeholderImageId, index);
          }

          // Reset prompt field
          prompt.value = '';

          // Don't reset isGenerating yet - we'll do that when polling completes
          return;
        }

        // Handle immediate response (non-async generation)
        const generatedImageUrl = referenceImages.value.length > 0 ?
          result.editedImages[0]?.url :
          result.generatedImages[0]?.url;

        if (!generatedImageUrl) {
          throw new Error('No image URL in response');
        }

        // Find and update the placeholder
        const index = generatedImages.value.findIndex(img => img.id === tempId);
        if (index !== -1) {
          try {
            // Get dimensions from the generated image
            const img = new Image();
            await new Promise<void>((resolve) => {
              img.onload = () => {
                resolve();
              };
              img.onerror = () => {
                console.warn('Failed to load image for dimensions, using defaults');
                resolve();
              };
              img.src = generatedImageUrl;
            });

            // Use actual dimensions or defaults
            const width = img.width || 1024;
            const height = img.height || 1024;

            console.log('Generated image dimensions:', { width, height });

            // Update the placeholder with the generated image
            generatedImages.value[index] = {
              ...placeholderImage,
              url: generatedImageUrl,
              id: `img-${Date.now()}`, // Temporary ID until we save to planner-campaign-images
              isDraft: true, // Mark as draft image
              width: width,
              height: height,
              size: `${width}x${height}`,
              isGenerating: false // Animation is finished
            };
            selectedImage.value = generatedImages.value[index];

            // Save to planner-campaign-images if we have a campaign ID
            if (props.campaignId) {
              try {
                // Create a planner campaign image record for the draft image
                const plannerCampaignImageResponse = await fetch(`${URL_DOMAIN}/planner-campaigns/${props.campaignId}/planner-campaign-images`, {
                  method: 'POST',
                  headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                    // Omit imageId entirely for draft images
                    url: generatedImageUrl,
                    isDraft: true // This is a draft image
                  })
                });

                if (!plannerCampaignImageResponse.ok) {
                  console.error('Failed to save draft image to planner-campaign-images:', await plannerCampaignImageResponse.text());
                } else {
                  const plannerCampaignImage = await plannerCampaignImageResponse.json();
                  console.log('Saved draft image to planner-campaign-images:', plannerCampaignImage);

                  // Update the image ID in our gallery to match the planner-campaign-image ID
                  generatedImages.value[index].id = plannerCampaignImage.id.toString();
                }
              } catch (plannerError) {
                console.error('Error saving draft image to planner-campaign-images:', plannerError);
                // Don't throw here, we still want to show success for the image generation
              }
            }
          } catch (error) {
            console.error('Error processing generated image:', error);

            // Use default dimensions
            generatedImages.value[index] = {
              ...placeholderImage,
              url: generatedImageUrl,
              id: `img-${Date.now()}`, // Update with temporary ID
              isDraft: true, // Mark as draft image
              width: 1024,
              height: 1024,
              size: '1024x1024'
            };
            selectedImage.value = generatedImages.value[index];
          }
        }

        // Reset form and show success
        prompt.value = '';
        handleStatusUpdate('success', 'Image generated successfully');
      } catch (error) {
        console.error('Error generating image:', error);

        // Instead of removing the placeholder, update it to show an error state
        const index = generatedImages.value.findIndex(img => img.id === tempId);
        if (index !== -1) {
          generatedImages.value[index] = {
            ...generatedImages.value[index],
            hasError: true,
            errorMessage: error instanceof Error ? error.message : 'The image failed to generate due to content policy restrictions. Try rephrasing your prompt with different wording.',
            isGenerating: false,
            imageId: -2 // Mark as failed
          };
        }

        handleStatusUpdate('error', 'The image failed to generate due to content policy restrictions. Try rephrasing your prompt with different wording.');
      } finally {
        isGenerating.value = false;
      }
    };

    // Function to poll for image generation status (only use with numeric IDs)
    const pollImageStatus = async (placeholderId: number, imageIndex: number) => {
      // Set up polling interval (4 seconds)
      const pollInterval = 4000;
      let pollCount = 0;
      const maxPolls = 60; // Maximum number of polls (4 minutes total)

      const pollTimer = setInterval(async () => {
        try {
          pollCount++;

          // Check if we've reached the maximum number of polls
          if (pollCount > maxPolls) {
            clearInterval(pollTimer);
            handleStatusUpdate('error', 'Generation timed out. This may be due to content policy restrictions. Try rephrasing your prompt with different wording.');

            // Update the placeholder to show error instead of removing it
            const index = generatedImages.value.findIndex(img => img.id === placeholderId.toString());
            if (index !== -1) {
              generatedImages.value[index] = {
                ...generatedImages.value[index],
                hasError: true,
                errorMessage: 'Generation timed out. This may be due to content policy restrictions. Try rephrasing your prompt with different wording.',
                isGenerating: false,
                imageId: -2 // Mark as failed
              };
            }
            isGenerating.value = false;
            return;
          }

          // Check the status of the image generation
          const response = await fetch(`${URL_DOMAIN}/imagegen/status/${placeholderId}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });

          if (!response.ok) {
            console.error(`Error checking image status: ${response.status}`);
            return; // Continue polling
          }

          const result = await response.json();
          //console.log(`Poll ${pollCount}: Image generation status:`, result);

          // Check if the image is still generating
          if (result.status === 'generating') {
            return; // Continue polling
          }

          // Check if the image generation failed
          if (result.status === 'failed' || result.status === 'error') {
            clearInterval(pollTimer);
            handleStatusUpdate('error', 'The image failed to generate due to content policy restrictions. Try rephrasing your prompt with different wording.');

            // Update the placeholder to show error instead of removing it
            const index = generatedImages.value.findIndex(img => img.id === placeholderId.toString());
            if (index !== -1) {
              generatedImages.value[index] = {
                ...generatedImages.value[index],
                hasError: true,
                errorMessage: 'The image failed to generate due to content policy restrictions. Try rephrasing your prompt with different wording.',
                isGenerating: false,
                imageId: -2 // Mark as failed
              };
            }
            isGenerating.value = false;
            return;
          }

          // Check if the image is ready
          if (result.status === 'completed' && result.url) {
            clearInterval(pollTimer);

            // Find the placeholder in our gallery
            const index = generatedImages.value.findIndex(img => img.id === placeholderId.toString());
            if (index !== -1) {
              try {
                // Get dimensions from the generated image
                const img = new Image();
                await new Promise<void>((resolve) => {
                  img.onload = () => {
                    resolve();
                  };
                  img.onerror = () => {
                    console.warn('Failed to load image for dimensions, using defaults');
                    resolve();
                  };
                  img.src = result.url;
                });

                // Use actual dimensions or defaults
                const width = img.width || 1024;
                const height = img.height || 1024;

                // Update the placeholder with the generated image
                generatedImages.value[index] = {
                  ...generatedImages.value[index],
                  url: result.url,
                  isDraft: result.isDraft,
                  imageId: result.imageId,
                  width: width,
                  height: height,
                  size: `${width}x${height}`,
                  isGenerating: false // Animation is finished
                };
                selectedImage.value = generatedImages.value[index];

                handleStatusUpdate('success', 'Image generated successfully');
              } catch (error) {
                console.error('Error processing generated image:', error);

                // Update with basic info if we can't get dimensions
                generatedImages.value[index] = {
                  ...generatedImages.value[index],
                  url: result.url,
                  isDraft: result.isDraft,
                  imageId: result.imageId,
                  width: 1024,
                  height: 1024,
                  size: '1024x1024',
                  isGenerating: false // Animation is finished
                };
                selectedImage.value = generatedImages.value[index];

                handleStatusUpdate('success', 'Image generated successfully');
              }
            } else {
              console.warn('Placeholder not found in gallery:', placeholderId);
            }

            isGenerating.value = false;
            return;
          }

        } catch (error) {
          console.error('Error polling for image status:', error);
          // Continue polling despite errors
        }
      }, pollInterval);
    };

    const selectImage = (image: GeneratedImage) => {
      selectedImage.value = image;
    };

    const downloadImage = async (image?: GeneratedImage) => {
      const targetImage = image || selectedImage.value;
      if (!targetImage) return;

      try {
        handleStatusUpdate('loading', 'Downloading image...');

        let filename = `image-${targetImage.id || Date.now()}.png`;

        // CORS Workaround: Instead of fetching the image directly, we'll open it in a new tab
        // and let the user save it from there
        if (targetImage.url.includes('cloudfront.net')) {
          // For CloudFront images, we'll use a different approach to avoid CORS
          // Create a new window/tab with the image URL
          const newWindow = window.open(targetImage.url, '_blank');

          if (newWindow) {
            // Show instructions to the user
            handleStatusUpdate('info', 'Image opened in new tab. Right-click and select "Save image as..." to download.');
          } else {
            // If popup was blocked
            handleStatusUpdate('warning', 'Please allow popups and try again, or right-click on the image and select "Save image as..."');

            // As a fallback, try to trigger download directly
            // This might still fail due to CORS, but worth trying
            const link = document.createElement('a');
            link.href = targetImage.url;
            link.download = filename;
            link.target = '_blank';
            link.rel = 'noopener noreferrer';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
          return;
        }

        // For data URLs or URLs from our own domain, we can use the blob approach
        let blob: Blob;

        if (targetImage.url.startsWith('data:')) {
          // For data URLs, convert to blob
          try {
            // Extract the file type from the data URL if possible
            const fileTypeMatch = targetImage.url.match(/data:image\/([a-zA-Z0-9]+);/);
            if (fileTypeMatch && fileTypeMatch[1]) {
              filename = `image-${targetImage.id || Date.now()}.${fileTypeMatch[1]}`;
            }

            // Convert data URL to blob
            const response = await fetch(targetImage.url);
            blob = await response.blob();
          } catch (error) {
            console.error('Error converting data URL to blob:', error);
            throw new Error('Failed to convert image data');
          }
        } else if (targetImage.url.startsWith(URL_DOMAIN)) {
          // For images from our server, fetch with authentication
          try {
            const response = await fetch(targetImage.url, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              }
            });

            if (!response.ok) {
              throw new Error(`Failed to fetch image: ${response.status}`);
            }

            blob = await response.blob();

            // Get a better filename from the Content-Disposition header if available
            const contentDisposition = response.headers.get('Content-Disposition');
            if (contentDisposition) {
              const filenameMatch = contentDisposition.match(/filename="(.+)"/);
              if (filenameMatch && filenameMatch[1]) {
                filename = filenameMatch[1];
              }
            }
          } catch (error) {
            console.error('Error fetching image from server:', error);
            throw new Error('Failed to fetch image from server');
          }
        } else {
          // For other URLs, try to fetch directly
          try {
            const response = await fetch(targetImage.url);
            if (!response.ok) {
              throw new Error(`Failed to fetch image: ${response.status}`);
            }
            blob = await response.blob();
          } catch (error) {
            console.error('Error fetching image from URL:', error);
            throw new Error('Failed to fetch image');
          }
        }

        // Create a blob URL for the download
        const blobUrl = URL.createObjectURL(blob);

        // Create a temporary link to download the image
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = filename; // This is crucial for forcing a download
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(link);
          URL.revokeObjectURL(blobUrl);
        }, 100);

        handleStatusUpdate('success', 'Image downloaded successfully');
      } catch (error) {
        console.error('Error downloading image:', error);
        handleStatusUpdate('error', 'Failed to download image');
      }
    };

    const addAsReference = (image: GeneratedImage) => {
      // Check if we've reached the maximum number of reference images
      if (referenceImages.value.length >= maxReferenceImages) {
        handleStatusUpdate('warning', `You can only add up to ${maxReferenceImages} reference images`);
        return;
      }

      // Check if the image URL is a data URL - if so, issue a warning
      if (image.url.startsWith('data:')) {
        console.warn('Attempting to add a data URL as reference. This could cause performance issues.');
        handleStatusUpdate('warning', 'This image may be too large to use as a reference.');
      }

      // Add the image to reference images
      // Create a dummy File object to satisfy TypeScript
      const dummyFile = new File([], "dummy.png", { type: "image/png" });

      referenceImages.value.push({
        url: image.url,
        file: dummyFile // Using a dummy file to satisfy TypeScript
      });

      handleStatusUpdate('success', 'Image added as reference');
    };

    const removeImage = async (index: number) => {
      // Get the image to be removed
      const imageToRemove = generatedImages.value[index];

      if (!imageToRemove) {
        return;
      }

      try {
        handleStatusUpdate('loading', 'Removing image from gallery...');

        // First, determine what type of image we're dealing with
        if (props.campaignId) {
          // This is a planner-campaign-image
          if (imageToRemove.id) {
            // Delete the planner-campaign-image record using the where query parameter
            const whereFilter = encodeURIComponent(JSON.stringify({ id: parseInt(imageToRemove.id) }));
            const deleteResponse = await fetch(`${URL_DOMAIN}/planner-campaigns/${props.campaignId}/planner-campaign-images?where=${whereFilter}`, {
              method: 'DELETE',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
              }
            });

            if (!deleteResponse.ok) {
              console.error('Failed to delete planner-campaign-image:', await deleteResponse.text());
              handleStatusUpdate('error', 'Failed to remove image from gallery');
              return; // Don't remove from UI if the API call failed
            }

            console.log('Deleted planner-campaign-image successfully');

            // If this is a draft image (not saved to assets yet), we're done
            if (imageToRemove.isDraft) {
              handleStatusUpdate('success', 'Draft image removed from gallery');
            }
            // If this is an existing image with imageId, we don't delete the actual image from assets by default
            // as it might be used elsewhere
            else if (imageToRemove.imageId) {
              console.log('Image remains in assets with ID:', imageToRemove.imageId);
              handleStatusUpdate('success', 'Image removed from gallery (still available in assets)');
            }
          }
        } else if (imageToRemove.imageId && !imageToRemove.isDraft) {
          // If we're not in a campaign context but the image has an imageId and is NOT a draft,
          // we need to delete the actual image from the branding/images endpoint
          try {
            console.log('Deleting image from assets with ID:', imageToRemove.imageId);

            // Use the imageId (not the plannercampaignimage id) to delete the actual image
            const deleteImageResponse = await fetch(`${URL_DOMAIN}/branding/images/${imageToRemove.imageId}`, {
              method: 'DELETE',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
              }
            });

            if (deleteImageResponse.ok) {
              console.log('Deleted image from assets successfully');
              handleStatusUpdate('success', 'Image deleted successfully');
            } else {
              console.warn('Failed to delete image from assets:', await deleteImageResponse.text());
              handleStatusUpdate('warning', 'Failed to delete image from assets, but removed from gallery');
            }
          } catch (deleteError) {
            console.error('Error deleting image from assets:', deleteError);
            handleStatusUpdate('warning', 'Error deleting image from assets, but removed from gallery');
          }
        } else if (imageToRemove.isDraft) {
          // If this is a draft image, we don't need to delete anything from branding/images
          console.log('Draft image removed from gallery');
          handleStatusUpdate('success', 'Draft image removed from gallery');
        } else {
          // If we're not in a campaign context and the image doesn't have an imageId, just remove from UI
          console.log('No campaign ID or imageId, just removing from UI');
        }

        // Remove the image from the generatedImages array
        generatedImages.value.splice(index, 1);

        // If the removed image was the selected image, clear the selection
        if (selectedImage.value && selectedImage.value === imageToRemove) {
          selectedImage.value = null;
        }
      } catch (error) {
        console.error('Error removing image:', error);
        handleStatusUpdate('error', 'Failed to remove image from gallery');
      }
    };

    const copyImageUrl = () => {
      if (!selectedImage.value) return;

      navigator.clipboard.writeText(selectedImage.value.url)
        .then(() => {
          isCopied.value = true;
          setTimeout(() => {
            isCopied.value = false;
          }, 2000);
        })
        .catch(err => {
          console.error('Failed to copy URL:', err);
          alert('Failed to copy URL to clipboard');
        });
    };

    const useImage = () => {
      if (!selectedImage.value) return;

      // Emit event to parent component to use the selected image
      emit('use-image', selectedImage.value);
    };

    const useInEmail = (image: GeneratedImage) => {
      if (!image) return;

      // Create JSON object for the new image tag format
      const imageData = {
        name: image.prompt || 'Generated Image',
        url: image.url
      };

      // Emit event to parent component to use the image in email
      // The parent will format this as <image>{"name":"...", "url":"..."}</image>
      emit('use-in-email', {
        url: image.url,
        name: image.prompt || 'Generated Image',
        jsonData: JSON.stringify(imageData) // Add the stringified JSON data
      });

      handleStatusUpdate('success', 'Image added to email content');
    };

    // Edit Image Modal methods
    const openEditImageModal = (image: GeneratedImage) => {
      // If the image is undefined (which might happen with static examples),
      // create a placeholder image
      if (!image) {
        image = {
          id: `temp-${Date.now()}`, // Assign a temporary ID for new images
          url: `https://picsum.photos/seed/${Date.now()}/800/600`,
          prompt: 'Example image',
          style: 'realistic',
          size: 'square',
          createdAt: new Date()
        };
      }

      // Log the incoming image data
      console.log('Opening edit modal for image:', {
        id: image.id,
        isExisting: image.isExisting,
        url: image.url
      });

      // When selecting an existing image
      const description = image.description || (image.isExisting ? image.prompt : '');

      // For existing images, ensure we're using the correct ID
      // For non-draft images, use imageId if available, otherwise use id
      const imageId = image.isExisting ? (image.imageId || image.id) : null;
      console.log('Using image ID for edit modal:', imageId);

      // Ensure width and height are numbers with default values
      const width = image.width ? parseInt(image.width.toString()) : 1024;
      const height = image.height ? parseInt(image.height.toString()) : 1024;

      console.log('Image dimensions for edit modal:', { width, height });

      imageToEdit.value = {
        id: imageId, // Only set ID if it's an existing image
        imageId: image.imageId, // Pass the imageId separately to ensure we have it
        url: image.url,
        name: image.prompt.substring(0, 30) + (image.prompt.length > 30 ? '...' : ''),
        friendlyname: image.prompt.substring(0, 30) + (image.prompt.length > 30 ? '...' : ''),
        imageType: image.imageType || image.style || '',
        description: description,
        width: width,
        height: height,
        isDraft: image.isDraft // Pass the draft status
      };

      // Log the transformed data
      console.log('Transformed imageToEdit:', imageToEdit.value);
      showEditImageModal.value = true;
    };

    const closeEditImageModal = () => {
      showEditImageModal.value = false;
      imageToEdit.value = null;
    };

    const addImageToAssets = async (image: GeneratedImage) => {
      if (!image || !image.url || image.id.startsWith('generating-') || image.isGenerating || image.imageId === -1) {
        return;
      }

      try {
        handleStatusUpdate('loading', 'Adding image to assets...');

        // If this is a draft image (already in planner-campaign-images but not in assets)
        if (image.isDraft) {
          // We need to update the planner-campaign-image record to mark it as saved
          // First, we need to create an image record in the assets

          // Create metadata for the image
          const friendlyName = 'Generated Image';
          const defaultDescription = 'AI Generated image for use in email communications';

          // Check if URL is a data URL - if so, we need to upload it differently
          const isDataUrl = image.url.startsWith('data:');

          let serverUrl = image.url;

          // If it's a data URL, log that we're skipping to avoid payload issues
          if (isDataUrl) {
            console.warn('Image has a data URL. This should not happen for AI-generated images.');
            handleStatusUpdate('warning', 'Image appears to be in an incorrect format.');
            // Create a more accessible error message to help debug
            console.error('URL starts with:', image.url.substring(0, 30) + '...');
          }

          let metadataResponse;
          try {
            metadataResponse = await fetch(`${URL_DOMAIN}/branding/images/metadata`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                url: serverUrl, // Use the existing URL
                width: parseInt(image.width?.toString() || '1024'), // Ensure width is a number
                height: parseInt(image.height?.toString() || '1024'), // Ensure height is a number
                friendlyname: friendlyName,
                assetType: 'email',
                imageType: image.style || '',
                description: defaultDescription
              })
            });
          } catch (error) {
            // Check if it's a 413 error (Payload Too Large)
            if (error instanceof TypeError && error.message.includes('413')) {
              console.error('413 Payload Too Large error:', error);
              handleStatusUpdate('error', 'Image is too large to add to assets. Try a different image.');
              return;
            }
            throw error;
          }

          if (!metadataResponse.ok) {
            throw new Error(`Failed to save image metadata: ${metadataResponse.status}`);
          }

          // Get the database ID from the response
          const metadataResult = await metadataResponse.json();
          const databaseId = metadataResult.id;

          console.log('Image added to assets with database ID:', databaseId);

          // Update the image in our gallery
          const index = generatedImages.value.findIndex(img => img.id === image.id);
          if (index !== -1) {
            // Update the image with the new metadata
            generatedImages.value[index] = {
              ...image,
              isExisting: true,
              isDraft: false,
              imageId: databaseId,
              id: databaseId.toString() // Use the actual database ID
            };
          }

          // Now update the planner-campaign-image record if we have a campaign ID
          if (props.campaignId) {
            try {
              // Update the planner campaign image record using the where query parameter
              const whereFilter = encodeURIComponent(JSON.stringify({ id: parseInt(image.id) }));
              const updateResponse = await fetch(`${URL_DOMAIN}/planner-campaigns/${props.campaignId}/planner-campaign-images?where=${whereFilter}`, {
                method: 'PATCH',
                headers: {
                  'Authorization': `Bearer ${localStorage.getItem('token')}`,
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  imageId: databaseId,
                  url: "", // We're using imageId, so url is empty string
                  isDraft: false // No longer a draft
                })
              });

              if (!updateResponse.ok) {
                // If the PATCH fails, try using DELETE and POST as a fallback
                console.warn('Failed to update planner-campaign-image with PATCH, trying alternative approach');

                // Delete the existing record
                const whereFilter = encodeURIComponent(JSON.stringify({ id: parseInt(image.id) }));
                const deleteResponse = await fetch(`${URL_DOMAIN}/planner-campaigns/${props.campaignId}/planner-campaign-images?where=${whereFilter}`, {
                  method: 'DELETE',
                  headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                  }
                });

                if (!deleteResponse.ok) {
                  console.error('Failed to delete old planner-campaign-image:', await deleteResponse.text());
                }

                // Create a new record
                const createResponse = await fetch(`${URL_DOMAIN}/planner-campaigns/${props.campaignId}/planner-campaign-images`, {
                  method: 'POST',
                  headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                    imageId: databaseId,
                    url: "",
                    isDraft: false
                  })
                });

                if (!createResponse.ok) {
                  console.error('Failed to create new planner-campaign-image:', await createResponse.text());
                } else {
                  console.log('Created new planner-campaign-image successfully');
                }
              } else {
                console.log('Updated planner-campaign-image successfully');
              }
            } catch (error) {
              console.error('Error updating planner-campaign-image:', error);
              // Don't throw here, we still want to show success for the main save
            }
          }
        }
        // If this is an existing image with an imageId, we just need to create a planner-campaign-image record
        else if (image.imageId) {
          console.log('Image already exists in assets with ID:', image.imageId);

          // Just create a planner-campaign-image record if we have a campaign ID
          if (props.campaignId) {
            try {
              // Create a planner campaign image record
              const plannerCampaignImageResponse = await fetch(`${URL_DOMAIN}/planner-campaigns/${props.campaignId}/planner-campaign-images`, {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${localStorage.getItem('token')}`,
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  imageId: image.imageId,
                  url: "",
                  isDraft: false
                })
              });

              if (plannerCampaignImageResponse.ok) {
                const plannerCampaignImage = await plannerCampaignImageResponse.json();
                console.log('Saved to planner-campaign-images:', plannerCampaignImage);
              } else {
                console.error('Failed to save to planner-campaign-images:', await plannerCampaignImageResponse.text());
              }
            } catch (error) {
              console.error('Error saving to planner-campaign-images:', error);
            }
          }
        }
        // If this is neither a draft image nor an existing image with an imageId, something is wrong
        else {
          console.error('Image is neither a draft nor has an imageId:', image);
          handleStatusUpdate('error', 'Invalid image state');
          return;
        }

        handleStatusUpdate('success', 'Image added to assets successfully');
      } catch (error) {
        console.error('Error adding image to assets:', error);
        handleStatusUpdate('error', 'Failed to add image to assets');
      }
    };

    const saveEditedImage = async (imageData: any) => {
      try {
        if (!imageToEdit.value) {
          throw new Error('No image selected for editing');
        }

        console.log('Received image data from modal:', imageData);

        // Store the image data we need before closing the modal
        const oldId = imageToEdit.value.id;
        const oldImageId = imageToEdit.value.imageId;

        // Find the image in our gallery
        const index = generatedImages.value.findIndex(img => {
          // Match by ID first
          if (img.id === oldId) return true;
          // If we have an imageId, also try to match by that
          if (oldImageId && img.imageId === oldImageId) return true;
          return false;
        });

        if (index === -1) {
          console.warn('Image not found in gallery');
          return;
        }

        // Ensure width and height are numbers
        const width = imageData.width ? parseInt(imageData.width) : 1024;
        const height = imageData.height ? parseInt(imageData.height) : 1024;

        // Update the image in our gallery
        generatedImages.value[index] = {
          ...generatedImages.value[index],
          id: imageData.id || oldId,
          imageId: imageData.imageId || oldImageId,
          prompt: imageData.name,
          imageType: imageData.imageType,
          description: imageData.description,
          style: imageData.imageType,
          url: imageData.url || generatedImages.value[index].url,
          isExisting: true,
          width: width,
          height: height,
          isDraft: false
        };

        // If we have a campaign ID, update the planner-campaign-image
        if (props.campaignId) {
          try {
            // Always use PATCH to update the existing planner-campaign-image
            const updateResponse = await fetch(`${URL_DOMAIN}/planner-campaigns/${props.campaignId}/planner-campaign-images/${oldId}`, {
              method: 'PATCH',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                imageId: imageData.imageId || oldImageId,
                url: "",
                isDraft: false
              })
            });

            if (!updateResponse.ok) {
              console.error('Failed to update planner-campaign-image:', await updateResponse.text());
            } else {
              console.log('Updated planner-campaign-image successfully');
            }
          } catch (error) {
            console.error('Error updating planner-campaign-image:', error);
          }
        }

        closeEditImageModal();
        handleStatusUpdate('success', 'Image saved successfully');
      } catch (error) {
        console.error('Error saving image:', error);
        handleStatusUpdate('error', 'Failed to save image');
      }
    };

    const handleStatusUpdate = (status: string, message: string) => {
      console.log(`Status update: ${status} - ${message}`);

      // Emit the status update to the parent component
      emit('status-update', { status, message });

      // Also show a notification directly if possible
      try {
        // Check if we have access to the global notification system
        // Using any type to avoid TypeScript errors with custom window properties
        const win = window as any;
        if (win.$raleon && win.$raleon.notify) {
          win.$raleon.notify({
            title: status.charAt(0).toUpperCase() + status.slice(1),
            message: message,
            type: status === 'error' ? 'danger' :
                  status === 'success' ? 'success' :
                  status === 'warning' ? 'warning' : 'info'
          });
        }
      } catch (e) {
        console.warn('Could not show notification:', e);
      }
    };

    // Gallery methods
    const selectExistingImage = () => {
      // Open the select images modal
      showSelectImagesModal.value = true;
    };

    const closeSelectImagesModal = () => {
      showSelectImagesModal.value = false;
    };

    const addSelectedImages = async (images: any[]) => {
      // Convert the selected images to the format expected by generatedImages
      const newGalleryImages = images.map(image => {
        console.log('Adding selected image with ID:', image.id);
        return {
          id: image.id.toString(), // Ensure ID is a string
          url: image.url,
          prompt: image.friendlyname || 'Existing image',
          style: image.imageType || 'custom',
          size: `${image.width || '?'}x${image.height || '?'}`,
          createdAt: image.createdAt || new Date(),
          description: image.description || '',
          imageType: image.imageType || '',
          width: image.width,
          height: image.height,
          // Add a flag to indicate this is an existing image (not in draft state)
          isExisting: true
        };
      });

      // Add the selected images to the generatedImages array
      generatedImages.value.unshift(...newGalleryImages);

      // Save selected images to planner-campaign-images if we have a campaign ID
      if (props.campaignId) {
        for (const image of images) {
          try {
            // Create a planner campaign image record
            const plannerCampaignImageResponse = await fetch(`${URL_DOMAIN}/planner-campaigns/${props.campaignId}/planner-campaign-images`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                imageId: image.id,
                url: "", // We're using imageId, so url is empty string
                isDraft: false // This is a saved image, not a draft
              })
            });

            if (!plannerCampaignImageResponse.ok) {
              console.error('Failed to save selected image to planner-campaign-images:', await plannerCampaignImageResponse.text());
            } else {
              console.log('Saved selected image to planner-campaign-images:', await plannerCampaignImageResponse.json());
            }
          } catch (error) {
            console.error('Error saving selected image to planner-campaign-images:', error);
            // Don't throw, we still want to show success for the selection
          }
        }
      }

      // Show a success message
      handleStatusUpdate('success', `Added ${newGalleryImages.length} image${newGalleryImages.length !== 1 ? 's' : ''} to gallery`);
    };

    const openUploadDialog = () => {
      // This would typically open a file upload dialog
      if (fileInput.value) {
        fileInput.value.click();
      } else {
        // Fallback if fileInput ref is not available
        alert('Upload functionality would open here');
      }
    };

    const formatDate = (date: Date) => {
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    };

    // Reference selection mode methods
    const toggleReferenceSelectionMode = () => {
      // Toggle the selection mode
      isReferenceSelectionMode.value = !isReferenceSelectionMode.value;
      console.log('Reference selection mode toggled:', isReferenceSelectionMode.value);

      // When entering selection mode
      if (isReferenceSelectionMode.value) {
        // First clear any existing selections
        selectedGalleryImages.value = [];

        // Then pre-select any images that are already used as references
        if (referenceImages.value.length > 0) {
          // For each reference image, find the matching gallery image and select it
          referenceImages.value.forEach(refImage => {
            const matchingGalleryImage = generatedImages.value.find(galleryImage =>
              galleryImage.url === refImage.url
            );

            if (matchingGalleryImage) {
              console.log('Pre-selecting existing reference image:', matchingGalleryImage.id);
              selectedGalleryImages.value.push(matchingGalleryImage);
            }
          });

          console.log('Pre-selected gallery images:', selectedGalleryImages.value.length);
        }

        handleStatusUpdate('info', `Select up to ${maxReferenceImages} reference images`);
      } else {
        // When exiting selection mode, just reset the selection state
        // No need to update reference images as they were updated in real-time
        selectedGalleryImages.value = [];
      }
    };

    const toggleImageSelection = (image: GeneratedImage) => {
      if (!isReferenceSelectionMode.value) {
        console.log('Not in reference selection mode, ignoring click');
        return;
      }

      console.log('Toggling image selection for image:', image.id);

      // We now allow selecting up to maxReferenceImages total, regardless of how many are already in referenceImages
      const index = selectedGalleryImages.value.findIndex(img => img.id === image.id);

      if (index === -1) {
        // If not already selected and we haven't reached the limit, add it
        if (selectedGalleryImages.value.length < maxReferenceImages) {
          selectedGalleryImages.value.push(image);
          console.log('Image added to selection, total selected:', selectedGalleryImages.value.length);

          // Immediately add to reference images
          const dummyFile = new File([], "dummy.png", { type: "image/png" });
          referenceImages.value.push({
            url: image.url,
            file: dummyFile
          });
        } else {
          handleStatusUpdate('warning', `You can only select up to ${maxReferenceImages} reference images total`);
        }
      } else {
        // If already selected, remove it
        selectedGalleryImages.value.splice(index, 1);
        console.log('Image removed from selection, total selected:', selectedGalleryImages.value.length);

        // Immediately remove from reference images
        const refIndex = referenceImages.value.findIndex(ref => ref.url === image.url);
        if (refIndex !== -1) {
          referenceImages.value.splice(refIndex, 1);
        }
      }
    };

    // Reference selection mode is now handled directly in toggleReferenceSelectionMode
    // and toggleImageSelection functions

    // Legacy file upload method (keeping for backward compatibility)
    const openFileSelector = () => {
      if (fileInput.value) {
        fileInput.value.click();
      }
    };

    const handleFileSelect = async (event: Event) => {
      const input = event.target as HTMLInputElement;
      if (!input.files?.length) return;

      try {
        const fileCount = input.files.length;
        handleStatusUpdate('loading', `Uploading ${fileCount} image${fileCount !== 1 ? 's' : ''}...`);

        // Process all files
        for (let i = 0; i < input.files.length; i++) {
          const file = input.files[i];

          // Get image dimensions
          const dimensions = await getImageDimensions(file);

          // Create form data
          const formData = new FormData();
          formData.append('file', file);

          // Create URL with query parameters
          // Create initial description from filename
          const defaultName = file.name.split('.')[0];
          const defaultDescription = `Image "${defaultName}" for use in email communications`;

          const queryParams = new URLSearchParams({
            name: defaultName,
            imageType: '', // Default to no type
            assetType: 'email',
            description: defaultDescription,
            width: dimensions.width.toString(),
            height: dimensions.height.toString(),
            temp: 'true' // Mark as temporary to prevent duplicate database entries
          });

          const uploadUrl = `${URL_DOMAIN}/branding/image/upload?${queryParams.toString()}`;

          const response = await fetch(uploadUrl, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
            body: formData,
          });

          if (response.ok) {
            const imageUrl = await response.text();

            // Now we need to get the actual database ID for this image
            // First, we'll create metadata for the image to get its database ID
            const metadataResponse = await fetch(`${URL_DOMAIN}/branding/images/metadata`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                url: imageUrl,
                width: parseInt(dimensions.width.toString()), // Ensure width is a number
                height: parseInt(dimensions.height.toString()), // Ensure height is a number
                friendlyname: defaultName,
                assetType: 'email',
                imageType: '',
                description: defaultDescription
              })
            });

            if (!metadataResponse.ok) {
              throw new Error(`Failed to save image metadata: ${metadataResponse.status}`);
            }

            // Get the database ID from the response
            const metadataResult = await metadataResponse.json();
            const databaseId = metadataResult.id;

            console.log('Image uploaded with database ID:', databaseId);

            // Add uploaded image to gallery
            const newImage: GeneratedImage = {
              id: databaseId.toString(), // Use the actual database ID
              url: imageUrl,
              prompt: defaultName,
              style: 'custom',
              imageType: '', // Initially no type selected
              description: defaultDescription,
              size: `${dimensions.width}x${dimensions.height}`,
              createdAt: new Date(),
              isExisting: true, // Mark as existing since it's uploaded
              width: dimensions.width,
              height: dimensions.height
            };

            generatedImages.value.unshift(newImage);

            // Now save to planner-campaign-images if we have a campaign ID
            if (props.campaignId) {
              try {
                // Create a planner campaign image record
                const plannerCampaignImageResponse = await fetch(`${URL_DOMAIN}/planner-campaigns/${props.campaignId}/planner-campaign-images`, {
                  method: 'POST',
                  headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                    imageId: databaseId,
                    url: "", // We're using imageId, so url is empty string
                    isDraft: false // This is a saved image, not a draft
                  })
                });

                if (!plannerCampaignImageResponse.ok) {
                  console.error('Failed to save to planner-campaign-images:', await plannerCampaignImageResponse.text());
                } else {
                  const plannerCampaignImage = await plannerCampaignImageResponse.json();
                  console.log('Saved to planner-campaign-images:', plannerCampaignImage);
                }
              } catch (plannerError) {
                console.error('Error saving to planner-campaign-images:', plannerError);
                // Don't throw here, we still want to show success for the main image upload
              }
            }

            // Update status for each successful upload
            if (i < input.files.length - 1) {
              handleStatusUpdate('loading', `Uploaded ${i+1}/${fileCount} images, continuing...`);
            }
          } else {
            const errorText = await response.text();
            console.error(`Upload failed for ${file.name}:`, errorText);
            handleStatusUpdate('error', `Failed to upload image ${file.name}`);
          }
        }

        // Final success message after all uploads
        handleStatusUpdate('success', `Successfully uploaded ${fileCount} image${fileCount !== 1 ? 's' : ''}`);
      } catch (error) {
        console.error('Error uploading images:', error);
        handleStatusUpdate('error', 'Failed to upload one or more images');
      } finally {
        // Reset input
        input.value = '';
      }
    };

    const removeReferenceImage = (index: number) => {
      console.log('Removing reference image at index:', index);

      // Get the URL of the image being removed
      const imageUrl = referenceImages.value[index].url;

      // Remove from reference images array
      referenceImages.value.splice(index, 1);
      console.log('Reference images after removal:', referenceImages.value.length);

      // Also unselect it from the gallery if we're in reference selection mode
      if (isReferenceSelectionMode.value) {
        const galleryIndex = selectedGalleryImages.value.findIndex(img => img.url === imageUrl);
        if (galleryIndex !== -1) {
          selectedGalleryImages.value.splice(galleryIndex, 1);
        }
      }

      // Find and update the corresponding gallery image to show it's no longer selected
      const galleryImage = generatedImages.value.find(img => img.url === imageUrl);
      if (galleryImage) {
        // No direct action needed here as the UI will update based on the referenceImages array
        // The UI checks if an image URL is in referenceImages to determine if it should show as selected
      }
    };

    // Function to fetch planner campaign images
    const fetchPlannerCampaignImages = async () => {
      if (!props.campaignId) {
        console.log('No campaign ID provided, skipping image fetch');
        return;
      }

      try {
        isLoading.value = true;
        handleStatusUpdate('loading', 'Loading saved images...');

        const response = await fetch(`${URL_DOMAIN}/planner-campaigns/${props.campaignId}/planner-campaign-images`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch images: ${response.status}`);
        }

        const plannerImages = await response.json();
        console.log('Fetched planner campaign images:', plannerImages);

        // Convert planner campaign images to the format expected by generatedImages
        const fetchedImages = await Promise.all(plannerImages.map(async (image: any) => {
          // Determine if this is a draft image or a saved image
          const isDraft = image.isDraft || false;

          // If we have an imageId, fetch the image details to get more metadata
          interface ImageDetails {
            width?: number;
            height?: number;
            url?: string;
            friendlyname?: string;
            imageType?: string;
            createdAt?: string;
            description?: string;
          }

          let imageDetails: ImageDetails | null = null;
          if (image.imageId && !isDraft) {
            try {
              const imageResponse = await fetch(`${URL_DOMAIN}/branding/images/${image.imageId}`, {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${localStorage.getItem('token')}`,
                  'Content-Type': 'application/json'
                }
              });

              if (imageResponse.ok) {
                imageDetails = await imageResponse.json() as ImageDetails;
                console.log('Fetched image details:', imageDetails);
              }
            } catch (error) {
              console.error('Error fetching image details:', error);
            }
          }

          // Ensure width and height are numbers with default values
          const width = imageDetails?.width ? parseInt(imageDetails.width.toString()) : 1024;
          const height = imageDetails?.height ? parseInt(imageDetails.height.toString()) : 1024;

          return {
            id: image.id.toString(),
            url: isDraft ? image.url : (imageDetails?.url || ''), // Use the URL from the image details for non-draft images
            prompt: imageDetails?.friendlyname || 'Saved Image',
            style: imageDetails?.imageType || 'custom',
            size: width && height ? `${width}x${height}` : 'custom',
            createdAt: imageDetails?.createdAt ? new Date(imageDetails.createdAt) : new Date(),
            isExisting: !isDraft, // If not draft, it's an existing image
            imageId: image.imageId,
            isDraft: isDraft,
            width: width,
            height: height,
            description: imageDetails?.description
          };
        }));

        // Add the fetched images to the gallery
        if (fetchedImages.length > 0) {
          generatedImages.value = [...fetchedImages, ...generatedImages.value];
          handleStatusUpdate('success', `Loaded ${fetchedImages.length} saved image(s)`);
        }
      } catch (error) {
        console.error('Error fetching planner campaign images:', error);
        handleStatusUpdate('error', 'Failed to load saved images');
      } finally {
        isLoading.value = false;
      }
    };

    // Function to check for pending image generations
    const checkPendingImageGenerations = async () => {
      if (!props.campaignId) {
        return;
      }

      try {
        // Fetch all planner campaign images
        const response = await fetch(`${URL_DOMAIN}/planner-campaigns/${props.campaignId}/planner-campaign-images`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch images: ${response.status}`);
        }

        const plannerImages = await response.json();

        // Look for images with imageId === -1 (still generating)
        const pendingImages = plannerImages.filter((image: any) => image.imageId === -1);

        if (pendingImages.length > 0) {
          console.log('Found pending image generations:', pendingImages);

          // For each pending image, start polling for its status
          pendingImages.forEach((image: any) => {
            // Add a placeholder to the UI
            const placeholderImage: GeneratedImage = {
              id: image.id.toString(),
              url: '', // Empty URL for placeholder
              prompt: 'Image being generated...',
              style: 'vivid',
              size: 'square',
              createdAt: new Date(),
              isDraft: true,
              imageId: -1,
              isGenerating: true // Explicitly mark as generating for visual feedback
            };

            // Add placeholder to start of gallery if it doesn't already exist
            if (!generatedImages.value.some(img => img.id === image.id.toString())) {
              generatedImages.value.unshift(placeholderImage);
            }

            // Start polling for the image status
            pollImageStatus(image.id, 0);
          });

          // Set isGenerating to true if we have pending images
          if (pendingImages.length > 0) {
            isGenerating.value = true;
          }
        }
      } catch (error) {
        console.error('Error checking for pending image generations:', error);
      }
    };

    // Call the fetch function when the component is mounted
    onMounted(() => {
      fetchPlannerCampaignImages().then(() => {
        // After loading saved images, check for any pending generations
        checkPendingImageGenerations();
      });
    });

    return {
      prompt,
      style,
      size,
      background,
      isGenerating,
      isCopied,
      isLoading,
      showPreviewModal,
      previewImage,
      openPreviewModal,
      closePreviewModal,
      referenceImages,
      maxReferenceImages,
      fileInput,
      openFileSelector,
      handleFileSelect,
      removeReferenceImage,
      generatedImages,
      selectedImage,
      generateImage,
      selectImage,
      downloadImage,
      copyImageUrl,
      useImage,
      useInEmail,
      formatDate,
      selectExistingImage,
      closeSelectImagesModal,
      addSelectedImages,
      openUploadDialog,
      addAsReference,
      removeImage,
      // Edit Image Modal
      showEditImageModal,
      showSelectImagesModal,
      imageToEdit,
      openEditImageModal,
      closeEditImageModal,
      saveEditedImage,
      handleStatusUpdate,
      addImageToAssets,
      fetchPlannerCampaignImages,
      checkPendingImageGenerations,
      pollImageStatus,
      // Reference selection mode
      isReferenceSelectionMode,
      selectedGalleryImages,
      toggleReferenceSelectionMode,
      toggleImageSelection
    };
  }
});
</script>

<style scoped>
.raleon-loader .rtop {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Animation for color gradient movement */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Animations for particles floating */
@keyframes particle1 {
  0%, 100% {
    transform: translateY(0) translateX(0);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-6px) translateX(3px);
    opacity: 1;
  }
  50% {
    transform: translateY(-2px) translateX(6px);
    opacity: 0.6;
  }
  75% {
    transform: translateY(3px) translateX(-2px);
    opacity: 0.9;
  }
}

@keyframes particle2 {
  0%, 100% {
    transform: translateY(0) translateX(0);
    opacity: 0.7;
  }
  25% {
    transform: translateY(4px) translateX(6px);
    opacity: 0.9;
  }
  50% {
    transform: translateY(8px) translateX(2px);
    opacity: 1;
  }
  75% {
    transform: translateY(4px) translateX(-2px);
    opacity: 0.6;
  }
}

@keyframes particle3 {
  0%, 100% {
    transform: translateY(0) translateX(0);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-8px) translateX(-4px);
    opacity: 1;
  }
  50% {
    transform: translateY(-2px) translateX(-8px);
    opacity: 0.8;
  }
  75% {
    transform: translateY(5px) translateX(-2px);
    opacity: 0.7;
  }
}

/* Animations for drawing lines */
@keyframes drawLineH {
  0% {
    width: 0;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  50%, 100% {
    width: 100%;
    opacity: 0.3;
  }
}

@keyframes drawLineV {
  0% {
    height: 0;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  50%, 100% {
    height: 100%;
    opacity: 0.3;
  }
}

@keyframes drawLineH-reverse {
  0% {
    width: 0;
    right: 0;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  50%, 100% {
    width: 100%;
    right: 0;
    opacity: 0.3;
  }
}

@keyframes drawLineV-reverse {
  0% {
    height: 0;
    bottom: 0;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  50%, 100% {
    height: 100%;
    bottom: 0;
    opacity: 0.3;
  }
}

/* Apply the animations to elements */
.animate-gradient {
  background-size: 300% 300%;
  animation: gradient 8s ease infinite;
}

.animate-particle1 {
  animation: particle1 6s ease-in-out infinite;
}

.animate-particle2 {
  animation: particle2 8s ease-in-out infinite;
}

.animate-particle3 {
  animation: particle3 7s ease-in-out infinite;
}

.animate-drawLineH {
  animation: drawLineH 4s ease-out infinite;
  animation-delay: 0.5s;
}

.animate-drawLineV {
  animation: drawLineV 4s ease-out infinite;
  animation-delay: 1s;
}

.animate-drawLineH-reverse {
  animation: drawLineH-reverse 4s ease-out infinite;
  animation-delay: 1.5s;
}

.animate-drawLineV-reverse {
  animation: drawLineV-reverse 4s ease-out infinite;
  animation-delay: 2s;
}
</style>
