<template>
  <div
    v-if="show"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="$emit('close')"
  >
    <div class="bg-white rounded-xl shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Edit Image</h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Image Preview -->
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3 text-sm text-gray-600 mb-4">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <span class="font-medium">Selected Image:</span>
        </div>
        <div class="bg-gray-50 rounded-lg p-3 max-h-32 overflow-hidden">
          <img
            :src="imageUrl"
            :alt="imageAlt"
            class="max-w-full h-auto rounded border border-gray-200"
            style="max-height: 100px;"
          />
        </div>
      </div>

      <!-- Mode Toggle -->
      <div class="p-6 border-b border-gray-200">
        <div class="text-sm font-medium text-gray-700 mb-3">What changes would you like to make to the selected image?</div>
        <div class="inline-flex bg-gray-100 rounded-lg p-1">
          <button
            @click="editMode = 'variation'"
            :class="[
              'px-3 py-2 text-sm font-medium rounded-md transition-all duration-200',
              editMode === 'variation'
                ? 'bg-white text-purple-700 shadow-sm'
                : 'text-gray-600 hover:text-purple-600'
            ]"
          >
            Create Variation
          </button>
          <button
            @click="editMode = 'text'"
            :class="[
              'px-3 py-2 text-sm font-medium rounded-md transition-all duration-200',
              editMode === 'text'
                ? 'bg-white text-purple-700 shadow-sm'
                : 'text-gray-600 hover:text-purple-600'
            ]"
          >
            Edit Text
          </button>
          <button
            @click="editMode = 'switch'"
            :class="[
              'px-3 py-2 text-sm font-medium rounded-md transition-all duration-200',
              editMode === 'switch'
                ? 'bg-white text-purple-700 shadow-sm'
                : 'text-gray-600 hover:text-purple-600'
            ]"
          >
            Switch Image
          </button>
        </div>
      </div>

      <!-- Content -->
      <div class="p-6">
        <!-- Create Variation Mode -->
        <div v-if="editMode === 'variation'">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Describe the changes you want to make:
          </label>
          <textarea
            v-model="variationDescription"
            placeholder="e.g., Make the image brighter, change the background color to blue, add more contrast..."
            class="w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
          ></textarea>
        </div>

        <!-- Switch Image Mode -->
        <div v-else-if="editMode === 'switch'">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Describe the type of image you'd like instead:
          </label>
          <textarea
            v-model="switchImageDescription"
            placeholder="e.g., a professional headshot instead, a nature landscape, a product photo on white background..."
            class="w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
          ></textarea>
        </div>

        <!-- Edit Text Mode -->
        <div v-else-if="editMode === 'text'">
          <!-- Collapsible Detect Text Section -->
          <div class="mb-4">
            <button
              @click="toggleDetectedTextSection"
              class="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
            >
              <div class="flex items-center space-x-2">
                <span>AI Detected Text in Image</span>
                <div v-if="isDetectingText" class="flex items-center space-x-1 text-blue-600">
                  <svg class="animate-spin h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="text-xs">Detecting text...</span>
                </div>
                <div v-else-if="detectedTextElements.length > 0" class="inline-flex items-center px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full">
                  {{ detectedTextElements.length }} found
                </div>
                <div v-else-if="hasDetectedText" class="inline-flex items-center px-2 py-0.5 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                  None found
                </div>
              </div>
              <svg 
                :class="['h-4 w-4 transition-transform duration-200', showDetectedTextSection ? 'rotate-180' : '']"
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            
            <!-- Collapsible Content -->
            <div 
              v-show="showDetectedTextSection" 
              class="mt-3 transition-all duration-300 ease-in-out overflow-hidden"
            >
              <!-- Detecting State -->
              <div v-if="isDetectingText" class="p-4 bg-blue-50 rounded-lg border border-blue-200 text-center">
                <div class="flex items-center justify-center space-x-2 text-blue-700">
                  <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="font-medium">Analyzing image for text...</span>
                </div>
                <p class="text-sm text-blue-600 mt-1">This usually takes a few seconds</p>
              </div>
              
              <!-- Detected Text Options -->
              <div v-else-if="detectedTextElements.length > 0">
                <p class="text-xs text-gray-600 mb-2">Click any text to auto-fill an edit field:</p>
                <div class="flex flex-wrap gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <button
                    v-for="(element, index) in detectedTextElements"
                    :key="`detected-${index}`"
                    @click="selectDetectedText(element.text)"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium bg-white border border-blue-300 text-blue-800 rounded-full hover:bg-blue-100 transition-colors duration-150"
                    :title="`${element.description} (${element.confidence} confidence)`"
                  >
                    "{{ element.text }}"
                  </button>
                </div>
              </div>
              
              <!-- No Text Found Message -->
              <div v-else-if="hasDetectedText && detectedTextElements.length === 0" class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center space-x-2">
                  <svg class="h-4 w-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.664-.833-2.464 0L4.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                  <div>
                    <p class="text-sm text-yellow-800 font-medium">No text detected in this image</p>
                    <p class="text-xs text-yellow-700">You can manually enter text in the fields below</p>
                  </div>
                </div>
              </div>
              
              <!-- Error Message -->
              <div v-else-if="textDetectionError" class="p-3 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center space-x-2">
                  <svg class="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <div>
                    <p class="text-sm text-red-800 font-medium">Failed to detect text</p>
                    <p class="text-xs text-red-700">{{ textDetectionError }}</p>
                    <button 
                      @click="detectText" 
                      class="text-xs text-red-800 underline hover:no-underline mt-1"
                    >
                      Try again
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <div
              v-for="(edit, index) in textEdits"
              :key="`edit-${index}`"
              class="bg-gray-50 rounded-lg p-4"
            >
              <div class="flex items-center justify-between mb-3">
                <span class="text-sm font-medium text-gray-700">Edit {{ index + 1 }}</span>
                <button
                  v-if="textEdits.length > 1"
                  @click="removeTextEdit(index)"
                  class="text-red-500 hover:text-red-700 transition-colors"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                </button>
              </div>

              <div class="space-y-3">
                <div>
                  <label class="block text-xs font-medium text-gray-600 mb-1">Text currently in the image:</label>
                  <input
                    v-model="edit.existingText"
                    type="text"
                    placeholder="Enter the exact text you see in the image"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-600 mb-1">New text:</label>
                  <input
                    v-model="edit.newText"
                    type="text"
                    placeholder="Text to replace it with"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
              </div>
            </div>

            <!-- Add Edit Button -->
            <button
              v-if="textEdits.length < 5"
              @click="addTextEdit"
              class="w-full py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-purple-300 hover:text-purple-600 transition-colors flex items-center justify-center space-x-2"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              <span>Add Another Edit</span>
            </button>

            <div v-if="textEdits.length >= 5" class="text-xs text-gray-500 text-center">
              Maximum of 5 text edits allowed
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="px-6 py-4 bg-gray-50 rounded-b-xl flex justify-end space-x-3">
        <button
          @click="$emit('close')"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          @click="handleSubmit"
          :disabled="!canSubmit"
          class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          {{ editMode === 'variation' ? 'Create Variation' : editMode === 'switch' ? 'Switch Image' : 'Apply Text Changes' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from '@vue/runtime-core';
import * as Utils from '../../../client-old/utils/Utils';

interface TextEdit {
  existingText: string;
  newText: string;
}

interface DetectedTextElement {
  text: string;
  description: string;
  confidence: string;
}

export default defineComponent({
  name: 'ImageEditModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    imageUrl: {
      type: String,
      required: true
    },
    imageAlt: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'submit'],
  setup(props, { emit }) {
    const editMode = ref<'variation' | 'text' | 'switch'>('variation');
    const variationDescription = ref('');
    const switchImageDescription = ref('');
    const textEdits = ref<TextEdit[]>([
      { existingText: '', newText: '' }
    ]);

    // Text detection state
    const isDetectingText = ref(false);
    const detectedTextElements = ref<DetectedTextElement[]>([]);
    const hasDetectedText = ref(false);
    const textDetectionError = ref('');
    const showDetectedTextSection = ref(false);

    // Reset form when modal opens/closes
    watch(() => props.show, (newShow) => {
      if (newShow) {
        // Reset to default state when opening
        editMode.value = 'variation';
        variationDescription.value = '';
        switchImageDescription.value = '';
        textEdits.value = [{ existingText: '', newText: '' }];
        // Reset text detection state
        detectedTextElements.value = [];
        hasDetectedText.value = false;
        textDetectionError.value = '';
        isDetectingText.value = false;
        showDetectedTextSection.value = false;
      }
    });

    // Auto-detect text when switching to text mode
    watch(() => editMode.value, (newMode) => {
      if (newMode === 'text' && props.show) {
        // Auto-start text detection
        showDetectedTextSection.value = true;
        detectText();
      }
    });

    const canSubmit = computed(() => {
      if (editMode.value === 'variation') {
        return variationDescription.value.trim().length > 0;
      } else if (editMode.value === 'switch') {
        return switchImageDescription.value.trim().length > 0;
      } else {
        // At least one text edit must have both fields filled
        return textEdits.value.some(edit =>
          edit.existingText.trim().length > 0 && edit.newText.trim().length > 0
        );
      }
    });

    const addTextEdit = () => {
      if (textEdits.value.length < 5) {
        textEdits.value.push({ existingText: '', newText: '' });
      }
    };

    const removeTextEdit = (index: number) => {
      if (textEdits.value.length > 1) {
        textEdits.value.splice(index, 1);
      }
    };

    const handleSubmit = () => {
      if (!canSubmit.value) return;

      const submitData = {
        imageUrl: props.imageUrl,
        editMode: editMode.value,
        variationDescription: editMode.value === 'variation' ? variationDescription.value.trim() : null,
        switchImageDescription: editMode.value === 'switch' ? switchImageDescription.value.trim() : null,
        textEdits: editMode.value === 'text' ? textEdits.value.filter(edit =>
          edit.existingText.trim().length > 0 && edit.newText.trim().length > 0
        ) : null
      };

      emit('submit', submitData);
      emit('close');
    };

    // Detect text in image using AI
    const detectText = async () => {
      if (isDetectingText.value || !props.imageUrl) return;

      isDetectingText.value = true;
      textDetectionError.value = '';

      try {
        const response = await fetch(`${Utils.URL_DOMAIN}/images/detect-text`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            imageUrl: props.imageUrl
          })
        });

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }

        const result = await response.json();
        detectedTextElements.value = result.textElements || [];
        hasDetectedText.value = true;

      } catch (error) {
        console.error('Error detecting text:', error);
        textDetectionError.value = 'Failed to detect text. Please try again or enter text manually.';
        detectedTextElements.value = [];
        hasDetectedText.value = true;
      } finally {
        isDetectingText.value = false;
      }
    };

    // Select detected text and populate the first empty field or create new edit
    const selectDetectedText = (text: string) => {
      // Find the first text edit that doesn't have existing text filled
      const emptyEditIndex = textEdits.value.findIndex(edit => edit.existingText.trim() === '');

      if (emptyEditIndex !== -1) {
        // Fill the empty field
        textEdits.value[emptyEditIndex].existingText = text;
      } else {
        // All fields are filled, add a new edit if under the limit
        if (textEdits.value.length < 5) {
          textEdits.value.push({ existingText: text, newText: '' });
        } else {
          // Replace the last edit
          textEdits.value[textEdits.value.length - 1].existingText = text;
        }
      }
    };

    // Toggle the collapsed section for detected text
    const toggleDetectedTextSection = () => {
      showDetectedTextSection.value = !showDetectedTextSection.value;
    };

    return {
      editMode,
      variationDescription,
      switchImageDescription,
      textEdits,
      canSubmit,
      addTextEdit,
      removeTextEdit,
      handleSubmit,
      // Text detection
      isDetectingText,
      detectedTextElements,
      hasDetectedText,
      textDetectionError,
      showDetectedTextSection,
      detectText,
      selectDetectedText,
      toggleDetectedTextSection
    };
  }
});
</script>
