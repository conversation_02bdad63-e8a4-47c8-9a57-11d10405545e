<template>
  <div class="fixed inset-0 top-0 bg-gray-900 bg-opacity-30 z-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-lg mx-4">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Re-sync Campaign to Klaviyo</h3>
      <p class="text-gray-600 mb-6">
        This will replace your existing campaign in Klaviyo with the campaign currently configured in Raleon. Choose how you want to resync:
      </p>

      <div class="space-y-4 mb-6">
        <div>
          <PrimaryButton cta="Resync Editable Campaign" size="xs" @click="emitResync('hybrid')" />
          <p class="text-xs text-gray-600 mt-2 leading-relaxed">Updates campaign with draggable regions that can be edited in Klaviyo's visual editor</p>
        </div>

        <div class="pt-2 border-t border-gray-100">
          <LightSecondaryButton cta="Resync HTML Only" @click="emitResync('html')" />
          <p class="text-xs text-gray-500 mt-2 leading-relaxed"><span class="font-medium">Having trouble?</span> Use HTML only mode for basic resync.<br />Email will only be editable via code in Klaviyo.</p>
        </div>
      </div>

      <div class="flex justify-center">
        <button @click="emit('close')" class="px-6 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">Cancel</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PrimaryButton from '../../PrimaryButton.ts.vue';
import LightSecondaryButton from '../../LightSecondaryButton.ts.vue';

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'resync', variant: 'hybrid' | 'html'): void;
}>();

function emitResync(variant: 'hybrid' | 'html') {
  emit('resync', variant);
}
</script>

<style scoped></style>
