<template>
  <span class="text-xs px-2 py-1 rounded-full" :class="classes">{{ displayLabel }}</span>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  state: 'complete' | 'pending';
  labels?: Record<'complete' | 'pending', string>;
}

const props = defineProps<Props>();

const displayLabel = computed(() => {
  if (props.labels) return props.labels[props.state];
  return props.state === 'complete' ? 'Complete' : 'Pending';
});

const classes = computed(() => {
  return props.state === 'complete'
    ? 'bg-green-100 text-green-800'
    : 'bg-gray-100 text-gray-800';
});
</script>

<style scoped></style>
