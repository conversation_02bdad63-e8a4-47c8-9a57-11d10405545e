<template>
  <div class="flex items-center">
    <div :class="indicatorClasses">
      <template v-if="currentStep > index">
        <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
        </svg>
      </template>
      <template v-else-if="currentStep === index">
        <span class="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></span>
      </template>
      <template v-else>
        <span class="text-xs text-gray-500">{{ index }}</span>
      </template>
    </div>
    <div class="ml-3 text-sm" :class="currentStep >= index ? 'text-blue-800' : 'text-gray-500'">
      {{ label }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  index: number;
  label: string;
  currentStep: number;
}

const props = defineProps<Props>();

const indicatorClasses = computed(() => [
  'w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0',
  props.currentStep >= props.index ? 'bg-blue-100' : 'bg-gray-100',
]);
</script>

<style scoped>
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
</style>
