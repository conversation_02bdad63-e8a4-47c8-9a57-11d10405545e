<template>
  <div class="space-y-4 bg-white">
    <!-- Subject Line -->
    <div class="flex items-center space-x-2">
      <label class="text-sm font-medium text-gray-700">Subject Line</label>
      <div class="relative">
        <svg class="w-4 h-4" viewBox="0 0 24 24" @mouseenter="showSubjectInfo = true" @mouseleave="showSubjectInfo = false">
          <path fill="currentColor" d="M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
        </svg>
        <div v-if="showSubjectInfo" class="absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-2 bg-white border rounded-lg shadow-lg z-10 mb-1">
          <p class="text-sm text-gray-700">
            Recommended Subject Line based on your brand's tone and style
          </p>
        </div>
      </div>
    </div>

    <div class="flex space-x-2">
      <div class="relative flex-1">
        <input
          type="text"
          :value="subjectLine"
          @input="$emit('update:subject-line', $event.target.value)"
          :disabled="isGenerating"
          class="w-full text-sm rounded-md px-2 py-1 disabled:bg-gray-100 disabled:cursor-not-allowed focus:outline-none transition-colors duration-200"
        />
        <div v-if="isGeneratingSubject" class="absolute inset-0 overflow-hidden rounded-md pointer-events-none">
          <div class="absolute inset-0 bg-gradient-to-r from-transparent via-violet-100/50 to-transparent animate-shimmer" style="backface-visibility: hidden"></div>
        </div>
      </div>
    </div>

    <!-- Preview Line -->
    <div class="space-y-2">
      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium text-gray-700">Preview Line</label>
        <div class="relative">
          <svg class="w-4 h-4" viewBox="0 0 24 24" @mouseenter="showPreviewInfo = true" @mouseleave="showPreviewInfo = false">
            <path fill="currentColor" d="M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
          </svg>
          <div v-if="showPreviewInfo" class="absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-2 bg-white border rounded-lg shadow-lg z-10 mb-1">
            <p class="text-sm text-gray-700">
              Recommended Preview Line based on your brand's tone and style
            </p>
          </div>
        </div>
      </div>

      <div class="flex space-x-2">
        <div class="relative flex-1">
          <input
            type="text"
            :value="previewLine"
            @input="$emit('update:preview-line', $event.target.value)"
            :disabled="isGenerating"
            class="w-full text-sm rounded-md px-2 py-1 disabled:bg-gray-100 disabled:cursor-not-allowed focus:outline-none transition-colors duration-200"
          />
          <div v-if="isGeneratingPreview" class="absolute inset-0 overflow-hidden rounded-md pointer-events-none">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-violet-100/50 to-transparent animate-shimmer" style="backface-visibility: hidden"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';

export default defineComponent({
  name: 'SubjectPreviewEditor',
  props: {
    subjectLine: {
      type: String,
      default: ''
    },
    previewLine: {
      type: String,
      default: ''
    },
    isGeneratingSubject: Boolean,
    isGeneratingPreview: Boolean,
    isGenerating: Boolean // Keep this prop
  },
  emits: ['update:subject-line', 'update:preview-line'],
  setup(props, { emit }) {
    const showSubjectInfo = ref(false);
    const showPreviewInfo = ref(false);

    // No need for local computed refs for subjectLine/previewLine here,
    // the props themselves will be reactive when passed from the parent's computed.
    // The template binding :value="subjectLine" will use the reactive prop directly.

    // Expose refs and props needed by the template
    return {
      showSubjectInfo,
      showPreviewInfo,
      // Directly expose props for template binding
      subjectLine: computed(() => props.subjectLine),
      previewLine: computed(() => props.previewLine),
      isGenerating: computed(() => props.isGenerating),
      isGeneratingSubject: computed(() => props.isGeneratingSubject),
      isGeneratingPreview: computed(() => props.isGeneratingPreview),
      // Emit function is implicitly available in the template
    };
  }
});
</script>
