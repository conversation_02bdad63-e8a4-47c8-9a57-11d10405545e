import { Extension } from '@tiptap/core'
import { <PERSON>lug<PERSON>, Plugin<PERSON><PERSON> } from 'prosemirror-state'
import { Decoration, DecorationSet } from 'prosemirror-view'

// Custom extension to highlight selected text
export const HighlightSelection = Extension.create({
  name: 'highlightSelection',

  addOptions() {
    return {
      isHighlighting: false,
      selectionRange: { from: 0, to: 0 },
      highlightClass: 'highlight-selection',
    }
  },

  addProseMirrorPlugins() {
    const { isHighlighting, selectionRange, highlightClass } = this.options

    return [
      new Plugin({
        key: new PluginKey('highlight-selection'),
        props: {
          decorations(state) {
            if (!isHighlighting.value) return null

            const { from, to } = selectionRange.value
            if (from === to) return null
            return DecorationSet.create(state.doc, [
              Decoration.inline(from, to, {
                class: highlightClass,
              })
            ])
          },
        },
      }),
    ]
  },
})
