<template>
  <div class="bg-white border-b px-6 py-4">
    <div class="flex items-start justify-between">
      <div class="flex flex-col">
        <div class="flex flex-col">
          <div class="flex items-center gap-4 justify-between w-full">
            <div class="flex items-center gap-4">
              <div class="shrink-0 cursor-pointer" @click="handleBackClick">
                <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368">
                  <path d="m313-440 224 224-57 56-320-320 320-320 57 56-224 224h487v80H313Z" />
                </svg>
              </div>
              <div>
                <h1 class="text-xl font-semibold">
                  <template v-if="campaign?.name">
                    <!-- View mode -->
                    <div v-if="!isEditingName"
                      @click="startEditingName"
                      class="cursor-pointer hover:bg-gray-100 px-2 py-1 rounded -ml-2 flex items-center"
                      title="Click to edit campaign name">
                      {{ campaign?.name }}
                    </div>
                    <!-- Edit mode -->
                    <input
                      v-else
                      ref="nameInput"
                      v-model="editableName"
                      @blur="saveCampaignName"
                      @keyup.enter="saveCampaignName"
                      @keyup.esc="cancelEditing"
                      class="border border-purple-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-purple-500 w-96"
                      placeholder="Enter campaign name"
                    />
                  </template>
                  <div v-else class="h-6 w-48 bg-gray-200 rounded animate-pulse"></div>
                </h1>
                <!-- Campaign Description -->
                <div class="flex text-sm mt-1 pl-2">
                  <template v-if="campaign">
                    <!-- View mode for description -->
                    <div v-if="!isEditingDescription"
                      @click="startEditingDescription"
                      class="cursor-pointer hover:bg-gray-100 px-2 py-1 rounded -ml-2 flex items-center text-gray-600"
                      title="Click to edit description">
                      <span>{{ truncatedDescription }}</span>
                    </div>
                    <!-- Edit mode for description -->
                    <textarea
                      v-else
                      ref="descriptionInput"
                      v-model="editableDescription"
                      @blur="saveCampaignDescription"
                      @keyup.enter="saveCampaignDescription"
                      @keyup.esc="cancelEditingDescription"
                      class="border border-purple-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-purple-500 w-3/4 min-w-[600px] h-14 resize-none"
                      placeholder="Enter campaign description"
                    />

                  </template>
                  <div v-else class="h-5 w-40 bg-gray-200 rounded animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex flex-col items-end">
        <div class="flex items-center gap-2">
			<!-- View Plan Link -->
			<div  v-if="plan?.id" class="flex-shrink-0 mr-2">
				<router-link :to="'/ai-strategist/planning/plan/' + plan?.id" class="text-ralprimary-main text-xs font-normal gap-1 cursor-pointer whitespace-nowrap w-auto hover:underline">
				View Plan
				</router-link>
			</div>

          <!-- Status Dropdown -->
          <div class="relative">
            <div @click="toggleDropdown"
                 data-dropdown-toggle
                 class="px-4 py-2 bg-gray-50 border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-100 hover:border-gray-300 transition-colors duration-200 flex items-center font-medium cursor-pointer">
              <div class="w-3 h-3 mr-2 rounded-sm" :style="{ backgroundColor: getStatusColor(status) }"></div>
              <span class="text-xs">{{ getStatusDisplayText(status) }}</span>
              <svg class="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </div>

            <!-- Dropdown Menu -->
            <div v-if="isStatusDropdownOpen"
                 data-dropdown-menu
                 class="absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50">
              <div class="py-1" @click.stop>
                <div v-for="(option, index) in statusOptions"
                     :key="index"
                     @click.stop="updateStatus(option.value, $event)"
                     class="px-4 py-2 text-xs text-gray-700 hover:bg-gray-100 flex items-center cursor-pointer">
                  <div class="w-3 h-3 mr-3 rounded-sm" :style="{ backgroundColor: option.color }"></div>
                  {{ option.label }}
                </div>
              </div>
            </div>
          </div>
        </div>
			<div class="text-gray-500 mt-1">
				<span class="text-xs font-normal">
				AI Strategist Model {{ task?.version || '0.3' }}
				</span>
			</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TopBar',
  data() {
    return {
      isEditingName: false,
      editableName: '',
      isEditingDescription: false,
      editableDescription: '',
      isStatusDropdownOpen: false,
      statusOptions: [
        { label: 'Campaign Ready', value: 'Campaign Ready', color: '#6366F1' }, // Indigo
        { label: 'Ready for Copywriting', value: 'Ready for Copywriting', color: '#8B5CF6' }, // Purple
        { label: 'In Copywriting', value: 'In Copywriting', color: '#A78BFA' }, // Light purple
        { label: 'Ready for Design', value: 'Ready for Design', color: '#EC4899' }, // Pink
        { label: 'In Design', value: 'In Design', color: '#F472B6' }, // Light pink
        { label: 'Quality Check', value: 'Quality Check', color: '#F59E0B' }, // Amber
        { label: 'Ready for Review', value: 'Ready for Review', color: '#10B981' }, // Emerald
        { label: 'In Review', value: 'In Review', color: '#34D399' }, // Green
        { label: 'Approved', value: 'Approved', color: '#3B82F6' }, // Blue
        { label: 'Done', value: 'Complete', color: '#059669' }, // Green
        { label: 'Archive', value: 'Archive', color: '#6B7280' } // Slate
      ]
    };
  },
  props: {
    task: {
      type: Object,
      default: null
    },
    campaign: {
      type: Object,
      default: null
    },
    status: {
      type: String,
      default: null
    },
    plan: {
      type: Object,
      default: null
    },
    klaviyoCampaignId: {
      type: String,
      default: null
    },
    isCreatingKlaviyoCampaign: {
      type: Boolean,
      default: false
    },
    klaviyoStatusMessage: {
      type: String,
      default: 'Creating...'
    },
    shouldShowKlaviyoButton: {
      type: Boolean,
      default: false
    }
  },
  emits: ['back', 'mark-complete', 'create-klaviyo', 'launch-klaviyo', 'update-campaign-name', 'update-campaign-description', 'update-status'],
  computed: {
    truncatedDescription() {
      const description = this.campaign?.description || '';
      if (description.length > 100) {
        return description.substring(0, 100) + '...';
      }
      return description;
    }
  },
  methods: {
    toggleDropdown(e) {
      // Prevent the default behavior and stop propagation
      e.preventDefault();
      e.stopPropagation();

      // Toggle the dropdown state
      this.isStatusDropdownOpen = !this.isStatusDropdownOpen;

      // If we're opening the dropdown, add a one-time event to handle clicks on dropdown items
      if (this.isStatusDropdownOpen) {
        // Add a one-time click handler to the dropdown menu to stop propagation
        this.$nextTick(() => {
          const dropdownMenu = this.$el.querySelector('[data-dropdown-menu]');
          if (dropdownMenu) {
            const stopPropagation = (e) => e.stopPropagation();
            dropdownMenu.addEventListener('click', stopPropagation);

            // Clean up this event listener when dropdown closes
            const cleanup = () => {
              dropdownMenu.removeEventListener('click', stopPropagation);
              this.$el.removeEventListener('dropdown-closed', cleanup);
            };

            this.$el.addEventListener('dropdown-closed', cleanup, { once: true });
          }
        });
      }
    },

    handleBackClick() {
      // Check if there's a planId in the route query parameters
      const planId = this.$route.query.planId;

      if (planId) {
        // Navigate to the plan details page
        this.$router.push(`/ai-strategist/planning/plan/${planId}`);
      } else {
        // Navigate back to the task list
        this.$emit('back');
      }
    },

    startEditingName() {
      this.editableName = this.campaign?.name || '';
      this.isEditingName = true;
      // Focus the input field after the DOM updates
      this.$nextTick(() => {
        if (this.$refs.nameInput) {
          this.$refs.nameInput.focus();
          this.$refs.nameInput.select();
        }
      });
    },

    saveCampaignName() {
      if (this.editableName.trim() !== '' && this.editableName !== this.campaign?.name) {
        // Emit event to parent component to handle the API call
        this.$emit('update-campaign-name', this.editableName);
      }
      this.isEditingName = false;
    },

    cancelEditing() {
      this.isEditingName = false;
      this.editableName = this.campaign?.name || '';
    },

    startEditingDescription() {
      this.editableDescription = this.campaign?.description || '';
      this.isEditingDescription = true;
      // Focus the input field after the DOM updates
      this.$nextTick(() => {
        if (this.$refs.descriptionInput) {
          this.$refs.descriptionInput.focus();
          this.$refs.descriptionInput.select();
        }
      });
    },

    saveCampaignDescription() {
      if (this.editableDescription !== this.campaign?.description) {
        // Emit event to parent component to handle the API call
        this.$emit('update-campaign-description', this.editableDescription);
      }
      this.isEditingDescription = false;
    },

    cancelEditingDescription() {
      this.isEditingDescription = false;
      this.editableDescription = this.campaign?.description || '';
    },

    updateStatus(newStatus, event) {
      // Stop propagation to prevent document click handler from interfering
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }

      // Emit the update event to parent
      this.$emit('update-status', newStatus);

      // Close the dropdown
      this.isStatusDropdownOpen = false;

      // Dispatch a custom event to signal dropdown closure for cleanup
      this.$el.dispatchEvent(new CustomEvent('dropdown-closed'));
    },

    getStatusColor(status) {
      const option = this.statusOptions.find(opt => opt.value === status);
      return option ? option.color : '#9CA3AF'; // Default gray color if not found
    },

    getStatusDisplayText(status) {
      if (!status) return 'Set Status';

      // For backward compatibility with old statuses
      if (status === 'Ready') return 'Not Started';
      if (status === 'In Progress') return 'In Progress';
      if (status === 'Complete') return 'Done';
      if (status === 'Archive') return 'Archive';

      // For new status format
      const option = this.statusOptions.find(opt => opt.value === status);
      return option ? option.label : status;
    }
  },

  mounted() {
    // Create a global click handler that will close the dropdown
    // when clicking anywhere outside the dropdown components
    this.handleClickOutside = (e) => {
      // Only process if dropdown is open
      if (!this.isStatusDropdownOpen) return;

      // Get references to dropdown elements
      const dropdownToggle = this.$el.querySelector('[data-dropdown-toggle]');

      // If the click is on the toggle button, let the toggle handler manage it
      if (dropdownToggle && dropdownToggle.contains(e.target)) {
        return;
      }

      // For any other click in the document, close the dropdown
      this.isStatusDropdownOpen = false;

      // Dispatch a custom event to signal dropdown closure for cleanup
      this.$el.dispatchEvent(new CustomEvent('dropdown-closed'));
    };

    // Add the event listener to the window object to catch all clicks
    // Using capture phase ensures this runs before other handlers
    window.addEventListener('click', this.handleClickOutside, { capture: true });
  },

  beforeUnmount() {
    // Clean up event listener - must use same target and options as when adding
    window.removeEventListener('click', this.handleClickOutside, { capture: true });
  }
}
</script>

<style scoped>
.animate-gradient {
  background-size: 200% 100%;
  animation: gradientMove 1.5s infinite linear;
}

@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
