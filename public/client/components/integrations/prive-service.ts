import * as Utils from '../../utils/utils.js';
const URL_DOMAIN = Utils.URL_DOMAIN;

export class PriveService {

	static async connect(apiKey: string): Promise<any> {
		const response = await fetch(
			`${URL_DOMAIN}/integration/prive/connect`,
			{
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({ apiKey })
			}
		);
		return await response.json();
	}

	static async disconnect(): Promise<any> {
		const response = await fetch(
			`${URL_DOMAIN}/integration/prive/disconnect`,
			{
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			}
		);
		return await response.json();
	}

	static async isConnected(): Promise<any> {
		const response = await fetch(
			`${URL_DOMAIN}/integration/prive/connected`,
			{
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				}
			}
		);
		const data = await response.json();
		return data.connected;
	}
}
