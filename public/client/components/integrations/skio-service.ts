import * as Utils from '../../utils/utils.js';
const URL_DOMAIN = Utils.URL_DOMAIN;

export class SkioService {

	static async connect(apiKey: string, webhookToken: string): Promise<any> {
		const response = await fetch(
			`${URL_DOMAIN}/integration/skio/connect`,
			{
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({ apiKey, webhookToken })
			}
		);
		return await response.json();
	}

	static async disconnect(): Promise<any> {
		const response = await fetch(
			`${URL_DOMAIN}/integration/skio/disconnect`,
			{
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			}
		);
		return await response.json();
	}

	static async isConnected(): Promise<any> {
		const response = await fetch(
			`${URL_DOMAIN}/integration/skio/connected`,
			{
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				}
			}
		);
		const data = await response.json();
		return data.connected;
	}
}
