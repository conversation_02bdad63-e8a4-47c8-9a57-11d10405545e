<template>
	<div class="p-6 space-y-6">
		<section class="">
			<div class="flex gap-10 align-center">
				<div class="flex-col w-1/4">
					<label class="block text-sm font-medium mb-1">Test Event</label>
					<select class="form-input w-full" v-model="selectedEvent">
						<option
							v-for="item in supportedEvents"
							:key="item.id"
							:value="item">
							{{ item.friendlyName }}
						</option>
					</select>
				</div>
				<div class="flex-col w-1/4">
					<label class="block text-sm font-medium mb-1">Shopify Customer Id</label>
					<input
						type="text"
						class="form-input w-full"
						v-model="customerId"
						placeholder="Customer Id"
					/>
				</div>
				<div class="flex-col w-1/4" v-if="testData">
					<label class="block text-sm font-medium mb-1">Data</label>
					<textarea
						class="form-input w-full"
						v-model="testData"
						placeholder=""
						rows="3"
					></textarea>
				</div>
				<div class="ml-auto">
					<button
						class=" btn bg-indigo-500 hover:bg-indigo-600 text-white cursor-pointer ml-auto"
						@click="sendTestEvent">
						Send Test Event
					</button>
				</div>
			</div>
		</section>
	</div>
</template>

<script>
import { ref, onMounted, watch } from 'vue';
import * as Utils from '../../../client-old/utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'KlaviyoEvents',
	emits: ['setStatus'],
	setup(props, context) {

		//TODO: change customerId to customer email for easier testing
		const customerId = ref('');
		const supportedEvents = ref([]);
		const selectedEvent = ref({});
		let testData = ref('');

		onMounted(async () => {
			const response = await fetch(`${URL_DOMAIN}/event-stream`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
			});
			const events = await response.json();
			supportedEvents.value = [...events];
			selectedEvent.value = events[0];
			testData.value = JSON.stringify(events[0].testData, null, 2);
		});

		watch(selectedEvent, (newEvent, oldEvent) => {
			if (newEvent !== oldEvent) {
				testData.value = JSON.stringify(newEvent?.testData, null, 2) || {};
			}
		});

		async function sendTestEvent() {
			const testEvent = supportedEvents.value.find((event) => event.name === selectedEvent.value.name);
			const response = await fetch(`${URL_DOMAIN}/event-stream`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					eventName: testEvent.name,
					customerId: customerId.value,
					testData: testData.value
				}),
			});
			const resp = await response.json();
			console.log(`got an event stream response: `, resp);
			if (resp.success) {
				setStatus({ type: 'success', message: 'Event sent successfully' });
			} else {
				setStatus({ type: 'fail', message: 'Failed to send event' });
			}
		}

		function setStatus({ type, message }) {
			context.emit('setStatus', { type, message });
		}

		return {
			setStatus,
			supportedEvents,
			selectedEvent,
			customerId,
			sendTestEvent,
			testData
		};
	}
};
</script>

<style scoped>
.form-input {
	padding: 0.5rem;
	border: 1px solid #d1d5db;
	border-radius: 0.375rem;
	transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
	border-color: #68d391;
	box-shadow: 0 0 0 1px #68d391;
}

.form-input[readonly] {
	background-color: #f3f4f6;
	/* Light grey background */
	color: #9ca3af;
	/* Dimmed text color */
	border: 1px dashed #d1d5db;
	/* Dashed border */
	cursor: not-allowed;
	/* Not-allowed cursor on hover */
}

.btn {
	padding: 0.5rem 1rem;
	border-radius: 0.375rem;
	font-weight: 600;
	transition: background-color 0.15s ease-in-out;
}

.btn.copied {
	background-color: #68d391;
	color: white;
}
</style>
