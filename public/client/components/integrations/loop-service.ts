import * as Utils from '../../../client-old/utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

export const LoopService = {
    async isConnected() {
        const response = await fetch(`${URL_DOMAIN}/integration/loop/connected`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
            }
        });
        const data = await response.json();
        return data.connected;
    },

    async connect(apiKey: string) {
        const response = await fetch(`${URL_DOMAIN}/integration/loop/connect`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
            body: JSON.stringify({
                apiKey
            }),
        });
        return response.json();
    },

    async disconnect() {
        const response = await fetch(`${URL_DOMAIN}/integration/loop/disconnect`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
            }
        });
        return response.json();
    }
};
