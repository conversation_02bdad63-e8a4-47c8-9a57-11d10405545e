<template>
	<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
		<!-- Section header -->
		<div class="mb-8 flex">
			<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Integration Settings</h1>
		</div>

		<!-- Section content -->
		<div class="bg-white shadow-lg rounded-sm mb-8">
			<div class="p-6 space-y-6">
				<section class="mt-5">
					<label class="block text-sm font-medium mb-1">Private API Key</label>
					<div class="flex">
						<input type="password" class="form-input w-full" v-model="apiKey" :readonly="connected"
							:placeholder="connected ? 'Connected' : 'Enter your Skio Private API Key'" />
					</div>
					<span class="text-xs my-1">Generate this API Key in Skio</span>

					<label class="block text-sm font-medium mt-4 mb-1">Webhook URL</label>
					<div class="flex">
						<input type="text" class="form-input w-full bg-gray-100 text-gray-500" v-model="webhookUrl"
							disabled />
						<button @click="copyToClipboard(webhookUrl)" :class="{'copied': copied}"
							class="ml-2 btn border-gray-300">
							{{ copied ? 'Copied!' : 'Copy' }}
						</button>
					</div>
					<span class="text-xs my-1">Paste this URL into Skio's Webhook input</span>

					<label class="block text-sm font-medium mt-4 mb-1">Webhook Token</label>
					<div class="flex">
						<input type="password" class="form-input w-full" v-model="webhookToken" :readonly="connected"
							:placeholder="connected ? 'Connected' : 'Enter your Skio Webhook Token'" />
					</div>
					<span class="text-xs my-1">Copy this token from the Webhook section in Skio after the Webhook URL is
						added</span>
				</section>

				<div class="flex justify-end mt-5">
					<button class="btn bg-indigo-500 hover:bg-indigo-600 text-white cursor-pointer" :class="{
							'cursor-not-allowed': validatingApiKey,
							'bg-gray-400': validatingApiKey
						}" :disabled="validatingApiKey" @click="() => connected ? disconnect() : connect()">
						{{ validatingApiKey ? 'Validating' : connected ? 'Disconnect' : 'Connect' }}
					</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { SkioService } from './skio-service';

export default {
	name: 'SkioIntegration',
	computed: {
		webhookUrl() {
			const isProd = location.hostname === 'app.raleon.io';
			return isProd ?
				'https://dfxabjk4fl.execute-api.us-east-1.amazonaws.com/v1/integrations?name=skio' :
				'https://son3tvhjo2.execute-api.us-east-1.amazonaws.com/v1/integrations?name=skio';
		},
	},
	setup(props, context) {
		const apiKey = ref('');
		const webhookToken = ref('');
		const validatingApiKey = ref(false);
		const connected = ref(false);
		const copied = ref(false);

		onMounted(async () => {
			connected.value = await SkioService.isConnected();
		})

		function copyToClipboard(text) {
			navigator.clipboard.writeText(text).then(() => {
				copied.value = true;
				setTimeout(() => {
					copied.value = false;
				}, 3000);
			}, (err) => {
				console.error('Could not copy text: ', err);
			});
		}

		async function connect() {
			if (!apiKey || !apiKey.value) {
				setStatus({
					type: 'fail',
					message: "Please enter a valid API Key to activate this integration.",
				});
				return;
			}
			const resp = await SkioService.connect(apiKey.value, webhookToken.value);
			setStatus({
				type: resp.success ? 'success' : 'fail',
				message: resp.success ?
					'Successfully connected Skio Integration.' :
					'Something went wrong connecting to Skio. Please try again, validate your api key and webhook token, or reach out to Raleon support for assistance.'
			});
			connected.value = resp.success;
			apiKey.value = '';
			webhookToken.value = '';
		}

		async function disconnect() {
			const resp = await SkioService.disconnect();
			if (resp.success) {
				connected.value = false;
				apiKey.value = '';
				webhookToken.value = '';
			}
			setStatus({
				type: resp.success ? 'success' : 'fail',
				message: resp.success ?
					'Successfully disconnected Skio Integration.' :
					'Something went wrong disconnecting from Skio. Please try again or reach out to Raleon support for assistance.'
			});
		}

		function setStatus({ type, message }) {
			context.emit('setStatus', { type, message });
		}

		return {
			apiKey,
			webhookToken,
			connected,
			validatingApiKey,
			connect,
			disconnect,
			setStatus,
			copyToClipboard,
			copied,
		};
	}
};
</script>

<style scoped>
.form-input {
	padding: 0.5rem;
	border: 1px solid #d1d5db;
	border-radius: 0.375rem;
	transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
	border-color: #68d391;
	box-shadow: 0 0 0 1px #68d391;
}

.form-input[readonly] {
	background-color: #f3f4f6;
	/* Light grey background */
	color: #9ca3af;
	/* Dimmed text color */
	border: 1px dashed #d1d5db;
	/* Dashed border */
	cursor: not-allowed;
	/* Not-allowed cursor on hover */
}

.btn {
	padding: 0.5rem 1rem;
	border-radius: 0.375rem;
	font-weight: 600;
	transition: background-color 0.15s ease-in-out;
}

.btn.copied {
	background-color: #68d391;
	color: white;
}
</style>
