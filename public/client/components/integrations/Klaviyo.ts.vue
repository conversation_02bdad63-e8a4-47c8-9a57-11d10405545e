<template>
	<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
		<!-- Section header -->
		<div class="mb-8 flex">
			<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Integration Settings</h1>
		</div>

		<!-- Required Scopes -->
		<div class="bg-white shadow-lg rounded-sm mb-8">
			<div class="p-6">
				<h2 class="text-lg font-semibold mb-4">Required API Key Scopes</h2>
				<p class="mb-4 text-sm text-gray-600">Your Klaviyo API key must have the following scopes enabled:</p>
				<ul class="list-disc pl-5 mb-4 text-sm text-gray-600">
					<li>Accounts: Read</li>
					<li>Campaigns: Read/Write</li>
					<li>Events: Read/Write</li>
					<li>Flows: Read/Write</li>
					<li>Images: Read</li>
					<li>Lists: Read/Write</li>
					<li>Metrics: Read</li>
					<li>Profiles: Read/Write</li>
					<li>Segments: Read/Write</li>
					<li>Templates: Read/Write</li>
				</ul>
				<p class="text-sm text-gray-600">
					<a href="https://developers.klaviyo.com/en/docs/create_api_key" target="_blank" class="text-indigo-600 hover:text-indigo-800">
						Learn how to create an API key with the proper scopes →
					</a>
				</p>
			</div>
		</div>

		<!-- API Key Input -->
		<div class="bg-white shadow-lg rounded-sm mb-8">
			<div class="p-6 space-y-6">
				<section class="mt-5">
					<label class="block text-sm font-medium mb-1">Private API Key</label>
					<div v-if="errorMessage" class="error-box mb-2">
						{{ errorMessage }}
					</div>
					<div class="flex">
						<input type="password" class="form-input w-full" v-model="apiKey" :readonly="connected"
							:placeholder="connected ? 'Connected' : 'Enter your Klaviyo Private API Key'" />
					</div>
				</section>

				<div class="flex justify-end mt-5">
					<button class="btn bg-indigo-500 hover:bg-indigo-600 text-white cursor-pointer" :class="{
							'cursor-not-allowed': validatingApiKey,
							'bg-gray-400': validatingApiKey
						}" :disabled="validatingApiKey" @click="() => connected ? disconnect() : connect()">
						{{ validatingApiKey ? 'Validating' : connected ? 'Disconnect' : 'Connect' }}
					</button>
				</div>
			</div>
		</div>

		<div class="mb-8 flex">
			<h1 class="text-2xl md:text-2xl text-slate-800 font-bold">Enable Email Tracking in Raleon</h1>
		</div>

		<!-- Section content -->
		<div v-if="connected" class="bg-white shadow-lg rounded-sm mb-8">
			<div class="p-6 pb-2 flex items-center justify-between">
				<label for="toggle" class="block text-sm font-medium mb-1 mr-4">Enable Tracking Pixel URL</label>
				<div class="relative inline-block w-10 align-middle select-none transition duration-200 ease-in">
					<input type="checkbox" id="toggle" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" v-model="emailPixelStatus" @change="saveEmailPixelStatus"/>
					<label for="toggle" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
				</div>
			</div>
			<div class="p-6 pt-2 flex items-center">
				<input
					type="text"
					class="form-input w-full pr-20 mr-4"
					:value="trackingUrl"
					readonly
				/>
				<button
					class="btn bg-indigo-500 hover:bg-indigo-600 text-white text-sm px-3 py-1 ml-3"
					@click="copyTrackingUrl"
					:class="{ 'bg-green-500': copied }"
				>
					{{ copied ? 'Copied!' : 'Copy' }}
				</button>
			</div>
		</div>

		<div class="mb-8 flex">
			<h1 class="text-2xl md:text-2xl text-slate-800 font-bold">Send Test Event</h1>
		</div>

		<!-- Section content -->
		<div v-if="connected" class="bg-white shadow-lg rounded-sm mb-8">
			<KlaviyoEvents @setStatus="setStatus" />
		</div>
	</div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import * as Utils from '../../../client-old/utils/Utils';
import KlaviyoEvents from './KlaviyoEvents.ts.vue';
import * as OrganizationSettings from '../../services/organization-settings.js';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'KlaviyoIntegration',
	components: {
		KlaviyoEvents,
	},
	setup(props, context) {
		const apiKey = ref('');
		const validatingApiKey = ref(false);
		const connected = ref(false);
		const emailPixelStatus = ref(OrganizationSettings.getOrganizationSetting('emailPixel') || false);
		const errorMessage = ref('');

		const copied = ref(false);
		const trackingUrl = computed(() => {
			const orgId = localStorage.getItem('userOrgId');
			const isProd = location.hostname === 'app.raleon.io';
			return !isProd ?
				`<img src="https://tw85l4184j.execute-api.us-east-1.amazonaws.com/prod/track/pixel?pid={{ person.KlaviyoID }}&ralid={{ person|lookup:'Raleon User Id' }}&time={% today "%Y-%m-%d-%H-%M-%S" as today %}{{ today }}&organization=${orgId}" width="1" height="1" alt="">`
				: `<img src="https://tgfzzzil85.execute-api.us-east-1.amazonaws.com/prod/track/pixel?pid={{ person.KlaviyoID }}&ralid={{ person|lookup:'Raleon User Id' }}&time={% today "%Y-%m-%d-%H-%M-%S" as today %}{{ today }}&organization=${orgId}" width="1" height="1" alt="">`;
		});

		function copyTrackingUrl() {
			navigator.clipboard.writeText(trackingUrl.value);
			copied.value = true;
			setTimeout(() => {
				copied.value = false;
			}, 2000);
		}

		onMounted(async () => {
			const response = await fetch(`${URL_DOMAIN}/integration/klaviyo/connected`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
			});
			const resp = await response.json();
			if (resp.connected) {
				connected.value = true;
			}

			emailPixelStatus.value = await OrganizationSettings.getOrganizationSetting('emailPixel') === 'true';
		})

		async function connect() {
			errorMessage.value = '';
			if (!apiKey || !apiKey.value) {
				errorMessage.value = "Please enter a valid API Key";
				return;
			}
			validatingApiKey.value = true;

			const response = await fetch(`${URL_DOMAIN}/integration/klaviyo/connect`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({ apiKey: apiKey.value }),
			});
			const resp = await response.json();
			if (resp.success) {
				connected.value = true;
				errorMessage.value = '';
			} else {
				errorMessage.value = resp.error?.message || 'Failed to connect. Please check your API key and try again.';
			}
			setStatus({
				type: resp.success ? 'success' : 'fail',
				message: resp.success ?
					'Successfully connected Klaviyo Integration.' :
					resp.error?.message || 'Something went wrong connecting to Klaviyo. Please ensure your API key has all required permissions or reach out to Raleon support for assistance.'
			});
			validatingApiKey.value = false;
		}

		async function disconnect() {
			const response = await fetch(`${URL_DOMAIN}/integration/klaviyo/disconnect`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
			});
			const resp = await response.json();
			if (resp.success) {
				connected.value = false;
				errorMessage.value = '';
			}
		}

		function saveEmailPixelStatus() {
			OrganizationSettings.updateOrganizationSetting('emailPixel', emailPixelStatus.value ? 'true' : 'false');
		}

		function setStatus({ type, message }) {
			context.emit('setStatus', { type, message });
		}

		return {
			apiKey,
			connected,
			validatingApiKey,
			connect,
			disconnect,
			setStatus,
			copyTrackingUrl,
			trackingUrl,
			copied,
			emailPixelStatus,
			saveEmailPixelStatus,
			errorMessage,
		};
	}
};
</script>

<style scoped>
.form-input {
	padding: 0.5rem;
	border: 1px solid #d1d5db;
	border-radius: 0.375rem;
	transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
	border-color: #68d391;
	box-shadow: 0 0 0 1px #68d391;
}

.form-input[readonly] {
	background-color: #f3f4f6;
	/* Light grey background */
	color: #9ca3af;
	/* Dimmed text color */
	border: 1px dashed #d1d5db;
	/* Dashed border */
	cursor: not-allowed;
	/* Not-allowed cursor on hover */
}

.btn {
	padding: 0.5rem 1rem;
	border-radius: 0.375rem;
	font-weight: 600;
	transition: background-color 0.15s ease-in-out;
}

.btn.copied {
	background-color: #68d391;
	color: white;
}

.toggle-checkbox {
	opacity: 0;
	width: 0;
	height: 0;
}

.toggle-label {
	display: block;
	overflow: hidden;
	cursor: pointer;
	height: 24px;
	/* Height of the toggle */
	padding: 0;
	line-height: 24px;
	background: #bbb;
	/* Background of the toggle */
	border-radius: 24px;
	transition: background-color 0.2s;
}

.toggle-label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 2px;
	width: 20px;
	/* Width of the toggle handle */
	height: 20px;
	/* Height of the toggle handle */
	border-radius: 20px;
	background: #fff;
	/* Background of the toggle handle */
	transition: 0.2s;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox:checked+.toggle-label:after {
	transform: translateX(18px);
}

.toggle-checkbox:checked {
	right: 0;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox {
	right: 4px;
}

.toggle-label {
	transition: background-color 0.2s;
}

.error-box {
	background-color: #fefce8;
	border: 1px solid #fbbf24;
	color: #92400e;
	padding: 0.5rem 1rem;
	border-radius: 0.375rem;
	font-size: 0.875rem;
	line-height: 1.25rem;
}
</style>
