<template>
	<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
		<!-- Section header -->
		<div class="mb-8 flex">
			<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Integration Settings</h1>
		</div>

		<!-- Section content -->
		<div class="bg-white shadow-lg rounded-sm mb-8">
			<div class="p-6 space-y-6">
				<section class="mt-5">
					<label class="block text-sm font-medium mb-1">Private API Key</label>
					<div class="flex">
						<input type="password" class="form-input w-full" v-model="apiKey" :readonly="connected"
							:placeholder="connected ? 'Connected' : 'Enter your Sendlane Private API Key'" />
					</div>
				</section>
				<!-- <section class="mt-5">
					<label class="block text-sm font-medium mb-1">Custom Integration Token</label>
					<div class="flex">
						<input type="password" class="form-input w-full" v-model="customIntegrationToken" :readonly="connected"
							:placeholder="connected ? 'Connected' : 'Enter your Sendlane Custom Integration Token'" />
					</div>
				</section> -->

				<div class="flex justify-end mt-5">
					<button class="btn bg-indigo-500 hover:bg-indigo-600 text-white cursor-pointer" :class="{
							'cursor-not-allowed': validatingApiKey,
							'bg-gray-400': validatingApiKey
						}" :disabled="validatingApiKey" @click="() => connected ? disconnect() : connect()">
						{{ validatingApiKey ? 'Validating' : connected ? 'Disconnect' : 'Connect' }}
					</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { ref, onMounted } from 'vue';
import * as Utils from '../../../client-old/utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'SendlaneIntegration',
	components: {
	},
	setup(props, context) {
		const apiKey = ref('');
		const validatingApiKey = ref(false);
		const connected = ref(false);

		onMounted(async () => {
			const response = await fetch(`${URL_DOMAIN}/integration/sendlane/connected`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
			});
			const resp = await response.json();
			if (resp.connected) {
				connected.value = true;
			}
		})

		async function connect() {
			if (!apiKey || !apiKey.value) {
				setStatus({
					type: 'fail',
					message: "Please enter a valid API Key to activate this integration.",
				});
				return;
			}
			validatingApiKey.value = true;

			const response = await fetch(`${URL_DOMAIN}/integration/sendlane/connect`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({ apiKey: apiKey.value }), // , customIntegrationToken: customIntegrationToken.value }),
			});
			const resp = await response.json();
			if (resp.success) {
				connected.value = true;
			}
			setStatus({
				type: resp.success ? 'success' : 'fail',
				message: resp.success ?
					'Successfully connected Sendlane Integration.' :
					'Something went wrong connecting to Sendlane. Please try again, validate your api key, or reach out to Raleon support for assistance.'
			});
			validatingApiKey.value = false;
		}

		async function disconnect() {
			const response = await fetch(`${URL_DOMAIN}/integration/sendlane/disconnect`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
			});
			const resp = await response.json();
			if (resp.success) {
				connected.value = false;
			}
		}

		function setStatus({ type, message }) {
			context.emit('setStatus', { type, message });
		}

		return {
			apiKey,
			connected,
			validatingApiKey,
			connect,
			disconnect,
			setStatus,
		};
	}
};
</script>

<style scoped>
.form-input {
	padding: 0.5rem;
	border: 1px solid #d1d5db;
	border-radius: 0.375rem;
	transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
	border-color: #68d391;
	box-shadow: 0 0 0 1px #68d391;
}

.form-input[readonly] {
	background-color: #f3f4f6;
	/* Light grey background */
	color: #9ca3af;
	/* Dimmed text color */
	border: 1px dashed #d1d5db;
	/* Dashed border */
	cursor: not-allowed;
	/* Not-allowed cursor on hover */
}

.btn {
	padding: 0.5rem 1rem;
	border-radius: 0.375rem;
	font-weight: 600;
	transition: background-color 0.15s ease-in-out;
}

.btn.copied {
	background-color: #68d391;
	color: white;
}
</style>
