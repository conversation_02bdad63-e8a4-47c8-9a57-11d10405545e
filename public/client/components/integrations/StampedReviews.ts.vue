<template>
    <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
        <!-- Section header -->
        <div class="mb-8 flex">
            <h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Webhook Settings</h1>
        </div>

        <!-- Section content -->
        <div class="bg-white shadow-lg rounded-sm mb-8">
            <div class="p-6 space-y-6">
                <!-- Webhook URL -->
                <section class="mt-5">
                    <label class="block text-sm font-medium mb-1">Webhook URL</label>
					<div class="flex">
						<input type="text" class="form-input w-full" v-model="webhookUrl" readonly />
						<button @click="copyToClipboard(webhookUrl, 'url')" :class="{'copied': copied.url}" class="ml-2 btn border-gray-300">
							{{ copied.url ? 'Copied!' : 'Copy' }}
						</button>
					</div>
                </section>

                <!-- Webhook Secret -->
                <section class="mt-5">
                    <label class="block text-sm font-medium mb-1">Webhook Secret</label>
                    <div class="flex">
                        <input type="text" class="form-input w-full" v-model="webhookSecret" readonly />
						<button @click="copyToClipboard(webhookSecret, 'secret')" :class="{'copied': copied.secret}" class="ml-2 btn border-gray-300">
							{{ copied.secret ? 'Copied!' : 'Copy' }}
						</button>
                    </div>
                </section>

                <!-- API Private Key -->
                <section class="mt-5">
                    <label class="block text-sm font-medium mb-1" for="apiKey">API Private Key</label>
                    <input id="apiKey" class="form-input w-full" type="text" v-model="apiKey" />
                </section>

                <!-- Store Hash -->
                <section class="mt-5">
                    <label class="block text-sm font-medium mb-1" for="storeHash">Store Hash</label>
                    <input id="storeHash" class="form-input w-full" type="text" v-model="storeHash" />
                </section>

                <!-- Save Button -->
                <div class="flex flex-col-reverse justify-end mt-5">
					<div>
						<button class="btn bg-indigo-500 hover:bg-indigo-600 text-white" @click="saveSettings">
							{{ isSaving ? 'Saving...' : 'Save' }}
						</button>
					</div>
					<div v-if="toastMessage" class="toast-message">
						<label class="block text-sm font-medium mb-1">{{ toastMessage }}</label>
					</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import { ref } from 'vue';
import * as Utils from '../../utils/utils.js';
const mainSecretKeyId = 'stamped_encrypt_key';
const generatedSecretKeyId = 'stamped-reviews-secret';
const storeHashKeyId = 'stamped-reviews-store-hash';
const apiKeyKeyId = 'stamped-reviews-api-key';
const URL_DOMAIN = Utils.URL_DOMAIN;
const webhookURL = URL_DOMAIN.includes('app.raleon') ? 'https://dfxabjk4fl.execute-api.us-east-1.amazonaws.com/v1/integrations?name=stamped-reviews' :
	'https://son3tvhjo2.execute-api.us-east-1.amazonaws.com/v1/integrations?name=stamped-reviews';
export default {
    name: 'WebhookSettings',
    setup() {
        const webhookUrl = ref(webhookURL);
        const webhookSecret = ref('12345');
        const apiKey = ref('');
        const storeHash = ref('');
		const copied = ref({ url: false, secret: false });
		const isSaving = ref(false);
		const toastMessage = ref('');

        function copyToClipboard(text, type) {
            navigator.clipboard.writeText(text).then(() => {
                copied.value[type] = true;
                setTimeout(() => {
                    copied.value[type] = false;
                }, 3000);
            }, (err) => {
                console.error('Could not copy text: ', err);
            });
        }

        return {
            webhookUrl,
            webhookSecret,
            apiKey,
            storeHash,
            copyToClipboard,
			copied,
			isSaving,
			toastMessage
        };
    },
	mounted() {
		this.getKey(apiKeyKeyId).then((key) => {
			this.apiKey = key;
		});
		this.getKey(storeHashKeyId).then((key) => {
			this.storeHash = key;
		});
		this.getKey(generatedSecretKeyId).then((key) => {
			if(key === '')
				this.generateSecretKey().then((key) => {
					this.webhookSecret = key;
				});
			else
			{
				this.webhookSecret = key;
			}
		});
	},
	methods: {
		async saveSettings() {
			this.isSaving = true;
			try {
				await this.saveKey(apiKeyKeyId, this.apiKey);
				await this.saveKey(storeHashKeyId, this.storeHash);
				await this.saveKey(generatedSecretKeyId, this.webhookSecret);
				this.toastMessage = 'Settings saved successfully!';
				setTimeout(() => {
					this.toastMessage = '';
				}, 3000);  // Hide the toast after 3 seconds
			} catch (error) {
				console.error('Save operation failed:', error);
				this.toastMessage = 'Error saving settings.';
				setTimeout(() => {
					this.toastMessage = '';
				}, 3000);  // Hide the toast after 3 seconds
			} finally {
				this.isSaving = false;
			}
		},
		async getKey(keyId) {
			let url = `/organizations/organization-keys/${keyId}`;
			const response = await fetch(`${URL_DOMAIN}${url}`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				}
			});

			let data = await response.json();
			return data.length > 0 ? data[0].value : '';
		},
		async saveKey(keyId, value) {
			let url = `/organizations/organization-keys?replace=true`;
			const response = await fetch(`${URL_DOMAIN}${url}`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					key: keyId,
					value: value,
					secretKeyId: mainSecretKeyId,
				}),
			});

			let data = await response.json();
			return data;
		},
		async generateSecretKey() {
			let url = `/organizations/organization-keys/generate`;
			const response = await fetch(`${URL_DOMAIN}${url}`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					key: generatedSecretKeyId,
					value: 'replaceme',
					secretKeyId: mainSecretKeyId,
				}),
			});

			let data = await response.json();
			return data.value;
		}
	}
};
</script>

<style scoped>
.form-input {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
    border-color: #68d391;
    box-shadow: 0 0 0 1px #68d391;
}

.form-input[readonly] {
    background-color: #f3f4f6; /* Light grey background */
    color: #9ca3af; /* Dimmed text color */
    border: 1px dashed #d1d5db; /* Dashed border */
    cursor: not-allowed; /* Not-allowed cursor on hover */
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
}

.btn.copied {
    background-color: #68d391;
    color: white;
}
</style>
