<template>
  <div class="space-y-6 p-6">
    <div class="bg-white rounded-lg border shadow-sm">
      <div class="p-6">
        <h2 class="text-lg font-semibold">Email Branding</h2>
        <p class="text-sm text-gray-500">Configure how your emails appear to customers</p>
      </div>
      <div class="p-6 pt-0 space-y-4">
        <div class="flex justify-between items-center mb-2">
          <span class="text-xs text-gray-500">Choose how to edit your email branding</span>
          <div class="flex items-center">
            <button
              @click="brandingEditMode = 'structured'"
              :class="[
                'px-3 py-1 text-sm rounded-l-lg border',
                brandingEditMode === 'structured' ? 'bg-purple-600 text-white' : 'bg-white text-gray-700'
              ]"
            >
              Structured
            </button>
            <button
              @click="brandingEditMode = 'raw'"
              :class="[
                'px-3 py-1 text-sm rounded-r-lg border',
                brandingEditMode === 'raw' ? 'bg-purple-600 text-white' : 'bg-white text-gray-700'
              ]"
            >
              Raw Text
            </button>
          </div>
        </div>

        <!-- Raw text mode - for backward compatibility -->
        <textarea
          v-if="brandingEditMode === 'raw'"
          v-model="emailBranding"
          placeholder="Describe your email branding guidelines..."
          class="w-full h-64 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
        ></textarea>

        <!-- Structured mode - for better UX -->
        <div v-else class="bg-gray-50 p-4 rounded-lg border">
          <!-- Brand Colors Section -->
          <div class="mb-6">
            <h3 class="font-medium mb-2 text-purple-700">Brand Colors</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="flex items-center space-x-2">
                <label class="text-sm w-1/2">Email background:</label>
                <div class="flex flex-1 relative">
                  <div class="color-picker-wrapper">
                    <div
                      class="color-swatch w-10 h-10 rounded border cursor-pointer"
                      :style="{ backgroundColor: parsedBranding.colors.emailBackground }"
                      @click="openColorPicker('emailBackground')"
                    ></div>
                    <div v-if="activeColorPicker === 'emailBackground'" class="color-picker-popup">
                      <div class="color-picker-header">
                        <span>Select Color (Hex)</span>
                        <button @click="activeColorPicker = null" class="close-btn">&times;</button>
                      </div>
                      <input
                        type="color"
                        v-model="parsedBranding.colors.emailBackground"
                        @input="colorToHex($event, 'emailBackground')"
                        class="w-full mb-2"
                      />
                      <input
                        type="text"
                        v-model="parsedBranding.colors.emailBackground"
                        placeholder="#RRGGBB"
                        class="w-full px-2 py-1 text-sm border rounded"
                      />
                    </div>
                  </div>
                  <input
                    type="text"
                    v-model="parsedBranding.colors.emailBackground"
                    class="flex-1 ml-2 px-3 py-2 border rounded-lg"
                  />
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <label class="text-sm w-1/2">Text color:</label>
                <div class="flex flex-1 relative">
                  <div class="color-picker-wrapper">
                    <div
                      class="color-swatch w-10 h-10 rounded border cursor-pointer"
                      :style="{ backgroundColor: parsedBranding.colors.text }"
                      @click="openColorPicker('text')"
                    ></div>
                    <div v-if="activeColorPicker === 'text'" class="color-picker-popup">
                      <div class="color-picker-header">
                        <span>Select Color (Hex)</span>
                        <button @click="activeColorPicker = null" class="close-btn">&times;</button>
                      </div>
                      <input
                        type="color"
                        v-model="parsedBranding.colors.text"
                        @input="colorToHex($event, 'text')"
                        class="w-full mb-2"
                      />
                      <input
                        type="text"
                        v-model="parsedBranding.colors.text"
                        placeholder="#RRGGBB"
                        class="w-full px-2 py-1 text-sm border rounded"
                      />
                    </div>
                  </div>
                  <input
                    type="text"
                    v-model="parsedBranding.colors.text"
                    class="flex-1 ml-2 px-3 py-2 border rounded-lg"
                  />
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <label class="text-sm w-1/2">Headings:</label>
                <div class="flex flex-1 relative">
                  <div class="color-picker-wrapper">
                    <div
                      class="color-swatch w-10 h-10 rounded border cursor-pointer"
                      :style="{ backgroundColor: parsedBranding.colors.headings }"
                      @click="openColorPicker('headings')"
                    ></div>
                    <div v-if="activeColorPicker === 'headings'" class="color-picker-popup">
                      <div class="color-picker-header">
                        <span>Select Color (Hex)</span>
                        <button @click="activeColorPicker = null" class="close-btn">&times;</button>
                      </div>
                      <input
                        type="color"
                        v-model="parsedBranding.colors.headings"
                        @input="colorToHex($event, 'headings')"
                        class="w-full mb-2"
                      />
                      <input
                        type="text"
                        v-model="parsedBranding.colors.headings"
                        placeholder="#RRGGBB"
                        class="w-full px-2 py-1 text-sm border rounded"
                      />
                    </div>
                  </div>
                  <input
                    type="text"
                    v-model="parsedBranding.colors.headings"
                    class="flex-1 ml-2 px-3 py-2 border rounded-lg"
                  />
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <label class="text-sm w-1/2">Links:</label>
                <div class="flex flex-1 relative">
                  <div class="color-picker-wrapper">
                    <div
                      class="color-swatch w-10 h-10 rounded border cursor-pointer"
                      :style="{ backgroundColor: parsedBranding.colors.links }"
                      @click="openColorPicker('links')"
                    ></div>
                    <div v-if="activeColorPicker === 'links'" class="color-picker-popup">
                      <div class="color-picker-header">
                        <span>Select Color (Hex)</span>
                        <button @click="activeColorPicker = null" class="close-btn">&times;</button>
                      </div>
                      <input
                        type="color"
                        v-model="parsedBranding.colors.links"
                        @input="colorToHex($event, 'links')"
                        class="w-full mb-2"
                      />
                      <input
                        type="text"
                        v-model="parsedBranding.colors.links"
                        placeholder="#RRGGBB"
                        class="w-full px-2 py-1 text-sm border rounded"
                      />
                    </div>
                  </div>
                  <input
                    type="text"
                    v-model="parsedBranding.colors.links"
                    class="flex-1 ml-2 px-3 py-2 border rounded-lg"
                  />
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <label class="text-sm w-1/2">Content background:</label>
                <div class="flex flex-1 relative">
                  <div class="color-picker-wrapper">
                    <div
                      class="color-swatch w-10 h-10 rounded border cursor-pointer"
                      :style="{ backgroundColor: parsedBranding.colors.contentBackground }"
                      @click="openColorPicker('contentBackground')"
                    ></div>
                    <div v-if="activeColorPicker === 'contentBackground'" class="color-picker-popup">
                      <div class="color-picker-header">
                        <span>Select Color (Hex)</span>
                        <button @click="activeColorPicker = null" class="close-btn">&times;</button>
                      </div>
                      <input
                        type="color"
                        v-model="parsedBranding.colors.contentBackground"
                        @input="colorToHex($event, 'contentBackground')"
                        class="w-full mb-2"
                      />
                      <input
                        type="text"
                        v-model="parsedBranding.colors.contentBackground"
                        placeholder="#RRGGBB"
                        class="w-full px-2 py-1 text-sm border rounded"
                      />
                    </div>
                  </div>
                  <input
                    type="text"
                    v-model="parsedBranding.colors.contentBackground"
                    class="flex-1 ml-2 px-3 py-2 border rounded-lg"
                  />
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <label class="text-sm w-1/2">Button:</label>
                <div class="flex flex-1 relative">
                  <div class="color-picker-wrapper">
                    <div
                      class="color-swatch w-10 h-10 rounded border cursor-pointer"
                      :style="{ backgroundColor: parsedBranding.colors.button }"
                      @click="openColorPicker('button')"
                    ></div>
                    <div v-if="activeColorPicker === 'button'" class="color-picker-popup">
                      <div class="color-picker-header">
                        <span>Select Color (Hex)</span>
                        <button @click="activeColorPicker = null" class="close-btn">&times;</button>
                      </div>
                      <input
                        type="color"
                        v-model="parsedBranding.colors.button"
                        @input="colorToHex($event, 'button')"
                        class="w-full mb-2"
                      />
                      <input
                        type="text"
                        v-model="parsedBranding.colors.button"
                        placeholder="#RRGGBB"
                        class="w-full px-2 py-1 text-sm border rounded"
                      />
                    </div>
                  </div>
                  <input
                    type="text"
                    v-model="parsedBranding.colors.button"
                    class="flex-1 ml-2 px-3 py-2 border rounded-lg"
                  />
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <label class="text-sm w-1/2">Button Text:</label>
                <div class="flex flex-1 relative">
                  <div class="color-picker-wrapper">
                    <div
                      class="color-swatch w-10 h-10 rounded border cursor-pointer"
                      :style="{ backgroundColor: parsedBranding.colors.buttonText }"
                      @click="openColorPicker('buttonText')"
                    ></div>
                    <div v-if="activeColorPicker === 'buttonText'" class="color-picker-popup">
                      <div class="color-picker-header">
                        <span>Select Color (Hex)</span>
                        <button @click="activeColorPicker = null" class="close-btn">&times;</button>
                      </div>
                      <input
                        type="color"
                        v-model="parsedBranding.colors.buttonText"
                        @input="colorToHex($event, 'buttonText')"
                        class="w-full mb-2"
                      />
                      <input
                        type="text"
                        v-model="parsedBranding.colors.buttonText"
                        placeholder="#RRGGBB"
                        class="w-full px-2 py-1 text-sm border rounded"
                      />
                    </div>
                  </div>
                  <input
                    type="text"
                    v-model="parsedBranding.colors.buttonText"
                    class="flex-1 ml-2 px-3 py-2 border rounded-lg"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Typography Section -->
          <div class="mb-4">
            <h3 class="font-medium mb-2 text-purple-700">Typography</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="text-sm">Text Font Family</label>
                <input
                  type="text"
                  v-model="parsedBranding.typography.textFont"
                  placeholder="e.g. Arial, sans-serif"
                  class="w-full px-3 py-2 border rounded-lg"
                />
              </div>

              <div class="space-y-2">
                <label class="text-sm">Text Size (px)</label>
                <input
                  type="number"
                  v-model="parsedBranding.typography.textSize"
                  min="8"
                  max="24"
                  class="w-full px-3 py-2 border rounded-lg"
                />
              </div>

              <div class="space-y-2">
                <label class="text-sm">Heading Font Family</label>
                <input
                  type="text"
                  v-model="parsedBranding.typography.headingFont"
                  placeholder="e.g. Georgia, serif"
                  class="w-full px-3 py-2 border rounded-lg"
                />
              </div>

              <div class="space-y-2">
                <label class="text-sm">Font Weight</label>
                <select
                  v-model="parsedBranding.typography.fontWeight"
                  class="w-full px-3 py-2 border rounded-lg"
                >
                  <option value="normal">Normal</option>
                  <option value="bold">Bold</option>
                  <option value="light">Light</option>
                </select>
              </div>

              <div class="space-y-2">
                <label class="text-sm">Heading Weight</label>
                <select
                  v-model="parsedBranding.typography.headingWeight"
                  class="w-full px-3 py-2 border rounded-lg"
                >
                  <option value="normal">Normal</option>
                  <option value="bold">Bold</option>
                  <option value="heavy">Heavy</option>
                </select>
              </div>

              <div class="space-y-2">
                <label class="text-sm">Line Height</label>
                <input
                  type="number"
                  v-model="parsedBranding.typography.lineHeight"
                  min="0.8"
                  max="2"
                  step="0.1"
                  class="w-full px-3 py-2 border rounded-lg"
                />
              </div>
            </div>
          </div>

          <div class="mt-4 text-right">
            <div class="flex justify-between">
              <button
                @click="resetBrandingToDefaults"
                class="px-4 py-2 border text-gray-700 rounded-lg hover:bg-gray-100"
              >
                Reset to Defaults
              </button>
              <div class="text-sm text-gray-500 italic mt-2">
                Edit colors and typography, then click "Save Knowledge" at the top to save
              </div>
            </div>
          </div>

          <!-- Preview Section -->
          <div class="mt-6 pt-6 border-t border-gray-200">
            <h3 class="font-medium mb-4 text-purple-700">Preview</h3>
            <div
              class="p-4 rounded-lg shadow-sm"
              :style="{ backgroundColor: parsedBranding.colors.emailBackground }"
            >
              <div
                class="p-6 rounded-lg mb-4"
                :style="{ backgroundColor: parsedBranding.colors.contentBackground }"
              >
                <div
                  class="text-2xl mb-2"
                  :style="{
                    color: parsedBranding.colors.headings,
                    fontFamily: parsedBranding.typography.headingFont,
                    fontWeight: parsedBranding.typography.headingWeight,
                    lineHeight: parsedBranding.typography.lineHeight
                  }"
                >
                  Sample Heading
                </div>
                <p
                  class="mb-4"
                  :style="{
                    color: parsedBranding.colors.text,
                    fontFamily: parsedBranding.typography.textFont,
                    fontSize: `${parsedBranding.typography.textSize}px`,
                    fontWeight: parsedBranding.typography.fontWeight,
                    lineHeight: parsedBranding.typography.lineHeight
                  }"
                >
                  This is sample text that shows how your email content will appear.
                  The styling is based on the brand settings you've configured above.
                </p>
                <a
                  href="#"
                  :style="{ color: parsedBranding.colors.links }"
                >
                  This is a sample link
                </a>
                <div class="mt-4">
                  <button
                    class="px-4 py-2 rounded"
                    :style="{
                      backgroundColor: parsedBranding.colors.button,
                      color: parsedBranding.colors.buttonText
                    }"
                  >
                    Sample Button
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "EmailBranding",
  props: {
    value: {
      type: [String, Object],
      default: ''
    }
  },
  data() {
    return {
      emailBranding: '',
      brandingEditMode: 'raw',
      updateTimeout: null,
      activeColorPicker: null,
      parsedBranding: {
        colors: {
          emailBackground: '#F7F7F7',
          text: '#1B2443',
          headings: '#1C2545',
          links: '#1155CC',
          contentBackground: '#E8F4E8',
          button: '#1B2443',
          buttonText: '#FFFFFF'
        },
        typography: {
          textFont: 'Courier, sans-serif',
          textSize: 14,
          headingFont: 'Courier, sans-serif',
          fontWeight: 'normal',
          headingWeight: 'heavy',
          lineHeight: 1.1
        }
      },
      useStructuredMode: false,
      defaultColors: {
        emailBackground: '#F7F7F7',
        text: '#1B2443',
        headings: '#1C2545',
        links: '#1155CC',
        contentBackground: '#E8F4E8',
        button: '#1B2443',
        buttonText: '#FFFFFF'
      },
      defaultTypography: {
        textFont: 'Courier, sans-serif',
        textSize: 14,
        headingFont: 'Courier, sans-serif',
        fontWeight: 'normal',
        headingWeight: 'heavy',
        lineHeight: 1.1
      }
    };
  },
  watch: {
    value: {
      handler(newValue) {
        console.log("Value prop changed:", newValue, "Type:", typeof newValue);

        try {
          if (typeof newValue === 'object' && newValue !== null) {
            // If we receive an object directly, use it for structured mode
            console.log("Value prop is an object, updating structured data");

            // Deep compare current parsedBranding with new value
            const currentJSON = JSON.stringify(this.parsedBranding);
            const newJSON = JSON.stringify(newValue);

            if (currentJSON !== newJSON) {
              console.log("Detected change in object value, updating");
              // Replace the entire object for Vue reactivity
              this.parsedBranding = JSON.parse(JSON.stringify(newValue));
              // Update string representation for raw mode
              this.emailBranding = JSON.stringify(newValue, null, 2);
              this.useStructuredMode = true;
              this.brandingEditMode = 'structured';
              // Force UI update
              this.$nextTick(() => this.forceUIUpdate());
            }
          } else if (newValue && typeof newValue === 'string' && newValue !== this.emailBranding) {
            // For string values, update raw mode value and try to parse
            console.log("Value prop is a string, updating raw data");
            this.emailBranding = newValue;
            this.preprocessEmailBranding();
            this.tryParseEmailBranding();
          } else if (!newValue) {
            // Handle empty value by initializing with defaults
            console.log("Empty value, resetting to defaults");
            this.resetBrandingToDefaults();
          }
        } catch (error) {
          console.error("Error in value watcher:", error);
        }
      },
      immediate: true, // Trigger immediately when component is created
      deep: true
    },
    emailBranding(newValue) {
		console.log("Email branding changed:", newValue);
      this.$emit('input', newValue);
      this.$emit('update', newValue);

      if (this.brandingEditMode === 'raw' && !newValue.trim()) {
        // If emailBranding becomes empty when in raw mode
        // Ensure we can switch to structured mode with default values
        this.brandingEditMode = 'structured';
      }
    },
    // Watch for changes to the branding edit mode
    brandingEditMode(newMode) {
      if (newMode === 'structured') {
        // Try to parse the raw emailBranding when switching to structured mode
        this.tryParseEmailBranding();
      } else if (newMode === 'raw') {
        // Make sure the raw text is updated with the current structured data
        this.updateEmailBrandingFromStructured();
      }
    },
    parsedBranding: {
      handler() {
        if (this.brandingEditMode === 'structured') {
          console.log("Detected change in structured data");
          // Debounce the updates to avoid too frequent JSON conversion
          clearTimeout(this.updateTimeout);
          this.updateTimeout = setTimeout(() => {
            this.updateEmailBrandingFromStructured();
          }, 300); // 300ms debounce
        }
      },
      deep: true
    }
  },
  created() {
    console.log("EmailBranding component created, incoming value:", this.value, "Type:", typeof this.value);

    try {
      // Handle different types of incoming value
      if (typeof this.value === 'object' && this.value !== null) {
        // If it's already an object, use it directly
        console.log("Using object directly:", this.value);
        this.parsedBranding = JSON.parse(JSON.stringify(this.value)); // Deep copy
        this.emailBranding = JSON.stringify(this.value, null, 2);
        this.useStructuredMode = true;
        this.brandingEditMode = 'structured';

        // Force UI update to ensure the parsed values are applied
        this.$nextTick(() => this.forceUIUpdate());

      } else if (this.value && typeof this.value === 'string') {
        // If it's a string, try to parse it
        console.log("Parsing string value:", this.value);
        this.emailBranding = this.value;
        this.preprocessEmailBranding();
        if (!this.tryParseEmailBranding()) {
          // If parsing fails, reset to defaults
          console.warn("Failed to parse emailBranding, using defaults");
          this.resetBrandingToDefaults();
        }
      } else {
        // If no value or invalid value, use defaults
        console.log("No valid value provided, using defaults");
        this.resetBrandingToDefaults();
      }
    } catch (error) {
      console.error("Error in created hook:", error);
      this.resetBrandingToDefaults();
    }
  },
  mounted() {
    console.log("EmailBranding component mounted, emailBranding:", this.emailBranding);

    // Force a reload if the component is recreated with new data
    setTimeout(() => {
      console.log("Running delayed initialization tasks");
      // Reparse with a small delay to ensure the component is fully mounted
      this.tryParseEmailBranding();
      // Force UI update to ensure displays are current
      this.forceUIUpdate();
    }, 300);
  },
  methods: {
    // Open the custom color picker popup
    openColorPicker(colorKey) {
      this.activeColorPicker = colorKey;
      // Close when clicking outside
      this.$nextTick(() => {
        document.addEventListener('click', this.closeColorPickerOnClickOutside);
      });
    },

    // Close color picker when clicking outside
    closeColorPickerOnClickOutside(event) {
      const popups = document.querySelectorAll('.color-picker-popup');
      const swatches = document.querySelectorAll('.color-swatch');

      let clickedInsidePopup = false;

      popups.forEach(popup => {
        if (popup.contains(event.target)) {
          clickedInsidePopup = true;
        }
      });

      swatches.forEach(swatch => {
        if (swatch.contains(event.target)) {
          clickedInsidePopup = true;
        }
      });

      if (!clickedInsidePopup) {
        this.activeColorPicker = null;
        document.removeEventListener('click', this.closeColorPickerOnClickOutside);
      }
    },

    // Add new method to convert color to hex format
    colorToHex(event, colorKey) {
      // Convert the color value to uppercase hex
      const hexColor = event.target.value.toUpperCase();
      // Update the color in our data model
      this.parsedBranding.colors[colorKey] = hexColor;
    },
    // Force UI update with current values
    forceUIUpdate() {
      // Create temporary copies to force reactivity update
      const tempColors = JSON.parse(JSON.stringify(this.parsedBranding.colors));
      const tempTypography = JSON.parse(JSON.stringify(this.parsedBranding.typography));

      this.$nextTick(() => {
        // Replace the entire objects to ensure Vue detects the changes
        this.parsedBranding.colors = { ...tempColors };
        this.parsedBranding.typography = { ...tempTypography };

        console.log("Forced UI update. Background color now:", this.parsedBranding.colors.emailBackground);
      });
    },
    // Pre-process the emailBranding value to handle different formats
    preprocessEmailBranding() {
      try {
        if (!this.emailBranding || !this.emailBranding.trim()) {
          return; // Nothing to process
        }

        // Check if it's a string that contains escaped JSON or double-stringified JSON
        if (this.emailBranding.includes('\\"') ||
            (this.emailBranding.startsWith('"') && this.emailBranding.endsWith('"') &&
             this.emailBranding.slice(1, -1).trim().startsWith('{'))) {

          try {
            // Try to parse it once (might be a stringified JSON string)
            const parsed = JSON.parse(this.emailBranding);

            // If the parsed result is a string and looks like JSON, parse it again
            if (typeof parsed === 'string' && parsed.trim().startsWith('{')) {
              try {
                const doublyParsed = JSON.parse(parsed);
                if (typeof doublyParsed === 'object') {
                  // We have a double-stringified JSON object, re-stringify once
                  this.emailBranding = JSON.stringify(doublyParsed);
                  console.log("Fixed double-stringified JSON");
                }
              } catch (e) {
                // Not double-stringified, continue with the first parsed result
                if (typeof parsed === 'object') {
                  this.emailBranding = JSON.stringify(parsed);
                  console.log("Converted parsed object back to JSON string");
                }
              }
            }
          } catch (e) {
            console.log("Not a valid JSON string, keeping as-is");
          }
        }
      } catch (error) {
        console.error("Error preprocessing emailBranding:", error);
      }
    },
    // Parse the emailBranding string if it's in JSON format
    tryParseEmailBranding() {
      console.log("Attempting to parse email branding:", this.emailBranding);
      console.log("Current brandingEditMode:", this.brandingEditMode);

      if (!this.emailBranding || this.emailBranding.trim() === '') {
        console.warn("Empty emailBranding, using structured mode with defaults");
        this.useStructuredMode = true;
        this.brandingEditMode = 'structured';
        console.log("Set brandingEditMode to structured with defaults");
        return false;
      }

      try {
        // Parse the JSON string to an object
        const parsedData = JSON.parse(this.emailBranding);
        console.log("Successfully parsed email branding:", parsedData);

        // Create a deep copy to ensure reactivity
        const cleanCopy = JSON.parse(JSON.stringify(parsedData));

        // Make sure all expected objects exist
        if (!cleanCopy.colors) cleanCopy.colors = { ...this.defaultColors };
        if (!cleanCopy.typography) cleanCopy.typography = { ...this.defaultTypography };

        // Replace the entire object for Vue reactivity
        this.parsedBranding = { ...cleanCopy };

        // Logging the applied values to verify
        console.log("Applied parsedBranding values:", this.parsedBranding);
        console.log("Background color being applied:", this.parsedBranding.colors.emailBackground);

        // Delay UI update to ensure reactivity
        this.$nextTick(() => {
          this.useStructuredMode = true;
          this.brandingEditMode = 'structured';
          this.forceUIUpdate();
        });

        return true;
      } catch (error) {
        console.error("Error parsing email branding JSON:", error);
        console.log("Raw emailBranding data:", this.emailBranding);

        // Fall back to raw mode if parsing fails
        this.useStructuredMode = false;
        this.brandingEditMode = 'raw';
        return false;
      }
    },
    // Convert structured data to raw JSON string when applying changes
    updateEmailBrandingFromStructured() {
      try {
        // Safely get a clean deep copy of the structured data
        const brandingData = JSON.parse(JSON.stringify(this.parsedBranding));

        // Debug log all the important values
        console.log("Updating from structured mode, colors:", brandingData.colors);
        console.log("Background color being emitted:", brandingData.colors.emailBackground);

        // Convert the data to a formatted JSON string
        const jsonString = JSON.stringify(brandingData, null, 2);

        // Update the component's internal value
        this.emailBranding = jsonString;

        // Emit both the object (for the parent's v-model binding) and events
        this.$emit('input', brandingData);
        this.$emit('update:value', brandingData);
        this.$emit('branding-updated', brandingData);

        console.log("Emitted updated branding to parent");

        return true;
      } catch (error) {
        console.error("Error updating email branding from structured data:", error);
        return false;
      }
    },
    // Reset to default values
    resetBrandingToDefaults() {
      this.parsedBranding = {
        colors: {
          emailBackground: '#F7F7F7',
          text: '#1B2443',
          headings: '#1C2545',
          links: '#1155CC',
          contentBackground: '#E8F4E8',
          button: '#1B2443',
          buttonText: '#FFFFFF'
        },
        typography: {
          textFont: 'Courier, sans-serif',
          textSize: 14,
          headingFont: 'Courier, sans-serif',
          fontWeight: 'normal',
          headingWeight: 'heavy',
          lineHeight: 1.1
        }
      };
      this.brandingEditMode = 'structured';
      this.updateEmailBrandingFromStructured();
      this.$emit('reset');
    }
  }
};
</script>

<style scoped>
/* Component styles */
.color-picker-wrapper {
  position: relative;
}

.color-picker-popup {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 100;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  padding: 12px;
  width: 220px;
  margin-top: 4px;
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
}
</style>
