<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
// Assuming Heroicons are installed: npm install @heroicons/vue
// Import specific icons as needed or rely on the parent component to pass them
// For this example, let's import the suggested ones for potential direct use or typing
import { CalendarIcon, MegaphoneIcon, EnvelopeIcon, LightBulbIcon } from '@heroicons/vue/24/outline';

// Define the structure for each prompt object
interface Prompt {
  title: string;
  description: string;
  promptText: string; // The actual text to send to the chat
  iconComponent: any; // Use Component type from 'vue' or string if name is passed
}

// Define the props the component accepts
// Using withDefaults to provide a default empty array if needed, though typically the parent would provide prompts
const props = withDefaults(defineProps<{
  prompts: Prompt[];
}>(), {
  prompts: () => []
});

// Define the events the component can emit
const emit = defineEmits<{
  (e: 'select-prompt', payload: string): void;
}>();

// Function to handle prompt selection
const selectPrompt = (prompt: Prompt) => {
  emit('select-prompt', prompt.promptText);
};
</script>

<template>
  <!-- Initial Chat Prompts Component -->
  <div class="flex flex-col space-y-4 w-full max-w-xl mx-auto py-6">
    <!-- Heading -->
	 <div class="flex items-center justify-center space-x-2">
		<div class="w-12 h-12">
			<svg width="40" height="38" viewBox="0 0 277 262" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M275.015 171.912L138.436 0L1.85665 171.912L27.241 198.347L0 231.206V261.611H58.3626L138.436 161.692L218.509 261.611H276.871V231.206L249.63 198.346L275.015 171.912ZM138.436 64.2174L40.9103 181.858L30.0901 170.59L138.436 34.2152L246.781 170.59L235.961 181.858L138.436 64.2174ZM21.2978 238.863L138.436 97.5647L255.574 238.863V240.35H228.745L138.436 127.657L48.1264 240.35H21.2978V238.863Z" fill="url(#paint0_linear_1552_13772)"/>
			<path d="M197.999 261.611L138.438 185.936L78.8765 261.611H105.962L138.438 220.349L170.913 261.611H197.999Z" fill="url(#paint1_linear_1552_13772)"/>
			<defs>
			<linearGradient id="paint0_linear_1552_13772" x1="-2.72981" y1="163.507" x2="294.07" y2="149.889" gradientUnits="userSpaceOnUse">
			<stop stop-color="#6536E2"/>
			<stop offset="1" stop-color="#1E90DB"/>
			</linearGradient>
			<linearGradient id="paint1_linear_1552_13772" x1="-2.72981" y1="163.507" x2="294.07" y2="149.889" gradientUnits="userSpaceOnUse">
			<stop stop-color="#6536E2"/>
			<stop offset="1" stop-color="#1E90DB"/>
			</linearGradient>
			</defs>
			</svg>
		</div>

		<h2 class="text-3xl font-semibold text-gray-600 text-center mb-2">
			How can I <span  class="extra-smooth-gradient-text">help you today</span>?</h2>
	 </div>

    <!-- Single Card Container for Prompts -->
    <div class="bg-white rounded-xl border border-gray-200 shadow-md overflow-hidden p-6">
      <div class="divide-y divide-gray-200">
        <!-- Loop through prompts -->
        <div
          v-for="(prompt, index) in props.prompts"
          :key="index"
          @click="selectPrompt(prompt)"
          class="p-4 hover:bg-gray-100 transition-colors duration-150 ease-in-out cursor-pointer group"
        >
          <div class="flex items-start space-x-4">
            <!-- Icon -->
            <div class="flex-shrink-0 p-2 bg-purple-100 rounded-lg">
              <!-- Dynamic Icon Rendering -->
              <component :is="prompt.iconComponent" class="h-6 w-6 text-purple-700" aria-hidden="true" />
            </div>
            <!-- Content -->
            <div class="flex-1 min-w-0">
              <h3 class="font-medium text-[#202020] mb-1 truncate">{{ prompt.title }}</h3>
              <p class="text-sm text-[#202020] line-clamp-2">{{ prompt.description }}</p>
            </div>
            <!-- Arrow Indicator -->
            <div class="flex-shrink-0 text-purple-500 group-hover:translate-x-1 transition-transform duration-200 ease-in-out self-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add any component-specific styles here if needed */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.extra-smooth-gradient-text {
  /* Using more color stops for ultra-smooth transition */
  background-image: linear-gradient(
    90deg,
    #6536E2 0%,
    #6536E2 15%,
    #4c63df 30%,
    #1E90DB 50%,
    #4c63df 70%,
    #6536E2 85%,
    #6536E2 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
</style>
