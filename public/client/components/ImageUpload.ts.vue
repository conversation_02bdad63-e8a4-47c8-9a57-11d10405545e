<template>
	<div v-bind:class="containerClasses ? containerClasses : 'relative'">
		<LightSecondaryButton :cta="buttonText || 'Choose Image'" @click.stop="toggleTooltip"></LightSecondaryButton>

		<div v-if="showTooltip" class="overlay" @click="closeModal"></div>
		<div v-if="showTooltip" ref="tooltip"
			class="bg-white rounded-lg shadow-lg transition-all duration-300 overflow-auto z-[500]"
			:class="{
				'centered-popup-upload': tab === 'upload',
				'centered-popup': tab !== 'upload'
			}"
			v-bind:class="popoverClasses ? popoverClasses : 'absolute md:w-full max-w-[600px] min-w-[300px] max-h-64'"
			:style="{
				left: left,
				right: right,
				minWidth: minWidth,
				minHeight: minHeight
			}">
			<div class="flex">
				<ul class="flex p-4">
					<li class="hover:bg-gray-200 px-2 py-1 rounded-xl cursor-pointer transition-all duration-300"
						:class="{'bg-gray-300': tab === 'upload'}" @click="tab = 'upload'">Upload</li>
					<li class="hover:bg-gray-200 px-2 py-1 rounded-xl cursor-pointer transition-all duration-300 ml-2"
						:class="{'bg-gray-300': tab === 'gallery'}" @click="tab = 'gallery'">Gallery</li>
					<li class="hover:bg-gray-200 px-2 py-1 rounded-xl cursor-pointer transition-all duration-300 ml-2"
						:class="{'bg-gray-300': tab === 'icons'}" @click="tab = 'icons'">Icons</li>
				</ul>
				<div class="flex-grow"></div>
				<button v-if="showCloseButton" class="text-slate-400 hover:text-slate-500 mr-2" @click="close">
					<div class="sr-only">Close</div>
					<svg class="w-8 h-8 fill-current">
						<path style="scale: 2"
							d="M7.95 6.536l4.242-4.243a1 1 0 111.415 1.414L9.364 7.95l4.243 4.242a1 1 0 11-1.415 1.415L7.95 9.364l-4.243 4.243a1 1 0 01-1.414-1.415L6.536 7.95 2.293 3.707a1 1 0 011.414-1.414L7.95 6.536z" />
					</svg>
				</button>
			</div>
			<div v-if="tab === 'upload'" class="flex pl-4 pr-4 pb-4">
				<label for="image-upload"
					class="w-full flex items-center justify-center bg-ralbackground-light-tertiary text-ralbackground-dark-base p-2 rounded-lg mt-2 hover:bg-opacity-80 transition-all duration-300 cursor-pointer">
					<span v-if="!isUploading">Upload File</span>
					<svg v-if="isUploading" class="animate-spin w-4 h-4 fill-current shrink-0 mr-2" viewBox="0 0 16 16">
						<path
							d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
					</svg>

				</label>
				<input type="file" id="image-upload" class="hidden" @change="handleUpload" />
			</div>

			<div v-if="tab === 'gallery'">

				<div class="mt-2">

					<div class="pl-4 pr-4 m-0">
						<input
							class="no-focus-outline text-ralsecondary-start text-base font-medium font-['Inter'] bg-transparent appearance-none outline-none rounded-lg border border-gray-400 w-full"
							placeholder="Search available images" v-model="fuzzyFilterText" />
					</div>

					<div v-for="(images, category) in categorizedImages" :key="category" class="pl-4 pr-4 pb-4">
						<h2 class="text-lg font-bold my-4">{{ category }}</h2>
						<div class="grid grid-cols-3 gap-4 z-20">
							<div v-for="(image, index) in images" :key="index" class="relative">
								<div class="gallery-item cursor-pointer" :title="image.fullName"
									@click="selectImage(image)">
									<img :src="image.src" :alt="image.name"
										class="thumbnail-image rounded-lg sm:w-full sm:h-24" />
									<div class="overlay absolute inset-0 hidden group-hover:block"></div>
								</div>
								<div class="text-sm truncate text-ralbackground-dark-secondary text-center mt-2">
									<template v-if="image.user">
										By
										<a :href="`${image.user.links.html}?utm_source=raleon&utm_medium=referral`"
											target="_blank" class="underline hover:text-ralpurple-500" :title="image.name">
											{{ image.name }}
										</a>
									</template>
									<template v-else>
										{{ image.name }}
									</template>
								</div>
							</div>
						</div>
					</div>
					<div class="flex mt-2 mb-2 text-xs items-center justify-center text-ralbackground-dark-secondary">Search
						to see more images.</div>
				</div>
			</div>
			<div v-if="tab === 'icons'" class="flex flex-col pl-4 pr-4 pb-4">
				<div class="color-picker-container">
					<LvColorPicker class="color-picker text-sm font-semibold font-['Inter']" label="Icon Fill Color"
						v-model="selectedFillColor" :clearable="false" :value="selectedFillColor" :bottomBar="false" />
				</div>

				<div class="icons-grid">
					<div v-for="(icon, index) in updatedSvgIcons" :key="index" class="icon-container"
						:style="{ backgroundColor: icon.backgroundColor }"
						@click="selectIcon(icon)">
						<div v-html="icon.svgData"></div>
					</div>
				</div>
				<div class="flex mt-2 mb-2 text-xs items-center justify-center text-ralbackground-dark-secondary">Hint: Icon background will be be transparent. The background color here is to ensure visibility.</div>
			</div>

		</div>
	</div>
</template>

<script>
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue';
import * as Utils from '../../client-old/utils/Utils';
import Fuse from 'fuse.js'
import LvColorPicker from 'lightvue/color-picker';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: [
		'minWidth',
		'minHeight',
		'left',
		'right',
		'containerClasses',
		'popoverClasses',
		'showCloseButton',
		'aspectRatio',
		'buttonText'
	],
	components: {
		LightSecondaryButton,
		LvColorPicker
	},
	emits: ['uploadImage', 'imageSelected', 'imageUploading'],
	data() {
		return {
			showTooltip: false,
			tab: 'upload',
			selectedImage: null,
			isUploading: false,
			fuzzyFilterText: '',
			orgImageList: [],
			orgImageFuse: null,
			orgImageListLimit: 30,
			stockImageMetadata: [],
			stockImageFuse: null,
			stockImageListLimit: 30,
			unsplashImages: [],
			initialUnsplashImages: [],
			fetchDebouncedUnsplashImages: async () => { },
			selectedFillColor: '#000000',
			selectedIcon: null,
			svgIcons: [
				{ name: 'Icon2', svgData: '<svg fill="#15803D" slot="points-per-reward" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M480-165q-17 0-33-7.5T419-194L113-560q-9-11-13.5-24T95-611q0-9 1.5-18.5T103-647l75-149q11-20 29.5-32t41.5-12h462q23 0 41.5 12t29.5 32l75 149q5 8 6.5 17.5T865-611q0 14-4.5 27T847-560L541-194q-12 14-28 21.5t-33 7.5Zm-95-475h190l-60-120h-70l-60 120Zm55 347v-267H218l222 267Zm80 0 222-267H520v267Zm144-347h106l-60-120H604l60 120Zm-474 0h106l60-120H250l-60 120Z"/></svg>' },
				{ name: 'Icon3', svgData: '<svg fill="#15803D" slot="dollar-off-reward" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M570-104q-23 23-57 23t-57-23L104-456q-11-11-17.5-26T80-514v-286q0-33 23.5-56.5T160-880h286q17 0 32 6.5t26 17.5l352 353q23 23 23 56.5T856-390L570-104Zm-57-56 286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640ZM160-800Z"/></svg>' },
				{ name: 'Icon4', svgData: '<svg fill="#15803D" slot="percent-off-reward" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M300-520q-58 0-99-41t-41-99q0-58 41-99t99-41q58 0 99 41t41 99q0 58-41 99t-99 41Zm0-80q25 0 42.5-17.5T360-660q0-25-17.5-42.5T300-720q-25 0-42.5 17.5T240-660q0 25 17.5 42.5T300-600Zm360 440q-58 0-99-41t-41-99q0-58 41-99t99-41q58 0 99 41t41 99q0 58-41 99t-99 41Zm0-80q25 0 42.5-17.5T720-300q0-25-17.5-42.5T660-360q-25 0-42.5 17.5T600-300q0 25 17.5 42.5T660-240Zm-472 52q-11-11-11-28t11-28l528-528q11-11 28-11t28 11q11 11 11 28t-11 28L244-188q-11 11-28 11t-28-11Z"/></svg>' },
				{ name: 'Icon5', svgData: '<svg fill="#15803D" slot="free-product-reward" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M160-160v-360q-33 0-56.5-23.5T80-600v-80q0-33 23.5-56.5T160-760h128q-5-9-6.5-19t-1.5-21q0-50 35-85t85-35q23 0 43 8.5t37 23.5q17-16 37-24t43-8q50 0 85 35t35 85q0 11-2 20.5t-6 19.5h128q33 0 56.5 23.5T880-680v80q0 33-23.5 56.5T800-520v360q0 33-23.5 56.5T720-80H240q-33 0-56.5-23.5T160-160Zm400-680q-17 0-28.5 11.5T520-800q0 17 11.5 28.5T560-760q17 0 28.5-11.5T600-800q0-17-11.5-28.5T560-840Zm-200 40q0 17 11.5 28.5T400-760q17 0 28.5-11.5T440-800q0-17-11.5-28.5T400-840q-17 0-28.5 11.5T360-800ZM160-680v80h280v-80H160Zm280 520v-360H240v360h200Zm80 0h200v-360H520v360Zm280-440v-80H520v80h280Z"/></svg>' },
				{ name: 'Icon6', svgData: '<svg fill="#15803D" slot="free-shipping-reward" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M440-183v-274L200-596v274l240 139Zm80 0 240-139v-274L520-457v274Zm-80 92L160-252q-19-11-29.5-29T120-321v-318q0-22 10.5-40t29.5-29l280-161q19-11 40-11t40 11l280 161q19 11 29.5 29t10.5 40v318q0 22-10.5 40T800-252L520-91q-19 11-40 11t-40-11Zm200-528 77-44-237-137-78 45 238 136Zm-160 93 78-45-237-137-78 45 237 137Z" /></svg>' },
				{ name: 'Icon7', svgData: '<svg fill="#15803D" slot="ig-follow" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 0 56.7 56.7" width="33"> <g> <path d="M28.2,16.7c-7,0-12.8,5.7-12.8,12.8s5.7,12.8,12.8,12.8S41,36.5,41,29.5S35.2,16.7,28.2,16.7z M28.2,37.7 c-4.5,0-8.2-3.7-8.2-8.2s3.7-8.2,8.2-8.2s8.2,3.7,8.2,8.2S32.7,37.7,28.2,37.7z" /> <circle cx="41.5" cy="16.4" r="2.9" /> <path d="M49,8.9c-2.6-2.7-6.3-4.1-10.5-4.1H17.9c-8.7,0-14.5,5.8-14.5,14.5v20.5c0,4.3,1.4,8,4.2,10.7c2.7,2.6,6.3,3.9,10.4,3.9 h20.4c4.3,0,7.9-1.4,10.5-3.9c2.7-2.6,4.1-6.3,4.1-10.6V19.3C53,15.1,51.6,11.5,49,8.9z M48.6,39.9c0,3.1-1.1,5.6-2.9,7.3 s-4.3,2.6-7.3,2.6H18c-3,0-5.5-0.9-7.3-2.6C8.9,45.4,8,42.9,8,39.8V19.3c0-3,0.9-5.5,2.7-7.3c1.7-1.7,4.3-2.6,7.3-2.6h20.6 c3,0,5.5,0.9,7.3,2.7c1.7,1.8,2.7,4.3,2.7,7.2V39.9L48.6,39.9z" /> </g> </svg>' },
				{ name: 'Icon8', svgData: '<svg fill="#15803D" slot="fb-follow" xmlns="http://www.w3.org/2000/svg" height="33" width="33" version="1.1" viewBox="0 0 512 512" xml:space="preserve"> <path d="M288,192v-38.1c0-17.2,3.8-25.9,30.5-25.9H352V64h-55.9c-68.5,0-91.1,31.4-91.1,85.3V192h-45v64h45v192h83V256h56.4l7.6-64  H288z M330.2,240h-41.1H272v15.5V432h-51V255.5V240h-14.9H176v-32h30.1H221v-16.5v-42.2c0-24.5,5.4-41.2,15.5-51.8  C247.7,85.5,267.6,80,296.1,80H336v32h-17.5c-12,0-27.5,1.1-37.1,11.7c-8.1,9-9.4,20.1-9.4,30.1v37.6V208h17.1H334L330.2,240z" stroke="#15803D" stroke-width="15" /> </svg>' },
				{ name: 'Icon9', svgData: '<svg viewBox="0 0 256 256" fill="#15803D" width="33" height="33" slot="tt-follow" xmlns="http://www.w3.org/2000/svg"> <rect fill=\'none\' height="256" width="256" /> <path d="M168,106a95.9,95.9,0,0,0,56,18V84a56,56,0,0,1-56-56H128V156a28,28,0,1,1-40-25.3V89.1A68,68,0,1,0,168,156Z" fill=\'none\' stroke="#15803D" stroke-linecap="round" stroke-linejoin="round" stroke-width="16" /> </svg>' },
			],
		};
	},
	created() {
		const debounce = (fn, wait) => {
			let timer;
			return function (...args) {
				return new Promise((res, rej) => {
					if (timer) clearTimeout(timer);

					const context = this;
					timer = setTimeout(async () => {
						try {
							const result = await fn.apply(context, args);
							res(result);
						} catch (error) {
							rej(error);
						}
					}, wait);
				})

			}
		}
		this.fetchDebouncedUnsplashImages = debounce(this.fetchUnsplashImages, 500);
	},
	watch: {
		async fuzzyFilterText(newValue) {
			const perPage = newValue == '' ? 3 : 21;
			const images = await this.fetchDebouncedUnsplashImages(newValue, 1, perPage);
			const newImages = Array.isArray(images) ? images.map(x => ({
				...x,
				src: x.urls.thumb,
				alt: x.alt_description,
				name: x.user.name,
				fullName: `${x.alt_description} by ${x.user.name}`
			})) : [];
			this.unsplashImages = [...newImages];
		},
		selectedFillColor(newColor) {
			if (this.selectedIcon) {

			}
		},
	},
	computed: {
		updatedSvgIcons() {
			return this.svgIcons.map(icon => {
				let updatedSvgData = icon.svgData.replace(/fill="[^"]*"/g, `fill="${this.selectedFillColor}"`);
				if (icon.svgData.includes('stroke="')) {
					updatedSvgData = updatedSvgData.replace(/stroke="[^"]*"/g, `stroke="${this.selectedFillColor}"`);
				}
				let backgroundColor = this.getBackgroundColorForIcon(this.selectedFillColor);
				return { ...icon, svgData: updatedSvgData, backgroundColor: backgroundColor };
			});
		},
		categorizedImages() {
			const searchPattern = this.fuzzyFilterText;

			const orgImageList = searchPattern
				? this.orgImageFuse.search(searchPattern).map(x => x.item)
				: this.orgImageList.slice(0, 9);

			const unsplashImages = Array.isArray(this.unsplashImages) ? this.unsplashImages.map(x => ({
				...x,
				src: x.urls.thumb,
				alt: x.alt_description,
				name: x.user.name,
				fullName: `${x.alt_description} by ${x.user.name}`
			})) : [];

			return {
				'Your Images': orgImageList.slice(0, this.orgImageListLimit).map(x => ({
					id: x.url,
					src: x.url,
					alt: 'User Image',
					name: x.name,
					fullName: x.name
				})),
				'Images from Unsplash': unsplashImages
			}
		}
	},
	methods: {
		getBackgroundColorForIcon(fillColor) {
			const color = this.hexToRgb(fillColor);
			// Calculate luminance
			const luminance = (0.299 * color.r + 0.587 * color.g + 0.114 * color.b) / 255;
			// If luminance is high (light color), return dark background, else light background
			return luminance > 0.7 ? '#333333' : '#ffffff';
		},
		hexToRgb(hex) {
			let r = 0, g = 0, b = 0;
			// 3 digits
			if (hex.length == 4) {
				r = parseInt(hex[1] + hex[1], 16);
				g = parseInt(hex[2] + hex[2], 16);
				b = parseInt(hex[3] + hex[3], 16);
			}
			// 6 digits
			else if (hex.length == 7) {
				r = parseInt(hex[1] + hex[2], 16);
				g = parseInt(hex[3] + hex[4], 16);
				b = parseInt(hex[5] + hex[6], 16);
			}
			return {r, g, b};
		},
		closeModal() {
			this.handleClickOutside();
		},
		async selectIcon(icon) {
			this.selectedIcon = icon;
			const url = await this.convertAndUploadSvg(this.selectedIcon.svgData);
			if (url) {
				console.log('SVG uploaded, URL:', url);
				this.$emit('imageSelected', { url: url });
			}
			this.showTooltip = false;
		},
		uploadIcon() {
			const customizedIcon = this.applyFillColorToIcon(this.selectedIcon, this.selectedFillColor);
		},

		applyFillColorToIcon(icon, fillColor) {
			const svgData = icon.svgData;
			return svgData.replace(/fill="[^"]*"/g, `fill="${fillColor}"`);
		},
		toggleTooltip() {
			console.log('toggleTooltip');
			this.showTooltip = !this.showTooltip;
		},
		handleClickOutside() {
			//console.log('****handleClickOutside', this.showTooltip, event.target, this.$refs.tooltip)
			//if (this.showTooltip && (!this.$refs.tooltip || !this.$refs.tooltip.contains(event.target))) {
			this.showTooltip = false;
			this.tab = 'upload';
			this.unsplashImages = [...this.initialUnsplashImages];
			this.fuzzyFilterText = '';
			//}
		},
		close(event) {
			this.toggleTooltip();
			this.tab = 'upload';
			this.fuzzyFilterText = '';
		},
		async selectImage(image) {
			if (!image.user) { //not from unsplash
				this.selectedImage = image.id;
				this.showTooltip = false;
				this.$emit('imageSelected', {
					url: image.id
				});
			} else {
				this.selectedImage = image.urls.small;
				this.showTooltip = false;
				this.$emit('imageSelected', {
					url: image.urls.small
				});
			}

			this.tab = 'upload';
			this.fuzzyFilterText = '';
		},
		async handleUpload(event) {
			console.log('handleUpload')
			const file = event.target.files[0];
			const fileUrl = await this.handleFileUpload(file, this.aspectRatio || 1);
			this.$emit('imageSelected', { url: fileUrl });
			await this.fetchImageLibrary();
			this.toggleTooltip();
		},
		async fetchUnsplashImages(searchText, page = 1, perPage = 3) {
			try {
				const params = new URLSearchParams({
					query: searchText || 'loyalty program',
					page: page,
					per_page: perPage,
					orientation: 'landscape'
				});
				const url = `${URL_DOMAIN}/unsplash-images?${params.toString()}`;
				const response = await fetch(url, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					}
				});
				return await response.json();
			} catch (err) {
				console.log("Error fetching unsplash images");
			}
		},
		async fetchImageLibrary() {
			const [orgImageList, unsplashImages] = await Promise.all([
				this.fetchOrgImageList(),
				this.fetchUnsplashImages(),
			]);

			this.orgImageList = orgImageList.map(x => ({
				url: x,
				name: x.split('/').reverse()[0]
			}));

			this.unsplashImages = unsplashImages.map(x => ({
				...x,
				url: x.urls.thumb,
				name: x.alt_description,
				description: x.description,
				tags: x.tags?.map(x => x.title),
			}));

			this.initialUnsplashImages = this.unsplashImages;
			this.orgImageFuse = new Fuse(this.orgImageList, {
				keys: ["name"]
			});
		},
		async fetchOrgImageList() {
			const response = await fetch(`${URL_DOMAIN}/images/loyalty-image-gallery/org-images`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});

			const data = await response.json();
			//console.log('Org Image List:', data);
			return data;
		},
		async fetchStockImageMetadata() {
			const response = await fetch('https://dqpqjbq51w8fz.cloudfront.net/images/stock-images/stock-image-gallery.json');

			return response.json();
		},
		async handleUnsplashDownload(image) {
			fetch(`${URL_DOMAIN}/unsplash-track-download?download_location=${image.links.download_location}`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			}).then(response => {
				response.json().catch(error => {
					console.log(`error tracking download: ${JSON.stringify(error)}`)
				});
			});
		},
		async handleFileUpload(file, aspectRatio) {
			this.isUploading = true;
			this.$emit('imageUploading');
			return new Promise((resolve, reject) => {
				if (!file) {
					return reject(new Error('No file is selected'));
				}

				console.log('Step 1')
				// Create an image element
				const img = document.createElement('img');
				const reader = new FileReader();

				reader.onload = (e) => {
					img.src = e.target.result;


					img.onload = async () => {
						// Target aspect ratio
						const targetRatio = aspectRatio;

						console.log('Target Ratio:', targetRatio, 'Image:', img.width, img.height);
						// Image's aspect ratio
						const imageRatio = img.width / img.height;

						console.log('Image Ratio:', imageRatio)

						let cropWidth, cropHeight;

						if (this.aspectRatio != 'auto') {
							if (imageRatio > targetRatio) {
							console.log('Crop width')
							cropHeight = img.height;
							cropWidth = img.width;
							console.log('Crop width:', cropWidth, 'Crop height:', cropHeight);
							} else {
								// Otherwise, crop the height
								console.log('Crop height')
								cropWidth = img.width;
								cropHeight = img.width / targetRatio;
								console.log('Crop width:', cropWidth, 'Crop height:', cropHeight);
							}
						}

						// Calculate the position to start the crop so that we crop from the center
						let startX = (img.width - cropWidth) / 2;
						let startY = (img.height - cropHeight) / 2;

						if (this.aspectRatio == 'auto') {
							cropHeight = img.height;
							cropWidth = img.width;
							startX = 0;
							startY = 0;
						}

						// Set canvas size to the cropped image size
						const canvas = document.createElement('canvas');
						canvas.width = cropWidth;
						canvas.height = cropHeight;
						const ctx = canvas.getContext('2d');

						// Draw the image on canvas with the desired dimensions
						ctx.drawImage(img, startX, startY, cropWidth, cropHeight, 0, 0, cropWidth, cropHeight);

						// Convert canvas to blob and upload
						canvas.toBlob(async (blob) => {
							try {
								const formData = new FormData();
								formData.append('file', blob, file.name);

								const endpoint = `${URL_DOMAIN}/branding/image/upload`;

								const response = await fetch(endpoint, {
									method: 'POST',
									credentials: 'include',
									headers: {
										Authorization: `Bearer ${localStorage.getItem('token')}`
									},
									body: formData
								});

								if (!response.ok) {
									throw new Error('Network response was not ok.');
								}

								const result = await response.text();
								console.log('Uploaded URL:', result);
								resolve(result);
							} catch (error) {
								reject(error);
							}
						}, 'image/png');
					};
				};

				reader.onerror = (error) => {
					console.log("Error: ", error);
					reject(error);
				};

				reader.readAsDataURL(file);
			}).then(x => (this.isUploading = false, x)).catch(() => this.isUploading = false);
		},
		async convertSvgToPng(svgData, width, height) {
			return new Promise((resolve, reject) => {
				// Create an SVG Blob
				const blob = new Blob([svgData], { type: 'image/svg+xml' });
				const url = URL.createObjectURL(blob);

				// Create an Image to Draw into Canvas
				let img = new Image();
				img.onload = () => {
					// Create Canvas
					let canvas = document.createElement('canvas');
					canvas.width = width;
					canvas.height = height;
					let ctx = canvas.getContext('2d');
					ctx.drawImage(img, 0, 0, width, height);

					// Convert Canvas to PNG
					canvas.toBlob((blob) => {
						resolve(blob);
					}, 'image/png');
				};

				img.onerror = () => {
					reject(new Error('Error in loading SVG for conversion'));
				};

				img.src = url;
			});
		},

		async uploadPng(pngBlob) {
			this.isUploading = true;
			this.$emit('imageUploading');

			try {
				const formData = new FormData();
				let randomString = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
				formData.append('file', pngBlob, `icon_${randomString}.png`);

				// Upload the PNG blob
				const endpoint = `${URL_DOMAIN}/branding/image/upload`;
				const response = await fetch(endpoint, {
					method: 'POST',
					credentials: 'include',
					headers: {
						Authorization: `Bearer ${localStorage.getItem('token')}`
					},
					body: formData
				});

				if (!response.ok) {
					throw new Error('Network response was not ok.');
				}

				const result = await response.text();
				return result;  // The URL of the uploaded PNG
			} catch (error) {
				console.error('Error during PNG upload:', error);
				return null;
			} finally {
				this.isUploading = false;
			}
		},


		async convertAndUploadSvg(svgData) {
			try {
				const width = 512;
				const height = 512;

				const pngBlob = await this.convertSvgToPng(svgData, width, height);
				const uploadUrl = await this.uploadPng(pngBlob);
				console.log('Uploaded PNG URL:', uploadUrl);
				return uploadUrl;
			} catch (error) {
				console.error('Error during SVG to PNG conversion and upload:', error);
				return null;
			}
		},
	},
	mounted() {
		this.fetchImageLibrary().catch();
		//document.addEventListener('click', this.handleClickOutside);
	},
	beforeDestroy() {
		//document.removeEventListener('click', this.handleClickOutside);
	}
};
</script>

<style scoped>

.gallery-item:hover {
	transform: scale(1.1);
	background-color: rgba(0, 0, 0, 0.1);
}

.gallery-item {
	position: relative;
	width: 100%;
	padding-top: 56.25%;
	overflow: hidden;
}

.thumbnail-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

.centered-popup {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 500;
	width: 50vw;
	height: 60vh;
	min-height: 400px;
	max-height: 90%;
	border: 1px solid #e5e7eb;
}

.centered-popup-upload {
	position: fixed;
	top: 50%;
	left: 50%;
	height: 140px;
	transform: translate(-50%, -50%);
	z-index: 500;
	width: 50vw;
	min-height: 140px;
	max-height: 90%;
	overflow: hidden;
	border: 1px solid #e5e7eb;
}

.color-picker-container {
	margin-bottom: 10px;
	margin-top: 10px;
}

.icon-picker-container {
	padding: 16px;
}

.color-picker {
	margin-bottom: 20px;
}

.icons-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
	gap: 10px;
}

.icon-container {
	width: 75px;
	height: 75px;
	flex: 0 0 75px;
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;
	transition: all 0.3s ease;
}

.icon-container:hover {
	transform: scale(1.1);
	background-color: rgba(0, 0, 0, 0.1);
}

.icon-container div {
	max-width: 100%;
	max-height: 100%;
}

.icon-container svg {
	max-width: 100%;
	max-height: 100%;
}

.overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0);
	z-index: 499;
}
</style>
