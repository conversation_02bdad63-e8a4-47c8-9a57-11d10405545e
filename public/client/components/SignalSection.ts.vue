<template>
	<div>
		<div
			:class="sectionClasses"
			@dragover.prevent
			@dragenter.prevent="handleDragEnter"
			@dragleave="handleDragLeave"
			@drop="handleDrop"
		>
			<signal-badge
				v-for="signal in signals"
				:key="signal.id"
				:signal="signal"
				:section="section"
				@click="handleSignalClick"
			/>
			<div v-if="!signals || signals.length === 0" class="text-gray-400 text-sm text-center p-4">
				<p class="text-xs mt-1">Drag signals here to include them in the segment</p>
			</div>
		</div>
	</div>
</template>

<script>
import SignalBadge from './SignalBadge.ts.vue';

export default {
	name: 'SignalSection',
	components: {
		SignalBadge,
	},
	props: {
		title: { type: String, required: true },
		section: { type: String, required: true },
		signals: { type: Array, default: () => [] },
		isDraggingOver: { type: String, default: null },
	},
	computed: {
		sectionClasses() {
			return [
				'min-h-48 mt-4 transition-colors duration-200 drop-zone',
				this.isDraggingOver === this.section ? 'bg-blue-50 border-2 border-blue-400' : 'bg-white border border-gray-200',
				(!this.signals || this.signals.length === 0) ? 'flex items-center justify-center' : 'flex flex-wrap content-start',
			];
		},
	},
	methods: {
		handleSignalClick(data) {
			this.$emit('signal-click', data);
		},
		handleDrop(event) {
			event.preventDefault();
			const signalId = parseInt(event.dataTransfer.getData('signalId'));
			const sourceSection = event.dataTransfer.getData('sourceSection');
			this.$emit('drop', { signalId, sourceSection, targetSection: this.section });
		},
	},
};


</script>

<style scoped>
/* Add padding to the drop zone container */
.drop-zone {
	padding: 1rem; /* Adjust as needed */
}
</style>
