<template>
	<div
		class="w-full h-fit max-w-[300px] bg-white bg-opacity-75 rounded-2xl border border-violet-200 p-2 md:p-7 mt-4 md:mt-0 md:ml-4">
		<span class="font-[Inter] font-semibold text-ralsecondary-start text-lg">Summary</span>
		<hr class="border-t border-ralbackground-light-line w-full mt-4 mb-4">

		<div>
			<p class="font-[Inter] font-medium text-ralgray-dark text-base">Customer Behavior</p>

			<div class="rounded-lg bg-white border px-4 py-2 mt-2 flex items-center">
				<WTEActionIcon :icon-type="wteData?.imageSlotKey" class="mr-3"></WTEActionIcon>
				<div>
					<p class="font-[Inter] font-base text-ralgray-dark text-sm">{{ getWTEName }}</p>
					<p class="font-[Inter] font-base text-ralgray-dark text-xs"> {{ getWTEDetail }}</p>
				</div>
			</div>

		</div>
		<hr class="border-t border-ralbackground-light-line w-full mt-4 mb-4">

		<div>
			<p class="font-[Inter] font-medium text-ralgray-dark text-base">Give Customer Reward(s)</p>

			<div v-if="getRewardHistory.length > 0 || getEditingRewardName != 'Unknown'">
				<div v-for="reward in getRewardHistory" :key="reward.id">
					<div class="rounded-lg bg-white border px-4 py-2 mt-2 flex items-center">
						<!-- <svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960"
							width="33">
							<path
								d="M480-165q-17 0-33-7.5T419-194L113-560q-9-11-13.5-24T95-611q0-9 1.5-18.5T103-647l75-149q11-20 29.5-32t41.5-12h462q23 0 41.5 12t29.5 32l75 149q5 8 6.5 17.5T865-611q0 14-4.5 27T847-560L541-194q-12 14-28 21.5t-33 7.5Zm-95-475h190l-60-120h-70l-60 120Zm55 347v-267H218l222 267Zm80 0 222-267H520v267Zm144-347h106l-60-120H604l60 120Zm-474 0h106l60-120H250l-60 120Z" />
						</svg> -->
						<WTETypeIcon :icon-type="reward.imageSlotKey" class="mr-3"></WTETypeIcon>
						<div>
							<p class="font-[Inter] font-base text-ralgray-dark text-sm">{{ replaceCurrencyTagsSync(reward.title) }}</p>
							<p class="font-[Inter] font-base text-ralgray-dark text-xs">{{ reward.subtitle }}</p>
						</div>
					</div>
				</div>
				<!-- <div class="rounded-lg bg-white border px-4 py-2 mt-2 flex items-center" v-if="getEditingRewardName != 'Unknown'">
					<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M480-165q-17 0-33-7.5T419-194L113-560q-9-11-13.5-24T95-611q0-9 1.5-18.5T103-647l75-149q11-20 29.5-32t41.5-12h462q23 0 41.5 12t29.5 32l75 149q5 8 6.5 17.5T865-611q0 14-4.5 27T847-560L541-194q-12 14-28 21.5t-33 7.5Zm-95-475h190l-60-120h-70l-60 120Zm55 347v-267H218l222 267Zm80 0 222-267H520v267Zm144-347h106l-60-120H604l60 120Zm-474 0h106l60-120H250l-60 120Z"/></svg>
					<div class="ml-2">
						<p class="font-[Inter] font-base text-ralgray-dark text-sm">{{ getEditingRewardName }}</p>
						<div v-for="condition in getEditingRewardConditions" :key="condition.id">
						<p class="font-[Inter] font-base text-ralgray-dark text-xs">{{condition.text}}</p>
						</div>
					</div>
				</div>-->
			</div>
			<div v-else-if="(getRewardHistory.length <= 0 || getRewardHistory == 0) && getEditingRewardName == 'Unknown'">
				<p class="font-[Inter] text-sm text-ralprimary-dark mt-4 cursor-pointer">Add Rewards</p>
			</div>
		</div>
	</div>
</template>

<script>
import * as CurrencyUtils from '../services/currency.js';
import WTETypeIcon from './WTETypeIcon.ts.vue';
import WTEActionIcon from './WTEActionIcon.ts.vue';
export default {
	props: ['wteData', 'editingRewardData', 'rewardListData'],
	components: {
		WTETypeIcon,
		WTEActionIcon,
	},
	data() {
		return {

		};
	},
	computed: {
		getWTEName() {
			console.log('NAME', this.wteData?.title, this.wteData)
			if (this.wteData != null)
				return this.wteData?.title;
			else
				return "Select a way to earn";
		},
		getWTEDetail() {
			if (this.wteData != null)
				return this.wteData?.subtitle;
			else
				return '';
		},
		getEditingRewardName() {
			if (this.editingRewardData && this.editingRewardData.title !== undefined)
				return this.editingRewardData.title;
			else
				return "Unknown";
		},
		getEditingRewardConditions() {
			if (this.editingRewardData && this.editingRewardData.uiRewardRestrictions !== undefined)
				return this.editingRewardData.uiRewardRestrictions;
			else
				return null;
		},
		getRewardHistory() {
			if (this.rewardListData && this.rewardListData !== undefined)
				return this.rewardListData;
			else
				return 0;
		}
	},
	mounted() {

	},
	methods: {
		replaceCurrencyTagsSync(text) {
			return CurrencyUtils.replaceCurrencyTagsSync(text);
		},
		formatWTE() {

		},
	},
};
</script>

<style>
.fade-enter-active,
.fade-leave-active {
	transition-property: opacity, transform;
	transition-duration: 500ms;
	transition-timing-function: ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
	opacity: 0;
	transform: translateX(10px);
}

.fade-enter-to,
.fade-leave-from {
	opacity: 1;
	transform: translateX(0);
}
</style>
