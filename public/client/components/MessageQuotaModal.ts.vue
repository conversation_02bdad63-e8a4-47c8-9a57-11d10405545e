<template>
  <!-- Modal backdrop -->
  <transition
    enter-active-class="transition ease-out duration-200"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition ease-out duration-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div v-show="show" class="fixed inset-0 bg-slate-900 bg-opacity-30 z-[9999] transition-opacity" aria-hidden="true" @click="handleOverlayClick"></div>
  </transition>

  <!-- Modal dialog -->
  <transition
    enter-active-class="transition ease-in-out duration-200"
    enter-from-class="opacity-0 translate-y-4"
    enter-to-class="opacity-100 translate-y-0"
    leave-active-class="transition ease-in-out duration-200"
    leave-from-class="opacity-100 translate-y-0"
    leave-to-class="opacity-0 translate-y-4"
  >
    <div v-show="show" class="fixed inset-0 z-[10000] overflow-hidden flex items-center my-4 justify-center transform px-4 sm:px-6" role="dialog" aria-modal="true">
      <div :class="['quota-modal', { 'showing-success': showSuccessAnimation }]" @click.stop>
        <!-- Header -->
        <div class="modal-header">
          <div class="header-content">
            <div class="message-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H18L22 22V4C22 2.9 21.1 2 20 2Z" fill="white" stroke="none"/>
                <circle cx="7" cy="9" r="1.5" fill="currentColor"/>
                <circle cx="12" cy="9" r="1.5" fill="currentColor"/>
                <circle cx="17" cy="9" r="1.5" fill="currentColor"/>
              </svg>
            </div>
            <div>
              <h2 class="modal-title">Message Usage</h2>
              <p class="modal-subtitle">{{ planName }}</p>
            </div>
          </div>
          <button @click="$emit('close')" class="close-button">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>

        <!-- Usage Summary -->
        <div class="usage-summary">
          <div class="remaining-count" :class="{ 'animating': isAnimating, 'text-ralerror-dark': totalMessages <= 0 }">
            {{ isAnimating ? animatedTotal : totalMessages }}
          </div>
          <div class="remaining-label">
            <span v-if="totalMessages <= 0">⚠️ You're out of messages</span>
            <span v-else>total messages available</span>
          </div>
          <div class="reset-info" :class="{ 'out-of-messages': totalMessages <= 0 }">
            <span v-if="totalMessages <= 0">Earn more messages below or your daily quota resets on {{ resetDate }}</span>
            <span v-else>Daily usage resets on {{ resetDate }}</span>
          </div>
        </div>

        <!-- Get More Messages Section -->
        <div class="earn-section">
          <h3 class="section-title">⚡ Get More Messages</h3>

          <div class="task-item">
            <div class="task-icon-badge shopify">CS</div>
            <div class="task-content">
              <div class="task-title">Connect Shopify</div>
              <div class="task-reward">+20 messages</div>
            </div>
            <button
              @click="handleTask('shopify')"
              :disabled="earned.shopify"
              :class="['task-button', {
                'claimed': earned.shopify,
                'connect': !integrationStatus.shopify,
                'claim': integrationStatus.shopify && !earned.shopify
              }]"
            >
              <span v-if="earned.shopify">Claimed</span>
              <span v-else-if="integrationStatus.shopify">Claim</span>
              <span v-else>Connect</span>
            </button>
          </div>

          <div class="task-item">
            <div class="task-icon-badge email">✉</div>
            <div class="task-content">
              <div class="task-title">Connect Klaviyo</div>
              <div class="task-reward">+20 messages</div>
            </div>
            <button
              @click="handleTask('klaviyo')"
              :disabled="earned.klaviyo"
              :class="['task-button', {
                'claimed': earned.klaviyo,
                'connect': !integrationStatus.klaviyo,
                'claim': integrationStatus.klaviyo && !earned.klaviyo
              }]"
            >
              <span v-if="earned.klaviyo">Claimed</span>
              <span v-else-if="integrationStatus.klaviyo">Claim</span>
              <span v-else>Connect</span>
            </button>
          </div>

          <div class="task-item">
            <div class="task-icon-badge daily">🔵</div>
            <div class="task-content">
              <div class="task-title">Daily Login</div>
              <div class="task-reward">+5 messages</div>
            </div>
            <button
              @click="handleTask('dailyLogin')"
              :disabled="earned.dailyLogin || !canClaimDailyLogin"
              :class="['task-button', {
                'claimed': earned.dailyLogin,
                'claim': canClaimDailyLogin && !earned.dailyLogin
              }]"
            >
              <span v-if="earned.dailyLogin">Claimed</span>
              <span v-else-if="canClaimDailyLogin">Claim</span>
              <span v-else>{{ timeUntilNextClaim }}</span>
            </button>
          </div>
        </div>

        <!-- Trial Active Section -->
        <div v-if="isTrialActive" class="trial-section">
          <div class="trial-header">
            <span class="trial-icon">⏱️</span>
            <span class="trial-title">Free Trial Active</span>
            <div class="trial-badge">{{ trialDaysLeft }} days left</div>
          </div>
          <div class="trial-description">Enjoying all the benefits of <span class="raleon-max-text">Raleon Pro</span></div>
          <div class="trial-cta">
            <span class="trial-cta-text">Continue after trial prices start at $20/month</span>
          </div>
          <button class="trial-button" @click="navigateToPlans">View Plans</button>
        </div>

        <!-- Regular Upgrade Section -->
        <div v-else-if="shouldShowUpgrade" class="upgrade-section">
          <div class="upgrade-header">
            <span class="crown-icon">👑</span>
            <span class="upgrade-title">Upgrade to {{ upgradePlanName }}</span>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="arrow-icon">
              <path d="M7 17L17 7M17 7H7M17 7V17" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="upgrade-description">Unlimited messages & advanced features</div>
          <div class="upgrade-pricing">
            <span class="price">${{ upgradePlanPrice }}/month</span>
            <span class="billing">Billed monthly</span>
          </div>
          <button class="upgrade-button" @click="navigateToPlans">Upgrade Now</button>
        </div>


        <!-- Success Animation Overlay -->
        <div v-if="showSuccessAnimation" class="success-overlay">
          <div class="success-animation">
            <div class="success-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
              </svg>
            </div>
            <div class="success-text">+{{ lastReward }} Messages Earned!</div>
            <div class="confetti" v-for="i in 20" :key="i" :style="getConfettiStyle(i)"></div>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import {ref, computed, onMounted, watch} from '@vue/runtime-core';
import {fetchMessageQuota, fetchPlanInfo, useQuotaService, type PlanInfo} from '../services/messageQuotaService';
import { URL_DOMAIN } from '../utils/utils';
import { useRouter } from 'vue-router';
import { customerIOTrackEvent } from '../services/customerio.js';

const props = defineProps<{show:boolean}>();
const emit = defineEmits(['close']);
const router = useRouter();

// Use the shared quota service instead of local state
const quotaService = useQuotaService();
const planInfo = ref<PlanInfo | null>(null);
const earned = ref<Record<string, boolean>>({});
const integrationStatus = ref({shopify: false, klaviyo: false});
const showSuccessAnimation = ref(false);
const lastReward = ref(0);
const lastDailyLoginDate = ref<string>('');
const canClaimDailyLogin = ref(false);
const timeUntilNextClaim = ref('');
const animatedTotal = ref(0);
const isAnimating = ref(false);

const remaining = computed(() => {
  if (!quotaService.quota.value) return 0;
  return Math.max(0, quotaService.quota.value.dailyLimit - quotaService.quota.value.dailyUsed);
});
const totalMessages = computed(() => {
  if (!quotaService.quota.value) return 0;
  return remaining.value + quotaService.quota.value.premium;
});
const usagePercentage = computed(() => {
  if (!quotaService.quota.value || quotaService.quota.value.dailyLimit <= 0) return 0;
  return (quotaService.quota.value.dailyUsed / quotaService.quota.value.dailyLimit) * 100;
});
const resetDate = computed(() => {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  return tomorrow.toLocaleDateString('en-US', { month: 'long', day: 'numeric' });
});

const planName = computed(() => planInfo.value?.currentPlan?.name || 'Free Plan');
const shouldShowUpgrade = computed(() => planInfo.value?.shouldShowUpgrade || false);
const upgradePlanName = computed(() => planInfo.value?.upgradePlan?.name || '');
const upgradePlanPrice = computed(() => planInfo.value?.upgradePlan?.price || 0);
const isTrialActive = computed(() => planInfo.value?.inEffect || false);
const trialDaysLeft = computed(() => planInfo.value?.daysLeft || 0);

async function loadPlanInfo() {
  try {
    const data = await fetchPlanInfo();
    if (data) {
      planInfo.value = data;
    }
  } catch (error) {
    console.error('Error loading plan info:', error);
  }
}

async function load() {
  await quotaService.refreshQuota();
  // Update earned status based on claimed rewards
  if (quotaService.quota.value?.claimedRewards) {
    quotaService.quota.value.claimedRewards.forEach((rewardType: string) => {
      earned.value[rewardType] = true;
    });
  }
}

async function checkIntegrations() {
  const token = localStorage.getItem('token');
  try {
    // Check Shopify connection
    const shopifyResponse = await fetch(`${URL_DOMAIN}/integration/shopify/connected`, {
      headers: {'Authorization': `Bearer ${token}`}
    });
    if (shopifyResponse.ok) {
      const shopifyData = await shopifyResponse.json();
      integrationStatus.value.shopify = shopifyData.connected;
    }

    // Check Klaviyo connection
    const klaviyoResponse = await fetch(`${URL_DOMAIN}/integration/klaviyo/connected`, {
      headers: {'Authorization': `Bearer ${token}`}
    });
    if (klaviyoResponse.ok) {
      const klaviyoData = await klaviyoResponse.json();
      integrationStatus.value.klaviyo = klaviyoData.connected;
    }
  } catch (error) {
    console.error('Error checking integrations:', error);
  }
}

function animateCounter(from: number, to: number, duration: number = 1000) {
  isAnimating.value = true;
  animatedTotal.value = from;

  const startTime = Date.now();
  const difference = to - from;

  const animate = () => {
    const currentTime = Date.now();
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // Easing function for smoother animation
    const easeOutQuart = 1 - Math.pow(1 - progress, 4);

    animatedTotal.value = Math.round(from + (difference * easeOutQuart));

    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      isAnimating.value = false;
    }
  };

  requestAnimationFrame(animate);
}

async function earn(type: string, amount: number = 20) {
  const token = localStorage.getItem('token');
  try {
    const response = await fetch(`${URL_DOMAIN}/messages/quota/earn`, {
      method:'POST',
      headers:{'Authorization':`Bearer ${token}`,'Content-Type':'application/json'},
      body: JSON.stringify({type, amount, reason: `Connected ${type}`})
    });

    const result = await response.json();

    if (response.ok && result.success) {
      const oldTotal = totalMessages.value;

      earned.value[type] = true;
      lastReward.value = amount;
      showSuccessAnimation.value = true;

      // Hide animation after 3 seconds
      setTimeout(() => {
        showSuccessAnimation.value = false;
      }, 3000);

      await load();

      // Animate the counter from old total to new total
      const newTotal = totalMessages.value;
      if (newTotal > oldTotal) {
        animateCounter(oldTotal, newTotal);
      }

      // Emit event to update quota in sidebar
      window.dispatchEvent(new CustomEvent('quotaUpdated'));
    } else if (result.alreadyClaimed) {
      // Already claimed, just update the UI state
      earned.value[type] = true;
      console.log(`Reward ${type} was already claimed`);
    } else {
      console.error('Error earning credits:', result.message);
    }
  } catch (error) {
    console.error('Error earning credits:', error);
  }
}

function checkDailyLoginStatus() {
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

  // Check if already claimed from server data (organization-specific)
  if (earned.value.dailyLogin) {
    canClaimDailyLogin.value = false;
    timeUntilNextClaim.value = 'tomorrow';
  } else {
    // Can claim today
    canClaimDailyLogin.value = true;
    timeUntilNextClaim.value = '';
  }
}

function handleTask(type: string) {
  if (earned.value[type]) return; // Already completed

  if (type === 'dailyLogin') {
    if (canClaimDailyLogin.value) {
      // Claim the daily login bonus
      earn(type, 5);
      checkDailyLoginStatus(); // Update UI state
      customerIOTrackEvent('Claimed Daily');
    }
    return;
  }

  if (integrationStatus.value[type]) {
    // Integration is connected, claim the reward
    if (type === 'shopify') {
	  customerIOTrackEvent('Claimed Shopify');
    } else if (type === 'klaviyo') {
      customerIOTrackEvent('Claimed Klaviyo');
    }
    earn(type, 20);
  } else {
    // Integration not connected, redirect to integrations page
    emit('close');
    router.push('/integrations');
  }
}

function handleOverlayClick() {
  emit('close');
}

function navigateToPlans() {
  emit('close');
  customerIOTrackEvent('Clicked Upgrade from Messages');
  router.push('/loyalty/settings/plans');
}

function getConfettiStyle(index: number) {
  const colors = ['#6e3ff2', '#8b5cf6', '#a855f7', '#c084fc', '#e879f9'];
  const color = colors[index % colors.length];
  const left = Math.random() * 100;
  const delay = Math.random() * 2;

  return {
    backgroundColor: color,
    left: `${left}%`,
    animationDelay: `${delay}s`
  };
}

// Watch for modal open/close to refresh data
watch(() => props.show, async (newShow) => {
  if (newShow) {
    // Modal is opening, refresh all data
    await Promise.all([
      load(),
      loadPlanInfo(),
      checkIntegrations()
    ]);
    checkDailyLoginStatus();
  }
});

onMounted(async () => {
  await quotaService.initializeQuota();
  await Promise.all([
    load(),
    loadPlanInfo(),
    checkIntegrations()
  ]);
  checkDailyLoginStatus();
});
</script>

<style scoped>
/* Main Modal */
.quota-modal {
  background: #f8f9fa;
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  position: relative;
}

/* Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px 12px;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.message-icon {
  background: theme('colors.ralprimary.main');
  border-radius: 8px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #212529;
  margin: 0;
  font-family: 'Inter', sans-serif;
}

.modal-subtitle {
  font-size: 14px;
  color: #6c757d;
  margin: 2px 0 0 0;
  font-family: 'Inter', sans-serif;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  color: #6c757d;
}

.close-button:hover {
  background: #f8f9fa;
  color: #495057;
}

/* Usage Summary */
.usage-summary {
  background: white;
  padding: 24px 20px;
  text-align: center;
}

.remaining-count {
  font-size: 40px;
  font-weight: 700;
  color: #212529;
  line-height: 1;
  margin-bottom: 6px;
  font-family: 'Inter', sans-serif;
  transition: all 0.3s ease;
}

.remaining-count.animating {
  color: theme('colors.ralprimary.main');
  transform: scale(1.1);
  text-shadow: 0 2px 8px rgba(90, 22, 201, 0.3);
}

.remaining-label {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
  font-family: 'Inter', sans-serif;
}

.reset-info {
  font-size: 12px;
  color: #868e96;
  font-family: 'Inter', sans-serif;
  margin-bottom: 20px;
}

.reset-info.out-of-messages {
  color: #dc3545;
  font-weight: 500;
}


/* Earn Section */
.earn-section {
  padding: 20px;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #212529;
  margin-bottom: 12px;
  font-family: 'Inter', sans-serif;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
}

.task-item:last-child {
  border-bottom: none;
}

.task-icon-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.task-icon-badge.shopify {
  background: #95bf47;
  color: white;
}

.task-icon-badge.email {
  background: theme('colors.ralprimary.main');
  color: white;
}

.task-icon-badge.daily {
  background: #0066cc;
  color: white;
}

.task-content {
  flex: 1;
}

.task-title {
  font-size: 14px;
  font-weight: 500;
  color: #212529;
  margin-bottom: 2px;
  font-family: 'Inter', sans-serif;
}

.task-reward {
  font-size: 12px;
  color: #6c757d;
  font-family: 'Inter', sans-serif;
}

.task-button {
  padding: 6px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Inter', sans-serif;
  min-width: 80px;
}

.task-button.claimed {
  background: #d4edda;
  color: #155724;
  cursor: default;
}

.task-button.connect,
.task-button.claim {
  background: theme('colors.ralprimary.main');
  color: white;
}

.task-button.connect:hover,
.task-button.claim:hover {
  background: theme('colors.ralprimary.dark');
}

.task-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Trial Section */
.trial-section {
  background: #2c3e50;
  color: white;
  padding: 16px 20px;
  margin: 0 20px 20px;
  border-radius: 10px;
  position: relative;
}

.trial-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.trial-icon {
  font-size: 16px;
}

.trial-title {
  font-size: 16px;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  flex: 1;
}

.trial-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
}

.trial-description {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10px;
  font-family: 'Inter', sans-serif;
}

.trial-cta {
  margin-bottom: 12px;
}

.trial-cta-text {
  font-size: 14px;
  font-weight: 500;
  color: white;
  font-family: 'Inter', sans-serif;
}

.trial-button {
  background: white;
  color: #2c3e50;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Inter', sans-serif;
  width: 100%;
}

.trial-button:hover {
  background: #f8f9fa;
}

.raleon-max-text {
  color: theme('colors.ralprimary.main');
  font-weight: 600;
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: 'Inter', sans-serif;
  text-shadow: none;
}

/* Upgrade Section */
.upgrade-section {
  background: #2c3e50;
  color: white;
  padding: 16px 20px;
  margin: 0 20px 20px;
  border-radius: 10px;
  position: relative;
}

.upgrade-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.crown-icon {
  font-size: 16px;
}

.upgrade-title {
  font-size: 16px;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  flex: 1;
}

.arrow-icon {
  opacity: 0.7;
}

.upgrade-description {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10px;
  font-family: 'Inter', sans-serif;
}

.upgrade-pricing {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 12px;
}

.price {
  font-size: 18px;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
}

.billing {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-family: 'Inter', sans-serif;
}

.upgrade-button {
  background: white;
  color: #2c3e50;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Inter', sans-serif;
  width: 100%;
}

.upgrade-button:hover {
  background: #f8f9fa;
}


/* Success Animation */
.success-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  z-index: 10001;
  overflow: hidden;
}

.quota-modal.showing-success {
  overflow: hidden !important;
}

.success-animation {
  text-align: center;
  position: relative;
}

.success-icon {
  color: theme('colors.ralprimary.main');
  margin-bottom: 16px;
  animation: bounce 0.6s ease;
  display: flex;
  justify-content: center;
}

.success-text {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'Inter', sans-serif;
}

.confetti {
  position: absolute;
  width: 6px;
  height: 6px;
  background: theme('colors.ralprimary.main');
  animation: confettiFall 3s linear infinite;
}

/* Animations */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20px); }
  60% { transform: translateY(-10px); }
}

@keyframes confettiFall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .quota-modal {
    width: 95%;
    margin: 20px;
  }

  .remaining-count {
    font-size: 36px;
  }

  .task-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
    text-align: center;
  }

  .task-content {
    text-align: center;
  }
}
</style>
