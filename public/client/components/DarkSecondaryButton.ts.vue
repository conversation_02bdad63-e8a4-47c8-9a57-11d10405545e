<template>
	<div class="inline-flex whitespace-nowrap items-center text-xs border bg-white bg-opacity-10 border-opacity-10 border-white text-white font-semibold py-2 px-4 rounded-full ml-6 md:mt-0 lg:mt-0 xl:mt-0 mt-4 cursor-pointer"
	@mouseover="this.isHovering = true;"
	@mouseout="this.isHovering = false;"
	@click="$emit('click')">
		<span class="ml-2 text-xs font-semibold text-white font-['Inter']"
		:class="getTextColor"
		>{{this.cta}}</span>
		<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="ml-2 min-w-[16px]">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M7.52858 2.86201C7.78893 2.60166 8.21104 2.60166 8.47139 2.86201L13.1381 7.52868C13.3984 7.78903 13.3984 8.21114 13.1381 8.47149L8.47139 13.1382C8.21104 13.3985 7.78893 13.3985 7.52858 13.1382C7.26824 12.8778 7.26824 12.4557 7.52858 12.1953L11.0572 8.66675H3.33332C2.96513 8.66675 2.66666 8.36827 2.66666 8.00008C2.66666 7.63189 2.96513 7.33341 3.33332 7.33341H11.0572L7.52858 3.80482C7.26824 3.54447 7.26824 3.12236 7.52858 2.86201Z" fill="url(#paint0_linear_1290_96)"/>
			<defs>
			<linearGradient id="paint0_linear_1290_96" x1="13.3333" y1="2.66675" x2="2.66666" y2="13.3334" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FFFFFF"/>
			<stop offset="1" stop-color="#FFFFFF"/>
			</linearGradient>
			</defs>
		</svg>
	</div>
  </template>

  <script>
  export default {
	name: 'DarkSecondaryButton',
	props: ['cta'],
  	emits: ['click'],
	data() {
		return {
			isHovering: false,
		}
	},
	computed: {
		getTextColor() {
			if(this.isHovering)
				return 'text-white';
			else
				return 'text-ralprimary-dark';
		}
	}
  };
  </script>
