<template>
	<div class="flex h-screen overflow-hidden bg-gray-50">
		<!-- Content area -->
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<main>
				<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<!-- Page header -->
					<div class="mb-8 flex">
						<h1 class="text-3xl md:text-4xl text-gray-900 font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent">Program Settings</h1>
					</div>

					<!-- Content -->
					<div class="bg-white shadow-xl rounded-2xl mb-8 border border-gray-100">
						<div class="flex flex-col md:flex-row md:-mr-px">
							<SettingsSidebar />

							<div class="grow" v-if="isFeatureAvailable">
								<!-- Panel body -->
								<div class="p-8 space-y-8">
									<!-- Toggle switch for program status -->
									<section>
										<div class="mb-6">
											<label class="block text-lg font-semibold text-gray-900 mb-4">Loyalty Program Status</label>
											<div class="flex items-center gap-4">
												<div class="relative inline-block w-12 h-6">
													<input type="checkbox" id="toggle"
														class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
														v-model="programStatus" />
													<label for="toggle"
														class="toggle-label block overflow-hidden h-6 rounded-full cursor-pointer"></label>
												</div>
												<span class="text-gray-700 font-medium">{{ programStatus ? 'Live' : 'Offline' }}</span>
											</div>
										</div>

										<div class="bg-blue-50 border border-blue-200 rounded-2xl p-6">
											<div class="flex items-start mb-4">
												<svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
													<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
												</svg>
												<div>
													<h3 class="text-blue-900 text-base font-semibold mb-2">What happens when your loyalty program is live?</h3>
													<ul class="text-blue-800 text-sm space-y-2 list-disc list-inside">
														<li>The Loyalty Panel will appear on your store</li>
														<li>Customers will begin earning points and rewards</li>
														<li>Customers will be able to redeem points for rewards</li>
														<li>We'll automatically track your Return on Loyalty Spend to give you an ROI</li>
														<li>Your Raleon Copilot will start learning more about your customers and provide you recommendations</li>
													</ul>
												</div>
											</div>
										</div>
									</section>

									<section>
										<!-- Program Name -->
										<div class="mb-6">
											<label class="block text-lg font-semibold text-gray-900 mb-2" for="program-name">Program Name</label>
											<input 
												id="program-name" 
												class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200" 
												type="text"
												v-model="programName" 
												placeholder="Enter program name"
											/>
										</div>
									</section>

									<section>
										<div class="mb-6">
											<label class="block text-lg font-semibold text-gray-900 mb-2" for="points-name">Points Name</label>
											<input 
												id="points-name" 
												class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200" 
												type="text"
												v-model="currency.name" 
												placeholder="e.g., Points, Stars, Coins"
											/>
										</div>
									</section>

									<section>
										<div class="mb-6">
											<label class="block text-lg font-semibold text-gray-900 mb-2" for="abbreviated-name">Abbreviated Points Name</label>
											<input 
												id="abbreviated-name" 
												class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200" 
												type="text"
												v-model="currency.abbreviatedName" 
												placeholder="e.g., pts, stars"
											/>
										</div>
									</section>
								</div>
								<footer class="border-t border-gray-100 bg-gray-50/50 rounded-b-2xl">
									<div class="flex flex-col px-8 py-6">
										<div class="flex justify-end gap-4">
											<button class="px-6 py-3 border border-gray-200 text-gray-600 font-medium rounded-xl hover:bg-gray-50 hover:border-gray-300 transition-all duration-200">
												Cancel
											</button>
											<button 
												@click="updateProgramDetails()"
												class="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200"
											>
												Save Changes
											</button>
										</div>
									</div>
								</footer>
							</div>

							<div class="grow" v-if="!isFeatureAvailable">
								<div class="bg-white rounded-r-2xl">
									<div class="p-8 flex flex-col items-center justify-center text-center">
										<img src="../images/VIPTeirPromo.png" width="800" class="rounded-xl shadow-lg mb-8">
										<h2 class="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent mb-4">
											Upgrade to Loyalty Plan
										</h2>
										<p class="text-gray-600 text-lg mb-8 max-w-2xl">
											Loyalty programs powered by Raleon help brands increase repeat purchase rate and customer lifetime value.
										</p>
										<PrimaryButton
													cta="Upgrade Loyalty Plan"
													size="xs"
													@click="() => this.$router.push('/loyalty/settings/plans')"
													/>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</main>
		</div>
	</div>

	<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>
</template>

<script>
import Header from '../../client-old/partials/Header.vue'
import SettingsSidebar from '../../client-old/partials/settings/SettingsSidebar.vue'
import ToastStatus from '../../client-old/pages/component/ToastStatus.vue'
import StatusMessage from '../components/StatusMessage.ts.vue';
import { customerIOTrackEvent } from '../services/customerio.js';

import * as Utils from '../../client-old/utils/Utils';
import { isFeatureAvailable } from '../services/features.js';
import PrimaryButton from './PrimaryButton.ts.vue';


const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'Account',
	components: {
		Header,
		SettingsSidebar,
                ToastStatus,
                StatusMessage,
                PrimaryButton
	},
	async mounted() {
		try {
			customerIOTrackEvent('Program Settings Viewed');

			const response = await fetch(`${URL_DOMAIN}/loyalty-programs`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});

			let jsonresponse = await response.json();

			if (jsonresponse && jsonresponse[0]) {
				const program = jsonresponse[0];

				this.programName = program.name;
				this.programId = program.id;
				this.programStatus = program.active;
				this.activationDate = program.activationDate;
				this.launcherActive = program.launcherActive ?? true;
				this.notificationsActive = program.notificationsActive ?? false;
				this.referralsActive = program.referralsActive ?? false;

                                if (program.loyaltyCurrencies && program.loyaltyCurrencies[0]) {
					this.currency = program.loyaltyCurrencies[0];
					if (!this.currency.abbreviatedName) {
						this.currency.abbreviatedName = 'pts';
					}
				}
			}
		} catch (error) {
			console.error('Error loading program settings:', error);
			this.status.type = 'fail';
			this.status.message = 'Failed to load program settings';
		}
	},
	computed: {
		isFeatureAvailable() {
			return isFeatureAvailable('loyalty-app');
		},
		isTrialEnded() {
			const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));

			return freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0
		}
	},
	data() {
		return {
			programId: -1,
			programName: '',
			programCurrencyName: '',
			programStatus: false,
			activationDate: '',
			launcherActive: false,
			notificationsActive: false,
                        currency: {
                                name: '',
                                abbreviatedName: 'pts',
                                conversionToUSD: 0
                        },
                        status: { message: '', type: 'nope' }
                }
        },
        methods: {
		async updateProgramDetails() {
			//Validate Currency details
			if (this.currency.name == '' || this.currency.conversionToUSD == 0 || isNaN(this.currency.conversionToUSD)) {
				// this.status = 'error';
				// this.statusText = 'Please enter a valid currency name and conversion rate.';
				// return;
				this.currency.name = 'Points';
				this.currency.abbreviatedName = 'pts';
				this.currency.conversionToUSD = 100;
			}

			//Validate Program Name
			if (this.programName == '') {
				this.status.type = 'fail';
				this.status.message = 'Please enter a valid program name.';
				return;
			}

                        const programDetails = {
                                name: this.programName,
                                active: this.programStatus,
                                launcherActive: this.launcherActive,
                                notificationsActive: this.notificationsActive,
                                referralsActive: this.referralsActive,
                        };

			if (!this.activationDate && this.programStatus) {
				programDetails.activationDate = new Date().toISOString();
			}
			const response = await fetch(`${URL_DOMAIN}/loyalty-programs/${this.programId}`, {
				method: 'PATCH',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(programDetails)
			});
			if (response.error) {
				this.status.type = 'fail';
				this.status.message = jsonresponse.error;
				return;
			}

			if (this.programStatus == true) {
				customerIOTrackEvent('Loyalty Live');
			}
			else {
				customerIOTrackEvent('Loyalty Off');
			}


			//Update Currency Details
			const responseCurrency = await fetch(`${URL_DOMAIN}/loyalty-programs/${this.programId}/loyalty-currencies/${this.currency.id}`, {
				method: 'PATCH',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					name: this.currency.name,
					abbreviatedName: this.currency.abbreviatedName,
					conversionToUSD: parseInt(this.currency.conversionToUSD)
				})
			});
			let jsonresponseCurrency = await responseCurrency.json();
			if (jsonresponseCurrency.error) {
				this.status.type = 'fail';
				this.status.message = jsonresponseCurrency.error;
				return;
			}

			this.status.type = 'success';
			this.status.message = 'Program details updated successfully.';
		}
	}
}
</script>
<style scoped>
.toggle-checkbox {
	opacity: 0;
	width: 0;
	height: 0;
}

.toggle-label {
	display: block;
	overflow: hidden;
	cursor: pointer;
	height: 24px;
	width: 48px;
	padding: 0;
	line-height: 24px;
	background: #d1d5db;
	border-radius: 24px;
	transition: background-color 0.3s ease;
	position: relative;
}

.toggle-label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 2px;
	width: 20px;
	height: 20px;
	border-radius: 20px;
	background: #fff;
	transition: transform 0.3s ease;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-checkbox:checked + .toggle-label {
	background: linear-gradient(135deg, #7c3aed, #8b5cf6);
}

.toggle-checkbox:checked + .toggle-label:after {
	transform: translateX(24px);
}

.toggle-checkbox:focus + .toggle-label {
	box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}
</style>
