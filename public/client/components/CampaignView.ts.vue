<template>
  <div>
    <!-- Card/Table toggle on the right side -->
    <div v-if="sortedCampaigns.length > 0" class="flex justify-end mb-4">
      <div class="inline-flex p-1 bg-gray-100 rounded-lg">
        <button type="button"
          @click="toggleViewMode('card')"
          :class="[
            shouldShowTableView ? 'text-gray-600 hover:text-purple-600' : 'bg-white text-purple-700 shadow-sm',
            'px-3 py-1.5 text-sm rounded-md transition-colors focus:outline-none flex items-center gap-2'
          ]">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
          </svg>
          Card
        </button>
        <button type="button"
          @click="toggleViewMode('table')"
          :class="[
            shouldShowTableView ? 'bg-white text-purple-700 shadow-sm' : 'text-gray-600 hover:text-purple-600',
            'px-3 py-1.5 text-sm rounded-md transition-colors focus:outline-none flex items-center gap-2'
          ]">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z" clip-rule="evenodd" />
          </svg>
          Table
        </button>
      </div>
    </div>

    <!-- Add Campaign Button -->
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium text-gray-900">Campaigns</h3>
      <button
        v-if="!isPlanInProgress"
        @click="$emit('add-campaign')"
        class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        Add Campaign
      </button>
    </div>

    <!-- Empty state message when there are no campaigns -->
    <div v-if="sortedCampaigns.length === 0" class="bg-white rounded-xl shadow-sm p-8 text-center">
      <div class="text-gray-500 mb-4">No campaigns found in this plan.</div>
      <button
        v-if="!isPlanInProgress"
        @click="$emit('add-campaign')"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        Add Campaign
      </button>
    </div>

    <!-- Campaign views -->
    <div v-else>
      <!-- Table View -->
      <CampaignTableView
        v-if="shouldShowTableView"
        :sorted-campaigns="sortedCampaigns"
        :is-plan-in-progress="isPlanInProgress"
        :editing-state="editingState"
        :edit-value="editValue"
        :archived-campaigns="archivedCampaigns"
        :removed-campaigns="removedCampaigns"
        @start-editing="onStartEditing"
        @save="onSave"
        @navigate-to-task="onNavigateToCampaignTask"
        @delete-campaign="onDeleteCampaign"
        @skip-campaign="onSkipCampaign"
        @undo-skip-campaign="onUndoSkipCampaign"
      />

      <!-- Card View -->
      <CampaignCardView
        v-else
        :sorted-campaigns="sortedCampaigns"
        :is-plan-in-progress="isPlanInProgress"
        :editing-state="editingState"
        :edit-value="editValue"
        :archived-campaigns="archivedCampaigns"
        :removed-campaigns="removedCampaigns"
        @start-editing="onStartEditing"
        @save="onSave"
        @navigate-to-task="onNavigateToCampaignTask"
        @delete-campaign="onDeleteCampaign"
        @skip-campaign="onSkipCampaign"
        @undo-skip-campaign="onUndoSkipCampaign"
      />
    </div>
  </div>
</template>

<script>
import CampaignTableView from './CampaignTableView.ts.vue';
import CampaignCardView from './CampaignCardView.ts.vue';

export default {
  name: 'CampaignView',

  components: {
    CampaignTableView,
    CampaignCardView
  },

  props: {
    sortedCampaigns: {
      type: Array,
      required: true
    },
    isPlanInProgress: {
      type: Boolean,
      required: true
    },
    editingState: {
      type: Object,
      required: true
    },
    editValue: {
      type: String,
      required: true
    },
    archivedCampaigns: {
      type: Object, // Set
      required: true
    },
    removedCampaigns: {
      type: Object, // Set
      required: true
    },
    viewMode: {
      type: String,
      default: 'card'
    }
  },

  computed: {
    shouldShowTableView() {
      return this.viewMode === 'table';
    }
  },

  methods: {
    toggleViewMode(mode) {
      this.$emit('toggle-view-mode', mode);
    },

    onStartEditing(data) {
      this.$emit('start-editing', data);
    },

    onSave(data) {
      this.$emit('save', data);
    },

    onNavigateToCampaignTask(campaignId) {
      this.$emit('navigate-to-task', campaignId);
    },

    onDeleteCampaign(campaignId) {
      this.$emit('delete-campaign', campaignId);
    },

    onSkipCampaign(campaignId) {
      this.$emit('skip-campaign', campaignId);
    },

    onUndoSkipCampaign(campaignId) {
      this.$emit('undo-skip-campaign', campaignId);
    }
  }
}
</script>

<style scoped>
/* Component specific styles */
</style>
