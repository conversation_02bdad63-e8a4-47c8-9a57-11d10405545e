<template>
	<div class="w-full max-w-3xl p-8">
	  <!-- Steps -->
	  <div class="space-y-2">
		<template v-for="(step, index) in steps" :key="index">
		  <!-- Month headers -->
		  <div v-if="step.isMonth"
			   class="mt-6 mb-2 font-medium text-gray-900">
			<type-writer :text="step.text" :delay="100" />
		  </div>

		  <!-- Step items -->
		  <div v-else
			   :class="[
				 'flex items-center gap-3 p-3',
				 step.isMonthStep ? 'ml-6 border-l-2 border-l-gray-100' : '',
				 index === currentStep ? 'bg-purple-50 rounded-lg' : ''
			   ]">
			<fade-in :delay="150" v-if="step.icon">
			  <div :class="[
				'relative p-1.5 rounded-md transition-colors duration-300',
				index === currentStep ? 'bg-purple-100 text-purple-600' :
				index < currentStep || isComplete ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
			  ]">
				<svg v-if="index < currentStep || isComplete" class="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
					<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
				</svg>
				<svg v-else class="w-4 h-4" v-html="icons[step.icon]" viewBox="0 0 24 24" fill="currentColor" />
			  </div>
			</fade-in>

			<type-writer :text="step.text" :delay="150">
			  <span :class="[
				'font-medium',
				index === currentStep ? 'text-purple-900' :
				index < currentStep || isComplete ? 'text-gray-700' : 'text-gray-400'
			  ]" />
			</type-writer>

			<fade-in v-if="index === currentStep" :delay="150">
			  <div class="w-4 h-4 border-t-2 border-purple-500 animate-spin rounded-full ml-auto" />
			</fade-in>
		  </div>
		</template>
	  </div>
	</div>
  </template>

  <script>
  import TypeWriter from './AgentTyper.ts.vue';
  import FadeIn from './AgentFadeIn.ts.vue';

  export default {
	name: 'MarketingPlanner',
	components: {
	  TypeWriter,
	  FadeIn,
	},

	data() {
	  return {
		steps: [],
		currentStep: 0,
		isComplete: false,
		isThinking: false,
		currentMessageIndex: 0,
		loadingMessages: [
		  'Understanding your brand and audience',
		  'Developing marketing strategy',
		  'Planning campaigns',
		  'Reviewing results'
		],
		messageInterval: null,
		icons: {
			brain: '<path d="M13 3c3.9 0 7 3.1 7 7 0 2.8-1.6 5.2-4 6.3V21H8v-4.7c-2.4-1.1-4-3.5-4-6.3 0-3.9 3.1-7 7-7m0-2C7.5 1 3 5.5 3 11c0 3.8 2.1 7.1 5.2 8.8V23h7.6v-3.2c3.1-1.7 5.2-5 5.2-8.8 0-5.5-4.5-10-10-10z"/>',
			lightbulb: '<path d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6C7.8 12.16 7 10.63 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1z"/>',
			calendar: '<path d="M19 4h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V10h14v10zm0-12H5V6h14v2zm-7 5h5v5h-5z"/>',
			mail: '<path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>',
			users: '<path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>',
			chart: '<path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>'
		}
	  }
	},

	computed: {
	  currentLoadingMessage() {
		return this.loadingMessages[this.currentMessageIndex];
	  }
	},

	methods: {
		async initializeSteps() {
      this.currentStep = 0;
      this.steps = [{
        text: "Thinking about your request",
        icon: 'brain'
      }];

      await new Promise(r => setTimeout(r, 3000));

      this.currentStep = 1;
      this.steps.push({
        text: "Understanding our brand and audience",
        icon: 'lightbulb'
      });

      await new Promise(r => setTimeout(r, 3000));

      // January
      this.currentStep = 2;
      this.steps.push({
        text: "January planning...",
        isMonth: true
      });

      await new Promise(r => setTimeout(r, 2000));

      const januarySteps = [
        { text: "Analyzing the month", icon: 'calendar' },
        { text: "Coming up with campaigns", icon: 'mail' },
      ];

      for (const step of januarySteps) {
        this.currentStep++;
        this.steps.push({ ...step, isMonthStep: true });
        await new Promise(r => setTimeout(r, 2500));
      }

		// February
		this.currentStep++;
		this.steps.push({
		  text: "February planning...",
		  isMonth: true
		});

		await new Promise(r => setTimeout(r, 2000));

		const februarySteps = [
		  { text: "Analyzing the month", icon: 'calendar' },
		  { text: "Coming up with campaigns", icon: 'mail' },
		];

		for (const step of februarySteps) {
		  this.currentStep++;
		  this.steps.push({ ...step, isMonthStep: true });
		  await new Promise(r => setTimeout(r, 2500));
		}

		// Conclusion
		this.currentStep++;
		this.steps.push({
		  text: "Reviewing results...",
		  isMonth: true
		});

		await new Promise(r => setTimeout(r, 2000));

		this.currentStep++;
		this.steps.push({
		  text: "Analyzing campaign performance",
		  icon: 'lightbulb'
		});

		await new Promise(r => setTimeout(r, 2500));

		this.isComplete = true;
	  },

	  startThinking() {
		this.isThinking = true;
		this.messageInterval = setInterval(() => {
		  this.currentMessageIndex = (this.currentMessageIndex + 1) % this.loadingMessages.length;
		}, 3000);
	  },

	  stopThinking() {
		this.isThinking = false;
		if (this.messageInterval) {
		  clearInterval(this.messageInterval);
		}
	  }
	},

	mounted() {
	  this.initializeSteps();
	},

	beforeUnmount() {
	  if (this.messageInterval) {
		clearInterval(this.messageInterval);
	  }
	}
  }
  </script>

  <style scoped>
  @keyframes fadeInUp {
	from {
	  opacity: 0;
	  transform: translateY(20px);
	}
	to {
	  opacity: 1;
	  transform: translateY(0);
	}
  }

  @keyframes robotFade {
	0% {
	  opacity: 1;
	  transform: scale(1);
	}
	50% {
	  opacity: 0.3;
	  transform: scale(0.95);
	}
	100% {
	  opacity: 1;
	  transform: scale(1);
	}
  }

  .robot-fade {
	animation: robotFade 2s ease-in-out infinite;
	-webkit-font-smoothing: antialiased;
	user-select: none;
  }
  </style>
