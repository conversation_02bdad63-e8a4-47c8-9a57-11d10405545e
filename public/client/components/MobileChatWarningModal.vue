<template>
  <!-- Modal backdrop -->
  <transition
    enter-active-class="transition ease-out duration-200"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition ease-out duration-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div v-show="show" class="fixed inset-0 bg-slate-900 bg-opacity-30 z-50 transition-opacity" aria-hidden="true" @click="close"></div>
  </transition>
  <!-- Modal dialog -->
  <transition
    enter-active-class="transition ease-in-out duration-200"
    enter-from-class="opacity-0 translate-y-4"
    enter-to-class="opacity-100 translate-y-0"
    leave-active-class="transition ease-in-out duration-200"
    leave-from-class="opacity-100 translate-y-0"
    leave-to-class="opacity-0 translate-y-4"
  >
    <div v-show="show" class="fixed inset-0 z-50 overflow-hidden flex items-center my-4 justify-center transform px-4 sm:px-6" role="dialog" aria-modal="true">
      <div class="bg-white rounded-lg shadow-lg max-w-sm w-full p-6 relative" @click.stop>
        <button class="absolute top-2 right-2 text-slate-400 hover:text-slate-500" @click="close">
          <span class="sr-only">Close</span>
          <svg class="w-4 h-4 fill-current" viewBox="0 0 16 16">
            <path d="M7.95 6.536l4.242-4.243a1 1 0 111.415 1.414L9.364 7.95l4.243 4.242a1 1 0 11-1.415 1.415L7.95 9.364l-4.243 4.243a1 1 0 01-1.414-1.415L6.536 7.95 2.293 3.707a1 1 0 011.414-1.414L7.95 6.536z" />
          </svg>
        </button>
        <h2 class="text-xl font-semibold mb-2 text-slate-800">Mobile Support In Progress</h2>
        <p class="text-slate-600 mb-4">For the best experience, please use a desktop browser. Mobile support is still a work in progress.</p>
        <div class="text-right">
          <button class="btn bg-indigo-500 hover:bg-indigo-600 text-white" @click="close">Continue</button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
const props = defineProps<{ show: boolean }>();
const emit = defineEmits(['close']);
function close() {
  emit('close');
  if (typeof window !== 'undefined') {
    localStorage.setItem('chat_mobile_warning_shown', 'true');
  }
}
</script>

<style scoped>
</style>

