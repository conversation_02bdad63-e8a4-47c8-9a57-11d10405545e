<template>
	<div class="flex items-center relative">
		<div
			id="editableHeader"
			contenteditable="true"
			placeholder="Untitled"
			spellcheck="false"
			class="mb-4 sm:mb-0 min-w-24
			text-5xl md:text-5xl font-bold cursor-text
			bg-clip-text bg-gradient-to-r from-ralpurple-500 to-ralocean-500
			decoration-2
			hover-underline-animation
			focus-underline"
			@mouseover="editing = true"
			@mouseout="editing = false"
			@focusin="editing = true"
			@focusout="updateHeader"
			@keydown.enter="handleEnterKeyPress"
			ref="editableHeader"
		>
			{{headerText}}
		</div>
	</div>
</template>

<script>
	export default {
		name: 'EditableHeader',
		components: {
		},
		emits: ['updatedHeader'],
		props: ['headerText'],
		data() {
			return {
				editing: false
			}
		},
		methods: {
			updateHeader() {
				this.editing = false;
				const newHeader = this.$refs.editableHeader.innerText;
				if (!(newHeader.trim())) {
					this.$emit('updatedHeader', 'Untitled');
				} else {
					this.$emit('updatedHeader', newHeader);
				}
			},
			handleEnterKeyPress() {
				this.$refs.editableHeader.blur();
			}
		},
		mounted() {
			if (this.headerText === 'Untitled' || this.headerText === '') {
				this.$refs.editableHeader.focus();
			}
		}
	}

</script>

<style>
	[contenteditable]:empty::after {
		content: attr(placeholder);
		opacity: .5;
	}

	#editableHeader:not(:focus) {
		color: transparent
	}

.hover-underline-animation {
  position: relative;
  display: inline-block;
}

.hover-underline-animation::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: #9254F7;
  visibility: hidden;
  transition: all 0.5s ease-in-out;
}

.hover-underline-animation:hover::after {
  visibility: visible;
  width: 100%;
}

.focus-underline:focus {
	opacity: 0.50;
	text-decoration-line: underline;
	text-underline-offset: 0.7rem;
}
</style>
