<template>
  <div ref="editorRef" :class="[
    'fixed z-50 w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4',
    'transition-all duration-200',
    {'opacity-0 translate-y-2': !isVisible, 'opacity-100 translate-y-0': isVisible}
  ]" :style="{top: `${position.top}px`, left: `${position.left}px`}">

    <div class="flex justify-between">
      <div>
          <p class="text-sm text-gray-600">
            {{ signal.description || 'No description defined.' }}
          </p>
      </div>

      <div>
        <button @click="$emit('close')" class="p-1 hover:bg-gray-100 rounded-full">
          <svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px"
            fill="#9ca3af">
            <path
              d="M480-424 284-228q-11 11-28 11t-28-11q-11-11-11-28t11-28l196-196-196-196q-11-11-11-28t11-28q11-11 28-11t28 11l196 196 196-196q11-11 28-11t28 11q11 11 11 28t-11 28L536-480l196 196q11 11 11 28t-11 28q-11 11-28 11t-28-11L480-424Z" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Product Selector for Replenishment Desire and Likely for Rebuy -->
    <div v-if="['replenishment-desire', 'likely-for-rebuy-soon'].includes(signal.segmentKey)" class="mt-4">
      <div class="flex items-center justify-between mb-1">
        <label class="block text-sm font-medium text-gray-700">
          Products
        </label>
        <button
          v-if="selectedProducts.length > 0"
          @click="clearSelection"
          class="text-xs text-blue-600 hover:text-blue-800"
        >
          Clear selection
        </button>
      </div>

      <!-- Selected Products Pills -->
      <div v-if="selectedProducts.length > 0" class="mb-2 flex flex-wrap gap-1">
        <div
          v-for="product in selectedProducts"
          :key="product.id"
          class="inline-flex items-center gap-1 px-2 py-1 bg-blue-50 text-blue-700 rounded-full text-xs"
        >
          <span class="truncate max-w-[180px]">{{ product.title }}</span>
          <button
            @click="toggleProduct(product)"
            class="hover:bg-blue-100 rounded-full p-0.5"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Search Input -->
      <input
        type="text"
        class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm mb-2"
        placeholder="Search products..."
        v-model="searchTerm"
      />

      <!-- Loading State -->
      <div v-if="isLoading" class="text-center text-gray-600 py-4">
        Loading products...
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center text-red-600 py-4">
        {{ error }}
      </div>

      <!-- Product List -->
      <div v-else class="border border-gray-200 rounded-md">
        <ul class="border max-h-60 overflow-auto divide-y divide-gray-200">
          <li>
            <button
              class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center justify-between"
              :class="{'bg-blue-50': selectedProducts.length === 0}"
              @click="handleAllProducts"
            >
              <span>All products</span>
              <svg
                v-if="selectedProducts.length === 0"
                class="h-4 w-4 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </button>
          </li>
          <li v-for="product in filteredProducts" :key="product.id">
            <button
              class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center justify-between"
              :class="{'bg-blue-50': isProductSelected(product)}"
              @click="toggleProduct(product)"
            >
              <span>{{ product.title }}</span>
              <svg
                v-if="isProductSelected(product)"
                class="h-4 w-4 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </button>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import * as Utils from '../utils/utils.js'

const props = defineProps({
  signal: {
    type: Object,
    required: true
  },
  position: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'update'])

// Refs
const editorRef = ref(null)
const isVisible = ref(false)
const searchTerm = ref('')
const selectedProducts = ref([])
const products = ref([])
const isLoading = ref(false)
const error = ref(null)

// Computed
const filteredProducts = computed(() => {
  if (!searchTerm.value) return products.value
  return products.value.filter(product =>
    product.title.toLowerCase().includes(searchTerm.value.toLowerCase())
  )
})

// Methods
const fetchProducts = async () => {
  isLoading.value = true
  error.value = null

  try {
    const response = await fetch(`${Utils.URL_DOMAIN}/shop-products`, {
      method: 'GET',
      credentials: 'omit',
      mode: 'cors',
      headers: {
        'Content-Type': 'application',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    })

    if (!response.ok) {
      throw new Error('Failed to fetch products')
    }

    products.value = await response.json()
  } catch (err) {
    console.error('Error fetching products:', err)
    error.value = 'Failed to load products. Please try again.'
  } finally {
    isLoading.value = false
  }
}

const isProductSelected = (product) => {
  return selectedProducts.value.some(p => p.id === product.id)
}

const toggleProduct = (product) => {
  const index = selectedProducts.value.findIndex(p => p.id === product.id)
  if (index === -1) {
    selectedProducts.value.push(product)
  } else {
    selectedProducts.value.splice(index, 1)
  }
  updateQuery()
}

const handleAllProducts = () => {
  selectedProducts.value = []
  updateQuery()
}

const clearSelection = () => {
  selectedProducts.value = []
  updateQuery()
}

const updateQuery = () => {
  let updatedQuery;

  if (selectedProducts.value.length != 0) {
    // Configure query based on signal type
    const queryConfig = {
      'replenishment-desire': {
        scoreField: 'replenishmentScore',
        productField: 'replenishmentProduct',
        threshold: 500
      },
      'likely-for-rebuy-soon': {
        scoreField: 'rebuyPropensity',
        productField: 'rebuyProduct',
        threshold: 500
      }
    }[props.signal.segmentKey]

    updatedQuery = {
      operator: "and",
      conditions: [
        {
          field: queryConfig.scoreField,
          operator: ">",
          value: queryConfig.threshold
        },
        {
          operator: "or",
          conditions: selectedProducts.value.map(product => ({
            field: queryConfig.productField,
            operator: "=",
            value: product.id
          }))
        }
      ]
    }
  }

  const productInfo = selectedProducts.value.length === 0
    ? 'All products'
    : `${selectedProducts.value.length} product${selectedProducts.value.length === 1 ? '' : 's'}`;

  const updatedSignal = {
    ...props.signal,
    queryOverride: updatedQuery ? JSON.stringify(updatedQuery) : null,
    details: productInfo
  }

  emit('update', updatedSignal, {})
}

// Lifecycle
onMounted(async () => {
  document.addEventListener('mousedown', handleClickOutside)
  requestAnimationFrame(() => isVisible.value = true)

  // Fetch products immediately on mount
  await fetchProducts()

  // Initialize selected products from existing query if available
  if (props.signal.query) {
    try {
      const query = JSON.parse(props.signal.query)
      if (query.operator === "and") {
        // Find the OR condition containing product selections
        const orCondition = query.conditions.find(c => c.operator === "or")
        if (orCondition) {
          // Extract product IDs from conditions
          const productIds = orCondition.conditions.map(c => c.value)
          // Find matching products from the fetched products list after they're loaded
          selectedProducts.value = products.value.filter(p => productIds.includes(p.id))

          // Call updateQuery to refresh the UI with the correct product count
          updateQuery()
        }
      }
    } catch (e) {
      console.error('Error parsing signal query:', e)
    }
  }
})

onBeforeUnmount(() => {
  document.removeEventListener('mousedown', handleClickOutside)
})

function handleClickOutside(event) {
  if (editorRef.value && !editorRef.value.contains(event.target)) {
    emit('close')
  }
}
</script>

<style scoped>
.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.transition-opacity {
  transition: opacity 200ms ease-in-out;
}

.border { /* Target the border-class of the product list div */
  overflow-y: auto; /* Enable scrolling if content exceeds height */
}

.border::-webkit-scrollbar { /* Chrome, Safari, Opera */
    width: 4px; /* Set width */
    background-color: rgba(200,200,200,0.5); /* Light background color */
}
.border::-webkit-scrollbar-thumb { /* Scrollbar thumb styling */
    background-color: rgba(100, 100, 100, 0.4); /* Darker thumb color */
    border-radius: 5px; /* Rounded corners */
}

/* Firefox scrollbar styling */
.border {
  scrollbar-width: thin; /* Set scrollbar width */
}
</style>
