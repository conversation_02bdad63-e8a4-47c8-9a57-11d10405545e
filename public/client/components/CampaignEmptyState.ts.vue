<template>
	<svg width="540" height="30" viewBox="0 0 501 30" fill="none" xmlns="http://www.w3.org/2000/svg">
		<g opacity="0.7">
			<g filter="url(#filter0_d_605_5769)">
				<rect x="6" y="4" width="501" height="17.0606" rx="8.53032" fill="white" fill-opacity="0.75"
					shape-rendering="crispEdges" />
				<rect x="6.5" y="4.5" width="500" height="16.0606" rx="8.03032" stroke="#D6D1FD"
					shape-rendering="crispEdges" />
			</g>
			<rect x="387" y="10" width="110" height="5" rx="2.5" fill="url(#paint0_linear_605_5769)" />
			<rect x="237" y="10" width="110" height="5" rx="2.5" fill="url(#paint1_linear_605_5769)" />
			<rect x="47" y="10" width="110" height="5" rx="2.5" fill="url(#paint2_linear_605_5769)" />
		</g>
		<defs>
			<filter id="filter0_d_605_5769" x="0" y="0" width="513" height="29.0605" filterUnits="userSpaceOnUse"
				color-interpolation-filters="sRGB">
				<feFlood flood-opacity="0" result="BackgroundImageFix" />
				<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
					result="hardAlpha" />
				<feOffset dy="2" />
				<feGaussianBlur stdDeviation="3" />
				<feComposite in2="hardAlpha" operator="out" />
				<feColorMatrix type="matrix" values="0 0 0 0 0.0509804 0 0 0 0 0.0392157 0 0 0 0 0.172549 0 0 0 0.08 0" />
				<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_605_5769" />
				<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_605_5769" result="shape" />
			</filter>
			<linearGradient id="paint0_linear_605_5769" x1="430" y1="-0.555557" x2="433.874" y2="26.1776"
				gradientUnits="userSpaceOnUse">
				<stop stop-color="#D9D9D9" />
				<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
			</linearGradient>
			<linearGradient id="paint1_linear_605_5769" x1="280" y1="-0.555557" x2="283.874" y2="26.1776"
				gradientUnits="userSpaceOnUse">
				<stop stop-color="#D9D9D9" />
				<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
			</linearGradient>
			<linearGradient id="paint2_linear_605_5769" x1="90" y1="-0.555557" x2="93.8744" y2="26.1776"
				gradientUnits="userSpaceOnUse">
				<stop stop-color="#D9D9D9" />
				<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
			</linearGradient>
		</defs>
	</svg>
</template>

<script>
export default {
	name: 'CampaignEmptyState',
}
</script>
