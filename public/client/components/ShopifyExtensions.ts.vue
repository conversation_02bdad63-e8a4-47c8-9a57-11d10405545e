<template>
	<div class="flex h-screen overflow-hidden bg-gray-50">
		<!-- Content area -->
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<ToastStatus :status="status" :text="statusText" @clear-status="clearStatus()" />
			<main>
				<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<!-- Page header -->
					<div class="mb-8 flex">
						<h1 class="text-3xl md:text-4xl text-gray-900 font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent">Shopify Extensions</h1>
					</div>

					<!-- Content -->
					<div class="bg-white shadow-xl rounded-2xl mb-8 border border-gray-100">
						<div class="flex flex-col md:flex-row md:-mr-px">
							<SettingsSidebar />

							<div class="grow">
								<!-- Panel body -->
								<div class="p-8 space-y-8">
									<!-- Extension Cards -->
									<section v-for="(extension, index) in extensions" :key="index" class="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow duration-200">
										<div class="flex justify-between items-start mb-6">
											<div class="flex-1">
												<h2 class="text-lg font-semibold text-gray-900 mb-2">{{ extension.name }}</h2>
												<p class="text-gray-600 leading-relaxed">{{ extension.description }}</p>
											</div>
											<div class="ml-6">
												<button
													@click="handleToggle(extension)"
													class="px-4 py-2 rounded-lg font-medium transition-all duration-200"
													:class="extension.enabled 
														? 'bg-red-100 text-red-700 hover:bg-red-200 border border-red-200' 
														: 'bg-green-100 text-green-700 hover:bg-green-200 border border-green-200'"
												>
													{{ extension.enabled ? 'Disable' : 'Enable' }}
												</button>
											</div>
										</div>

										<template v-if="extension.endpoint == '/extensions/vip-shipping'">
											<div v-if="!extension.enabled" class="space-y-4">
												<div>
													<label class="block text-sm font-medium text-gray-700 mb-2">Delivery Option Name</label>
													<input
														v-model="extension.metafield"
														type="text"
														placeholder="e.g., VIP Express Shipping"
														class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200"
													/>
												</div>
												<div>
													<label class="block text-sm font-medium text-gray-700 mb-2">VIP Segment Identifier</label>
													<input
														v-model="extension.segment"
														type="text"
														placeholder="e.g., VIP, Premium, Gold"
														class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200"
													/>
												</div>
											</div>
										</template>
									</section>

									<div v-if="extensions.length === 0" class="text-center py-12">
										<svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
										</svg>
										<h3 class="text-lg font-medium text-gray-900 mb-2">No Extensions Available</h3>
										<p class="text-gray-500">Extensions will appear here when they become available for your store.</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</main>
		</div>
	</div>
</template>



<script>
import { ref, watch } from 'vue'
import Header from '../../client-old/partials/Header.vue'
import SettingsSidebar from '../../client-old/partials/settings/SettingsSidebar.vue'
import ToastStatus from '../../client-old/pages/component/ToastStatus.vue'
import { customerIOTrackEvent } from '../services/customerio.js';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue';
import * as Utils from '../../client-old/utils/Utils';
import { getExtenstions, createExtension, deleteExtension, getProducts } from '../services/extensions.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'Account',
	components: {
		Header,
		SettingsSidebar,
		ToastStatus,
		LightSecondaryButton
	},
	setup() {
		const extensions = ref([]);
		const status = ref('');
		const statusText = ref('');
		const products = ref([]);
		const selectedProduct = ref('');
		const selectedVariant = ref('');
		const minOrderAmount = ref('');

		async function fetchExtensions() {
			let extensionsResponse = await getExtenstions();
			extensionsResponse.forEach(extension => {
				extension.metafield = '';
				extension.segment = '';
			});
			extensionsResponse = extensionsResponse.filter(x => x.endpoint !== '/extensions/free-gift');
			extensions.value = extensionsResponse;

			const shopProducts = await getProducts();
			products.value = shopProducts;
			console.log('products: ', products.value);
		}

		watch(selectedProduct, (newVal, oldVal) => {
			if (newVal.variants?.length === 1) {
				selectedVariant.value = newVal.variants[0];
			} else {
				selectedVariant.value = '';
			}
		});

		return {
			extensions,
			status,
			statusText,
			fetchExtensions,
			products,
			selectedProduct,
			selectedVariant,
			minOrderAmount,
		};
	},
	mounted() {
		this.fetchExtensions();
	},
	methods: {
		async handleToggle(extension) {
			extension.enabled = !extension.enabled;

			if (extension.enabled) {
				const body = this.getBody(extension);
				try {
					console.log(`Enabling extension: `, extension);
					await createExtension(extension.endpoint, body);
				} catch(e) {
					console.error('Error enabling extension: ', e);
					extension.enabled = false;
				}
			} else {
				console.log(`Deleting extension: `, extension);
				await deleteExtension(extension);
			}

			await this.fetchExtensions();
		},
		getBody(extension) {
			if (extension.endpoint == '/extensions/free-gift') {
				console.log(`selectedVariant: `, this.selectedVariant.id);
				console.log(`minOrderAmount: `, this.minOrderAmount);
				return JSON.stringify({
					functionId: extension.functionId,
					productVariantId: this.selectedVariant.id,
					minimumOrderAmount: this.minOrderAmount || 0,
					price: this.selectedVariant.price,
				});
			} else if (extension.endpoint == '/extensions/vip-shipping') {
				return JSON.stringify({
					functionId: extension.functionId,
					metafield: extension.metafield || 'VIP Shipping', // Default value if not provided
					segment: extension.segment || 'VIP', // Default value if not provided
				});
			}
		},
		clearStatus() {
			this.status = '';
		},
		logout() {
			localStorage.removeItem('token');
		},
	}
}
</script>


<style scoped>
.toggle-checkbox {
	opacity: 0;
	width: 0;
	height: 0;
}

.toggle-label {
	display: block;
	overflow: hidden;
	cursor: pointer;
	height: 24px;
	/* Height of the toggle */
	padding: 0;
	line-height: 24px;
	background: #bbb;
	/* Background of the toggle */
	border-radius: 24px;
	transition: background-color 0.2s;
}

.toggle-label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 2px;
	width: 20px;
	/* Width of the toggle handle */
	height: 20px;
	/* Height of the toggle handle */
	border-radius: 20px;
	background: #fff;
	/* Background of the toggle handle */
	transition: 0.2s;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox:checked+.toggle-label:after {
	transform: translateX(18px);
}

.toggle-checkbox:checked {
	right: 0;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox {
	right: 4px;
}

.toggle-label {
	transition: background-color 0.2s;
}

.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}
</style>
