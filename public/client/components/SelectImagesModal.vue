<template>
  <div v-if="show"
       class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
       tabindex="-1"
       role="dialog">
    <!-- Modal Content -->
    <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto animate-modal-appear"
         role="document"
         @click.stop>
      <div class="sticky top-0 bg-white p-6 border-b z-10 flex justify-between items-center">
        <h3 class="text-lg font-semibold">Select Images</h3>
        <button @click="closeModal" class="text-gray-400 hover:text-gray-600 transition">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Filter and Search -->
      <div class="p-4 border-b">
        <div class="flex items-center gap-4">
          <div class="flex-1">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search images by name..."
              class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              @input="filterImages"
            />
          </div>
          <div>
            <select
              v-model="categoryFilter"
              class="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              @change="filterImages"
            >
              <option value="">All categories</option>
              <!-- Default categories -->
              <option value="Logo">Logos</option>
              <option value="Hero Image">Hero Images</option>
              <option value="Banner">Banners</option>
              <option value="Product">Products</option>
              <option value="Background">Backgrounds</option>
              <option value="Icon">Icons</option>
              <option value="Reviews">Reviews</option>
              <!-- Custom categories -->
              <option v-for="category in customCategories" :key="category" :value="category">
                {{ category }}
              </option>
            </select>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center py-10">
        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-purple-600"></div>
      </div>

      <!-- No Results State -->
      <div v-else-if="filteredImages.length === 0" class="p-10 text-center">
        <div class="mx-auto w-16 h-16 mb-4 rounded-full bg-gray-100 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-gray-400">
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
          </svg>
        </div>
        <h3 class="text-gray-500 text-lg font-medium mb-1">No images found</h3>
        <p class="text-gray-400">Try adjusting your search or filter criteria</p>
      </div>

      <!-- Image Grid -->
      <div v-else class="p-6 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
        <div
          v-for="image in filteredImages"
          :key="image.id"
          class="border rounded-lg overflow-hidden"
          :class="{ 'ring-2 ring-purple-500 ring-offset-2': isSelected(image) }"
        >
          <div class="relative group">
            <div class="h-40 bg-gray-50 flex items-center justify-center p-2">
              <img
                :src="image.url"
                :alt="image.friendlyname"
                class="object-contain max-w-full max-h-full"
              />
            </div>

            <!-- Selection Checkbox -->
            <div class="absolute top-2 right-2">
              <div
                class="w-6 h-6 rounded-md border-2 flex items-center justify-center cursor-pointer"
                :class="isSelected(image) ? 'bg-purple-500 border-purple-500' : 'bg-white border-gray-300'"
                @click.stop="toggleSelection(image)"
              >
                <svg v-if="isSelected(image)" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>

          <div
            class="p-3 border-t cursor-pointer"
            @click="toggleSelection(image)"
          >
            <p class="font-medium text-sm truncate">{{ image.friendlyname }}</p>
            <p class="text-xs text-gray-500">{{ getImageType(image) }}</p>
            <p class="text-xs text-gray-400">{{ image.width || '?' }} × {{ image.height || '?' }}px</p>
          </div>
        </div>
      </div>

      <!-- Footer with Actions -->
      <div class="sticky bottom-0 bg-white p-4 border-t flex justify-between items-center">
        <div class="text-sm text-gray-500">
          {{ selectedImages.length }} image{{ selectedImages.length !== 1 ? 's' : '' }} selected
        </div>
        <div class="flex gap-3">
          <button
            @click="closeModal"
            class="px-4 py-2 border rounded-lg hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            @click="addSelectedImages"
            class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="selectedImages.length === 0"
          >
            Add Selected Images
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
  name: 'SelectImagesModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    assetType: {
      type: String,
      default: 'email'
    },
    customCategories: {
      type: Array,
      default: () => []
    },
    maxSelections: {
      type: Number,
      default: Infinity
    }
  },
  data() {
    return {
      isLoading: true,
      images: [],
      selectedImages: [],
      searchQuery: '',
      categoryFilter: '',
      filteredImages: []
    };
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.fetchImages();
      } else {
        // Reset selections when modal is closed
        this.selectedImages = [];
      }
    }
  },
  methods: {
    async fetchImages() {
      this.isLoading = true;
      try {
        const response = await fetch(`${URL_DOMAIN}/branding/images?assetType=${this.assetType}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (response.ok) {
          this.images = await response.json();
          this.filterImages();
        } else {
          console.error('Failed to fetch brand images:', await response.text());
          this.$emit('status-update', 'error', 'Failed to fetch brand images');
        }
      } catch (error) {
        console.error('Error fetching brand images:', error);
        this.$emit('status-update', 'error', 'Error fetching brand images');
      } finally {
        this.isLoading = false;
      }
    },
    filterImages() {
      let filtered = [...this.images];

      // Apply category filter
      if (this.categoryFilter) {
        filtered = filtered.filter(img =>
          img.imageType === this.categoryFilter ||
          img.contentType === this.categoryFilter
        );
      }

      // Apply search filter
      if (this.searchQuery.trim()) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(img =>
          (img.friendlyname && img.friendlyname.toLowerCase().includes(query)) ||
          (img.description && img.description.toLowerCase().includes(query))
        );
      }

      this.filteredImages = filtered;
    },
    getImageType(image) {
      // First try imageType, then try contentType, otherwise "Uncategorized"
      const imageType = image.imageType || image.contentType || '';

      // Verify this is a valid category
      const allCategories = [
        'Logo', 'Hero Image', 'Banner', 'Product',
        'Background', 'Icon', 'Reviews', 'Other',
        ...this.customCategories
      ];

      if (imageType && allCategories.includes(imageType)) {
        return imageType;
      }

      return 'Uncategorized';
    },
    isSelected(image) {
      return this.selectedImages.some(selected => selected.id === image.id);
    },
    toggleSelection(image) {
      if (this.isSelected(image)) {
        // Remove from selection
        this.selectedImages = this.selectedImages.filter(selected => selected.id !== image.id);
      } else {
        // Check if we've reached the maximum number of selections
        if (this.selectedImages.length >= this.maxSelections) {
          this.$emit('status-update', 'warning', `You can only select up to ${this.maxSelections} images`);
          return;
        }

        // Add to selection
        this.selectedImages.push(image);
      }
    },
    closeModal() {
      this.$emit('close');
    },
    addSelectedImages() {
      if (this.selectedImages.length === 0) return;

      this.$emit('select', this.selectedImages);
      this.closeModal();
    }
  }
};
</script>

<style scoped>
/* Modal animations */
.animate-modal-appear {
  animation: modal-appear 0.3s ease-out forwards;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
