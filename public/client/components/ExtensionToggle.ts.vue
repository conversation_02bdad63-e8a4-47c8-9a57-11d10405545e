<template>
	<div>
	  <label class="block text-sm font-medium mb-1">{{ extension.name }}</label>
	  <div class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in">
		<input type="checkbox" id="toggle" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" v-model="extension.enabled" @change="toggleExtension" />
		<label for="toggle" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
	  </div>
	</div>
  </template>

  <script>
  export default {
	props: {
	  extension: {
		type: Object,
		required: true
	  }
	},
	methods: {
	  toggleExtension() {
		this.$emit('toggle', this.extension);
	  }
	}
  }
  </script>
<style scoped>
.toggle-checkbox {
	opacity: 0;
	width: 0;
	height: 0;
}

.toggle-label {
	display: block;
	overflow: hidden;
	cursor: pointer;
	height: 24px;
	/* Height of the toggle */
	padding: 0;
	line-height: 24px;
	background: #bbb;
	/* Background of the toggle */
	border-radius: 24px;
	transition: background-color 0.2s;
}

.toggle-label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 2px;
	width: 20px;
	/* Width of the toggle handle */
	height: 20px;
	/* Height of the toggle handle */
	border-radius: 20px;
	background: #fff;
	/* Background of the toggle handle */
	transition: 0.2s;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox:checked+.toggle-label:after {
	transform: translateX(18px);
}

.toggle-checkbox:checked {
	right: 0;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox {
	right: 4px;
}

.toggle-label {
	transition: background-color 0.2s;
}
</style>
