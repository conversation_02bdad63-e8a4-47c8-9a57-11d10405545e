<template>
	<div >

	  <div
		:class="sectionClasses"
		@dragover.prevent
		@dragenter.prevent="$emit('dragenter', section)"
		@dragleave="$emit('dragleave')"
		@drop="handleDrop"
	  >
	  <signal-badge
		v-for="signal in signals"
		:key="signal.id"
		:signal="signal"
		:section="section"
		@click="handleSignalClick"
		/>
		<div v-if="!signals || signals.length === 0" class="text-gray-400 text-sm text-center">
		  <p class="text-xs mt-1">Drag signals here to include them in the segment</p>
		</div>
	  </div>
	</div>
  </template>

  <script>
  import SignalBadge from './SignalBadgeX.ts.vue'

  export default {
	name: 'SignalSection',
	components: {
	  SignalBadge
	},
	props: {
	  title: { type: String, required: true },
	  section: { type: String, required: true },
	  signals: { type: Array, default: () => [] },
	  isDraggingOver: { type: String, default: null }
	},
	computed: {
	  sectionClasses() {
		return [
		  'min-h-48 p-4 transition-colors duration-200',
		  this.isDraggingOver === this.section ? 'bg-blue-50' : 'bg-white',
		  (!this.signals || this.signals.length === 0) ? 'flex items-center justify-center' : 'flex flex-wrap content-start'
		]
	  }
	},
	methods: {
	  handleDrop(e) {
		e.preventDefault()
		const signalId = parseInt(e.dataTransfer.getData('signalId'))
		const sourceSection = e.dataTransfer.getData('sourceSection')
		this.$emit('drop', { signalId, sourceSection, targetSection: this.section })
	  },
	  handleSignalClick(data) {
		this.$emit('signal-click', data);
	  }
	}
  }
  </script>
