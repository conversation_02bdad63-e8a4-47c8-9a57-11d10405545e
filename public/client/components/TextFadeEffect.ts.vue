<template>
	<transition name="fade" mode="out-in">
		<span :key="'text-' + aiTextIndex">{{ textList[aiTextIndex] }}</span>
	</transition>
</template>

  <script>
  export default {
	props: ['textList', 'speed'],
	emits: ['textComplete', 'textIndex'],
	data() {
	  return {
		aiTextIndex: 0,
		interval: null,
	  };
	},
	mounted() {
		if(this.speed == null)
	  		this.interval = setInterval(this.updateAIText, 3000);
		else
			this.interval = setInterval(this.updateAIText, this.speed);
	},
	beforeDestroy() {
			clearInterval(this.interval); // Clear the interval when the component is destroyed
	},
	methods: {
		updateAIText() {
				if(this.aiTextIndex < (this.textList.length - 1)) {
					this.aiTextIndex++;
					this.$emit('textIndex', this.aiTextIndex);
				}
				else if(this.aiTextIndex == (this.textList.length - 1))
				{
					this.$emit('textComplete', 'true');
					clearInterval(this.interval);
				}
			},
	},
  };
  </script>

  <style>

.fade-enter-active, .fade-leave-active {
    transition-property: opacity, transform;
    transition-duration: 500ms;
    transition-timing-function: ease-in-out;
}
.fade-enter-from, .fade-leave-to {
    opacity: 0;
    transform: translateX(10px);
}
.fade-enter-to, .fade-leave-from {
    opacity: 1;
    transform: translateX(0);
}
  </style>
