<template>
	<div class="max-w-sm rounded-lg border bg-white p-6 shadow-md fade-out-transition" :class="{'opacity-0': recommendation.fadeOut}"
		style="max-width: 400px;">
		<div class="mb-4">
			<span class="block text-xs font-medium text-gray-500 tracking-wider">
				{{ title }}
			</span>
		</div>
		<div class="flex items-center mb-4">
			<div class="p-2 rounded-full bg-green-100">
				<div class="p-2 rounded-full bg-green-100" v-html="recommendation.icon"></div>
			</div>
			<div class="ml-3">
				<p class="text-md font-medium text-gray-900">
					{{ recommendation.title }}
				</p>
			</div>
		</div>
		<div class="flex justify-between items-center">
			<div class="flex items-center">
				<button class="text-xs font-semibold text-gray-400 hover:text-gray-500">
					Copilot Recommended
				</button>
				<img src="../images/magic.svg" alt="Magic" class="ml-2" width="20" height="20" />
			</div>
			<div>
				<button
					class="text-sm font-semibold text-gray-400 hover:text-gray-500 mr-4"
					@click="() => $emit('ignoreRecommendation', recommendation, index)">
					Ignore
				</button>
				<PrimaryButton
					cta="Add"
					size="xs"
					icon="false"
					@click="() => $emit('addRecommendation', recommendation, index)">
				</PrimaryButton>
			</div>
		</div>
	</div>
</template>

<script>

import PrimaryButton from './PrimaryButton.ts.vue';
export default {
	name: 'RecommendationCard',
	props: ['recommendation', 'title', 'index', 'fadeOut'],
	emits: ['addRecommendation', 'ignoreRecommendation'],
	components: {
		PrimaryButton,
	},
	data() { },
	mounted() { },
}
</script>

<style>
.fade-out-transition {
	transition: opacity 0.5s ease-out;
}

.fade-out {
	opacity: 0;
}
</style>
