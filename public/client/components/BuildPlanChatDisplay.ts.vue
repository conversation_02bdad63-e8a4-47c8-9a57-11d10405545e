<template>
  <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm max-w-md hover:shadow-md transition-shadow duration-200">
    <div class="flex items-center">
      <!-- Icon with animation when generating -->
      <div class="relative mr-3">
        <svg
          class="h-6 w-6 text-purple-600"
          :class="{ 'animate-spin': segment.isGenerating }"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M12 2v4M12 18v4M4.93 4.93l2.84 2.84M16.23 16.23l2.84 2.84M2 12h4M18 12h4M4.93 19.07l2.84-2.84M16.23 7.77l2.84-2.84"/>
        </svg>
      </div>
      <div class="flex flex-col">
        <span class="font-medium text-gray-800">Build Plan Action</span>
        <span v-if="segment.isGenerating" class="text-xs text-purple-600">Building your plan...</span>
        <span v-else class="text-xs text-gray-500">Plan will be built and saved</span>
      </div>
    </div>
    <div class="mt-2 text-xs text-gray-600">
      <p>This action will save your plan to the system and navigate you to the plan page.</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';

// Basic interface for the segment prop
interface BuildPlanSegment {
  id: string;
  type: 'buildplan_trigger';
  sender: 'ai';
  isGenerating?: boolean;
}

defineProps({
  segment: {
    type: Object as PropType<BuildPlanSegment>,
    required: true,
  },
});
</script>

<style scoped>
/* Add any specific styles if needed */
</style>
