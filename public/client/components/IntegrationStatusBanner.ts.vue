<template>
  <transition name="banner-slide">
    <div v-if="shouldShowBanner" class="integration-banner">
    <div class="banner-content">
      <div class="banner-left">
        <div class="knowledge-status">
          <div v-if="isFineTuningKnowledge" class="knowledge-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
            </svg>
          </div>
          <span 
            v-if="isFineTuningKnowledge"
            :class="['knowledge-text', 'typing-animation', 'has-tooltip']"
            :title="'Your quality will improve greatly when fine tuning is done. Start a new chat once this finishes to see the difference.'"
          >
            {{ knowledgeStatusText }}<span class="dots">...</span>
          </span>
        </div>
      </div>
      
      <div class="banner-actions">
        <a href="/ai-strategist/knowledge" class="knowledge-link">AI knowledge</a>
        
        <button
          v-if="!integrationStatus.shopify"
          @click="handleIntegrationAction('shopify')"
          class="integration-button"
        >
          <span class="button-icon shopify">
            <svg width="16" height="16" viewBox="-18 0 292 292" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid">
              <path d="M223.774 57.34c-.201-1.46-1.48-2.268-2.537-2.357-1.055-.088-23.383-1.743-23.383-1.743s-15.507-15.395-17.209-17.099c-1.703-1.703-5.029-1.185-6.32-.805-.19.056-3.388 1.043-8.678 2.68-5.18-14.906-14.322-28.604-30.405-28.604-.444 0-.901.018-1.358.044C129.31 3.407 123.644.779 118.75.779c-37.465 0-55.364 46.835-60.976 70.635-14.558 4.511-24.9 7.718-26.221 8.133-8.126 2.549-8.383 2.805-9.45 10.462C21.3 95.806.038 260.235.038 260.235l165.678 31.042 89.77-19.42S223.973 58.8 223.775 57.34zM156.49 40.848l-14.019 4.339c.005-.988.01-1.96.01-3.023 0-9.264-1.286-16.723-3.349-22.636 8.287 1.04 13.806 10.469 17.358 21.32zm-27.638-19.483c2.304 5.773 3.802 14.058 3.802 25.238 0 .572-.005 1.095-.01 1.624-9.117 2.824-19.024 5.89-28.953 8.966 5.575-21.516 16.025-31.908 25.161-35.828zm-11.131-10.537c1.617 0 3.246.549 4.805 1.622-12.007 5.65-24.877 19.88-30.312 48.297l-22.886 7.088C75.694 46.16 90.81 10.828 117.72 10.828z" fill="#95BF46"/>
              <path d="M221.237 54.983c-1.055-.088-23.383-1.743-23.383-1.743s-15.507-15.395-17.209-17.099c-.637-.634-1.496-.959-2.394-1.099l-12.527 256.233 89.762-19.418S223.972 58.8 223.774 57.34c-.201-1.46-1.48-2.268-2.537-2.357" fill="#5E8E3E"/>
              <path d="M135.242 104.585l-11.069 32.926s-9.698-5.176-21.586-5.176c-17.428 0-18.305 10.937-18.305 13.693 0 15.038 39.2 20.8 39.2 56.024 0 27.713-17.577 45.558-41.277 45.558-28.44 0-42.984-17.7-42.984-17.7l7.615-25.16s14.95 12.835 27.565 12.835c8.243 0 11.596-6.49 11.596-11.232 0-19.616-32.16-20.491-32.16-52.724 0-27.129 19.472-53.382 58.778-53.382 15.145 0 22.627 4.338 22.627 4.338" fill="#FFF"/>
            </svg>
          </span>
          <span>Connect Shopify</span>
        </button>
        
        <button
          v-if="!integrationStatus.klaviyo"
          @click="handleIntegrationAction('klaviyo')"
          class="integration-button"
        >
          <span class="button-icon klaviyo">
            <svg width="16" height="16" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
              <path d="M0,0H150V150H0V0Z" fill="none"/>
              <path d="M148.76,124.01H3.24V26.63H148.76l-30.55,48.69,30.55,48.69Z" fill="currentColor"/>
            </svg>
          </span>
          <span>Connect Klaviyo</span>
        </button>
      </div>
    </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { URL_DOMAIN } from '../utils/utils';
import * as OrgServices from '../services/organization.js';

// No emits needed anymore
const router = useRouter();

const integrationStatus = ref({
  shopify: false,
  klaviyo: false
});

const brandVoice = ref('');
const currentOrg = ref<any>(null);
const hasLoadedInitialData = ref(false);
let knowledgeCheckInterval: NodeJS.Timeout | null = null;

const isFineTuningKnowledge = computed(() => {
  return !brandVoice.value || brandVoice.value.trim() === '';
});

const knowledgeStatusText = computed(() => {
  return isFineTuningKnowledge.value ? 'Fine-tuning knowledge base' : 'Constantly Learning';
});

const shouldShowBanner = computed(() => {
  // Don't show banner until we've loaded initial data
  if (!hasLoadedInitialData.value) {
    return false;
  }
  
  // Show banner if:
  // - Currently fine-tuning knowledge base, OR
  // - Shopify is not connected, OR  
  // - Klaviyo is not connected
  return isFineTuningKnowledge.value || 
         !integrationStatus.value.shopify || 
         !integrationStatus.value.klaviyo;
});

async function checkIntegrations() {
  const token = localStorage.getItem('token');
  try {
    // Check Shopify connection
    const shopifyResponse = await fetch(`${URL_DOMAIN}/integration/shopify/connected`, {
      headers: {'Authorization': `Bearer ${token}`}
    });
    if (shopifyResponse.ok) {
      const shopifyData = await shopifyResponse.json();
      integrationStatus.value.shopify = shopifyData.connected;
    }

    // Check Klaviyo connection
    const klaviyoResponse = await fetch(`${URL_DOMAIN}/integration/klaviyo/connected`, {
      headers: {'Authorization': `Bearer ${token}`}
    });
    if (klaviyoResponse.ok) {
      const klaviyoData = await klaviyoResponse.json();
      integrationStatus.value.klaviyo = klaviyoData.connected;
    }
  } catch (error) {
    console.error('Error checking integrations:', error);
  }
}

async function checkKnowledgeBase() {
  try {
    // Get current organization data (exactly like in AgentKnowledge.ts.vue)
    currentOrg.value = await OrgServices.getCurrentOrg();
    const oldBrandVoice = brandVoice.value;
    brandVoice.value = currentOrg.value.sampleLanguage || '';
    
    // If fine-tuning status changed, update polling interval
    const wasFineTuning = !oldBrandVoice || oldBrandVoice.trim() === '';
    const isNowFineTuning = !brandVoice.value || brandVoice.value.trim() === '';
    
    if (wasFineTuning !== isNowFineTuning) {
      setupPolling();
    }
    
    console.log('Brand voice value:', brandVoice.value); // Debug log
  } catch (error) {
    console.error('Error checking knowledge base:', error);
  }
}

function setupPolling() {
  // Clear existing interval
  if (knowledgeCheckInterval) {
    clearInterval(knowledgeCheckInterval);
  }
  
  // Set interval based on fine-tuning status
  const interval = isFineTuningKnowledge.value ? 10000 : 60000; // 10 seconds if fine-tuning, 60 seconds if not
  
  knowledgeCheckInterval = setInterval(checkKnowledgeBase, interval);
  
  console.log(`Knowledge polling set to ${interval/1000} seconds (fine-tuning: ${isFineTuningKnowledge.value})`);
}

function handleIntegrationAction(type: string) {
  if (integrationStatus.value[type]) {
    // Already connected, maybe show success or do nothing
    return;
  } else {
    // Not connected, redirect to integrations page
    router.push('/integrations');
  }
}

onMounted(async () => {
  // Wait for both integrations and knowledge base to load before showing banner
  await checkIntegrations();
  await checkKnowledgeBase();
  
  // Now we have all the data needed to determine if banner should show
  hasLoadedInitialData.value = true;
  
  // Set up dynamic polling based on fine-tuning status
  setupPolling();
  
  // Listen for quota updates to refresh integration status
  window.addEventListener('quotaUpdated', checkIntegrations);
  
  // Refresh knowledge base status when window gains focus (in case user updated it in another tab)
  window.addEventListener('focus', checkKnowledgeBase);
});

onUnmounted(() => {
  // Clean up intervals and event listeners
  if (knowledgeCheckInterval) {
    clearInterval(knowledgeCheckInterval);
  }
  window.removeEventListener('quotaUpdated', checkIntegrations);
  window.removeEventListener('focus', checkKnowledgeBase);
});
</script>

<style scoped>
.integration-banner {
  background-color: #1d1644;
  color: white;
  padding: 8px 16px;
  border-radius: 0;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding-left: 12px;
  padding-right: 12px;
}

.banner-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.integration-message {
  display: flex;
  align-items: center;
  gap: 8px;
}

.knowledge-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.banner-icon,
.knowledge-icon {
  color: #fbbf24;
  display: flex;
  align-items: center;
}

.banner-text,
.knowledge-text {
  font-size: 14px;
  font-weight: 500;
  font-family: 'Inter', sans-serif;
}

.knowledge-text.has-tooltip {
  cursor: help;
}

.knowledge-link {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Inter', sans-serif;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-decoration: none;
}

.knowledge-link:hover {
  background: rgba(255, 255, 255, 0.3);
}

.banner-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.integration-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Inter', sans-serif;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.integration-button:hover {
  background: rgba(255, 255, 255, 0.3);
}


.button-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  flex-shrink: 0;
}

.button-icon.shopify {
  background: transparent;
  color: white;
}

.button-icon.klaviyo {
  background: transparent;
  color: white;
}


/* Typing Dots Animation */
.dots {
  animation: typing 1.5s infinite;
}

@keyframes typing {
  0%, 60% {
    opacity: 0;
  }
  30% {
    opacity: 1;
  }
}

/* Pulsing Animation for the whole text */
.typing-animation {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .banner-left {
    justify-content: center;
  }
  
  .banner-actions {
    justify-content: center;
  }
  
  .integration-button {
    font-size: 11px;
    padding: 3px 8px;
  }
  
  .banner-text {
    font-size: 13px;
  }
}

/* Banner slide transition */
.banner-slide-enter-active {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

.banner-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19);
  overflow: hidden;
}

.banner-slide-enter-from {
  transform: translateY(-100%);
  opacity: 0;
  max-height: 0;
}

.banner-slide-enter-to {
  transform: translateY(0);
  opacity: 1;
  max-height: 100px;
}

.banner-slide-leave-from {
  transform: translateY(0);
  opacity: 1;
  max-height: 100px;
}

.banner-slide-leave-to {
  transform: translateY(-100%);
  opacity: 0;
  max-height: 0;
}
</style>