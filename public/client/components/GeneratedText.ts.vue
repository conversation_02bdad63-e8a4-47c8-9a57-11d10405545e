<template>
	<div v-html="animatedHtml"></div>
</template>

  <script>
  export default {
	props: {
	  htmlText: {
		type: String,
		required: true,
	  },
	},
	data() {
	  return {
		animatedHtml: '',
	  };
	},
	mounted() {
	  this.animateText();
	},
	methods: {
	  animateText() {
		const characters = this.htmlText.split('');
		let html = '';
		let tag = false;
		let currentTag = '';
		let liIndex = 0;

		characters.forEach((char, index) => {
		  if (char === '<') {
			tag = true;
			currentTag = '<';
		  } else if (char === '>') {
			currentTag += '>';
			tag = false;

			if (currentTag.toLowerCase() === '<ul>') {
			  html += '<ul class="custom-ul">';
			} else if (currentTag.toLowerCase() === '</ul>') {
			  html += '</ul>';
			} else if (currentTag.toLowerCase() === '<li>') {
			  html += `<li><span class="custom-disc fade-in" style="animation-delay:${index * 50 + liIndex * 100}ms">• </span>`;
			  liIndex++;
			} else {
			  html += currentTag;
			}
		  } else if (tag) {
			currentTag += char;
		  } else {
			html += `<span class="fade-in" style="animation-delay:${index * 20}ms">${char}</span>`;
		  }
		});

		this.animatedHtml = html;
	  },
	},
  };
  </script>

  <style>
  .custom-ul {
	list-style: none;
  }

  .custom-disc {
	opacity: 0;
	animation: fadeInEffect 0.5s forwards;
	margin-right: 5px;
  }

  .fade-in {
	opacity: 0;
	animation: fadeInEffect 0.5s forwards;
  }

  @keyframes fadeInEffect {
	to {
	  opacity: 1;
	}
  }
  </style>
