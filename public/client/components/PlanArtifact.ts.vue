<template>
  <div>
    <div v-if="isGenerating" class="streaming-view p-4 bg-gray-100 rounded-md flex flex-col h-full">
      <div class="flex items-center mb-4">
        <!-- Simplified Raleon Loader -->
        <div class="raleon-loader-inline mr-3">
          <svg width="24" height="18" viewBox="0 0 834 605" fill="none" xmlns="http://www.w3.org/2000/svg" class="rtop-inline">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M417 0L833.341 523.732L755.961 604.265L714.292 554.032L747.276 519.704L417 104.237L86.7254 519.704L119.709 554.032L78.0404 604.265L0.660156 523.732L417 0Z" fill="#5A16C9"/>
          </svg>
        </div>
        <!-- Informative loading text -->
        <div>
          <div class="text-gray-800 font-medium">Generating your campaign plan</div>
          <div class="text-sm text-gray-600">Analyzing data and building campaign steps...</div>
        </div>
      </div>
      <pre ref="streamingPre" class="whitespace-pre-wrap font-mono flex-1 overflow-y-auto bg-white p-3 rounded-md border border-gray-200">{{ streamingContent }}</pre>
    </div>

    <!-- JSON Parse Error State -->
    <div v-else-if="hasParseError && rawJsonContent" class="flex flex-col overflow-y-auto p-6">
      <div class="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex items-start">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <div>
            <h3 class="text-lg font-medium text-red-800">Invalid Plan Format</h3>
            <p class="text-red-700 mt-1">The plan data couldn't be processed due to a formatting error.</p>
          </div>
        </div>
        <button
          @click="$emit('retry-parse')"
          class="mt-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Retry
        </button>
      </div>

      <!-- Raw JSON Display -->
      <div class="bg-gray-50 rounded-lg border border-gray-200 p-4">
        <h3 class="text-lg font-medium text-gray-700 mb-2">Raw JSON Content</h3>
        <pre class="whitespace-pre-wrap font-mono text-sm bg-white p-3 rounded-md border border-gray-200 overflow-x-auto max-h-[500px] overflow-y-auto">{{ rawJsonContent }}</pre>
      </div>
    </div>

    <!-- Successfully Parsed Plan Display -->
    <div v-else-if="!isGenerating && planData" class="flex flex-col overflow-y-auto p-6">
      <!-- Plan Header -->
      <div class="mb-4">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">{{ planData.name || 'Monthly Campaign Plan' }}</h1>

        <!-- Date Range Badge -->
        <div class="inline-block p-1 text-gray-405 rounded-full text-sm font-medium mb-4">
          {{ formatDate(planData.startDate) }} - {{ formatDate(planData.endDate) }}
        </div>

        <!-- Goal Section -->
        <div class="bg-gray-50 p-4 rounded-lg mb-4">
          <div class="font-medium text-gray-700 mb-1">Goal:</div>
          <div class="text-gray-800">{{ planData.goal }}</div>
        </div>
      </div>

      <!-- Plan Analysis Section (Collapsible) -->
      <div class="mb-4">
        <div
          @click="toggleSection('analysis')"
          class="flex items-center justify-between bg-gray-100 p-4 rounded-lg cursor-pointer hover:bg-gray-200 transition-colors"
        >
          <h2 class="text-lg font-semibold text-gray-700">Plan Analysis</h2>
          <!-- Plus/Minus Icon -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-gray-500 transition-opacity duration-200"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <!-- Minus sign (shown when expanded) -->
            <path
              v-if="expandedSections.analysis"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M20 12H4"
            />
            <!-- Plus sign (shown when collapsed) -->
            <path
              v-else
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
        </div>

        <transition name="expand">
          <div v-if="expandedSections.analysis" class="p-4 border border-gray-200 rounded-b-lg bg-white mt-1">
            <p class="text-gray-700">{{ planData.description }}</p>
          </div>
        </transition>
      </div>

      <!-- Data Summary Section (Collapsible) -->
      <div class="mb-6">
        <div
          @click="toggleSection('dataSummary')"
          class="flex items-center justify-between bg-gray-100 p-4 rounded-lg cursor-pointer hover:bg-gray-200 transition-colors"
        >
          <h2 class="text-lg font-semibold text-gray-700">Data Summary</h2>
          <!-- Plus/Minus Icon -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-gray-500 transition-opacity duration-200"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <!-- Minus sign (shown when expanded) -->
            <path
              v-if="expandedSections.dataSummary"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M20 12H4"
            />
            <!-- Plus sign (shown when collapsed) -->
            <path
              v-else
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
        </div>

        <transition name="expand">
          <div v-if="expandedSections.dataSummary" class="p-4 border border-gray-200 rounded-b-lg bg-white mt-1">
            <p class="text-gray-700">{{ planData.dataSummary }}</p>
          </div>
        </transition>
      </div>

      <!-- Campaigns Section -->
      <h2 class="text-xl font-semibold text-gray-800 mb-4">Campaigns</h2>

      <div class="space-y-4">
        <div
          v-for="(campaign, index) in planData.campaigns"
          :key="index"
          class="border border-gray-200 rounded-lg overflow-hidden bg-white"
        >
          <!-- Campaign Header - Entire row clickable -->
          <div
            @click="toggleCampaign(index)"
            class="grid grid-cols-12 gap-2 p-4 border-b border-gray-100 items-center cursor-pointer hover:bg-gray-50 transition-colors"
          >
            <!-- Campaign Name (spans 6 columns) -->
            <div class="col-span-6">
              <div class="text-lg font-medium text-gray-800 truncate">{{ campaign.name }}</div>
            </div>

            <!-- Campaign Type Badge (fixed width, 2 columns) -->
            <div class="col-span-2 text-center">
              <span :class="[
                'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full justify-center',
                getCampaignTypeClass(campaign.campaignType)
              ]">
                {{ campaign.campaignType }}
              </span>
            </div>

            <!-- Date (fixed width, 3 columns) -->
            <div class="col-span-3 text-sm text-gray-500 text-right">
              {{ formatDate(campaign.scheduledDate) }}
            </div>

            <!-- Expand/Collapse Icon (1 column) -->
            <div class="col-span-1 text-right">
              <!-- Plus/Minus Icon -->
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 text-gray-400 transition-opacity duration-200 inline-block"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <!-- Minus sign (shown when expanded) -->
                <path
                  v-if="expandedCampaigns[index]"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M20 12H4"
                />
                <!-- Plus sign (shown when collapsed) -->
                <path
                  v-else
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4v16m8-8H4"
                />
              </svg>
            </div>
          </div>

          <!-- Campaign Details (Expandable) -->
          <transition name="expand">
            <div
              v-if="expandedCampaigns[index]"
              class="p-4 bg-gray-50"
            >
              <!-- Description -->
              <div class="mb-4">
                <p class="text-gray-700">{{ campaign.description }}</p>
              </div>

              <!-- Campaign Details Grid -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Target Audience -->
                <div class="bg-white p-3 rounded border border-gray-200">
                  <div class="text-sm font-medium text-gray-500 mb-1">Target Audience</div>
                  <p class="text-sm text-gray-700">{{ campaign.targetSegment }}</p>
                </div>

                <!-- Rationale -->
                <div v-if="campaign.whyText" class="bg-white p-3 rounded border border-gray-200">
                  <div class="text-sm font-medium text-gray-500 mb-1">Rationale</div>
                  <p class="text-sm text-gray-700">{{ campaign.whyText }}</p>
                </div>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, ref, watch, nextTick } from '@vue/runtime-core';

interface Campaign {
  name: string;
  description: string;
  targetSegment: string;
  scheduledDate: string;
  campaignType: string;
  whyText: string;
}

interface PlanData {
  campaigns: Campaign[];
  goal: string;
  description: string;
  name: string;
  startDate: string;
  endDate: string;
  dataSummary: string;
}

export default defineComponent({
  name: 'PlanArtifact',
  props: {
    planData: {
      type: Object as PropType<PlanData>,
      required: false,
    },
    isGenerating: {
      type: Boolean,
      default: false,
    },
    streamingContent: {
      type: String,
      default: '',
    },
    hasParseError: {
      type: Boolean,
      default: false,
    },
    rawJsonContent: {
      type: String,
      default: '',
    },
  },
  emits: ['retry-parse'],
  setup(props) {
    // Reference to the streaming pre element for auto-scrolling
    const streamingPre = ref<HTMLPreElement | null>(null);

    // Watch for changes to streamingContent and auto-scroll to bottom
    watch(() => props.streamingContent, async () => {
      await nextTick(); // Wait for DOM update
      if (streamingPre.value) {
        streamingPre.value.scrollTop = streamingPre.value.scrollHeight;
      }
    });

    // Track which campaigns are expanded
    const expandedCampaigns = ref<Record<number, boolean>>({
      // Expand the first campaign by default if there are campaigns
      0: !!props.planData?.campaigns?.length
    });

    // Track which sections are expanded
    const expandedSections = ref({
      analysis: true, // Start with analysis expanded
      dataSummary: false // Start with data summary collapsed
    });

    // Toggle expansion state of a campaign
    const toggleCampaign = (index: number) => {
      expandedCampaigns.value[index] = !expandedCampaigns.value[index];
    };

    // Toggle expansion state of a section
    const toggleSection = (section: string) => {
      expandedSections.value[section] = !expandedSections.value[section];
    };

    return {
      expandedCampaigns,
      expandedSections,
      toggleCampaign,
      toggleSection,
      streamingPre,
    };
  },
  methods: {
    formatDate(dateString: string): string {
      if (!dateString) return 'N/A';

      try {
        const date = new Date(dateString);
        // Parse as UTC and format without timezone adjustment
        return date.toLocaleDateString('en-US', {
          month: 'long',
          day: 'numeric',
          year: 'numeric',
          timeZone: 'UTC', // Force UTC timezone
        });
      } catch (e) {
        return dateString; // Return the original string if parsing fails
      }
    },
    truncate(text: string, length: number): string {
      if (!text) return '';
      return text.length > length ? text.substring(0, length) + '...' : text;
    },
    getCampaignTypeClass(type: string): string {
      switch (type?.toLowerCase()) {
        case 'promotion':
          return 'bg-green-100 text-green-800';
        case 'education':
          return 'bg-blue-100 text-blue-800';
        case 'awareness':
          return 'bg-yellow-100 text-yellow-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    },
  },
});
</script>

<style scoped>
/* Raleon Loader Inline Animation */
.raleon-loader-inline {
  position: relative;
  width: 24px;
  height: 18px;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

.rtop-inline {
  animation: pulse 1.5s ease-in-out infinite;
}
.v-enter-active,
.v-leave-active {
  transition: opacity 0.3s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}

/* Expand/collapse animation */
.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease-out;
  max-height: 300px;
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  max-height: 0;
  opacity: 0;
  padding-top: 0;
  padding-bottom: 0;
}

/* Smooth rotation for the arrow icon */
.rotate-180 {
  transform: rotate(180deg);
}
</style>
