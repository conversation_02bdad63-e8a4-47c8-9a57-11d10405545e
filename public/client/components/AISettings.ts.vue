<template>
	<div class="flex h-screen overflow-hidden bg-gray-50">
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<ToastStatus :status="status" :text="statusText" @clear-status="clearStatus()" />
			<main>
				<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<div class="mb-8 flex">
						<h1 class="text-3xl md:text-4xl text-gray-900 font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent">AI Settings</h1>
					</div>
					<div class="bg-white shadow-xl rounded-2xl mb-8 border border-gray-100">
						<div class="flex flex-col md:flex-row md:-mr-px">
							<SettingsSidebar />
							<div class="grow">
								<div class="p-8 space-y-8">
									<section>
										<div class="mb-6">
											<label class="block text-lg font-semibold text-gray-900 mb-2" for="store-description">Store Description</label>
											<p class="text-gray-600 mb-4">Help our AI understand your store's context and purpose for better recommendations.</p>
											<textarea 
												id="store-description" 
												class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200 resize-none" 
												rows="4"
												v-model="storeDescription" 
												placeholder="Describe your store's products, target audience, and brand personality..."
											></textarea>
										</div>
									</section>
									<section>
										<div class="mb-6">
											<label class="block text-lg font-semibold text-gray-900 mb-2" for="example-language">Brand Voice & Tone</label>
											<p class="text-gray-600 mb-4">Provide examples of your brand's communication style to help AI match your voice.</p>
											<textarea 
												id="example-language" 
												class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200 resize-none" 
												rows="4"
												v-model="exampleLanguage" 
												placeholder="e.g., 'We use friendly, casual language with exclamation points! Our tone is upbeat and encouraging.'"
											></textarea>
										</div>
									</section>
									<div class="pt-4">
										<button 
											@click="saveStoreSettings()"
											class="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200"
										>
											Save Settings
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</main>
		</div>
	</div>
</template>

<script>
import { ref } from 'vue'
import SettingsSidebar from '../../client-old/partials/settings/SettingsSidebar.vue'
import ToastStatus from '../../client-old/pages/component/ToastStatus.vue'
import * as Utils from '../../client-old/utils/Utils';
import * as OrgServices from '../services/organization';

export default {
	name: 'StoreLanguageSettings',
	components: {
		SettingsSidebar,
		ToastStatus
	},
	setup() {
		const status = ref('');
		const statusText = ref('');
		const storeDescription = ref('');
		const exampleLanguage = ref('');
		const currentOrg = ref({});

		return {
			status,
			statusText,
			storeDescription,
			exampleLanguage,
			currentOrg
		};
	},
	async mounted() {
		this.currentOrg = await OrgServices.getCurrentOrg();
		console.log('currentOrg', this.currentOrg)
		this.storeDescription = this.currentOrg.description || '';
		this.exampleLanguage = this.currentOrg.sampleLanguage || '';
		console.log('storeDescription', this.storeDescription)
	},
	methods: {
		saveStoreSettings() {
			OrgServices.patchOrgById(this.currentOrg.id, {
				description: this.storeDescription,
				sampleLanguage: this.exampleLanguage
			}).then(() => {
				this.status = 'success';
				this.statusText = 'Store AI settings saved successfully';
			}).catch((error) => {
				this.status = 'error';
				this.statusText = 'Failed to save store AI settings';
			});
		},
		clearStatus() {
			this.status = '';
			this.statusText = '';
		}
	}
}
</script>

<style scoped>
/* Add your styles here */
</style>
