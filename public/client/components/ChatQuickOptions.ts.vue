<template>
  <div class="flex flex-wrap items-center justify-center gap-x-3 gap-y-2 mt-4 w-full">
    <button
      v-for="(action, index) in quickActions"
      :key="index"
      @click="selectAction(action.id)"
      :disabled="isDisabled || chatHasStarted || action.id === 'flows'"
      :class="[
        'flex items-center gap-2 px-4 py-2 rounded-full text-sm transition-colors duration-150 border',
        selectedAction === action.id
          ? 'bg-purple-50 border-purple-300 text-purple-700'
          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50',
        (isDisabled || chatHasStarted || action.id === 'flows') ? 'opacity-50 cursor-not-allowed' : '',
      ]"
    >
      <component :is="action.icon" class="h-4 w-4" />
      {{ action.title }}
    </button>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, watch } from '@vue/runtime-core';
import {
  CalendarIcon,
  EnvelopeIcon,
  LightBulbIcon,
  PhotoIcon
} from '@heroicons/vue/24/outline';

export interface QuickActionOption {
  id: string;
  title: string;
  icon: any;
  initialPrompts: Array<{
    title: string;
    description: string;
    promptText: string;
    iconComponent?: any;
  }>;
}

export default defineComponent({
  name: 'ChatQuickOptions',
  components: {
    CalendarIcon,
    EnvelopeIcon,
    LightBulbIcon,
    PhotoIcon
  },
  props: {
    modelValue: {
      type: String,
      required: true
    },
    isDisabled: {
      type: Boolean,
      default: false
    },
    chatHasStarted: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const selectedAction = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    });

    const quickActions = ref<QuickActionOption[]>([
      {
        id: 'planMonth',
        title: 'Plan my month',
        icon: CalendarIcon,
        initialPrompts: [
          {
            title: 'Help me plan next month',
            description: 'Build a plan of campaign ideas for the upcoming month.',
            promptText: 'Help me plan my marketing campaigns for next month using your best assumptions.',
            iconComponent: CalendarIcon,
          },
          {
            title: 'Give me a Plan for emails next week',
            description: 'Create a plan for emails to send next week.',
            promptText: 'Help me plan my emails for next week.',
            iconComponent: EnvelopeIcon,
          },
          {
            title: 'Generate a plan for a new product launch',
            description: 'Develop a plan for launching a new product.',
            promptText: 'Help me plan a new product launch.',
            iconComponent: LightBulbIcon,
          },
          {
            title: 'Help me plan emails for an upcoming holiday',
            description: 'Create a plan for emails to send during the next holiday.',
            promptText: 'Help me plan my emails for the upcoming holiday.',
            iconComponent: CalendarIcon,
          },
        ]
      },
      {
        id: 'planCampaigns',
        title: 'Send an email',
        icon: EnvelopeIcon,
        initialPrompts: [
          {
            title: 'Give me 5 ideas for emails I can send',
            description: 'Get email campaign ideas based on best assumptions for your brand.',
            promptText: 'Give me 5 ideas for emails I can send using your best assumptions.',
            iconComponent: EnvelopeIcon,
          },
          {
            title: 'Give me a brief for an email I can send',
            description: 'Request an email brief using AI analysis of your brand.',
            promptText: 'Give me a brief that you think will work using your best analysis.',
            iconComponent: EnvelopeIcon,
          },
          {
            title: 'Generate a Promotional Brief',
            description: 'Create a promotional email brief tailored to your brand.',
            promptText: 'Generate a Brief for a Promotional Email using your best estimate on what would work for my brand.',
            iconComponent: LightBulbIcon,
          },
          {
            title: 'Give me 5 educational ideas for emails',
            description: 'Get educational content ideas based on store analysis.',
            promptText: 'Give me 5 educational ideas for emails using your analysis of my store.',
            iconComponent: LightBulbIcon,
          },
        ]
      },
      {
        id: 'brainstorm',
        title: 'Brainstorm',
        icon: LightBulbIcon,
        initialPrompts: []
      },
      {
        id: 'flows',
        title: 'Analyze Flows',
        icon: PhotoIcon,
        initialPrompts: []
      }
    ]);

    const selectAction = (actionId: string) => {
      selectedAction.value = actionId;
    };

    return {
      quickActions,
      selectedAction,
      selectAction
    };
  }
});
</script>
