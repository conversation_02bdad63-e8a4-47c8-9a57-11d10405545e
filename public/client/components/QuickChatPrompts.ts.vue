<template>
  <div class="flex flex-wrap items-center justify-start gap-2 mb-3">
    <div
      v-for="(action, index) in quickActions"
      :key="index"
      class="relative group"
    >
      <button
        @click="handleQuickAction(action.promptText)"
        :disabled="isDisabled"
        class="flex items-center px-4 py-1.5 text-xs font-medium rounded-full bg-white border border-gray-200 hover:bg-gray-50 hover:border-purple-200 transition-colors duration-150 shadow-sm"
        :class="{'opacity-70 cursor-not-allowed': isDisabled}"
      >
        <span class="text-[#5A16C9]">{{ action.title }}</span>
      </button>
      <!-- Hover tooltip -->
      <div
        class="absolute left-0 bottom-full mb-2 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10"
      >
        {{ action.description }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from '@vue/runtime-core';

export interface QuickAction {
  title: string;
  description: string;
  promptText: string;
  imageUrl?: string | null;
}

export default defineComponent({
  name: 'QuickChatPrompts',
  props: {
    quickActions: {
      type: Array as PropType<QuickAction[]>,
      required: true,
    },
    isDisabled: {
      type: Boolean,
      default: false,
    }
  },
  emits: ['quick-action-selected'],
  setup(props, { emit }) {
    const handleQuickAction = (promptText: string) => {
      emit('quick-action-selected', promptText);
    };

    return {
      handleQuickAction,
    };
  },
});
</script>