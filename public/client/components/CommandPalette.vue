<template>
  <div
    v-if="show"
    class="absolute bottom-full left-0 right-0 mb-1 w-full bg-white border border-gray-200 rounded-xl shadow-lg z-20"
    :style="{ maxWidth: 'calc(100% - 2rem)', marginLeft: '1rem', marginRight: '1rem' }"
  >
    <ul class="py-1">
      <li class="px-4 pt-2 pb-1 text-xs font-semibold text-gray-400 uppercase tracking-wider">
        Commands
      </li>
      <li
        v-for="command in filteredCommands"
        :key="command.name"
        @click="executeCommand(command)"
        :class="[
          'px-4 py-2 hover:bg-purple-50 cursor-pointer flex items-center transition-colors duration-150',
          isCommandDisabled(command) ? 'opacity-50 cursor-not-allowed' : ''
        ]"
      >
        <div class="font-medium text-purple-700 mr-3 text-sm">{{ command.name }}</div>
        <div class="text-sm text-gray-600">{{ command.description }}</div>
        <div v-if="isCommandDisabled(command)" class="ml-auto text-xs text-gray-400">
          {{ getDisabledReason(command) }}
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { computed } from '@vue/runtime-core';
import { Command } from '../services/chatCommandService';

interface Props {
  show: boolean;
  commands: Command[];
  searchText: string;
  conversationId?: string | null;
  hasMessages: boolean;
}

interface Emits {
  (e: 'execute-command', command: Command): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const filteredCommands = computed(() => {
  if (!props.searchText.startsWith('/')) {
    return [];
  }
  const search = props.searchText.toLowerCase();
  return props.commands.filter(command =>
    command.name.toLowerCase().includes(search)
  );
});

const isCommandDisabled = (command: Command): boolean => {
  // Disable clear and compress commands if there are no messages
  if ((command.name === '/clear' || command.name === '/compress') && !props.hasMessages) {
    return true;
  }

  return false;
};

const getDisabledReason = (command: Command): string => {
  if ((command.name === '/clear' || command.name === '/compress') && !props.hasMessages) {
    return 'No messages to ' + (command.name === '/clear' ? 'clear' : 'compress');
  }

  return '';
};

const executeCommand = (command: Command) => {
  if (isCommandDisabled(command)) {
    return;
  }
  emit('execute-command', command);
};
</script>
