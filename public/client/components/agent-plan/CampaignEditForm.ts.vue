<template>
  <div class="p-6 bg-white rounded-lg shadow-lg">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Edit Campaign</h3>

    <form @submit.prevent="handleSubmit">
      <!-- Campaign Name -->
      <div class="mb-4">
        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Campaign Name</label>
        <input
          id="name"
          type="text"
          v-model="formData.name"
          class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          required
        />
      </div>

      <!-- Campaign Type -->
      <div class="mb-4">
        <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Campaign Type</label>
        <select
          id="type"
          v-model="formData.campaignType"
          class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          required
        >
          <option value="Email">Email</option>
          <option value="Social Media">Social Media</option>
          <option value="Content">Content</option>
          <option value="Webinar">Webinar</option>
          <option value="Event">Event</option>
          <option value="PPC">PPC</option>
          <option value="SEO">SEO</option>
          <option value="Other">Other</option>
        </select>
      </div>

      <!-- Target Audience -->
      <div class="mb-4">
        <label for="audience" class="block text-sm font-medium text-gray-700 mb-1">Target Audience</label>
        <input
          id="audience"
          type="text"
          v-model="formData.audience"
          class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
        />
      </div>

      <!-- Scheduled Date -->
      <div class="mb-4">
        <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Scheduled Date</label>
        <input
          id="date"
          type="date"
          v-model="formData.scheduledDate"
          class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          required
        />
      </div>

      <!-- Description -->
      <div class="mb-6">
        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
        <textarea
          id="description"
          v-model="formData.description"
          rows="4"
          class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
        ></textarea>
      </div>

      <!-- Form actions -->
      <div class="flex justify-end gap-3">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="isSaving"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isSaving">Saving...</span>
          <span v-else>Save Changes</span>
        </button>
      </div>
    </form>
  </div>
</template>

<script>
export default {
  name: 'CampaignEditForm',
  props: {
    campaign: {
      type: Object,
      required: true
    },
    isSaving: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        name: '',
        campaignType: '',
        audience: '',
        scheduledDate: '',
        description: ''
      }
    };
  },
  created() {
    // Initialize form with campaign data
    this.formData = {
      name: this.campaign.name || '',
      campaignType: this.campaign.campaignType || 'Email',
      audience: this.campaign.audience || '',
      scheduledDate: this.formatDateForInput(this.campaign.scheduledDate) || '',
      description: this.campaign.description || ''
    };
  },
  methods: {
    formatDateForInput(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    },
    handleSubmit() {
      // Format the date properly before submitting
      const formattedData = {
        ...this.formData,
        scheduledDate: new Date(this.formData.scheduledDate).toISOString()
      };

      this.$emit('save', {
        id: this.campaign.id,
        updatedData: formattedData
      });
    }
  }
}
</script>
