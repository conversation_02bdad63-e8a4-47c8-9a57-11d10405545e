<template>
  <div :data-campaign-id="campaign.id"
    class="relative border border-gray-200 rounded-lg p-3 pl-5 pr-5 hover:border-purple-200 transition-all duration-200 overflow-hidden"
    :class="{ 'highlight-new-campaign': isHighlighted }">
    <button v-if="!isArchived" class="w-full text-left" :disabled="inProgress">
      <div class="flex justify-between items-center gap-2 mb-2">
        <!-- Task Content -->
        <div class="flex items-center gap-2">
          <svg v-if="campaign.taskType === 'Email'" class="h-4 w-4 text-purple-600" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4-8 5-8-5V6l8 5 8-5v2z" />
          </svg>
          <!-- SMS Icon -->
          <svg v-else-if="campaign.taskType === 'SMS'" class="h-4 w-4 text-purple-600" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 18H4V4h16v16zM7 9h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2zm-6 4h8v2H9z" />
          </svg>
          <!-- Loyalty Icon -->
          <svg v-else-if="campaign.taskType === 'Loyalty'" class="h-4 w-4 text-purple-600" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
          </svg>
          <div class="group">
            <template v-if="!isEditing('name')">
              <h4 @click.stop="!inProgress && startEditing('name')" class="font-medium text-gray-900"
                :class="{'group-hover:bg-gray-100 cursor-pointer px-2 py-1 -mx-2 rounded': !inProgress, 'px-2 py-1 -mx-2': inProgress}">
                {{ campaign.name }}
              </h4>
            </template>
            <div v-else class="editing-container">
              <input v-model="editFields.name" @blur="handleSave('name')" @keyup.enter="handleSave('name')"
                style="width: 384px !important; min-width: 384px !important;"
                class="font-medium text-gray-900 bg-gray-100 px-2 py-1 rounded border-none focus:outline-none focus:ring-0 w-96 min-w-[24rem]"
                ref="nameInput">
            </div>
          </div>
        </div>

        <!-- Task Actions -->
        <div>
          <div class="flex items-center gap-2">
            <div class="relative group">
              <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 cursor-pointer" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                aria-label="More information" role="tooltip">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12" y2="8"></line>
              </svg>
              <!-- Tooltip Text -->
              <div class="absolute right-0 mt-2 w-64 p-2 bg-white border border-gray-200 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity duration-200 z-10">
                <p class="text-sm text-gray-600">{{ campaign.whyText }}</p>
              </div>
            </div>
            <span v-if="!isEditing('type')" @click.stop="!inProgress && startEditing('type')"
              class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full text-xs font-medium"
              :class="{
                'bg-green-100 text-green-600': campaign.type === 'Promotion',
                'bg-blue-50 text-blue-700': campaign.type === 'Education',
                'bg-orange-50 text-orange-700': campaign.type === 'Awareness',
                'cursor-pointer': !inProgress
              }">
              {{ campaign.type }}
            </span>
            <select v-else v-model="editFields.type" @blur="handleSave('type')" @keyup.enter="handleSave('type')"
              class="text-xs font-medium rounded-full focus:outline-none focus:ring-0"
              :class="{
                'bg-green-100 text-green-600': editFields.type === 'Promotion',
                'bg-blue-50 text-blue-700': editFields.type === 'Education',
                'bg-orange-50 text-orange-700': editFields.type === 'Awareness'
              }">
              <option value="Promotion">Promotion</option>
              <option value="Education">Education</option>
              <option value="Awareness">Awareness</option>
            </select>

            <button class="inline-flex items-center justify-center h-8 w-8 text-gray-500 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
              @click.stop="$emit('delete', campaign.id)">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Rest of task content -->
      <div class="group">
        <template v-if="!isEditing('description')">
          <p @click.stop="!inProgress && startEditing('description')" class="text-gray-600 text-sm mb-3"
            :class="{'group-hover:bg-gray-100 cursor-pointer px-2 py-1 -mx-2 rounded': !inProgress, 'px-2 py-1 -mx-2': inProgress}">
            {{ campaign.description }}
          </p>
        </template>
        <textarea v-else v-model="editFields.description" @blur="handleSave('description')" @keyup.enter="handleSave('description')"
          class="w-full text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded border-none focus:outline-none focus:ring-0"
          rows="3" ref="descriptionInput"></textarea>
      </div>

      <div class="flex items-center gap-2 text-gray-500 text-sm mb-2" v-if="campaign.type == 'Promotion'">
        <div class="bg-green-100 rounded-full p-2">
          <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px"
            fill="#16a34a">
            <path
              d="M856-390 570-104q-12 12-27 18t-30 6q-15 0-30-6t-27-18L103-457q-11-11-17-25.5T80-513v-287q0-33 23.5-56.5T160-880h287q16 0 31 6.5t26 17.5l352 353q12 12 17.5 27t5.5 30q0 15-5.5 29.5T856-390ZM513-160l286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640Zm220 160Z" />
          </svg>
        </div>
        <div class="group" v-if="campaign.type == 'Promotion'">
          <template v-if="!isEditing('promotionDescription')">
            <span @click.stop="!inProgress && startEditing('promotionDescription')" class="text-gray-600 text-sm"
              :class="{'group-hover:bg-gray-100 cursor-pointer px-2 py-1 -mx-2 rounded': !inProgress, 'px-2 py-1 -mx-2': inProgress}">
              {{ campaign.promotionDescription }}
            </span>
          </template>
          <input v-else v-model="editFields.promotionDescription" @blur="handleSave('promotionDescription')"
            @keyup.enter="handleSave('promotionDescription')"
            class="w-auto font-medium text-gray-900 bg-gray-100 px-2 py-1 -mx-2 rounded border-none focus:outline-none focus:ring-0"
            ref="promotionDescriptionInput">
        </div>
      </div>

      <div class="flex items-center gap-4 text-sm">
        <div class="flex items-center gap-2 text-gray-500">
          <svg class="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z" />
          </svg>
          <div class="group">
            <template v-if="!isEditing('targetSegment')">
              <span @click.stop="!inProgress && startEditing('targetSegment')"
                :class="{
                  'group-hover:bg-gray-100 cursor-pointer px-2 py-1 rounded': !inProgress,
                  'px-2 py-1 rounded': inProgress
                }"
                class="font-medium">
                {{ campaign.targetSegment }}
              </span>
            </template>
            <input v-else v-model="editFields.targetSegment" @blur="handleSave('targetSegment')"
              @keyup.enter="handleSave('targetSegment')"
              class="font-medium text-gray-900 bg-gray-100 px-2 py-1 rounded border-none focus:outline-none focus:ring-0"
              ref="targetSegmentInput">
          </div>
        </div>
        <div class="flex items-center gap-2 bg-gray-200 rounded-full px-2 text-gray-700">
          <svg class="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M19 4h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V10h14v10zM9 14H7v-2h2v2zm4 0h-2v-2h2v2zm4 0h-2v-2h2v2z" />
          </svg>
          <div class="group">
            <span v-if="!isEditing('scheduledDate')" @click="!inProgress && startEditing('scheduledDate')"
              :class="{
                'group-hover:bg-gray-100 cursor-pointer px-2 py-1 rounded': !inProgress,
                'px-2 py-1 rounded': inProgress
              }"
              class="inline-block">
              {{ formatDate(campaign.scheduledDate) }}
            </span>
            <input v-else type="date" v-model="editFields.scheduledDate" @blur="handleSave('scheduledDate')"
              @keyup.enter="handleSave('scheduledDate')"
              class="bg-gray-100 px-2 py-1 rounded border-none focus:outline-none focus:ring-0"
              ref="scheduledDateInput">
          </div>
        </div>
      </div>

      <!-- Add campaign status and View Campaign button -->
      <div class="flex items-center justify-between mt-4 border-t border-gray-100 bg-[#eef2ff] -mx-5 -mb-6 rounded-b-lg" v-if="inProgress">
        <div class="flex items-center gap-2 pl-5 my-3">
          <span v-if="campaign.taskStatus === 'In Progress'" class="flex items-center px-2.5 py-1 rounded-full bg-indigo-100 text-indigo-700 text-sm font-medium">
            <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            In Progress
          </span>
          <span v-else-if="campaign.taskStatus === 'Complete'" class="flex items-center px-2.5 py-1 rounded-full bg-green-100 text-green-700 text-sm font-medium">
            <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            Complete
          </span>
          <span v-else-if="campaign.taskStatus === 'Processing'" class="flex items-center px-2.5 py-1 rounded-full bg-yellow-100 text-yellow-700 text-sm font-medium">
            <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Pending
          </span>
          <span v-else class="flex items-center px-2.5 py-1 rounded-full bg-gray-100 text-gray-700 text-sm font-medium">
            <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            Not Started
          </span>
        </div>
        <button v-if="campaign.taskStatus !== 'Processing'" @click="$emit('navigate', campaign.id)"
          class="inline-flex items-center px-3 my-3 text-sm font-medium text-indigo-600 hover:bg-indigo-50 transition-colors pr-5">
          View Campaign
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </button>
    <div v-if="isArchived && !isFullyRemoved" class="skipped-message">
      This Campaign idea has been skipped!
    </div>
  </div>
</template>

<script>
export default {
  name: 'CampaignCard',

  props: {
    campaign: {
      type: Object,
      required: true
    },
    inProgress: {
      type: Boolean,
      default: false
    },
    isArchived: {
      type: Boolean,
      default: false
    },
    isFullyRemoved: {
      type: Boolean,
      default: false
    },
    isHighlighted: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      editingField: null,
      editFields: {
        name: '',
        description: '',
        type: '',
        promotionDescription: '',
        targetSegment: '',
        scheduledDate: ''
      }
    }
  },

  methods: {
    formatDate(dateString) {
      if (!dateString) return '';

      // The direct string parsing approach prevents timezone issues
      if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
        // Extract year, month, day directly from string
        const [year, month, day] = dateString.split('-').map(num => parseInt(num, 10));

        // Map month number to month name (avoiding Date object timezone issues)
        const monthNames = [
          "January", "February", "March", "April", "May", "June",
          "July", "August", "September", "October", "November", "December"
        ];

        // Format directly without using Date object
        return `${monthNames[month-1]} ${day}, ${year}`;
      }

      // Fallback to UTC-based Date object for other date formats
      try {
        // Create a UTC date to avoid timezone shifts
        const date = new Date(dateString);
        // Force UTC timezone display
        return date.toLocaleDateString('en-US', {
          month: 'long',
          day: 'numeric',
          year: 'numeric',
          timeZone: 'UTC' // Force UTC timezone
        });
      } catch (error) {
        console.error('Error formatting date in CampaignCard:', error, dateString);
        return dateString; // Return original string as fallback
      }
    },

    isEditing(field) {
      return this.editingField === field;
    },

    startEditing(field) {
      if (this.inProgress) return; // Don't allow editing if in progress

      this.editingField = field;
      this.editFields[field] = this.campaign[field] || '';

      this.$nextTick(() => {
        if (this.$refs[`${field}Input`]) {
          this.$refs[`${field}Input`].focus();
        }
      });
    },

    handleSave(field) {
      if (!this.editFields[field]) {
        this.editingField = null;
        return;
      }

      this.$emit('edit-field', {
        field,
        campaignId: this.campaign.id,
        value: this.editFields[field]
      });

      this.editingField = null;
    }
  }
}
</script>

<style scoped>
.skipped-message {
  text-align: center;
  padding: 1rem;
  color: #6B7280;
  font-size: 0.875rem;
  animation: fadeOut 1s ease-out 1s forwards;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 1; /* Keep the message fully visible for half the animation */
  }
  100% {
    opacity: 0;
    height: 0;
    margin: 0;
    padding: 0;
  }
}

select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-color: transparent;
  border: none;
  padding: 0.25rem 0.5rem;
  cursor: pointer;
}

select:focus {
  outline: none;
  box-shadow: none;
}

.group .tooltip {
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.group:hover .tooltip {
  visibility: visible;
  opacity: 1;
}
</style>
