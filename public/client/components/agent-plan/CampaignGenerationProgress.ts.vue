<template>
  <div v-if="campaignsGenerating" class="px-8 py-6 bg-indigo-50 border-t border-indigo-100">
    <div class="flex flex-col items-center mb-2">
      <div class="text-lg font-medium text-indigo-700 mb-3">Your AI Team is working...</div>

      <!-- AI Team Roles in vertical layout -->
      <div class="w-full max-w-md mb-6">
        <!-- Only show the active role with a transition effect -->
        <transition name="role-fade" mode="out-in">
          <!-- Analyst Role -->
          <div v-if="activeRole === 'analyst'" key="analyst" class="flex items-center mb-4">
            <div class="w-14 h-14 rounded-full flex items-center justify-center transition-all duration-500 mr-4 bg-indigo-600 text-white transform scale-110 shadow-lg animate-pulse">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div class="flex-1 overflow-hidden">
              <div class="text-sm font-medium mb-1 text-indigo-800">
                Analyst
              </div>
              <div class="overflow-hidden h-5">
                <div class="text-sm text-indigo-700 transform transition-all duration-500 translate-x-0 opacity-100">
                  Analyzing audience segments and purchase patterns...
                </div>
              </div>
            </div>
          </div>

          <!-- Strategist Role -->
          <div v-else-if="activeRole === 'strategist'" key="strategist" class="flex items-center mb-4">
            <div class="w-14 h-14 rounded-full flex items-center justify-center transition-all duration-500 mr-4 bg-indigo-600 text-white transform scale-110 shadow-lg animate-pulse">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <div class="flex-1 overflow-hidden">
              <div class="text-sm font-medium mb-1 text-indigo-800">
                Strategist
              </div>
              <div class="overflow-hidden h-5">
                <div class="text-sm text-indigo-700 transform transition-all duration-500 translate-x-0 opacity-100">
                  Developing targeted campaign approaches and timing...
                </div>
              </div>
            </div>
          </div>

          <!-- Writer Role -->
          <div v-else-if="activeRole === 'writer'" key="writer" class="flex items-center mb-4">
            <div class="w-14 h-14 rounded-full flex items-center justify-center transition-all duration-500 mr-4 bg-indigo-600 text-white transform scale-110 shadow-lg animate-pulse">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </div>
            <div class="flex-1 overflow-hidden">
              <div class="text-sm font-medium mb-1 text-indigo-800">
                Writer
              </div>
              <div class="overflow-hidden h-5">
                <div class="text-sm text-indigo-700 transform transition-all duration-500 translate-x-0 opacity-100">
                  Creating engaging copy and persuasive messaging...
                </div>
              </div>
            </div>
          </div>

          <!-- Designer Role -->
          <div v-else-if="activeRole === 'designer'" key="designer" class="flex items-center mb-4">
            <div class="w-14 h-14 rounded-full flex items-center justify-center transition-all duration-500 mr-4 bg-indigo-600 text-white transform scale-110 shadow-lg animate-pulse">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            </div>
            <div class="flex-1 overflow-hidden">
              <div class="text-sm font-medium mb-1 text-indigo-800">
                Designer
              </div>
              <div class="overflow-hidden h-5">
                <div class="text-sm text-indigo-700 transform transition-all duration-500 translate-x-0 opacity-100">
                  Designing visual elements and finalizing creative assets...
                </div>
              </div>
            </div>
          </div>
        </transition>
      </div>
    </div>
    <!-- Centered progress bar container -->
    <div class="flex flex-col items-center w-full">
      <div class="w-full max-w-md bg-white rounded-full h-4 overflow-hidden">
        <div class="bg-indigo-600 h-full rounded-full transition-all duration-200" :style="{ width: `${internalProgress}%` }"></div>
      </div>
      <p class="text-sm text-indigo-600 mt-2 text-center">
        {{ statusText }}
      </p>
    </div>
  </div>
</template>

<script>
import * as Utils from '../../../client-old/utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
  name: 'CampaignGenerationProgress',

  props: {
    generationProgress: {
      type: Number,
      default: 0
    },
    campaignsGenerating: {
      type: Boolean,
      required: true
    },
    currentPlan: {
      type: Object,
      required: true
    },
    planId: {
      type: [String, Number],
      required: true
    }
  },

  emits: ['progress-update', 'generation-complete'],

  data() {
    return {
      internalProgress: 0,
      progressInterval: null,
      roleTransitionTimeout: null,
      simulationIntervals: [],
      pollingActive: false
    };
  },

  computed: {
    activeRole() {
      // Determine the active role based on the generation progress
      // New order: Analyst, Strategist, Writer, Designer
      if (this.internalProgress < 25) {
        return 'analyst'; // 0-25%: Analyst is active
      } else if (this.internalProgress < 50) {
        return 'strategist'; // 25-50%: Strategist is active
      } else if (this.internalProgress < 75) {
        return 'writer'; // 50-75%: Writer is active
      } else {
        return 'designer'; // 75-100%: Designer is active
      }
    },

    statusText() {
      if (!this.currentPlan.inProgress) {
        return 'Your AI team is preparing to generate campaigns...';
      }

      const campaigns = this.currentPlan.plannerPlanVersions[0]?.plannerCampaigns || [];
      const totalTasks = campaigns.length;

      if (totalTasks === 0) {
        return 'Setting up your campaign tasks...';
      }

      const completedTasks = campaigns.filter(campaign =>
        campaign.task && campaign.task.status !== 'Processing'
      ).length;

      if (completedTasks === 0) {
        return 'Starting to design your campaigns...';
      } else if (completedTasks === totalTasks) {
        return 'All campaigns generated successfully!';
      } else {
        return `Generating campaigns: ${completedTasks} of ${totalTasks} complete`;
      }
    }
  },

  watch: {
    generationProgress(newValue) {
      // If parent component is controlling the progress, update internal value
      if (newValue !== this.internalProgress) {
        this.internalProgress = newValue;
      }
    },

    campaignsGenerating(newValue) {
      if (newValue) {
        // Start progress simulation when generation begins
        this.startProgressSimulation();
        // Start polling for plan status
        this.pollPlanStatus(this.planId);
      } else {
        // Clean up when generation stops
        this.cleanupIntervals();
      }
    }
  },

  methods: {
    startProgressSimulation() {
      // Simulate initial progress while waiting for the first poll to complete
      // We'll animate up to 10% initially, then the task-based polling will take over
      const progressInterval = setInterval(() => {
        if (this.internalProgress < 10) {
          this.internalProgress += 0.5;
          this.$emit('progress-update', this.internalProgress);
        } else {
          clearInterval(progressInterval);
        }
      }, 200);

      // Store the interval ID to clear it later
      this.progressInterval = progressInterval;

      // Set up role transition pauses
      // This will be cleared if the real tasks complete before our simulation
      this.roleTransitionTimeout = setTimeout(() => {
        // For demonstration purposes, we'll simulate transitions between roles
        // if the actual tasks take longer to start reporting progress
        const simulateRoleProgress = () => {
          // Create transition points with slight pauses
          const simulateRoleTransition = (target, nextDelay) => {
            const interval = setInterval(() => {
              if (this.internalProgress < target) {
                this.internalProgress += 0.3;
                this.$emit('progress-update', this.internalProgress);
              } else {
                clearInterval(interval);
                if (nextDelay) {
                  setTimeout(nextDelay, 800); // Pause briefly at role transitions
                }
              }
            }, 200);
            return interval;
          };

          // Analyst to Strategist transition
          const strategistTransition = () => {
            simulateRoleTransition(50, writerTransition);
          };

          // Strategist to Writer transition
          const writerTransition = () => {
            simulateRoleTransition(75, designerTransition);
          };

          // Writer to Designer transition
          const designerTransition = () => {
            simulateRoleTransition(90, null);
          };

          // Start the analyst phase (up to 25%)
          const analystInterval = simulateRoleTransition(25, strategistTransition);

          // Store intervals for cleanup
          this.simulationIntervals = [analystInterval];
        };

        // Only start the role simulation if we don't have real progress yet
        if (this.internalProgress <= 10) {
          simulateRoleProgress();
        }
      }, 2000); // Give real tasks 2 seconds to start reporting before simulation
    },

    async pollPlanStatus(planId) {
      // Set flag to indicate polling is active
      this.pollingActive = true;

      // Set up a polling interval to check the status of the plan
      const pollingInterval = 5000; // Poll every 5 seconds

      const checkStatus = async () => {
        try {
          const response = await fetch(
            `${URL_DOMAIN}/organizations/organization-planner-plans/${planId}`,
            {
              method: 'GET',
              headers: {
                Authorization: `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json',
              },
            }
          );

          const result = await response.json();

          if (response.ok) {
            // Emit event to update the parent component with the latest plan data
            this.$emit('plan-updated', result[0]);

            // Check if campaigns have been generated and the plan is now in progress
            const currentPlan = result[0];
            if (currentPlan.inProgress) {
              // Check the status of individual tasks
              const campaigns = currentPlan.plannerPlanVersions[0]?.plannerCampaigns || [];
              const totalTasks = campaigns.length;

              if (totalTasks === 0) {
                // No tasks yet, keep polling
                return false;
              }

              // Count tasks that are not in "Processing" status
              const completedTasks = campaigns.filter(campaign =>
                campaign.task && campaign.task.status !== 'Processing'
              ).length;

              // If all tasks are no longer processing, we're done
              if (completedTasks === totalTasks) {
                // Clean up any simulation intervals and timeouts
                this.cleanupIntervals();

                // Complete the progress with a smooth transition
                const finalInterval = setInterval(() => {
                  if (this.internalProgress < 100) {
                    // Speed up at the end to complete more quickly
                    this.internalProgress += (100 - this.internalProgress) / 10;
                    this.$emit('progress-update', this.internalProgress);

                    if (this.internalProgress > 99.5) {
                      this.internalProgress = 100;
                      this.$emit('progress-update', 100);
                      clearInterval(finalInterval);

                      // Wait a moment for the user to see 100%, then turn off generating state
                      setTimeout(() => {
                        this.$emit('generation-complete');
                      }, 1000);
                    }
                  } else {
                    clearInterval(finalInterval);
                  }
                }, 100);

                // Stop polling
                return true;
              } else {
                // Update progress based on completed tasks
                // Start at 10% and go up to 90% based on task completion
                // The last 10% will be shown when all tasks are complete

                // Calculate target progress more smoothly
                const targetProgress = 10 + (completedTasks / totalTasks * 80);

                // Smoothly transition to the target progress
                if (this.internalProgress < targetProgress) {
                  // Clear any existing simulation
                  if (this.simulationIntervals) {
                    this.simulationIntervals.forEach(interval => {
                      if (interval) clearInterval(interval);
                    });
                  }

                  // Create a smoother transition to the target
                  const progressInterval = setInterval(() => {
                    if (this.internalProgress < targetProgress - 0.5) {
                      this.internalProgress += (targetProgress - this.internalProgress) / 20;
                      this.$emit('progress-update', this.internalProgress);
                    } else {
                      clearInterval(progressInterval);
                    }
                  }, 100);

                  this.simulationIntervals = [progressInterval];
                }

                // Continue polling until all tasks are complete
                return false;
              }
            }

            // Continue polling
            return false;
          }

          // Continue polling
          return false;
        } catch (error) {
          console.error("Error polling plan status:", error);
          return true; // Stop polling on error
        }
      };

      // Do the initial check
      let isDone = await checkStatus();

      // If not done, set up polling
      if (!isDone && this.pollingActive) {
        const poll = async () => {
          if (!this.pollingActive) return; // Stop if polling was deactivated

          isDone = await checkStatus();
          if (!isDone && this.pollingActive) {
            setTimeout(poll, pollingInterval);
          }
        };

        setTimeout(poll, pollingInterval);
      }
    },

    cleanupIntervals() {
      // Clean up any active intervals and timeouts
      if (this.progressInterval) {
        clearInterval(this.progressInterval);
        this.progressInterval = null;
      }

      if (this.roleTransitionTimeout) {
        clearTimeout(this.roleTransitionTimeout);
        this.roleTransitionTimeout = null;
      }

      // Clear all simulation intervals
      if (this.simulationIntervals && this.simulationIntervals.length) {
        this.simulationIntervals.forEach(interval => {
          if (interval) clearInterval(interval);
        });
        this.simulationIntervals = [];
      }

      // Stop polling
      this.pollingActive = false;
    }
  },

  beforeUnmount() {
    // Clean up when component is unmounted
    this.cleanupIntervals();
  }
}
</script>

<style scoped>
/* Role transition animation */
.role-fade-enter-active,
.role-fade-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.role-fade-enter-from,
.role-fade-leave-to {
  opacity: 0;
  transform: translateY(15px);
}

.role-fade-enter-to,
.role-fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}

/* Text slide-in animation for AI team roles */
@keyframes slideIn {
  0% {
    transform: translateX(-30px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Typing animation for AI team texts */
@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

/* Add animation for text that appears when role is active */
.text-sm.text-indigo-700.transform.transition-all.duration-500.translate-x-0.opacity-100 {
  animation: slideIn 0.5s ease-out;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  border-right: 2px solid transparent;
  animation: typing 1.5s steps(30, end);
}
</style>
