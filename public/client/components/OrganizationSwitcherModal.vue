<template>
  <!-- Modal backdrop -->
  <transition
    enter-active-class="transition ease-out duration-200"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition ease-out duration-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div v-show="show" class="fixed inset-0 bg-slate-900 bg-opacity-30 z-50 transition-opacity" aria-hidden="true"></div>
  </transition>
  <!-- Modal dialog -->
  <transition
    enter-active-class="transition ease-in-out duration-200"
    enter-from-class="opacity-0 translate-y-4"
    enter-to-class="opacity-100 translate-y-0"
    leave-active-class="transition ease-in-out duration-200"
    leave-from-class="opacity-100 translate-y-0"
    leave-to-class="opacity-0 translate-y-4"
  >
    <div v-show="show" id="org-switcher-modal" class="fixed inset-0 z-50 overflow-hidden flex items-center my-4 justify-center transform px-1 sm:px-6" role="dialog" aria-modal="true">
      <div ref="modalContent" class="bg-white rounded-lg shadow-xl overflow-auto max-w-lg w-full max-h-full">
        <!-- Modal header -->
        <div class="px-6 py-4">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold text-slate-800">Switch Brand</h2>
            <button class="text-slate-400 hover:text-slate-500" @click="closeModal">
              <div class="sr-only">Close</div>
              <svg class="w-4 h-4 fill-current" viewBox="0 0 16 16">
                  <path d="M7.95 6.536l4.242-4.243a1 1 0 111.415 1.414L9.364 7.95l4.243 4.242a1 1 0 11-1.415 1.415L7.95 9.364l-4.243 4.243a1 1 0 01-1.414-1.415L6.536 7.95 2.293 3.707a1 1 0 011.414-1.414L7.95 6.536z" />
              </svg>
            </button>
          </div>
        </div>
        <!-- Modal body -->
        <div class="px-6 py-4">
          <p class="text-sm text-slate-600 mb-4">Current Brand: {{ currentOrg?.name || 'N/A' }}</p>
          <div class="space-y-4">
            <div v-if="otherOrgs.length > 0">
              <label class="block text-sm font-medium mb-1" for="org-select">Select Brand</label>
              <select
                id="org-select"
                v-model="selectedOrgId"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option disabled value="">Please select a brand</option>
                <option v-for="org in otherOrgs" :key="org.id" :value="org.id">
                  {{ org.name }}
                </option>
              </select>
            </div>
            <div v-else class="text-sm text-slate-500 italic mt-4">
              You don't have access to any other brands to switch to.
            </div>
          </div>
        </div>
        <!-- Modal footer -->
        <div class="px-6 py-4">
          <div class="flex flex-wrap justify-end space-x-2">
            <button class="btn-sm bg-slate-100 hover:bg-slate-200 text-slate-700" @click="closeModal">Cancel</button>
            <button
              class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white"
              @click="handleSwitchOrg"
              :disabled="!selectedOrgId || isLoading"
            >
              <span v-if="isLoading">Switching...</span>
              <span v-else>Switch Brand</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script lang="ts">
import { defineComponent, computed, ref, onMounted, onUnmounted } from 'vue'
// Removed useRouter as it's not used for navigation anymore
// import { useRouter } from 'vue-router'
// Removed ModalBasic import
// import ModalBasic from './ModalBasic.ts.vue'
import type { Organization } from '../../../src/types/organization';
import * as Utils from '../../client-old/utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default defineComponent({
  name: 'OrganizationSwitcherModal',
  // Removed ModalBasic from components
  components: { },
  props: {
    show: {
      type: Boolean,
      required: true
    },
    userOrgs: {
      type: Array as () => Array<Organization>, // Use imported type
      required: true
    }
  },
  emits: ['close'],

  setup(props, { emit }) {
    // Removed router initialization
    // const router = useRouter()
    const selectedOrgId = ref<number | string>('');
    const isLoading = ref(false);
    const modalContent = ref<HTMLDivElement | null>(null); // Ref for modal content

    const currentOrg = computed(() => {
      if (typeof window !== 'undefined') {
        const currentOrgId = localStorage.getItem('userOrgId');
        return props.userOrgs.find(org => org.id.toString() === currentOrgId);
      }
      return null;
    });

    const otherOrgs = computed(() => {
      if (typeof window !== 'undefined') {
        const currentOrgId = localStorage.getItem('userOrgId');
        return props.userOrgs.filter(org => org.id.toString() !== currentOrgId);
      }
      return [];
    });

    const closeModal = () => {
      selectedOrgId.value = '';
      isLoading.value = false;
      emit('close');
    };

    const handleSwitchOrg = async () => {
      console.log('Switch button clicked, orgId:', selectedOrgId.value);

      if (!selectedOrgId.value || isLoading.value) {
        console.log('Switch aborted (no selection or already loading)');
        return;
      }

      const targetOrg = props.userOrgs.find(org => org.id.toString() === selectedOrgId.value.toString());
      if (!targetOrg) {
        console.error('Selected organization not found.');
        return;
      }

      isLoading.value = true;
      try {
        const loginRequest = await fetch(`${URL_DOMAIN}/users/login/${targetOrg.id}`, {
          method: 'POST',
          credentials: 'omit',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          }
        });

        if (!loginRequest.ok) {
          console.error(`Failed to switch organization: ${loginRequest.status} ${loginRequest.statusText}`);
          // Consider showing user feedback here
          isLoading.value = false;
          return;
        }

        const loginResult = await loginRequest.json();
        if (loginResult.token) {
          localStorage.setItem('token', loginResult.token);
          localStorage.setItem('userOrgId', targetOrg.id.toString());

          // Determine redirect path based on organization type
          try {
            const orgResponse = await fetch(`${URL_DOMAIN}/organizations/${targetOrg.id}`, {
              method: 'GET',
              headers: {
                Authorization: `Bearer ${loginResult.token}`,
                'Content-Type': 'application/json',
              },
            });

            if (orgResponse.ok) {
              const orgData = await orgResponse.json();
              // Redirect agency organizations to /agency, others to /ai-strategist/planning
              const redirectPath = orgData.orgType === 'agency' ? '/agency' : '/ai-strategist/planning';
              window.location.assign(redirectPath); // Force reload
            } else {
              // Fallback to default if org fetch fails
              window.location.assign('/ai-strategist/planning');
            }
          } catch (error) {
            console.error('Error fetching organization details for redirect:', error);
            // Fallback to default if org fetch fails
            window.location.assign('/ai-strategist/planning');
          }
          // No need to set isLoading false here as page reloads/navigates away
        } else {
          console.error('No token received after switching organization.');
          isLoading.value = false;
        }
      } catch (error) {
          console.error('Error during organization switch:', error);
          isLoading.value = false; // Ensure loading state is reset on error
      }
    };

    // Close modal on click outside
    const clickHandler = ({ target }: MouseEvent) => {
      if (!props.show || modalContent.value?.contains(target as Node)) return;
      closeModal();
    };

    // Close modal on ESC key
    const keyHandler = ({ keyCode }: KeyboardEvent) => {
      if (!props.show || keyCode !== 27) return;
      closeModal();
    };

    onMounted(() => {
      document.addEventListener('click', clickHandler);
      document.addEventListener('keydown', keyHandler);
    });

    onUnmounted(() => {
      document.removeEventListener('click', clickHandler);
      document.removeEventListener('keydown', keyHandler);
    });

    return {
      modalContent, // Return ref
      currentOrg,
      otherOrgs,
      selectedOrgId,
      isLoading,
      handleSwitchOrg,
      closeModal,
    };
  }
})
</script>
