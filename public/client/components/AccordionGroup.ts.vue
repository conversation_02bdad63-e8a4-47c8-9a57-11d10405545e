<template>
	<Accordion
		v-for="accordion in accordions"
		:title="accordion.title"
		:subtitle="accordion.subtitle"
		:open="isOpen(accordion.key)"
		:open-height="accordion.height"
		:disabled="accordion.disabled"
		@close="_openAccordionKey = null"
		@opened="onAccordionOpened"
		@open="openKey(accordion.key)" class="mb-4">
			<slot :name="accordion.key"></slot>
	</Accordion>
</template>

<script>
	import * as Utils from '../../client-old/utils/Utils';
	import Accordion from './Accordion.ts.vue';

	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		props: ['accordions', 'initialOpenAccordionKey', 'previewType'],
		data() {
			return {
				_openAccordionKey: this.previewType || this.initialOpenAccordionKey,
				_previewType: this.previewType
			}
		},
		components: {
			Accordion
		},
		async mounted() {
		},
		watch: {
			previewType(newVal, oldVal) {
				if (newVal !== this._openAccordionKey) {
					this._openAccordionKey = newVal;
				}
			}
		},
		computed: {
			openAccordionKey() {
				return this._openAccordionKey;
			}
		},
		methods: {
			isOpen(accordionKey) {
				return this.openAccordionKey === accordionKey;
			},
			onAccordionOpened(accordionElement) {
				// Scroll the accordion into view when it is opened
				this.$nextTick(() => {
					accordionElement.scrollIntoView({
						behavior: 'smooth',
						block: 'start',
					});
				});
			},
			openKey(accordionKey) {
				this._openAccordionKey = accordionKey;
				this.$emit('open', accordionKey);
			}
		},
	}
</script>
