<template>
	<div v-if="isProgramInactive" class="w-full flex bg-gradient-to-r from-ralsecondary-start to-ralsecondary-end p-4 items-center justify-center">
		<p class="text-white">Your loyalty program is not live.</p>
		<DarkSecondaryButton cta="Go Live" @click="goLive"></DarkSecondaryButton>
	</div>
</template>

<script>
	import DarkSecondaryButton from '../components/DarkSecondaryButton.ts.vue';
	import { customerIOTrackEvent } from '../services/customerio.js';
	import * as Utils from '../../client-old/utils/Utils';

	const URL_DOMAIN = Utils.URL_DOMAIN;
	export default {
		name: 'ProgramLive',
		components: {
			DarkSecondaryButton
		},
		data() {
			return {
				isProgramInactive: false
			}
		},
		methods: {
			goLive() {
				customerIOTrackEvent('Go Live CTA');
                                this.$router.push('/settings/loyalty');
			}
		},
		async mounted() {
			let jsonresponse = {};
			try {
				const response = await fetch(`${URL_DOMAIN}/loyalty-programs`, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					}
				});
				jsonresponse = await response.json();
				console.log("Program Active: " + JSON.stringify(jsonresponse));
			} catch (err) {
				console.log("Error Program Active: " + JSON.stringify(err));
			}

			if (jsonresponse.length > 0) {
				this.isProgramInactive = !jsonresponse[0].active;
			}
		},
	}

</script>

<style>

</style>
