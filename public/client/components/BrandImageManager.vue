<!-- Brand Images Manager Component -->
<template>
  <div class="bg-white rounded-lg border shadow-sm">
    <div class="p-6 border-b">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg font-semibold">Brand Images</h2>
          <p class="text-sm text-gray-500">Upload and manage images for your brand's email communications</p>
        </div>
        <div class="flex items-center gap-2">
          <button
            @click="openTagSettings"
            class="flex items-center gap-1 px-3 py-2 rounded-lg border text-gray-700 hover:bg-gray-50 transition"
            title="Manage AI image tags"
          >
            <span class="text-sm">AI Tags</span>
            <span>⚙️</span>
          </button>
          <button
            @click="openModal"
            class="flex items-center gap-2 px-4 py-2 rounded-lg bg-purple-600 text-white hover:bg-purple-700 transition"
          >
            <span>+ Add Image</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Bulk Selection Toolbar -->
    <div v-if="selectedImageCount > 0" class="bg-purple-50 border-b border-purple-200 px-6 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <span class="text-sm font-medium text-purple-700">
            {{ selectedImageCount }} image{{ selectedImageCount === 1 ? '' : 's' }} selected
          </span>
          <button
            @click="clearSelection"
            class="text-sm text-purple-600 hover:text-purple-800 underline"
          >
            Clear selection
          </button>
        </div>
        <div class="flex items-center gap-2">
          <select
            v-model="bulkTagUpdate"
            class="text-sm border rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-purple-500"
            @change="handleBulkTagChange"
          >
            <option value="">Change tag...</option>
            <option value="">Remove tag (Exclude from AI)</option>
            <option value="Logo">Logo</option>
            <option value="Hero Image">Hero Image</option>
            <option value="Banner">Banner</option>
            <option value="Product">Product</option>
            <option value="Background">Background</option>
            <option value="Icon">Icon</option>
            <option value="Reviews">Reviews</option>
            <option v-for="category in customCategories" :key="category" :value="category">
              {{ category }}
            </option>
          </select>
          <button
            @click="confirmBulkDelete"
            class="px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition"
            :disabled="bulkOperationInProgress"
          >
            Delete Selected
          </button>
        </div>
      </div>
    </div>

    <!-- Gallery -->
    <div class="p-6">
      <div v-if="isLoading" class="flex justify-center items-center py-10">
        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-purple-600"></div>
      </div>

      <div v-else-if="images.length === 0" class="bg-gray-50 rounded-lg py-16 text-center">
        <div class="mx-auto w-16 h-16 mb-4 rounded-full bg-gray-100 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-gray-400">
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
          </svg>
        </div>
        <h3 class="text-gray-500 text-lg font-medium mb-1">No brand images uploaded yet</h3>
        <p class="text-gray-400 mb-4">Upload images to enhance your email communications</p>
        <button
          @click="openModal"
          class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition"
        >
          + Add your first image
        </button>
      </div>

      <div v-else>
        <!-- Top Section: Specified image types -->
        <div class="mb-10">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center gap-3">
              <h3 class="text-sm font-medium text-gray-700">
                AI-Ready Images ({{ typedImages.length }})
              </h3>
              <label class="flex items-center gap-2 text-sm text-gray-600 cursor-pointer">
                <input
                  type="checkbox"
                  :checked="allImagesSelected"
                  :indeterminate="someImagesSelected"
                  @change="toggleSelectAll"
                  class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
                Select all
              </label>
            </div>
            <div class="flex items-center gap-2">
              <select
                v-model="filter"
                class="text-sm border rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-purple-500 min-w-[140px]"
              >
                <option value="">All categories</option>
                <!-- Default categories -->
                <option value="Logo">Logos</option>
                <option value="Hero Image">Hero Images</option>
                <option value="Banner">Banners</option>
                <option value="Product">Products</option>
                <option value="Background">Backgrounds</option>
                <option value="Icon">Icons</option>
                <option value="Reviews">Reviews</option>
                <!-- Custom categories -->
                <option v-for="category in customCategories" :key="category" :value="category">
                  {{ category }}
                </option>
              </select>
            </div>
          </div>

          <div v-if="filteredTypedImages.length === 0" class="bg-gray-50 rounded-lg p-8 text-center">
            <p class="text-gray-500">No images found with the selected type.</p>
          </div>

          <div v-else class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
            <div
              v-for="(image, index) in filteredTypedImages"
              :key="image.id"
              class="group bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition"
            >
              <div class="relative h-44 bg-gray-50 flex items-center justify-center p-3">
                <!-- Selection checkbox -->
                <div class="absolute top-2 left-2 z-10">
                  <input
                    type="checkbox"
                    :checked="selectedImages.includes(image.id)"
                    @change="toggleImageSelection(image.id)"
                    @click.stop
                    class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                </div>

                <img
                  :src="image.url"
                  :alt="image.friendlyname"
                  class="object-contain max-w-full max-h-full"
                />

                <!-- Hover overlay -->
                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <div class="flex gap-2">
                    <button
                      @click.stop="editImage(image)"
                      class="bg-white rounded-full p-2 shadow-md hover:bg-blue-50 transition"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-blue-500">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                      </svg>
                    </button>
                    <button
                      @click.stop="confirmDelete(image.id)"
                      class="bg-white rounded-full p-2 shadow-md hover:bg-red-50 transition"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-red-500">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <div class="p-3 border-t">
                <p class="font-medium text-sm truncate">{{ image.friendlyname }}</p>
                <p class="text-xs text-gray-500">{{ getImageType(image) }}</p>
                <p class="text-xs text-gray-400">{{ image.width || '?' }} × {{ image.height || '?' }}px</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Bottom Section: Unspecified image types -->
        <div v-if="untypedImages.length > 0" class="mt-10 border-t pt-8">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-sm font-medium text-gray-700 flex items-center">
              Excluded From AI Email Generation ({{ untypedImages.length }})
            </h3>
          </div>

          <p class="text-sm text-gray-500 mb-4">
            These images will not be used in AI-generated emails.
            Select 'Edit' and choose a category if you want them to be available for AI use.
          </p>

          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
            <div
              v-for="(image, index) in untypedImages"
              :key="image.id"
              class="group bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition"
            >
              <div class="relative h-44 bg-gray-50 flex items-center justify-center p-3">
                <!-- Selection checkbox -->
                <div class="absolute top-2 left-2 z-10">
                  <input
                    type="checkbox"
                    :checked="selectedImages.includes(image.id)"
                    @change="toggleImageSelection(image.id)"
                    @click.stop
                    class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                </div>

                <img
                  :src="image.url"
                  :alt="image.friendlyname"
                  class="object-contain max-w-full max-h-full"
                />

                <!-- Hover overlay -->
                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <div class="flex gap-2">
                    <button
                      @click.stop="editImage(image)"
                      class="bg-white rounded-full p-2 shadow-md hover:bg-blue-50 transition"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-blue-500">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                      </svg>
                    </button>
                    <button
                      @click.stop="confirmDelete(image.id)"
                      class="bg-white rounded-full p-2 shadow-md hover:bg-red-50 transition"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-red-500">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <div class="p-3 border-t">
                <p class="font-medium text-sm truncate">{{ image.friendlyname }}</p>
                <p class="text-xs text-gray-500">Add category for AI use</p>
                <p class="text-xs text-gray-400">{{ image.width || '?' }} × {{ image.height || '?' }}px</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal Overlay -->
  <div v-if="showUploadForm"
       class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
       tabindex="-1"
       role="dialog">
    <!-- Modal Content -->
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-auto animate-modal-appear"
         role="document"
         @click.stop>
      <div class="sticky top-0 bg-white p-6 border-b z-10 flex justify-between items-center">
        <h3 class="text-lg font-semibold">{{ isEditMode ? 'Edit Brand Image' : 'Add Brand Image' }}</h3>
        <button @click="closeModal" class="text-gray-400 hover:text-gray-600 transition">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Tabs (only show in upload mode, not edit mode) -->
      <div v-if="!isEditMode" class="border-b">
        <div class="flex">
          <button
            @click="activeTab = 'upload'"
            class="px-6 py-3 text-sm font-medium"
            :class="activeTab === 'upload' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500 hover:text-gray-700'"
          >
            Upload
          </button>
          <button
            @click="activeTab = 'bulkUpload'"
            class="px-6 py-3 text-sm font-medium"
            :class="activeTab === 'bulkUpload' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500 hover:text-gray-700'"
          >
            Bulk Upload
          </button>
          <button
            @click="activeTab = 'klaviyo'; fetchKlaviyoImages()"
            class="px-6 py-3 text-sm font-medium"
            :class="activeTab === 'klaviyo' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500 hover:text-gray-700'"
          >
            Klaviyo Images
          </button>
        </div>
      </div>

      <!-- Upload Form -->
      <div class="p-6">
        <!-- Upload tab content -->
        <div v-if="activeTab === 'upload' || isEditMode" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Image Name</label>
              <input
                v-model="newImage.name"
                type="text"
                placeholder="e.g. Main Logo"
                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                @input="validateImageUpload"
              />
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">AI Image Tag <span class="text-gray-400 text-xs">(optional)</span></label>
              <select
                v-model="newImage.imageType"
                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                @change="validateImageUpload"
              >
                <option value="">Exclude from AI</option>
                <!-- Default categories -->
                <option value="Logo">Logo</option>
                <option value="Hero Image">Hero Image</option>
                <option value="Banner">Banner</option>
                <option value="Product">Product</option>
                <option value="Background">Background</option>
                <option value="Icon">Icon</option>
                <option value="Reviews">Reviews</option>
                <!-- Custom categories -->
                <option v-for="category in customCategories" :key="category" :value="category">
                  {{ category }}
                </option>
              </select>
              <p class="text-xs text-gray-500 mt-1">Only categorized images will be available for AI-generated emails</p>
            </div>
          </div>

          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <label class="block text-sm font-medium">Describe when AI should use this</label>
              <button
                @click="generateDescription"
                class="flex items-center gap-2 px-3 py-1.5 text-sm rounded-lg text-purple-600 hover:bg-purple-50 border border-purple-200 transition-colors"
                :disabled="isGenerating || (!isEditMode && !newImage.file)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" :class="['h-4 w-4', { 'animate-spin': isGenerating }]" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path>
                  <path d="M20 3v4"></path>
                  <path d="M22 5h-4"></path>
                  <path d="M4 17v2"></path>
                  <path d="M5 18H3"></path>
                </svg>
                <span>{{ isGenerating ? 'Generating...' : 'Generate AI Instructions' }}</span>
              </button>
            </div>
            <textarea
              v-model="newImage.description"
              rows="4"
              placeholder="Describe what this image is, and any limitations of when it should and shouldn't be used."
              class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
            ></textarea>
          </div>

          <!-- Remove the old width/height grid and replace with more compact format -->
          <div class="flex items-center justify-center border-2 border-dashed border-gray-200 rounded-lg p-6 mb-4 bg-white hover:bg-gray-50 transition cursor-pointer"
            v-if="!isEditMode || !newImage.url"
            @click="triggerFileInput"
          >
            <div class="flex flex-col items-center space-y-2 text-center">
              <div class="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                </svg>
              </div>
              <input
                ref="imageInput"
                type="file"
                class="hidden"
                accept="image/*"
                @change="handleImageUpload"
              />
              <span class="text-sm font-medium text-gray-700">
                {{ newImage.file ? newImage.file.name : 'Click to select an image' }}
              </span>
              <span class="text-xs text-gray-500">JPG, PNG, GIF, SVG</span>
              <!-- Display dimensions if available -->
              <span v-if="newImage.width && newImage.height" class="text-xs text-gray-500">
                Size: {{ newImage.width }}px × {{ newImage.height }}px
              </span>
            </div>
          </div>

          <!-- Image preview for edit mode -->
          <div v-if="isEditMode && newImage.url" class="border rounded-lg overflow-hidden">
            <div class="relative bg-gray-50 flex items-center justify-center p-4" style="height: 200px;">
              <img :src="newImage.url" :alt="newImage.name" class="object-contain max-w-full max-h-full" />
            </div>
            <!-- Display dimensions for edit mode image -->
            <div class="px-3 py-2 text-xs text-gray-500 text-center border-t">
              Size: {{ newImage.width || '?' }}px × {{ newImage.height || '?' }}px
            </div>
          </div>

          <!-- Upload validation status -->
          <div v-if="!isEditMode && newImage.file && !canUploadImage" class="text-sm text-amber-600 bg-amber-50 p-3 rounded-lg">
            Please provide an image name before uploading.
          </div>

          <!-- Actions -->
          <div class="flex justify-end gap-3 pt-2">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 border rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="button"
              @click="isEditMode ? updateImage() : uploadImage()"
              :disabled="!isEditMode && !canUploadImage"
              :class="[
                'px-4 py-2 rounded-lg transition',
                (isEditMode || canUploadImage)
                  ? 'bg-purple-600 text-white hover:bg-purple-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              ]"
            >
              {{ isEditMode ? 'Update Image' : 'Upload Image' }}
            </button>
          </div>
        </div>

        <!-- Bulk Upload tab content -->
        <div v-if="activeTab === 'bulkUpload'" class="space-y-4">
          <div class="mb-4">
            <h3 class="text-sm font-medium text-gray-700 mb-2">Upload Multiple Images</h3>
            <p class="text-sm text-gray-500 mb-4">
              Upload multiple images at once. All images will use the same AI Image Tag and settings.
            </p>
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium mb-1">AI Image Tag</label>
            <select
              v-model="bulkUploadImageType"
              class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="">Exclude from AI</option>
              <!-- Default categories -->
              <option value="Logo">Logo</option>
              <option value="Hero Image">Hero Image</option>
              <option value="Banner">Banner</option>
              <option value="Product">Product</option>
              <option value="Background">Background</option>
              <option value="Icon">Icon</option>
              <option value="Reviews">Reviews</option>
              <!-- Custom categories -->
              <option v-for="category in customCategories" :key="category" :value="category">
                {{ category }}
              </option>
            </select>
            <p class="text-xs text-gray-500 mt-1">All images will use this tag</p>
          </div>

          <div class="mb-4 p-4 bg-blue-50 rounded-md">
            <div class="flex items-start">
              <div class="flex-shrink-0 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
              </div>
              <div>
                <h3 class="text-sm font-medium text-blue-700">AI Description Generation</h3>
                <p class="text-sm text-blue-600 mt-1">
                  AI descriptions will be automatically generated for each image during upload, optimizing them for search and accessibility.
                </p>
              </div>
            </div>
          </div>

          <!-- Multiple file upload area -->
          <div
            class="flex items-center justify-center border-2 border-dashed border-gray-200 rounded-lg p-8 mb-4 bg-white hover:bg-gray-50 transition cursor-pointer"
            @click="triggerBulkFileInput"
          >
            <div class="flex flex-col items-center space-y-3 text-center">
              <div class="h-14 w-14 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-7 h-7">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                </svg>
              </div>
              <input
                ref="bulkImageInput"
                type="file"
                class="hidden"
                accept="image/*"
                multiple
                @change="handleBulkImageUpload"
              />
              <span class="text-base font-medium text-gray-700">
                Drop files here or click to select
              </span>
              <span class="text-sm text-gray-500">
                {{ bulkImages.length ? `${bulkImages.length} files selected` : 'Select multiple images to upload at once' }}
              </span>
              <span class="text-xs text-gray-500">JPG, PNG, GIF, SVG (max 10MB per file)</span>
            </div>
          </div>

          <!-- Selected files preview -->
          <div v-if="bulkImages.length > 0" class="border rounded-lg overflow-hidden mb-4">
            <div class="bg-gray-50 p-3 border-b flex justify-between items-center">
              <h4 class="font-medium text-sm">Selected Files ({{ bulkImages.length }})</h4>
              <button
                @click="clearBulkUpload"
                class="text-xs text-red-600 hover:text-red-800 px-2 py-1 rounded hover:bg-red-50"
              >
                Clear All
              </button>
            </div>
            <div class="max-h-60 overflow-y-auto p-2">
              <div v-for="(file, index) in bulkImages" :key="index" class="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
                    <img :src="getPreviewUrl(file)" class="object-cover w-full h-full" />
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</p>
                    <div class="flex gap-1 items-center flex-wrap">
                      <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
                      <p v-if="bulkImageDimensions[file.name]" class="text-xs text-gray-500 ml-2">
                        {{ bulkImageDimensions[file.name].width || 0 }}×{{ bulkImageDimensions[file.name].height || 0 }}px
                      </p>
                      <span v-if="bulkImageDescriptions[file.name]"
                            class="inline-flex items-center ml-2 px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                            title="This image has an AI-generated description"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-0.5" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm3.707 6.707a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        AI Description
                      </span>
                    </div>
                  </div>
                </div>
                <div class="flex gap-2">
                  <button
                    v-if="bulkImageDescriptions[file.name]"
                    @click="previewBulkImageDescription(file.name)"
                    class="text-blue-500 hover:text-blue-700"
                    title="View AI description"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                      <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </button>
                  <button
                    @click="removeBulkImage(index)"
                    class="text-gray-400 hover:text-red-500"
                    title="Remove file"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Upload status message -->
          <div v-if="bulkUploadStatus" class="p-3 rounded-lg" :class="bulkUploadStatusType === 'error' ? 'bg-red-50 text-red-700' : 'bg-blue-50 text-blue-700'">
            {{ bulkUploadStatus }}
          </div>

          <!-- Actions -->
          <div class="flex justify-end gap-3 pt-2">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 border rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="button"
              @click="uploadBulkImages"
              :disabled="!canUploadBulkImages"
              :class="[
                'px-4 py-2 rounded-lg transition',
                canUploadBulkImages
                  ? 'bg-purple-600 text-white hover:bg-purple-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              ]"
            >
              {{ isBulkUploading ? 'Processing...' : 'Upload with AI Descriptions' }}
            </button>
          </div>
        </div>

        <!-- Klaviyo Images tab content -->
        <div v-if="activeTab === 'klaviyo'" class="space-y-4">
          <!-- Search and Pagination Controls - hide when image selected -->
          <div v-if="!selectedKlaviyoImage"
               class="flex flex-col md:flex-row gap-4 mb-4 transition-opacity duration-300"
               :class="{
                 'opacity-100': !selectedKlaviyoImage && !isTransitioningFromSelected,
                 'opacity-0 h-0 overflow-hidden': selectedKlaviyoImage || isTransitioningFromSelected
               }">
            <div class="flex-1">
              <div class="relative">
                <input
                  v-model="klaviyoSearchQuery"
                  type="text"
                  placeholder="Search images by name..."
                  class="w-full px-3 py-2 pr-10 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  @keyup.enter="fetchKlaviyoImages()"
                />
                <button
                  @click="fetchKlaviyoImages()"
                  class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                  </svg>
                </button>
              </div>
            </div>
            <div class="flex gap-2">
              <select
                v-model="klaviyoPageSize"
                class="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 min-w-[140px] w-auto pr-8"
                @change="fetchKlaviyoImages(true)"
              >
                <option value="10">10 per page</option>
                <option value="20">20 per page</option>
                <option value="50">50 per page</option>
                <option value="100">100 per page</option>
              </select>
            </div>
          </div>

          <!-- Loading state -->
          <div v-if="isLoadingKlaviyoImages && !selectedKlaviyoImage" class="flex justify-center items-center py-10">
            <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-purple-600"></div>
          </div>

          <!-- No results state -->
          <div v-else-if="klaviyoImages.length === 0 && !selectedKlaviyoImage" class="bg-gray-50 rounded-lg py-16 text-center">
            <div class="mx-auto w-16 h-16 mb-4 rounded-full bg-gray-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-gray-400">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
              </svg>
            </div>
            <h3 class="text-gray-500 text-lg font-medium mb-1">No Klaviyo images found</h3>
            <p class="text-gray-400 mb-4">{{ klaviyoSearchQuery ? 'Try a different search term' : 'No images available in your Klaviyo account' }}</p>
          </div>

          <!-- Controls above the image grid -->
          <div v-if="!selectedKlaviyoImage && klaviyoImages.length > 0" class="mb-4">
            <!-- Simple header with action buttons -->
            <div class="flex justify-between items-center border-b pb-3 mb-4">
              <div class="flex items-center">
                <span class="text-sm font-medium text-gray-700">
                  {{ selectedKlaviyoImages.length === 0 ? 'Select images to import' : `${selectedKlaviyoImages.length} images selected` }}
                </span>
                <button
                  v-if="selectedKlaviyoImages.length > 0 && !isProcessingKlaviyoImages"
                  @click="clearKlaviyoSelection"
                  class="ml-2 text-xs text-gray-600 hover:text-gray-800 px-2 py-1 rounded border hover:bg-gray-50"
                >
                  Clear
                </button>
              </div>

              <div class="flex items-center gap-2">
                <!-- Tag dropdown only shown when images are selected -->
                <select
                  v-if="selectedKlaviyoImages.length > 0 && !isProcessingKlaviyoImages"
                  v-model="bulkUploadImageType"
                  class="text-sm border rounded-md px-2 py-1.5 focus:outline-none focus:ring-1 focus:ring-purple-500"
                >
                  <option value="" disabled>Select tag</option>
                  <option value="Logo">Logo</option>
                  <option value="Hero Image">Hero Image</option>
                  <option value="Banner">Banner</option>
                  <option value="Product">Product</option>
                  <option value="Background">Background</option>
                  <option value="Icon">Icon</option>
                  <option value="Reviews">Reviews</option>
                  <option v-for="category in customCategories" :key="category" :value="category">
                    {{ category }}
                  </option>
                </select>

                <button
                  v-if="selectedKlaviyoImages.length > 0"
                  @click="processSelectedKlaviyoImages"
                  :disabled="isProcessingKlaviyoImages || !bulkUploadImageType"
                  class="px-3 py-1.5 text-sm rounded flex items-center gap-1 transition"
                  :class="isProcessingKlaviyoImages || !bulkUploadImageType ?
                    'bg-gray-400 text-white cursor-not-allowed' :
                    'bg-purple-600 text-white hover:bg-purple-700'"
                >
                  <svg v-if="!isProcessingKlaviyoImages" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="animate-spin h-4 w-4" viewBox="0 0 24 24" fill="none">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isProcessingKlaviyoImages ? 'Processing...' : `Add Images` }}
                </button>
              </div>
            </div>

            <!-- Progress bar for processing -->
            <div v-if="isProcessingKlaviyoImages" class="w-full mb-4">
              <div class="bg-gray-200 rounded-full h-2.5 mb-2">
                <div class="bg-purple-600 h-2.5 rounded-full transition-all duration-300" :style="`width: ${klaviyoImagesProgress}%`"></div>
              </div>
              <p class="text-xs text-gray-600 text-center">{{ bulkUploadStatus }}</p>
            </div>
          </div>

          <!-- Image grid container with transition -->
          <div
            class="transition-all duration-300 ease-in-out"
            :class="{
              'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4': !selectedKlaviyoImage,
              'flex justify-center': selectedKlaviyoImage,
              'opacity-0': isTransitioningFromSelected,
              'grid-appear': !selectedKlaviyoImage && !isTransitioningFromSelected
            }"
          >
            <!-- Images grid with multi-select capabilities -->
            <template v-if="!selectedKlaviyoImage && klaviyoImages.length > 0">

              <div
                v-for="image in klaviyoImages"
                :key="image.id"
                class="relative group border rounded-lg overflow-hidden cursor-pointer transition-all hover:shadow-md"
                @click="toggleKlaviyoImageSelection(image)"
              >
                <div class="h-40 bg-gray-50 flex items-center justify-center p-2 relative">
                  <img
                    :src="image.attributes.image_url"
                    :alt="image.attributes.name"
                    class="object-contain max-w-full max-h-full"
                  />

                  <!-- Selection indicator -->
                  <div
                    v-if="isKlaviyoImageSelected(image)"
                    class="absolute top-2 right-2 bg-purple-600 text-white rounded-full p-0.5 shadow-md"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                </div>
                <div class="p-3 border-t bg-white">
                  <p class="font-medium text-sm truncate">{{ image.attributes.name || 'Untitled Image' }}</p>
                  <p class="text-xs text-gray-500">{{ image.attributes.format || 'image' }}</p>
                  <p class="text-xs text-gray-400">{{ image.attributes.created_at }}</p>
                </div>
                <div
                  class="absolute inset-0 transition-all"
                  :class="isKlaviyoImageSelected(image) ?
                    'bg-purple-600 bg-opacity-20 border-2 border-purple-600' :
                    'bg-purple-600 bg-opacity-0 group-hover:bg-opacity-10'"
                ></div>
              </div>
            </template>

            <!-- Selected image in focused view -->
            <div
              v-if="selectedKlaviyoImage"
              class="w-full max-w-2xl transition-all duration-300 ease-in-out animate-selected-image-appear"
            >
              <!-- Back button is now at the top of the selection view -->
              <button
                @click="deselectKlaviyoImage"
                class="mb-4 flex items-center gap-1 text-sm text-purple-600 hover:text-purple-800"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                </svg>
                Back to image selection
              </button>

              <!-- Selected image preview -->
              <div class="border rounded-lg overflow-hidden mb-6 shadow-md">
                <div class="relative bg-gray-50 flex items-center justify-center p-4" style="height: 240px;">
                  <img
                    :src="selectedKlaviyoImage.attributes.image_url"
                    :alt="newImage.name || selectedKlaviyoImage.attributes.name"
                    class="object-contain max-w-full max-h-full transition-all"
                  />
                  <div class="absolute bottom-2 right-2 text-xs bg-white px-2 py-1 rounded-md shadow-sm text-gray-500">
                    Klaviyo Image Preview
                  </div>
                </div>
                <!-- Display dimensions as small subtext -->
                <div class="px-3 py-2 text-xs text-gray-500 text-center border-t">
                  Size: {{ newImage.width || '?' }}px × {{ newImage.height || '?' }}px
                </div>
              </div>

              <h3 class="text-lg font-medium mb-4">Configure Selected Image</h3>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label class="block text-sm font-medium mb-1">Image Name</label>
                  <input
                    v-model="newImage.name"
                    type="text"
                    placeholder="e.g. Main Logo"
                    class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    @input="validateImageUpload"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium mb-1">AI Image Tag <span class="text-gray-400 text-xs">(optional)</span></label>
                  <select
                    v-model="newImage.imageType"
                    class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    @change="validateImageUpload"
                  >
                    <option value="">Exclude from AI</option>
                    <!-- Default categories -->
                    <option value="Logo">Logo</option>
                    <option value="Hero Image">Hero Image</option>
                    <option value="Banner">Banner</option>
                    <option value="Product">Product</option>
                    <option value="Background">Background</option>
                    <option value="Icon">Icon</option>
                    <option value="Reviews">Reviews</option>
                    <!-- Custom categories -->
                    <option v-for="category in customCategories" :key="category" :value="category">
                      {{ category }}
                    </option>
                  </select>
                  <p class="text-xs text-gray-500 mt-1">Only categorized images will be available for AI-generated emails</p>
                </div>
              </div>

              <div class="space-y-2 mb-4">
                <div class="flex items-center justify-between">
                  <label class="block text-sm font-medium">Describe when AI should use this</label>
                  <button
                    @click="generateDescription"
                    class="flex items-center gap-2 px-3 py-1.5 text-sm rounded-lg text-purple-600 hover:bg-purple-50 border border-purple-200 transition-colors"
                    :disabled="isGenerating"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" :class="['h-4 w-4', { 'animate-spin': isGenerating }]" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path>
                      <path d="M20 3v4"></path>
                      <path d="M22 5h-4"></path>
                      <path d="M4 17v2"></path>
                      <path d="M5 18H3"></path>
                    </svg>
                    <span>{{ isGenerating ? 'Generating...' : 'Generate AI Instructions' }}</span>
                  </button>
                </div>
                <textarea
                  v-model="newImage.description"
                  rows="4"
                  placeholder="Describe what this image is, and any limitations of when it should and shouldn't be used."
                  class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                ></textarea>
              </div>

              <div class="flex justify-end gap-3 pt-4 mt-2">
                <button
                  type="button"
                  @click="deselectKlaviyoImage"
                  class="px-4 py-2 border rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  @click="saveKlaviyoImage"
                  :disabled="!newImage.name"
                  :class="[
                    'px-4 py-2 rounded-lg transition',
                    newImage.name
                      ? 'bg-purple-600 text-white hover:bg-purple-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  ]"
                >
                  Save Image
                </button>
              </div>
            </div>
          </div>

          <!-- Pagination - hide when image selected -->
          <div
            v-if="klaviyoImages.length > 0 && !selectedKlaviyoImage"
            class="flex justify-between items-center mt-4 pt-4 border-t transition-opacity duration-300"
            :class="{
              'opacity-100': !selectedKlaviyoImage && !isTransitioningFromSelected,
              'opacity-0 h-0 overflow-hidden': selectedKlaviyoImage || isTransitioningFromSelected
            }"
          >
            <div class="text-sm text-gray-500">
              Showing {{ klaviyoImages.length }} images
            </div>
            <div class="flex gap-2">
              <button
                @click="loadPreviousKlaviyoImages()"
                class="px-4 py-2 border rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                :disabled="!klaviyoPrevPageCursor"
              >
                Previous
              </button>
              <button
                @click="loadNextKlaviyoImages()"
                class="px-4 py-2 border rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                :disabled="!klaviyoNextPageCursor"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div v-if="showDeleteConfirm"
       class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
       @click.self="cancelDelete"
       tabindex="-1"
       role="dialog">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full animate-modal-appear p-6"
         role="document"
         @click.stop>
      <div class="text-center">
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-50 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-red-500">
            <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Delete Image</h3>
        <p class="text-gray-600 mb-6">Are you sure you want to delete this image? This action cannot be undone and the image will be permanently removed from your brand assets.</p>
        <div class="flex justify-center space-x-3">
          <button
            @click="cancelDelete"
            class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition"
          >
            Cancel
          </button>
          <button
            @click="proceedWithDelete"
            class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition"
          >
            Delete Image
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- AI Tags Settings Modal -->
  <div v-if="showTagSettings"
       class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
       @click.self="closeTagSettings"
       tabindex="-1"
       role="dialog">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full animate-modal-appear max-h-[90vh] overflow-auto"
         role="document"
         @click.stop>
      <div class="sticky top-0 bg-white p-6 border-b z-10 flex justify-between items-center">
        <h3 class="text-lg font-semibold">Manage AI Image Tags</h3>
        <button @click="closeTagSettings" class="text-gray-400 hover:text-gray-600 transition">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div class="p-6">
        <div class="mb-4">
          <p class="text-sm text-gray-600 mb-4">
            AI Tags help categorize your images so they can be intelligently used in AI-generated emails.
            Add custom tags to better match your brand's specific image categories.
          </p>

          <!-- List of existing custom categories -->
          <div v-if="customCategories.length > 0" class="mb-6 space-y-2">
            <h4 class="text-sm font-medium text-gray-700 mb-2">Your Custom Tags</h4>
            <div v-for="(category, index) in customCategories" :key="index" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div v-if="editingCategoryIndex === index" class="flex-1 flex items-center">
                <input
                  v-model="editingCategoryValue"
                  class="w-full px-3 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  @keyup.enter="saveEditedCategory"
                  ref="editCategoryInput"
                />
              </div>
              <div v-else class="flex-1">
                <span>{{ category }}</span>
              </div>

              <div class="flex space-x-2 ml-3">
                <button
                  v-if="editingCategoryIndex === index"
                  @click="saveEditedCategory"
                  class="text-green-600 hover:text-green-800 px-2 bg-white rounded-md"
                  title="Save changes"
                >
                  ✓
                </button>
                <button
                  v-else
                  @click="startEditingCategory(index, category)"
                  class="text-blue-600 hover:text-blue-800"
                  title="Edit tag"
                >
                  ✏️
                </button>
                <button
                  @click="removeCategory(index)"
                  class="text-red-600 hover:text-red-800"
                  title="Delete tag"
                >
                  🗑️
                </button>
              </div>
            </div>
          </div>

          <!-- Add new tag -->
          <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-700 mb-2">Add New Tag</h4>
            <div class="flex items-center space-x-2">
              <input
                v-model="newCategoryName"
                placeholder="Enter new tag name"
                class="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                @keyup.enter="addCategory"
              />
              <button
                @click="addCategory"
                class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                :disabled="!newCategoryName.trim()"
              >
                Add
              </button>
            </div>
          </div>

          <!-- Default tags reference -->
          <div class="pt-4 border-t">
            <h4 class="text-sm font-medium text-gray-700 mb-2">Default Tags (always available)</h4>
            <div class="flex flex-wrap gap-2">
              <span
                v-for="category in defaultCategories"
                :key="category"
                class="px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-md"
              >
                {{ category }}
              </span>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 mt-6 pt-4 border-t">
          <button
            @click="closeTagSettings"
            class="px-4 py-2 border rounded-lg hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            @click="saveTagSettings"
            class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
  name: 'BrandImageManager',
  props: {
    assetType: {
      type: String,
      default: 'email'
    },
    customCategories: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isLoading: true,
      isGenerating: false,
      images: [],
      showUploadForm: false,
      isEditMode: false,
      editImageId: null,
      newImage: {
        name: '',
        imageType: '',
        width: '',
        height: '',
        file: null,
        url: null,
      },
      canUploadImage: false,
      filter: '',
      status: '',
      statusText: '',
      // Add new data properties for delete confirmation
      showDeleteConfirm: false,
      imageToDelete: null,
      showTagSettings: false,
      editingCategoryIndex: null,
      editingCategoryValue: '',
      newCategoryName: '',
      defaultCategories: ['Logo', 'Hero Image', 'Banner', 'Product', 'Background', 'Icon'],
      activeTab: 'upload',
      // Bulk upload properties
      bulkImages: [],
      bulkUploadImageType: 'Product', // Default to Product for easier selection
      bulkUploadStatus: '',
      bulkUploadStatusType: 'info',
      isBulkUploading: false,
      isBulkGenerating: false,
      bulkGeneratingCount: 0,
      bulkDescriptionsGenerated: false,
      bulkUploadProgress: 0,
      bulkPreviewUrls: {},
      bulkImageDescriptions: {},
      bulkImageDimensions: {},
      // Klaviyo properties
      klaviyoSearchQuery: '',
      klaviyoPageSize: '10',
      isLoadingKlaviyoImages: false,
      klaviyoImages: [],
      klaviyoPrevPageCursor: null,
      klaviyoNextPageCursor: null,
      selectedKlaviyoImage: null,
      selectedKlaviyoImages: [], // Array for multi-select functionality
      isProcessingKlaviyoImages: false, // Flag for batch processing
      klaviyoImagesProgress: 0, // Progress for batch processing
      klaviyoImageDescriptions: {}, // Store descriptions for selected Klaviyo images
      klaviyoImageDimensions: {}, // Store dimensions for Klaviyo images
      klaviyoPaginationHistory: [], // Stack to track pagination history
      currentKlaviyoPageIndex: 0, // Current position in pagination history
      isTransitioningFromSelected: false, // New data property for transition control
      // Bulk selection properties
      selectedImages: [], // Array of selected image IDs for bulk operations
      bulkSelectionMode: false, // Whether bulk selection mode is active
      bulkOperationInProgress: false, // Whether a bulk operation is currently running
      bulkTagUpdate: '', // Selected tag for bulk update
    };
  },
  computed: {
    filteredImages() {
      if (!this.filter) return this.images;
      return this.images.filter(img => img.imageType === this.filter);
    },
    typedImages() {
      return this.images.filter(img => {
        const type = this.getImageType(img);
        return type !== 'Excluded';
      });
    },
    untypedImages() {
      return this.images.filter(img => {
        const type = this.getImageType(img);
        return type === 'Excluded';
      });
    },
    filteredTypedImages() {
      if (!this.filter) return this.typedImages;
      return this.typedImages.filter(img => img.imageType === this.filter || img.contentType === this.filter);
    },
    // Computed property for all available categories (default + custom)
    allCategories() {
      // Start with default categories
      const defaultCategories = ['Logo', 'Hero Image', 'Banner', 'Product', 'Background', 'Icon', 'Reviews', 'Other'];

      // If no custom categories, just return defaults
      if (!this.customCategories || !this.customCategories.length) {
        return defaultCategories;
      }

      // Combine defaults with custom categories
      return [...defaultCategories, ...this.customCategories];
    },
    // Computed property for the bulk upload validation
    canUploadBulkImages() {
      return this.bulkImages && this.bulkImages.length > 0 && !this.isBulkUploading;
    },
    // Bulk selection computed properties
    selectedImageCount() {
      return this.selectedImages.length;
    },
    allImagesSelected() {
      const allImageIds = this.images.map(img => img.id);
      return allImageIds.length > 0 && allImageIds.every(id => this.selectedImages.includes(id));
    },
    someImagesSelected() {
      return this.selectedImages.length > 0 && !this.allImagesSelected;
    }
  },
  mounted() {
    this.fetchImages();
    // Add event listener for Escape key to close modal
    document.addEventListener('keydown', this.handleKeyDown);
  },
  beforeUnmount() {
    // Clean up event listener
    document.removeEventListener('keydown', this.handleKeyDown);
  },
  methods: {
    openModal() {
      this.showUploadForm = true;
      this.isEditMode = false;
      // Prevent background scrolling when modal is open
      document.body.style.overflow = 'hidden';
    },
    closeModal() {
      this.resetForm();
      this.showUploadForm = false;
      this.isEditMode = false;
      this.editImageId = null;
      this.selectedKlaviyoImage = null;
      this.selectedKlaviyoImages = [];
      this.klaviyoImageDescriptions = {};
      this.klaviyoImageDimensions = {};
      this.isProcessingKlaviyoImages = false;
      this.klaviyoImagesProgress = 0;
      // Reset to the upload tab for next time
      this.activeTab = 'upload';
      // Re-enable scrolling
      document.body.style.overflow = '';
    },
    handleKeyDown(e) {
      if (e.key === 'Escape' && this.showUploadForm) {
        this.closeModal();
      }
    },
    // Bulk selection methods
    toggleImageSelection(imageId) {
      const index = this.selectedImages.indexOf(imageId);
      if (index === -1) {
        this.selectedImages.push(imageId);
      } else {
        this.selectedImages.splice(index, 1);
      }
    },
    toggleSelectAll() {
      if (this.allImagesSelected) {
        this.selectedImages = [];
      } else {
        this.selectedImages = this.images.map(img => img.id);
      }
    },
    clearSelection() {
      this.selectedImages = [];
      this.bulkTagUpdate = '';
    },
    async handleBulkTagChange() {
      if (!this.bulkTagUpdate || this.selectedImages.length === 0) {
        return;
      }

      this.bulkOperationInProgress = true;
      this.$emit('status-update', 'loading', `Updating ${this.selectedImages.length} image(s)...`);

      try {
        const response = await fetch(`${URL_DOMAIN}/branding/images/bulk-update`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({
            imageIds: this.selectedImages,
            updates: {
              imageType: this.bulkTagUpdate
            }
          }),
        });

        if (response.ok) {
          const result = await response.json();
          this.$emit('status-update', 'success', `Successfully updated ${result.updated} image(s)`);

          if (result.failed.length > 0) {
            this.$emit('status-update', 'warning', `Failed to update ${result.failed.length} image(s)`);
          }

          // Refresh images and clear selection
          await this.fetchImages();
          this.clearSelection();
        } else {
          throw new Error('Failed to update images');
        }
      } catch (error) {
        console.error('Error updating images:', error);
        this.$emit('status-update', 'error', 'Failed to update images');
      } finally {
        this.bulkOperationInProgress = false;
      }
    },
    confirmBulkDelete() {
      if (this.selectedImages.length === 0) return;

      const message = `Are you sure you want to delete ${this.selectedImages.length} image(s)? This action cannot be undone.`;
      if (confirm(message)) {
        this.handleBulkDelete();
      }
    },
    async handleBulkDelete() {
      if (this.selectedImages.length === 0) return;

      this.bulkOperationInProgress = true;
      this.$emit('status-update', 'loading', `Deleting ${this.selectedImages.length} image(s)...`);

      try {
        const response = await fetch(`${URL_DOMAIN}/branding/images/bulk-delete`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({
            imageIds: this.selectedImages
          }),
        });

        if (response.ok) {
          const result = await response.json();
          this.$emit('status-update', 'success', `Successfully deleted ${result.deleted} image(s)`);

          if (result.failed.length > 0) {
            this.$emit('status-update', 'warning', `Failed to delete ${result.failed.length} image(s)`);
          }

          // Refresh images and clear selection
          await this.fetchImages();
          this.clearSelection();
        } else {
          throw new Error('Failed to delete images');
        }
      } catch (error) {
        console.error('Error deleting images:', error);
        this.$emit('status-update', 'error', 'Failed to delete images');
      } finally {
        this.bulkOperationInProgress = false;
      }
    },
    async fetchImages() {
      this.isLoading = true;
      try {
        console.log('Fetching images from:', `${URL_DOMAIN}/branding/images?assetType=${this.assetType}`);
        const response = await fetch(`${URL_DOMAIN}/branding/images?assetType=${this.assetType}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (response.ok) {
          this.images = await response.json();
          console.log('Loaded images:', this.images);
          // Emit event with images for parent component
          this.$emit('images-loaded', this.images);
        } else {
          console.error('Failed to fetch brand images:', await response.text());
          this.$emit('status-update', 'error', 'Failed to fetch brand images');
        }
      } catch (error) {
        console.error('Error fetching brand images:', error);
        this.$emit('status-update', 'error', 'Error fetching brand images');
      } finally {
        this.isLoading = false;
      }
    },
    triggerFileInput() {
      if (this.$refs.imageInput) {
        this.$refs.imageInput.click();
      } else {
        console.error('Image input reference not found');
      }
    },
    handleImageUpload(event) {
      const file = event.target.files[0];
      if (file) {
        console.log('Selected file:', file.name, 'type:', file.type, 'size:', file.size);
        this.newImage.file = file;

        // Use filename as default name if not already set
        if (!this.newImage.name) {
          // Remove file extension
          const fileName = file.name.replace(/\.[^/.]+$/, "");
          this.newImage.name = fileName;
        }

        // Auto-detect image dimensions
        const reader = new FileReader();
        reader.onload = (e) => {
          const img = new Image();
          img.onload = () => {
            console.log('Image dimensions detected:', img.width, 'x', img.height);
            this.newImage.width = img.width;
            this.newImage.height = img.height;
            this.validateImageUpload();
          };
          img.onerror = (err) => {
            console.error('Error loading image for dimension detection:', err);
          };
          img.src = e.target.result;
        };
        reader.onerror = (err) => {
          console.error('Error reading file:', err);
        };
        reader.readAsDataURL(file);

        this.validateImageUpload();
      }
    },
    validateImageUpload() {
      // In edit mode, we only need the name
      if (this.isEditMode) {
        const isValid = !!this.newImage.name;
        this.canUploadImage = isValid;
        return;
      }

      // In Klaviyo mode, we need the selected image and name
      if (this.activeTab === 'klaviyo' && this.selectedKlaviyoImage) {
        const isValid = !!this.newImage.name;
        console.log('Klaviyo image validation:', {
          name: !!this.newImage.name,
          isValid
        });
        this.canUploadImage = isValid;
        return;
      }

      // In upload mode, we need file and name (imageType is now optional)
      const isValid = !!(this.newImage.file && this.newImage.name);
      console.log('Image upload validation:', {
        file: !!this.newImage.file,
        name: !!this.newImage.name,
        isValid
      });
      this.canUploadImage = isValid;
    },
    editImage(image) {
      // Switch to edit mode and populate the form with image data
      this.isEditMode = true;
      this.editImageId = image.id;

      this.newImage = {
        name: image.friendlyname,
        imageType: image.imageType || '', // Try both fields
        width: image.width || '',
        height: image.height || '',
        description: image.description || '',
        file: null,
        url: image.url
      };

      this.showUploadForm = true;
      // Prevent background scrolling when modal is open
      document.body.style.overflow = 'hidden';
    },
    async updateImage() {
      try {
        this.$emit('status-update', 'loading', 'Updating image...');

        // Prepare update data
        const updateData = {
          url: this.newImage.url,
          friendlyname: this.newImage.name,
          imageType: this.newImage.imageType,
          width: this.newImage.width ? Number(this.newImage.width) : 0,
          height: this.newImage.height ? Number(this.newImage.height) : 0,
          assetType: this.assetType,
          description: this.newImage.description
        };

        console.log('Updating image with data:', updateData);

        // If there's a new file selected, we need to upload it first
        if (this.newImage.file) {
          // Upload the new file first, then update metadata
          return this.uploadImage();
        }

        // First delete the old image
        await this.deleteImage(this.editImageId, false); // Pass false to not refresh the list yet

        // Then create a new image with the updated metadata
        const response = await fetch(`${URL_DOMAIN}/branding/images/metadata`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updateData)
        });

        console.log('Update response status:', response.status);

        if (response.ok) {
          console.log('Image updated successfully');
          await this.fetchImages();
          this.closeModal();
          this.$emit('status-update', 'success', 'Brand image updated successfully');
          // Re-emit images-loaded with the updated images list
          this.$emit('images-loaded', this.images);
        } else {
          const errorText = await response.text();
          console.error('Update failed:', errorText);
          try {
            const errorData = errorText ? JSON.parse(errorText) : null;
            this.$emit('status-update', 'error', errorData?.error?.message || 'Failed to update brand image');
          } catch (e) {
            this.$emit('status-update', 'error', `Failed to update brand image: ${response.status} ${response.statusText}`);
          }
        }
      } catch (error) {
        console.error('Error updating brand image:', error);
        this.$emit('status-update', 'error', error.message || 'Failed to update brand image');
      }
    },
    async uploadImage() {
      if (!this.isEditMode && !this.canUploadImage) {
        console.warn('Cannot upload image: validation failed');
        return;
      }

      try {
        this.$emit('status-update', 'loading', this.isEditMode ? 'Updating image...' : 'Uploading image...');
        console.log('Preparing to upload image with:', {
          name: this.newImage.name,
          type: this.newImage.imageType,
          width: this.newImage.width,
          height: this.newImage.height,
          assetType: this.assetType,
          description: this.newImage.description,
          isEditMode: this.isEditMode,
          editImageId: this.editImageId
        });

        const formData = new FormData();
        formData.append('file', this.newImage.file);

        // Create URL with query parameters
        const queryParams = new URLSearchParams({
          width: this.newImage.width ? this.newImage.width.toString() : '0',
          height: this.newImage.height ? this.newImage.height.toString() : '0',
          imageType: this.newImage.imageType || '',
          assetType: this.assetType,
          name: this.newImage.name,
          description: this.newImage.description || ''
        });

        // If in edit mode, add the image ID to replace
        if (this.isEditMode && this.editImageId) {
          queryParams.append('replaceImageId', this.editImageId.toString());
        }

        const uploadUrl = `${URL_DOMAIN}/branding/image/upload?${queryParams.toString()}`;
        console.log('Uploading to URL:', uploadUrl);

        const response = await fetch(uploadUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
          body: formData,
        });

        console.log('Upload response status:', response.status);

        if (response.ok) {
          const imageUrl = await response.text();
          console.log('Upload successful, image URL:', imageUrl);
          await this.fetchImages();
          this.closeModal();
          this.$emit('status-update', 'success', this.isEditMode ? 'Brand image updated successfully' : 'Brand image uploaded successfully');
          // Re-emit images-loaded with the updated images list
          this.$emit('images-loaded', this.images);
        } else {
          const errorText = await response.text();
          console.error('Upload failed:', errorText);
          try {
            const errorData = errorText ? JSON.parse(errorText) : null;
            this.$emit('status-update', 'error', errorData?.error?.message || (this.isEditMode ? 'Failed to update brand image' : 'Failed to upload brand image'));
          } catch (e) {
            this.$emit('status-update', 'error', `${this.isEditMode ? 'Failed to update' : 'Failed to upload'} brand image: ${response.status} ${response.statusText}`);
          }
        }
      } catch (error) {
        console.error(this.isEditMode ? 'Error updating brand image:' : 'Error uploading brand image:', error);
        this.$emit('status-update', 'error', error.message || (this.isEditMode ? 'Failed to update brand image' : 'Failed to upload brand image'));
      }
    },
    resetForm() {
      this.newImage = {
        name: '',
        imageType: '',
        width: '',
        height: '',
        description: '',
        file: null,
        url: null,
      };
      this.canUploadImage = false;
      this.isEditMode = false;
      this.editImageId = null;
      this.selectedKlaviyoImage = null;

      // Reset file input
      if (this.$refs.imageInput) {
        this.$refs.imageInput.value = '';
      }
    },
    confirmDelete(id) {
      // Instead of the native confirm dialog, show our custom modal
      this.imageToDelete = id;
      this.showDeleteConfirm = true;
      // Prevent background scrolling when modal is open
      document.body.style.overflow = 'hidden';
    },
    cancelDelete() {
      this.showDeleteConfirm = false;
      this.imageToDelete = null;
      // Re-enable scrolling
      document.body.style.overflow = '';
    },
    proceedWithDelete() {
      if (this.imageToDelete) {
        this.deleteImage(this.imageToDelete);
        this.showDeleteConfirm = false;
        this.imageToDelete = null;
        // Re-enable scrolling
        document.body.style.overflow = '';
      }
    },
    async deleteImage(id, refreshAfterDelete = true) {
      try {
        this.$emit('status-update', 'loading', 'Deleting image...');
        console.log('Deleting image with ID:', id);

        const response = await fetch(`${URL_DOMAIN}/branding/images/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (response.ok) {
          console.log('Image deleted successfully');
          if (refreshAfterDelete) {
            await this.fetchImages();
            this.$emit('status-update', 'success', 'Brand image deleted successfully');
            // Re-emit images-loaded event after deletion to update counts
            this.$emit('images-loaded', this.images);
          }
          // Emit an event that the deletion was completed
          this.$emit('deletion-complete', id);
        } else {
          console.error('Failed to delete image:', response.status, response.statusText);
          this.$emit('status-update', 'error', 'Failed to delete brand image');
          throw new Error('Failed to delete image');
        }
      } catch (error) {
        console.error('Error deleting brand image:', error);
        this.$emit('status-update', 'error', 'Failed to delete brand image');
        throw error;
      }
    },
    getImageType(image) {
      // First try imageType, then try contentType (for backward compatibility), otherwise "Excluded"
      const imageType = image.imageType || image.contentType || '';

      // Verify this is a valid category (either default or custom)
      if (imageType && (this.allCategories.includes(imageType) || imageType === 'Other')) {
        return imageType;
      }

      return 'Excluded';
    },

    /**
     * Fetches images in the format required by the email editor
     * @param {Object} params - Parameters from the email editor
     * @param {number} params.page - Page number to fetch
     * @param {number} params.perPage - Number of items per page
     * @param {function} done - Callback function to return images
     */
    async getImagesForEmailEditor(params, done) {
      try {
        console.log('Email editor requested images with params:', params);
        const page = params.page || 1;
        const perPage = params.perPage || 20;
		const searchText = params.searchText || '';

        const response = await fetch(`${URL_DOMAIN}/branding/images?assetType=${this.assetType}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (!response.ok) {
          console.error('Failed to fetch images for email editor:', response.status);
          done([], { hasMore: false, page, perPage, total: 0 });
          return;
        }

        const allImages = await response.json();
        console.log(`Fetched ${allImages.length} images for email editor`);

        // Sort images by ID descending (most recent first)
        allImages.sort((a, b) => b.id - a.id);

        // Filter images based on searchText in friendlyname or description
        let filteredImages = allImages;
		console.log('searchText', searchText);
        if (searchText) {
          filteredImages = allImages.filter(image => {
            const friendlyName = (image.friendlyname || '').toLowerCase();
            const description = (image.description || '').toLowerCase();
            const search = searchText.toLowerCase();
            return friendlyName.includes(search) || description.includes(search);
          });
          console.log(`Filtered to ${filteredImages.length} images matching search: "${searchText}"`);
        }

        // Calculate pagination
        const startIndex = (page - 1) * perPage;
        const endIndex = startIndex + perPage;
        const images = filteredImages.slice(startIndex, endIndex);
        const hasMore = endIndex < filteredImages.length;
        const total = filteredImages.length;

        // Format images for email editor
        const formattedImages = images.map(image => ({
          id: image.id,
          location: image.url, // url is the field in our API, but location is what email editor expects
          width: image.width || 0,
          height: image.height || 0,
          contentType: image.contentType || 'image/png', // Support both field names
          source: 'user',
          name: image.friendlyname // Additional field that might be useful
        }));

        console.log(`Returning ${formattedImages.length} formatted images to email editor`);
        done(formattedImages, { hasMore, page, perPage, total });
      } catch (error) {
        console.error('Error fetching images for email editor:', error);
        done([], { hasMore: false, page: params.page || 1, perPage: params.perPage || 20, total: 0 });
      }
    },
    openTagSettings() {
      this.showTagSettings = true;
      // Prevent background scrolling when modal is open
      document.body.style.overflow = 'hidden';
    },
    closeTagSettings() {
      this.showTagSettings = false;
      // Re-enable scrolling
      document.body.style.overflow = '';
    },
    startEditingCategory(index, category) {
      this.editingCategoryIndex = index;
      this.editingCategoryValue = category;
    },
    saveEditedCategory() {
      if (this.editingCategoryIndex !== null) {
        this.customCategories[this.editingCategoryIndex] = this.editingCategoryValue;
        this.editingCategoryIndex = null;
      }
    },
    removeCategory(index) {
      this.customCategories.splice(index, 1);
    },
    addCategory() {
      if (this.newCategoryName.trim()) {
        this.customCategories.push(this.newCategoryName.trim());
        this.newCategoryName = '';
      }
    },
    saveTagSettings() {
      this.closeTagSettings();
      this.$emit('categories-updated', this.customCategories);
    },
    clearUpload() {
      this.newImage = { file: null, url: '', name: '', contentType: '' };
      if (this.$refs.imageInput) {
        this.$refs.imageInput.value = '';
      }
    },
    async generateDescription() {
      if (this.isGenerating) return;
      this.isGenerating = true;

      try {
        let imageUrl;
        let tempImageUrl = null;

        // Prepare status message
        this.$emit('status-update', 'loading', 'Preparing image for description...');

        // We need to get a proper hosted URL for the image, not a blob URL
        if (this.isEditMode) {
          // For edit mode, use the stored URL (already hosted)
          imageUrl = this.newImage.url;
        } else if (this.activeTab === 'klaviyo' && this.selectedKlaviyoImage) {
          // For Klaviyo images, we need to upload to our storage first
          // to get a proper URL that our backend can access
          const klaviyoImageUrl = this.selectedKlaviyoImage.attributes.image_url;

          if (!klaviyoImageUrl) {
            throw new Error('Klaviyo image URL is missing');
          }

          // Get the image through our proxy
          const proxyUrl = `${URL_DOMAIN}/klaviyo/proxy-image?url=${encodeURIComponent(klaviyoImageUrl)}`;
          console.log('Using proxy URL for image download:', proxyUrl);

          const response = await fetch(proxyUrl, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
          });

          if (!response.ok) {
            throw new Error(`Failed to download image from Klaviyo: ${response.status}`);
          }

          const imageBlob = await response.blob();

          // Create a File object from the blob
          const file = new File(
            [imageBlob],
            `temp_klaviyo_${this.selectedKlaviyoImage.id}.${this.selectedKlaviyoImage.attributes.format || 'png'}`,
            { type: imageBlob.type || 'image/png' }
          );

          // Upload the file temporarily to get a URL
          imageUrl = await this.uploadTemporaryImage(file);
          tempImageUrl = imageUrl; // Mark for cleanup later
        } else if (this.newImage.file) {
          // For file uploads, we need to upload it first to get a hosted URL
          imageUrl = await this.uploadTemporaryImage(this.newImage.file);
          tempImageUrl = imageUrl; // Mark for cleanup later
        } else {
          throw new Error('No image available for description generation');
        }

        // Now that we have a proper URL, generate the description
        this.$emit('status-update', 'loading', 'Generating AI description...');

        const response = await fetch(`${URL_DOMAIN}/branding/images/generate-description`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            imageUrl,
            imageType: this.newImage.imageType
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to generate description: ${response.status}`);
        }

        const data = await response.json();
        this.newImage.description = data.description;

        // Success message
        this.$emit('status-update', 'success', 'AI description generated successfully');
      } catch (error) {
        console.error('Error generating description:', error);
        this.$emit('status-update', 'error', 'Failed to generate image description: ' + error.message);
      } finally {
        this.isGenerating = false;
      }
    },

    async uploadTemporaryImage(file) {
      // Create form data
      const formData = new FormData();
      formData.append('file', file);

      // Add temporary flag
      const queryParams = new URLSearchParams({
        temp: 'true',
        width: this.newImage.width ? this.newImage.width.toString() : '0',
        height: this.newImage.height ? this.newImage.height.toString() : '0',
        name: 'temp_' + (file.name || 'image')
      });

      // Upload to our server to get a proper URL
      const uploadUrl = `${URL_DOMAIN}/branding/image/upload?${queryParams.toString()}`;
      console.log('Uploading temporary image for description:', uploadUrl);

      const response = await fetch(uploadUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to upload temporary image: ${response.status}`);
      }

      // Get the URL of the uploaded image
      const imageUrl = await response.text();
      console.log('Temporary image uploaded:', imageUrl);

      return imageUrl;
    },
    async fetchKlaviyoImages(refresh = false) {
      if (this.isLoadingKlaviyoImages && !refresh) return;

      this.isLoadingKlaviyoImages = true;
      try {
        // Reset pagination on refresh or search
        if (refresh) {
          this.klaviyoPrevPageCursor = null;
          this.klaviyoNextPageCursor = null;
          this.klaviyoPaginationHistory = [];
          this.currentKlaviyoPageIndex = 0;
        }

        // Construct the query parameters
        const params = new URLSearchParams();

        if (this.klaviyoSearchQuery) {
          params.append('nameFilter', this.klaviyoSearchQuery);
        }

        if (this.klaviyoPageSize) {
          params.append('pageSize', this.klaviyoPageSize);
        }

        const url = `${URL_DOMAIN}/klaviyo/images?${params.toString()}`;
        console.log('Fetching Klaviyo images from:', url);

        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (!response.ok) {
          console.error('Failed to fetch Klaviyo images:', response.status);
          this.klaviyoImages = [];
          this.klaviyoPrevPageCursor = null;
          this.klaviyoNextPageCursor = null;
          this.klaviyoPaginationHistory = [];
          this.currentKlaviyoPageIndex = 0;
        } else {
          const data = await response.json();
          console.log('Klaviyo images response:', data);

          // Extract images from the response
          if (data && data.data) {
            this.klaviyoImages = data.data;

            // For initial load, start fresh with pagination history
            this.klaviyoPaginationHistory = [{ data: data.data }];
            this.currentKlaviyoPageIndex = 0;

            // Extract pagination cursors from links
            if (data.links) {
              // For initial load, we don't have a previous page
              this.klaviyoPrevPageCursor = null;
              // Get next cursor from the response
              this.klaviyoNextPageCursor = data.links.next ? new URL(data.links.next).searchParams.get('page[cursor]') : null;

              // Store next cursor in pagination history
              this.klaviyoPaginationHistory[0].nextCursor = this.klaviyoNextPageCursor;
            } else {
              this.klaviyoPrevPageCursor = null;
              this.klaviyoNextPageCursor = null;
            }
          } else {
            this.klaviyoImages = [];
            this.klaviyoPrevPageCursor = null;
            this.klaviyoNextPageCursor = null;
            this.klaviyoPaginationHistory = [];
            this.currentKlaviyoPageIndex = 0;
          }
        }
      } catch (error) {
        console.error('Error fetching Klaviyo images:', error);
        this.klaviyoImages = [];
        this.klaviyoPrevPageCursor = null;
        this.klaviyoNextPageCursor = null;
        this.klaviyoPaginationHistory = [];
        this.currentKlaviyoPageIndex = 0;
      } finally {
        this.isLoadingKlaviyoImages = false;
      }
    },

    loadPreviousKlaviyoImages() {
      // If we're already at the beginning, we can't go back
      if (this.currentKlaviyoPageIndex <= 0) {
        console.log('Already at the first page');
        return;
      }

      // Move back in our history stack
      this.currentKlaviyoPageIndex--;

      // Get the page data from history
      const previousPage = this.klaviyoPaginationHistory[this.currentKlaviyoPageIndex];
      if (previousPage && previousPage.data) {
        // Just use the stored data
        this.klaviyoImages = previousPage.data;

        // Update cursor states based on current position
        this.klaviyoPrevPageCursor = this.currentKlaviyoPageIndex > 0 ? true : null;
        this.klaviyoNextPageCursor = previousPage.nextCursor || true;
      } else {
        console.error('Previous page data not found in history');
      }
    },

    loadNextKlaviyoImages() {
      if (!this.klaviyoNextPageCursor) {
        console.log('No next page available');
        return;
      }

      // If we're navigating forward after going back, use history if available
      if (this.currentKlaviyoPageIndex < this.klaviyoPaginationHistory.length - 1) {
        // We have this page already in history
        this.currentKlaviyoPageIndex++;
        const nextPage = this.klaviyoPaginationHistory[this.currentKlaviyoPageIndex];

        // Use the stored data
        this.klaviyoImages = nextPage.data;

        // Update cursor states
        this.klaviyoPrevPageCursor = true; // We can always go back if we're going forward in history
        this.klaviyoNextPageCursor = nextPage.nextCursor;
        return;
      }

      // Get the cursor for the next page
      const nextCursor = this.klaviyoNextPageCursor;

      // Fetch the next page from API
      this.isLoadingKlaviyoImages = true;
      const params = new URLSearchParams();

      if (this.klaviyoSearchQuery) {
        params.append('nameFilter', this.klaviyoSearchQuery);
      }

      if (this.klaviyoPageSize) {
        params.append('pageSize', this.klaviyoPageSize);
      }

      params.append('pageCursor', nextCursor);

      fetch(`${URL_DOMAIN}/klaviyo/images?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      })
      .then(response => {
        if (!response.ok) throw new Error('Failed to fetch next page');
        return response.json();
      })
      .then(data => {
        console.log('Next page data:', data);

        if (data && data.data) {
          // Update the displayed images
          this.klaviyoImages = data.data;

          // Move to the next position in history
          this.currentKlaviyoPageIndex++;

          // Add this page to history (truncate any forward history if we navigated back earlier)
          this.klaviyoPaginationHistory = this.klaviyoPaginationHistory.slice(0, this.currentKlaviyoPageIndex);
          this.klaviyoPaginationHistory.push({
            data: data.data,
            prevCursor: true, // We can always go back now
            nextCursor: data.links && data.links.next ?
              new URL(data.links.next).searchParams.get('page[cursor]') : null
          });

          // Update the cursors for UI
          this.klaviyoPrevPageCursor = true; // We can always go back now
          this.klaviyoNextPageCursor = data.links && data.links.next ?
            new URL(data.links.next).searchParams.get('page[cursor]') : null;
        }
      })
      .catch(error => {
        console.error('Error loading next page:', error);
        this.$emit('status-update', 'error', 'Failed to load next page');
      })
      .finally(() => {
        this.isLoadingKlaviyoImages = false;
      });
    },
    // For single image selection (used for editing)
    selectKlaviyoImage(image) {
      if (!image) return;

      // Set as the selected image
      this.selectedKlaviyoImage = image;

      // Populate the newImage object with Klaviyo image data
      const attributes = image.attributes;

      this.newImage = {
        ...this.newImage,
        name: attributes.name || `klaviyo_${image.id}`,
        url: attributes.image_url,
        width: attributes.width || 0,
        height: attributes.height || 0,
        original_url: attributes.image_url,
        description: this.newImage.description || '',
        imageSource: 'klaviyo',
        klaviyoId: image.id
      };

      // If we don't have width and height, calculate them
      if (!attributes.width || !attributes.height) {
        this.calculateImageDimensions(attributes.image_url);
      }
    },

    // For multi-select functionality
    toggleKlaviyoImageSelection(image) {
      if (!image) return;

      // Check if image is already selected
      const index = this.selectedKlaviyoImages.findIndex(selected => selected.id === image.id);

      if (index === -1) {
        // Add to selection
        this.selectedKlaviyoImages.push(image);
      } else {
        // Remove from selection
        this.selectedKlaviyoImages.splice(index, 1);
      }
    },

    isKlaviyoImageSelected(image) {
      return this.selectedKlaviyoImages.some(selected => selected.id === image.id);
    },

    clearKlaviyoSelection() {
      this.selectedKlaviyoImages = [];
    },

    async calculateImageDimensions(imageUrl) {
      try {
        // Using the proxy URL to avoid CORS issues
        const proxyUrl = `${URL_DOMAIN}/klaviyo/proxy-image?url=${encodeURIComponent(imageUrl)}`;
        console.log('Calculating dimensions using proxy URL:', proxyUrl);

        return new Promise((resolve, reject) => {
          const img = new Image();

          img.onload = () => {
            this.newImage.width = img.width;
            this.newImage.height = img.height;
            console.log(`Calculated image dimensions: ${img.width}x${img.height}`);
            resolve({ width: img.width, height: img.height });
          };

          img.onerror = (error) => {
            console.error('Error loading image for dimension calculation:', error);
            reject(error);
          };

          // Set authorization header for the proxy request
          // This is needed for the proxy endpoint but not supported directly on img.src
          // So we use fetch to get the blob and create an object URL
          fetch(proxyUrl, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
            }
          })
          .then(response => {
            if (!response.ok) throw new Error('Failed to load image');
            return response.blob();
          })
          .then(blob => {
            const objectUrl = URL.createObjectURL(blob);
            img.src = objectUrl;
            // Clean up object URL after loading or error
            img.onload = () => {
              URL.revokeObjectURL(objectUrl);
              this.newImage.width = img.width;
              this.newImage.height = img.height;
              console.log(`Calculated image dimensions: ${img.width}x${img.height}`);
              resolve({ width: img.width, height: img.height });
            };
            img.onerror = (error) => {
              URL.revokeObjectURL(objectUrl);
              console.error('Error loading image for dimension calculation:', error);
              reject(error);
            };
          })
          .catch(error => {
            console.error('Failed to fetch image for dimension calculation:', error);
            reject(error);
          });
        });
      } catch (error) {
        console.error('Error calculating image dimensions:', error);
        return { width: 0, height: 0 };
      }
    },
    async saveKlaviyoImage() {
      if (!this.selectedKlaviyoImage || !this.newImage.name) {
        this.$emit('status-update', 'error', 'Please provide a name for the image');
        return;
      }

      try {
        this.$emit('status-update', 'loading', 'Saving Klaviyo image...');

        // Log the Klaviyo image properties for debugging
        console.log('Selected Klaviyo image:', this.selectedKlaviyoImage);
        console.log('Image attributes:', this.selectedKlaviyoImage.attributes);
        console.log('Image URL property:', this.selectedKlaviyoImage.attributes.image_url);

        // First, we need to download the image from Klaviyo
        const imageUrl = this.selectedKlaviyoImage.attributes.image_url;

        if (!imageUrl) {
          throw new Error('Image URL is missing from Klaviyo image data');
        }

        // Download the image from Klaviyo
        this.$emit('status-update', 'loading', 'Downloading image from Klaviyo...');

        try {
          // Instead of direct fetch, use our backend API to proxy the image request
          // This avoids CORS issues when running locally
          const proxyUrl = `${URL_DOMAIN}/klaviyo/proxy-image?url=${encodeURIComponent(imageUrl)}`;
          console.log('Using proxy URL for image download:', proxyUrl);

          const imageResponse = await fetch(proxyUrl, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
          });

          if (!imageResponse.ok) {
            throw new Error(`Failed to download image from Klaviyo: ${imageResponse.status}`);
          }

          const imageBlob = await imageResponse.blob();

          // Create a File object from the blob
          const imageFile = new File(
            [imageBlob],
            `klaviyo_${this.selectedKlaviyoImage.id}.${this.selectedKlaviyoImage.attributes.format || 'png'}`,
            { type: imageBlob.type || 'image/png' }
          );

          console.log('Downloaded Klaviyo image as file:', imageFile);

          // Now use the same upload mechanism as for regular uploaded files
          const formData = new FormData();
          formData.append('file', imageFile);

          // Create URL with query parameters
          const queryParams = new URLSearchParams({
            width: this.newImage.width ? this.newImage.width.toString() : '0',
            height: this.newImage.height ? this.newImage.height.toString() : '0',
            imageType: this.newImage.imageType || '',
            assetType: this.assetType,
            name: this.newImage.name,
            description: this.newImage.description || ''
          });

          const uploadUrl = `${URL_DOMAIN}/branding/image/upload?${queryParams.toString()}`;
          console.log('Uploading Klaviyo image to:', uploadUrl);

          const response = await fetch(uploadUrl, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
            body: formData,
          });

          if (response.ok) {
            const savedImageUrl = await response.text();
            console.log('Klaviyo image saved successfully:', savedImageUrl);
            await this.fetchImages(); // Refresh the main images list
            this.closeModal();
            this.$emit('status-update', 'success', 'Klaviyo image saved successfully');
            this.$emit('images-loaded', this.images);
          } else {
            const errorText = await response.text();
            console.error('Failed to save Klaviyo image:', errorText);
            try {
              const errorData = errorText ? JSON.parse(errorText) : null;
              this.$emit('status-update', 'error', errorData?.error?.message || 'Failed to save Klaviyo image');
            } catch (e) {
              this.$emit('status-update', 'error', `Failed to save Klaviyo image: ${response.status} ${response.statusText}`);
            }
          }
        } catch (downloadError) {
          console.error('Error downloading image from Klaviyo:', downloadError);
          this.$emit('status-update', 'error', `Failed to download image from Klaviyo: ${downloadError.message}`);
        }
      } catch (error) {
        console.error('Error saving Klaviyo image:', error);
        this.$emit('status-update', 'error', error.message || 'Failed to save Klaviyo image');
      }
    },
    deselectKlaviyoImage() {
      // Create a smoother transition between views
      // First mark that we're transitioning
      this.isTransitioningFromSelected = true;

      // Add fade-out animation for the selected image
      const selectedImageElement = document.querySelector('.animate-selected-image-appear');
      if (selectedImageElement) {
        // Add fade-out animation
        selectedImageElement.style.opacity = '0';
        selectedImageElement.style.transform = 'scale(0.8)';
        selectedImageElement.style.transition = 'opacity 0.35s ease-out, transform 0.35s ease-out';
      }

      // Wait for animation to complete before clearing selection
      setTimeout(() => {
        this.selectedKlaviyoImage = null;

        // Wait a tiny bit before showing the grid (gives DOM time to update)
        setTimeout(() => {
          // Fade in the grid
          this.isTransitioningFromSelected = false;
        }, 80);
      }, 350);
    },

    // Helper method for temporary image upload
    async uploadTemporaryImage(file) {
      // Create form data
      const formData = new FormData();
      formData.append('file', file);

      // Add temporary flag
      const queryParams = new URLSearchParams({
        temp: 'true',
        width: '0',
        height: '0',
        name: 'temp_' + (file.name || 'image')
      });

      // Upload to our server to get a proper URL
      const uploadUrl = `${URL_DOMAIN}/branding/image/upload?${queryParams.toString()}`;
      console.log('Uploading temporary image for description:', uploadUrl);

      const response = await fetch(uploadUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to upload temporary image: ${response.status}`);
      }

      // Get the URL of the uploaded image
      const imageUrl = await response.text();
      console.log('Temporary image uploaded:', imageUrl);

      return imageUrl;
    },

    // Bulk Upload Methods
    triggerBulkFileInput() {
      if (this.$refs.bulkImageInput) {
        this.$refs.bulkImageInput.click();
      } else {
        console.error('Bulk image input reference not found');
      }
    },

    handleBulkImageUpload(event) {
      const files = event.target.files;
      if (files && files.length) {
        // Convert FileList to Array and append to existing bulk images
        const newFiles = Array.from(files);

        // Filter out any files that exceed size limit (10MB)
        const validFiles = newFiles.filter(file => file.size <= 10 * 1024 * 1024);
        const invalidFiles = newFiles.filter(file => file.size > 10 * 1024 * 1024);

        if (invalidFiles.length > 0) {
          this.bulkUploadStatus = `${invalidFiles.length} file(s) exceed the 10MB size limit and were excluded.`;
          this.bulkUploadStatusType = 'error';
        } else {
          this.bulkUploadStatus = '';
        }

        // Add valid files to bulk images array
        this.bulkImages.push(...validFiles);

        // Create a dimensions object to store dimensions for each file
        if (!this.bulkImageDimensions) {
          this.bulkImageDimensions = {};
        }

        // Generate preview URLs and detect dimensions for each file
        validFiles.forEach(file => {
          const reader = new FileReader();
          reader.onload = (e) => {
            // In Vue 3, directly modify the object property
            this.bulkPreviewUrls[file.name] = e.target.result;

            // Detect image dimensions using Image object
            const img = new Image();
            img.onload = () => {
              this.bulkImageDimensions[file.name] = {
                width: img.width,
                height: img.height
              };
              console.log(`Detected dimensions for ${file.name}: ${img.width}x${img.height}`);
            };
            img.src = e.target.result;
          };
          reader.readAsDataURL(file);
        });
      }
    },

    getPreviewUrl(file) {
      return this.bulkPreviewUrls[file.name] || '';
    },

    formatFileSize(size) {
      if (size < 1024) {
        return size + ' bytes';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(1) + ' KB';
      } else {
        return (size / (1024 * 1024)).toFixed(1) + ' MB';
      }
    },

    removeBulkImage(index) {
      // Get the file to be removed
      const file = this.bulkImages[index];

      // Remove the file from the array
      this.bulkImages.splice(index, 1);

      if (file && file.name) {
        // Clean up all related data for this file
        if (this.bulkPreviewUrls[file.name]) {
          delete this.bulkPreviewUrls[file.name];
        }

        if (this.bulkImageDescriptions[file.name]) {
          delete this.bulkImageDescriptions[file.name];
        }

        if (this.bulkImageDimensions[file.name]) {
          delete this.bulkImageDimensions[file.name];
        }
      }

      // Clear status if no files left
      if (this.bulkImages.length === 0) {
        this.bulkUploadStatus = '';
      }
    },

    clearBulkUpload() {
      this.bulkImages = [];
      this.bulkPreviewUrls = {};
      this.bulkUploadStatus = '';
      this.bulkImageDescriptions = {};
      this.bulkImageDimensions = {};
      this.bulkDescriptionsGenerated = false;

      // Also reset the file input
      if (this.$refs.bulkImageInput) {
        this.$refs.bulkImageInput.value = '';
      }
    },

    async generateBulkDescriptions() {
      if (this.bulkImages.length === 0) {
        this.$emit('status-update', 'error', 'No images available for description generation');
        return;
      }

      this.isBulkGenerating = true;
      this.bulkGeneratingCount = 0;
      this.bulkUploadStatus = `Generating descriptions for images...`;
      this.bulkUploadStatusType = 'info';

      try {
        // Create a new object to store descriptions
        const descriptions = {};

        // Process each image sequentially
        for (let i = 0; i < this.bulkImages.length; i++) {
          const file = this.bulkImages[i];
          const imageName = file.name.replace(/\.[^/.]+$/, "");

          try {
            this.bulkGeneratingCount = i + 1;

            // First, we need to upload the file temporarily to get a URL that the AI can access
            let imageUrl = await this.uploadTemporaryImage(file);

            // Now use the server endpoint to generate a description
            const response = await fetch(`${URL_DOMAIN}/branding/images/generate-description`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                imageUrl,
                imageType: this.bulkUploadImageType
              })
            });

            if (response.ok) {
              const data = await response.json();
              descriptions[file.name] = data.description;
            } else {
              descriptions[file.name] = "Failed to generate description for this image.";
            }
          } catch (error) {
            console.error(`Error generating description for "${file.name}":`, error);
            descriptions[file.name] = "Error generating description.";
          }
        }

        // Update bulk descriptions
        this.bulkImageDescriptions = descriptions;
        this.bulkDescriptionsGenerated = true;

        // Set the upload description to a summary
        this.bulkUploadDescription = "AI-generated descriptions for each image will be applied during upload.";

        // Success message
        this.bulkUploadStatus = `Successfully generated descriptions for ${Object.keys(descriptions).length} images.`;
        this.bulkUploadStatusType = 'info';

      } catch (error) {
        console.error('Error generating descriptions:', error);
        this.bulkUploadStatus = `Error generating descriptions: ${error.message || 'Unknown error'}`;
        this.bulkUploadStatusType = 'error';
      } finally {
        this.isBulkGenerating = false;
        this.bulkGeneratingCount = 0;
      }
    },

    async uploadBulkImages() {
      if (this.bulkImages.length === 0) {
        this.bulkUploadStatus = 'Please select at least one image to upload.';
        this.bulkUploadStatusType = 'error';
        return;
      }

      this.isBulkUploading = true;
      this.bulkUploadProgress = 0;
      this.bulkUploadStatus = `Preparing to upload ${this.bulkImages.length} images...`;
      this.bulkUploadStatusType = 'info';

      try {
        const successfulUploads = [];
        const failedUploads = [];

        // Phase 1: Generate AI descriptions for all images first
        this.bulkUploadStatus = `Generating AI descriptions (0/${this.bulkImages.length})...`;

        for (let i = 0; i < this.bulkImages.length; i++) {
          const file = this.bulkImages[i];

          // Skip if we already have a description for this image
          if (this.bulkImageDescriptions[file.name]) {
            continue;
          }

          try {
            // Update status
            this.bulkUploadStatus = `Generating AI descriptions (${i+1}/${this.bulkImages.length})...`;

            // First, upload the file temporarily to get a URL that the AI can access
            let imageUrl = await this.uploadTemporaryImage(file);

            // Now use the server endpoint to generate a description
            const response = await fetch(`${URL_DOMAIN}/branding/images/generate-description`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                imageUrl,
                imageType: this.bulkUploadImageType
              })
            });

            if (response.ok) {
              const data = await response.json();
              this.bulkImageDescriptions[file.name] = data.description;
              console.log(`Generated description for ${file.name}: ${data.description.substring(0, 50)}...`);
            } else {
              console.warn(`Failed to generate description for ${file.name}`);
            }
          } catch (error) {
            console.error(`Error generating description for "${file.name}":`, error);
          }

          // Update progress
          this.bulkUploadProgress = ((i + 1) / this.bulkImages.length) * 50; // First 50% for descriptions
        }

        // Phase 2: Upload the images with their descriptions
        this.bulkUploadStatus = `Uploading images (0/${this.bulkImages.length})...`;

        // Process each image sequentially to avoid overwhelming the server
        for (let i = 0; i < this.bulkImages.length; i++) {
          const file = this.bulkImages[i];
          // Get the filename without extension to use as image name
          const imageName = file.name.replace(/\.[^/.]+$/, "");

          try {
            // Get the AI-generated description or use an empty string if not available
            const description = this.bulkImageDescriptions[file.name] || '';

            // Get dimensions if available
            const dimensions = this.bulkImageDimensions[file.name] || { width: 0, height: 0 };

            // Create URL with query parameters for this specific image
            const queryParams = new URLSearchParams({
              width: String(dimensions.width || 0),
              height: String(dimensions.height || 0),
              imageType: this.bulkUploadImageType || '',
              assetType: this.assetType,
              name: imageName,
              description: description
            });

            const formData = new FormData();
            formData.append('file', file);

            const uploadUrl = `${URL_DOMAIN}/branding/image/upload?${queryParams.toString()}`;

            const response = await fetch(uploadUrl, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
              },
              body: formData,
            });

            if (response.ok) {
              const imageUrl = await response.text();
              successfulUploads.push({
                name: imageName,
                url: imageUrl,
                originalFileName: file.name
              });
            } else {
              failedUploads.push({
                name: file.name,
                error: `Status ${response.status}`
              });
            }
          } catch (error) {
            console.error(`Error uploading file "${file.name}":`, error);
            failedUploads.push({
              name: file.name,
              error: error.message || 'Unknown error'
            });
          }

          // Update progress (second 50% for uploads)
          this.bulkUploadProgress = 50 + ((i + 1) / this.bulkImages.length) * 50;
          this.bulkUploadStatus = `Uploading images (${i + 1}/${this.bulkImages.length})...`;
        }

        // Final status update
        if (failedUploads.length === 0) {
          const descriptionsCount = Object.keys(this.bulkImageDescriptions).length;
          this.bulkUploadStatus = `Success! Uploaded ${successfulUploads.length} images with AI-generated descriptions.`;
          this.bulkUploadStatusType = 'info';

          // Clear the bulk upload data since we're done
          this.clearBulkUpload();

          // Refresh the images list to include the new uploads
          await this.fetchImages();

          // After a short delay, close the modal
          setTimeout(() => {
            this.closeModal();
          }, 2000);
        } else {
          // Some uploads failed
          this.bulkUploadStatus = `Uploaded ${successfulUploads.length} images with AI descriptions, but ${failedUploads.length} uploads failed.`;
          this.bulkUploadStatusType = 'error';
        }
      } catch (error) {
        console.error('Error during bulk upload:', error);
        this.bulkUploadStatus = `Error during upload: ${error.message || 'Unknown error'}`;
        this.bulkUploadStatusType = 'error';
      } finally {
        this.isBulkUploading = false;

        // Re-emit images-loaded with the updated images list
        this.$emit('images-loaded', this.images);
      }
    },

    previewBulkImageDescription(fileName) {
      const description = this.bulkImageDescriptions[fileName];
      if (description) {
        // Simple alert for now, but you could enhance this with a modal if needed
        alert(`Description for ${fileName}:\n\n${description}`);
      }
    },

    async processSelectedKlaviyoImages() {
      if (this.selectedKlaviyoImages.length === 0) {
        this.$emit('status-update', 'error', 'No images selected for import');
        return;
      }

      this.isProcessingKlaviyoImages = true;
      this.klaviyoImagesProgress = 0;
      this.bulkUploadStatus = `Processing ${this.selectedKlaviyoImages.length} Klaviyo images...`;
      this.bulkUploadStatusType = 'info';

      try {
        const successfulImports = [];
        const failedImports = [];

        // Phase 1: Generate AI descriptions for all selected Klaviyo images
        this.bulkUploadStatus = `Generating AI descriptions (0/${this.selectedKlaviyoImages.length})...`;

        for (let i = 0; i < this.selectedKlaviyoImages.length; i++) {
          const image = this.selectedKlaviyoImages[i];
          const imageUrl = image.attributes.image_url;

          if (!imageUrl) {
            console.warn(`Klaviyo image ${image.id} has no URL, skipping description generation`);
            continue;
          }

          try {
            // Update status
            this.bulkUploadStatus = `Generating AI descriptions (${i+1}/${this.selectedKlaviyoImages.length})...`;

            // Get the image through our proxy
            const proxyUrl = `${URL_DOMAIN}/klaviyo/proxy-image?url=${encodeURIComponent(imageUrl)}`;

            const response = await fetch(proxyUrl, {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
              },
            });

            if (!response.ok) {
              console.warn(`Failed to download image from Klaviyo: ${response.status}`);
              continue;
            }

            const imageBlob = await response.blob();

            // Create a File object from the blob
            const file = new File(
              [imageBlob],
              `temp_klaviyo_${image.id}.${image.attributes.format || 'png'}`,
              { type: imageBlob.type || 'image/png' }
            );

            // Upload the file temporarily to get a URL
            const tempImageUrl = await this.uploadTemporaryImage(file);

            // Generate description
            const descResponse = await fetch(`${URL_DOMAIN}/branding/images/generate-description`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                imageUrl: tempImageUrl,
                imageType: this.bulkUploadImageType || '' // Use selected image type
              })
            });

            if (descResponse.ok) {
              const data = await descResponse.json();
              this.klaviyoImageDescriptions[image.id] = data.description;
              console.log(`Generated description for Klaviyo image ${image.id}`);
            }
          } catch (error) {
            console.error(`Error generating description for Klaviyo image ${image.id}:`, error);
          }

          // Update progress (first 50% for descriptions)
          this.klaviyoImagesProgress = ((i + 1) / this.selectedKlaviyoImages.length) * 50;
        }

        // Phase 2: Import the images with their descriptions
        this.bulkUploadStatus = `Importing Klaviyo images (0/${this.selectedKlaviyoImages.length})...`;

        for (let i = 0; i < this.selectedKlaviyoImages.length; i++) {
          const image = this.selectedKlaviyoImages[i];
          const attributes = image.attributes;
          const imageUrl = attributes.image_url;

          if (!imageUrl) {
            failedImports.push({
              id: image.id,
              name: attributes.name || `klaviyo_${image.id}`,
              error: 'Missing image URL'
            });
            continue;
          }

          try {
            // Update status
            this.bulkUploadStatus = `Importing Klaviyo images (${i+1}/${this.selectedKlaviyoImages.length})...`;

            // Get the image through our proxy
            const proxyUrl = `${URL_DOMAIN}/klaviyo/proxy-image?url=${encodeURIComponent(imageUrl)}`;

            const response = await fetch(proxyUrl, {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
              },
            });

            if (!response.ok) {
              failedImports.push({
                id: image.id,
                name: attributes.name || `klaviyo_${image.id}`,
                error: `Download failed with status ${response.status}`
              });
              continue;
            }

            const imageBlob = await response.blob();

            // Now let's detect the image dimensions from the blob
            try {
              // Create an object URL from the blob
              const objectUrl = URL.createObjectURL(imageBlob);

              // Wait for the image to load to get dimensions
              await new Promise(resolve => {
                const img = new Image();
                img.onload = () => {
                  // Store the dimensions for this image
                  if (!this.klaviyoImageDimensions) {
                    this.klaviyoImageDimensions = {};
                  }

                  this.klaviyoImageDimensions[image.id] = {
                    width: img.width,
                    height: img.height
                  };

                  console.log(`Detected dimensions for Klaviyo image ${image.id}: ${img.width}x${img.height}`);

                  // Clean up the object URL
                  URL.revokeObjectURL(objectUrl);
                  resolve();
                };

                img.onerror = () => {
                  console.warn(`Failed to load image for dimension detection: ${image.id}`);
                  URL.revokeObjectURL(objectUrl);
                  resolve();
                };

                img.src = objectUrl;
              });
            } catch (error) {
              console.warn(`Error detecting dimensions for image ${image.id}:`, error);
            }

            // Create a File object from the blob
            const file = new File(
              [imageBlob],
              `klaviyo_${image.id}.${attributes.format || 'png'}`,
              { type: imageBlob.type || 'image/png' }
            );

            // Prepare form data for upload
            const formData = new FormData();
            formData.append('file', file);

            // Get the AI-generated description if available
            const description = this.klaviyoImageDescriptions[image.id] || '';

            // Get dimensions if available or calculate them
            const width = attributes.width || 0;
            const height = attributes.height || 0;

            // Get a clean name for the image from Klaviyo
            // Use the name from Klaviyo if available, otherwise create a name from the ID
            let imageName = attributes.name;

            // If no name is provided or it's empty, use a default with the Klaviyo ID
            if (!imageName || imageName.trim() === '') {
              imageName = `klaviyo_image_${image.id}`;
            }

            // We need to detect the actual dimensions for the image
            // Let's create a temporary Image object to get dimensions
            let detectedWidth = width;
            let detectedHeight = height;

            // If we don't have valid dimensions yet, try to detect them
            if (!detectedWidth || !detectedHeight || detectedWidth === 0 || detectedHeight === 0) {
              // Store the detected dimensions in the object for future use
              if (!this.klaviyoImageDimensions) {
                this.klaviyoImageDimensions = {};
              }

              if (this.klaviyoImageDimensions[image.id]) {
                // Use cached dimensions if available
                detectedWidth = this.klaviyoImageDimensions[image.id].width || 0;
                detectedHeight = this.klaviyoImageDimensions[image.id].height || 0;
                console.log(`Using cached dimensions for ${imageName}: ${detectedWidth}x${detectedHeight}`);
              }
            }

            console.log(`Dimensions for ${imageName}: ${detectedWidth}x${detectedHeight}`);

            // Create URL with query parameters
            const queryParams = new URLSearchParams({
              width: String(detectedWidth || 0),
              height: String(detectedHeight || 0),
              imageType: this.bulkUploadImageType || '', // Use the same image type as bulk uploads if set
              assetType: this.assetType,
              name: imageName,
              description: description
            });

            const uploadUrl = `${URL_DOMAIN}/branding/image/upload?${queryParams.toString()}`;

            const uploadResponse = await fetch(uploadUrl, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
              },
              body: formData,
            });

            if (uploadResponse.ok) {
              const savedImageUrl = await uploadResponse.text();
              successfulImports.push({
                id: image.id,
                name: attributes.name || `klaviyo_${image.id}`,
                url: savedImageUrl
              });
            } else {
              failedImports.push({
                id: image.id,
                name: attributes.name || `klaviyo_${image.id}`,
                error: `Upload failed with status ${uploadResponse.status}`
              });
            }
          } catch (error) {
            console.error(`Error importing Klaviyo image ${image.id}:`, error);
            failedImports.push({
              id: image.id,
              name: attributes.name || `klaviyo_${image.id}`,
              error: error.message || 'Unknown error'
            });
          }

          // Update progress (second 50% for uploads)
          this.klaviyoImagesProgress = 50 + ((i + 1) / this.selectedKlaviyoImages.length) * 50;
        }

        // Final status update
        if (failedImports.length === 0) {
          this.bulkUploadStatus = `Success! Imported all ${successfulImports.length} Klaviyo images with AI descriptions.`;
          this.bulkUploadStatusType = 'info';

          // Clear selections
          this.selectedKlaviyoImages = [];
          this.klaviyoImageDescriptions = {};

          // Refresh the images list to include the new imports
          await this.fetchImages();

          // After a short delay, close the modal
          setTimeout(() => {
            this.closeModal();
          }, 2000);
        } else {
          // Some imports failed
          this.bulkUploadStatus = `Imported ${successfulImports.length} Klaviyo images, but ${failedImports.length} failed.`;
          this.bulkUploadStatusType = 'error';
        }
      } catch (error) {
        console.error('Error during Klaviyo image import:', error);
        this.bulkUploadStatus = `Error during Klaviyo image import: ${error.message || 'Unknown error'}`;
        this.bulkUploadStatusType = 'error';
      } finally {
        this.isProcessingKlaviyoImages = false;
        this.klaviyoImagesProgress = 0;

        // Re-emit images-loaded with the updated images list
        this.$emit('images-loaded', this.images);
      }
    }
  }
};
</script>

<style scoped>
/* Modal animations */
.animate-modal-appear {
  animation: modal-appear 0.3s ease-out forwards;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Selected image animation */
.animate-selected-image-appear {
  animation: selected-image-appear 0.4s ease-out forwards;
}

@keyframes selected-image-appear {
  from {
    opacity: 0.5;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Grid reveal animation */
@keyframes grid-appear {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.grid-appear {
  animation: grid-appear 0.4s ease-out forwards;
}

/* Optional: Transitions and animations */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* Image selection transition helpers */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.transition-opacity {
  transition-property: opacity, height, margin, padding;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
</style>
