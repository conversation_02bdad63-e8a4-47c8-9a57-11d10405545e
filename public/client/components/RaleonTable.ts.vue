<template>
	<div class="flex flex-col h-full">
		<div class="flex flex-col">
			<div class="w-full grid" v-if="!isLoading">
				<div v-if="!collapseOnMobile || dynamicGrid > 1"
					class="grid gap-4 text-sm font-['Inter'] uppercase font-semibold px-4 py-2"
					:style="{'grid-template-columns': forcedEqualWidth ? `repeat(${dynamicGrid}, minmax(0, 1fr))` : `repeat(${dynamicGrid}, 1fr)`}">
					<div v-for="header in columnHeaders" :key="header.value" class="flex flex-col sm:flex-row">
						<span>{{ header.name }}</span>
						<!--
						<img v-if="header.tooltip" :id="`tooltip-trigger-${header.name.replace(/\s/g, '')}`"
							src="../../client-old/images/help-circle.svg" class="pl-3 tooltip-trigger" alt="help" width="28"
							height="28" />

						<div :id="`tooltip-body-${header.name.replace(/\s/g, '')}`" role="tooltip" class="tooltip-body"
							style="">
							{{ header.tooltip }}
							<div class="tooltip-arrow" data-popper-arrow></div>
						</div>
						-->
					</div>
				</div>


				<div v-for="row in rowData" role="button" tabindex="0" :key="row" @click="$emit('row-clicked', row)"
					:class="{'cursor-pointer': clickableRows}"
					class="grid gap-4 border font-['Inter'] border-ralprimary-light border-opacity-50 rounded-2xl mb-2 bg-white bg-opacity-75 hover:border-opacity-75 hover:shadow-lg transition-all duration-300"
					:style="{'grid-template-columns': forcedEqualWidth ? `repeat(${dynamicGrid}, minmax(0, 1fr))` : `repeat(${dynamicGrid}, 1fr)`}">
					<!-- <template > -->
					<div v-for="(column, index) in this.columnHeaderValues" :key="column"
						class="px-4 py-2 flex text-base" :class="{'font-semibold': index === 0}"
						:style="{'overflow-wrap': autoWrap ? 'anywhere' : undefined}">

						<div v-if="showActiveOrNot && index === 0" class="px-4 py-2 flex text-base"
							:class="{'font-semibold': index === 0}"
							:style="{'overflow-wrap': autoWrap ? 'anywhere' : undefined}">
							<span v-if="showActiveOrNot && row.active"
								class="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>
							<span v-else-if="showActiveOrNot && !row.active"
								class="inline-block w-3 h-3 bg-red-500 rounded-full mr-2"></span>
						</div>


						<a
							v-if="row.href && index === 0"
							class="flex w-fit"
							:href="row.href"
							@click.prevent="$router.push(row.href)"
							:target="row.target || '_blank'">
							<span
								class="first:hover:underline first:hover:text-ralprimary-main group first:hover:cursor-pointer"
								:class="{[row[column]?.color]: (!!row[column]?.color)}">
								{{ row[column].value ? row[column].value : row[column] }}
								<img v-if="row.href && index == 0 && row.target == '_blank'"
									src="../../client-old/images/external-link-out.svg"
									class="invisible group-hover:visible w-3 h-3 float-right" style="margin-top: 3px"
									:class="getHrefStyle(row)" />
							</span>
						</a>
						<div v-if="!row.href || index !== 0" class="flex">
							<span :class="{[row[column]?.color]: (!!row[column]?.color)}">
								{{ row[column]?.value ? row[column].value : row[column] }}
							</span>
						</div>
						<div v-if="row.showDelete && index == this.columnHeaderValues.length - 1" class="flex justify-end ml-auto">
							<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" class="cursor-pointer delete-icon" @click.stop="$emit('delete-row-clicked', row)">
								<path d="M280-120q-33 0-56.5-23.5T200-200v-520h-40v-80h200v-40h240v40h200v80h-40v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM360-280h80v-360h-80v360Zm160 0h80v-360h-80v360ZM280-720v520-520Z"/>
							</svg>
						</div>
					</div>
					<div v-if="showCustomButtons" class="flex">
						<button v-if="row.showCustomButton"
							class="raleon-table-delete-row btn my-auto ml-4 border-slate-200 hover:border-slate-300"
							@click.stop="$emit('custom-button-row-clicked', row)">
							{{ row.showCustomButton }}
						</button>
					</div>
					<div v-if="collapseOnMobile && dynamicGrid === 1" class="border-b border-slate-200"></div>
					<!-- </template> -->
				</div>
			</div>
			<div class="bg-white p-2 sm:p-4 sm:h-64 flex flex-col sm:flex-row gap-5 select-none" v-if="isLoading">
				<div class="flex flex-col flex-1 gap-5 sm:p-2">
					<div class="flex flex-1 flex-col gap-5">
						<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
						<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
						<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
						<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
						<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
						<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>


<script>

import { createPopper } from '@popperjs/core';

export default {
	name: 'RaleonTable',
	props: ['columnHeaders', 'rowData', 'showDelete', 'isLoading', 'showActiveOrNot',
		'showCustomButtons', 'collapseOnMobile', 'forcedEqualWidth', 'autoWrap', 'clickableRows'
	],
	emits: ['delete-row-clicked', 'custom-button-row-clicked', 'row-clicked'],
	data() {
		return {
			icons: {}
		}
	},
	components: {},
	computed: {
		columnHeaderValues() {
			return this.columnHeaders.map(header => header.value);
		},
		dynamicGrid() {
			if (this.collapseOnMobile && window.outerWidth < 500) {
				return 1;
			}

			return this.columnHeaders.length + (this.showCustomButtons === true ? 1 : 0);
		},
	},
	methods: {
		getIcon(iconName) {
			console.log('getting icons');
			return this.icons[`../../client-old/images/table-icons/${iconName}`];
		},
		navigate(href) {
			if (href.startsWith('http')) {
				window.open(href, '_blank');
				return;
			}
			let route = this.$router.resolve({ path: href });
			window.open(route.href, '_blank');
		},
		getHrefStyle(row) {
			return row.hrefStyle || '';
		},
		setupTooltips() {
			const triggers = document.querySelectorAll('.tooltip-trigger');
			triggers.forEach(trigger => {
				const triggerId = trigger.getAttribute('id');
				const tooltipId = triggerId.replace('-trigger-', '-body-');
				const tooltip = document.querySelector(`#${tooltipId}`);

				const popperInstance = createPopper(trigger, tooltip, {
					placement: 'top',
					modifiers: [
						{
							name: 'offset',
							options: {
								offset: [0, 8],
							},
						},
					],
				});

				function show() {
					// Make the tooltip visible
					tooltip.setAttribute('data-show', '');

					// Enable the event listeners
					popperInstance.setOptions((options) => ({
						...options,
						modifiers: [
							...options.modifiers,
							{ name: 'eventListeners', enabled: true },
						],
					}));

					// Update its position
					popperInstance.update();
				}

				function hide() {
					// Hide the tooltip
					tooltip.removeAttribute('data-show');

					// Disable the event listeners
					popperInstance.setOptions((options) => ({
						...options,
						modifiers: [
							...options.modifiers,
							{ name: 'eventListeners', enabled: false },
						],
					}));
				}

				const showEvents = ['mouseenter', 'focus'];
				const hideEvents = ['mouseleave', 'blur'];

				showEvents.forEach((event) => {
					trigger.addEventListener(event, show);
				});

				hideEvents.forEach((event) => {
					trigger.addEventListener(event, hide);
				});
			});
		}
	},
	async beforeCreate() {
		console.log('before create');
		this.icons = import.meta.glob('../../client-old/images/table-icons/*.svg', {
			as: 'raw',
			eager: true,
		});
	},
	async mounted() {
		this.setupTooltips();
	},
}
</script>

<style scoped>
.raleon-table-container {
	/* background-color: white;
	box-shadow: 0 1px 5px rgba(64, 15, 146, 0.5); */
}

thead {
	border-bottom: solid 1px lightgrey;
}

.tooltip-body {
	display: none;
}

.tooltip-body[data-show] {
	display: inline;
}

.tooltip-body {
	background: #333;
	color: white;
	font-weight: bold;
	padding: 4px 8px;
	font-size: 13px;
	border-radius: 4px;
	display: none;
}

.tooltip-body[data-show] {
	display: block;
}

.tooltip-arrow,
.tooltip-arrow::before {
	position: absolute;
	width: 8px;
	height: 8px;
	background: inherit;
}

.tooltip-arrow {
	visibility: hidden;
	padding-left: 5px;
}

.tooltip-arrow::before {
	visibility: visible;
	content: '';
	transform: rotate(45deg);
}

.tooltip-body[data-popper-placement^='top']>#arrow {
	bottom: -4px;
}

.tooltip-body[data-popper-placement^='bottom']>#arrow {
	top: -4px;
}

.tooltip-body[data-popper-placement^='left']>#arrow {
	right: -4px;
}

.tooltip-body[data-popper-placement^='right']>#arrow {
	left: -4px;
}

.trow-single {
	background: rgba(255, 255, 255, 0.75);
	box-shadow: 0px 2px 6px 0px rgba(13, 10, 44, 0.08);
}

.circle {
	display: inline-block;
	width: 10px;
	/* Circle size */
	height: 10px;
	/* Circle size */
	border-radius: 50%;
	/* Makes it round */
	margin-right: 8px;
	/* Space between circle and text */
}

.circle.green {
	background-color: green;
	/* Green circle for active */
}

.circle.red {
	background-color: red;
	/* Red circle for inactive */
}
</style>
