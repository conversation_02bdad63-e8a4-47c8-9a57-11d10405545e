<template>
	<svg v-if="name === 'chat'" class="flex-shrink-0" xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px" fill="#FFFFFF">
		<path d="m240-240-92 92q-19 19-43.5 8.5T80-177v-623q0-33 23.5-56.5T160-880h640q33 0 56.5 23.5T880-800v480q0 33-23.5 56.5T800-240H240Zm-34-80h594v-480H160v525l46-45Zm-46 0v-480 480Zm120-80h240q17 0 28.5-11.5T560-440q0-17-11.5-28.5T520-480H280q-17 0-28.5 11.5T240-440q0 17 11.5 28.5T280-400Zm0-120h400q17 0 28.5-11.5T720-560q0-17-11.5-28.5T680-600H280q-17 0-28.5 11.5T240-560q0 17 11.5 28.5T280-520Zm0-120h400q17 0 28.5-11.5T720-680q0-17-11.5-28.5T680-720H280q-17 0-28.5 11.5T240-680q0 17 11.5 28.5T280-640Z"/>
	</svg>
	<svg v-else-if="name === 'free-product-reward'" class="flex-shrink-0" fill="#ffffff" xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px">
		<path d="M160-160v-360q-33 0-56.5-23.5T80-600v-80q0-33 23.5-56.5T160-760h128q-5-9-6.5-19t-1.5-21q0-50 35-85t85-35q23 0 43 8.5t37 23.5q17-16 37-24t43-8q50 0 85 35t35 85q0 11-2 20.5t-6 19.5h128q33 0 56.5 23.5T880-680v80q0 33-23.5 56.5T800-520v360q0 33-23.5 56.5T720-80H240q-33 0-56.5-23.5T160-160Zm400-680q-17 0-28.5 11.5T520-800q0 17 11.5 28.5T560-760q17 0 28.5-11.5T600-800q0-17-11.5-28.5T560-840Zm-200 40q0 17 11.5 28.5T400-760q17 0 28.5-11.5T440-800q0-17-11.5-28.5T400-840q-17 0-28.5 11.5T360-800ZM160-680v80h280v-80H160Zm280 520v-360H240v360h200Zm80 0h200v-360H520v360Zm280-440v-80H520v80h280Z" />
	</svg>
	<svg v-else-if="name === 'gift'" xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0" height="26px" width="26px" fill="none" viewBox="0 0 24 24" stroke="#FFFFFF">
		<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
			d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
	</svg>
	<svg v-else-if="name === 'help'" xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0" height="26px" width="26px" fill="none" viewBox="0 0 24 24" stroke="#FFFFFF">
		<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
	</svg>
	<svg v-else-if="name === 'flag'"
		xmlns="http://www.w3.org/2000/svg"
		height="24"
		viewBox="0 -960 960 960"
		width="24"
		fill="white"
		class="mr-2 flex-shrink-0"
		>
		<path d="M280-400v240q0 17-11.5 28.5T240-120q-17 0-28.5-11.5T200-160v-600q0-17 11.5-28.5T240-800h287q14 0 25 9t14 23l10 48h184q17 0 28.5 11.5T800-680v320q0 17-11.5 28.5T760-320H553q-14 0-25-9t-14-23l-10-48H280Zm306 0h134v-240H543q-14 0-25-9t-14-23l-10-48H280v240h257q14 0 25 9t14 23l10 48Zm-86-160Z" />
	</svg>
	<svg v-else-if="name === 'customer'" width="24" height="25" viewBox="0 0 1024 1024" class="icon mr-2 flex-shrink-0" version="1.1" xmlns="http://www.w3.org/2000/svg">
		<path d="M670.5 471.7c-7.1-3.1-14.2-5.9-21.4-8.5 49.8-40.3 81.6-101.8 81.6-170.6 0-121-98.4-219.4-219.4-219.4s-219.4 98.4-219.4 219.4c0 68.9 31.9 130.5 81.7 170.7C219.4 519.6 109 667.8 109 841.3h73.1c0-181.5 147.7-329.1 329.1-329.1 45.3 0 89.1 9 130.2 26.7l29.1-67.2zM511.3 146.3c80.7 0 146.3 65.6 146.3 146.3S592 438.9 511.3 438.9 365 373.2 365 292.6s65.6-146.3 146.3-146.3zM612.5 636.5c0 10.2 5.6 19.5 14.6 24.2l128 67.6c4 2.1 8.4 3.2 12.8 3.2s8.8-1.1 12.8-3.2l128-67.6c9-4.8 14.6-14.1 14.6-24.2s-5.6-19.5-14.6-24.2l-128-67.7c-8-4.2-17.6-4.2-25.6 0l-128 67.7c-9 4.7-14.6 14-14.6 24.2z m155.4-36.6l69.3 36.6-69.3 36.6-69.3-36.6 69.3-36.6z" fill="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
		<path d="M767.9 763.4l-147-77.7-25.6 48.5 172.6 91.2 171.9-90.8-25.6-48.5z" fill="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
		<path d="M767.9 851.4l-147-77.6-25.6 48.4 172.6 91.3 171.3-90.6-25.6-48.5z" fill="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
	</svg>
	<svg v-else-if="name === 'orbit'" width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg" class="mr-2">
		<path
		fill="white"
		d="M12.0002 12V14C13.1048 14 14.0002 13.1045 14.0002 12H12.0002ZM12.0002 12H10.0002C10.0002 13.1045 10.8956 14 12.0002 14V12ZM12.0002 12V9.99995C10.8956 9.99995 10.0002 10.8954 10.0002 12H12.0002ZM12.0002 12H14.0002C14.0002 10.8954 13.1048 9.99995 12.0002 9.99995V12ZM12.0002 13H12.0102V11H12.0002V13ZM14.8286 14.8284C12.7579 16.8991 10.5345 18.3566 8.64907 19.0636C6.67076 19.8055 5.45764 19.5995 4.92913 19.071L3.51492 20.4852C4.93902 21.9093 7.23488 21.7299 9.35132 20.9363C11.5606 20.1078 14.0178 18.4677 16.2428 16.2426L14.8286 14.8284ZM4.92913 19.071C4.40061 18.5425 4.19466 17.3294 4.93653 15.3511C5.64358 13.4656 7.10106 11.2422 9.17177 9.17152L7.75756 7.75731C5.5325 9.98237 3.89235 12.4395 3.06387 14.6488C2.2702 16.7653 2.09081 19.0611 3.51492 20.4852L4.92913 19.071ZM9.17177 9.17152C11.2425 7.10082 13.4658 5.64333 15.3513 4.93628C17.3296 4.19441 18.5427 4.40037 19.0713 4.92888L20.4855 3.51467C19.0614 2.09056 16.7655 2.26996 14.6491 3.06362C12.4398 3.8921 9.98262 5.53225 7.75756 7.75731L9.17177 9.17152ZM19.0713 4.92888C19.5998 5.4574 19.8057 6.67051 19.0639 8.64883C18.3568 10.5343 16.8993 12.7577 14.8286 14.8284L16.2428 16.2426C18.4679 14.0175 20.108 11.5604 20.9365 9.35108C21.7302 7.23464 21.9096 4.93878 20.4855 3.51467L19.0713 4.92888ZM14.8286 9.17152C16.8993 11.2422 18.3568 13.4656 19.0639 15.3511C19.8057 17.3294 19.5998 18.5425 19.0713 19.071L20.4855 20.4852C21.9096 19.0611 21.7302 16.7653 20.9365 14.6488C20.108 12.4395 18.4679 9.98237 16.2428 7.75731L14.8286 9.17152ZM19.0713 19.071C18.5427 19.5995 17.3296 19.8055 15.3513 19.0636C13.4658 18.3566 11.2425 16.8991 9.17177 14.8284L7.75756 16.2426C9.98262 18.4677 12.4398 20.1078 14.6491 20.9363C16.7655 21.7299 19.0614 21.9093 20.4855 20.4852L19.0713 19.071ZM9.17177 14.8284C7.10106 12.7577 5.64358 10.5343 4.93653 8.64883C4.19466 6.67051 4.40061 5.4574 4.92913 4.92888L3.51491 3.51467C2.09081 4.93878 2.2702 7.23464 3.06387 9.35108C3.89235 11.5604 5.5325 14.0175 7.75756 16.2426L9.17177 14.8284ZM4.92913 4.92888C5.45764 4.40037 6.67076 4.19441 8.64907 4.93628C10.5345 5.64333 12.7579 7.10082 14.8286 9.17152L16.2428 7.75731C14.0178 5.53225 11.5606 3.8921 9.35132 3.06362C7.23488 2.26996 4.93902 2.09056 3.51491 3.51467L4.92913 4.92888Z"
		/>
	</svg>
	<svg v-else-if="name === 'segment'"
		xmlns="http://www.w3.org/2000/svg"
		class="mr-2"
		height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF">
		<path d="M234-276q51-39 114-61.5T480-360q69 0 132 22.5T726-276q35-41 54.5-93T800-480q0-133-93.5-226.5T480-800q-133 0-226.5 93.5T160-480q0 59 19.5 111t54.5 93Zm246-164q-59 0-99.5-40.5T340-580q0-59 40.5-99.5T480-720q59 0 99.5 40.5T620-580q0 59-40.5 99.5T480-440Zm0 360q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q53 0 100-15.5t86-44.5q-39-29-86-44.5T480-280q-53 0-100 15.5T294-220q39 29 86 44.5T480-160Zm0-360q26 0 43-17t17-43q0-26-17-43t-43-17q-26 0-43 17t-17 43q0 26 17 43t43 17Zm0-60Zm0 360Z"/>
	</svg>
	<svg v-else-if="name === 'branding'" width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg" class="mr-2 flex-shrink-0">
		<path
			d="M6 7.7002V17.1854C6 18.5464 6 19.2268 6.20412 19.6433C6.58245 20.4151 7.41157 20.8588 8.26367 20.7454C8.7234 20.6842 9.28964 20.3067 10.4221 19.5518L10.4248 19.5499C10.8737 19.2507 11.0981 19.1011 11.333 19.0181C11.7642 18.8656 12.2348 18.8656 12.666 19.0181C12.9013 19.1012 13.1266 19.2515 13.5773 19.5519C14.7098 20.3069 15.2767 20.6841 15.7364 20.7452C16.5885 20.8586 17.4176 20.4151 17.7959 19.6433C18 19.2269 18 18.5462 18 17.1854V7.69691C18 6.57899 18 6.0192 17.7822 5.5918C17.5905 5.21547 17.2837 4.90973 16.9074 4.71799C16.4796 4.5 15.9203 4.5 14.8002 4.5H9.2002C8.08009 4.5 7.51962 4.5 7.0918 4.71799C6.71547 4.90973 6.40973 5.21547 6.21799 5.5918C6 6.01962 6 6.58009 6 7.7002Z"
			stroke="white"
			stroke-width="2"
			stroke-linecap="round"
			stroke-linejoin="round"
			fill="white"
		/>
	</svg>
	<svg v-else-if="name === 'email'"
		height="25px"
		viewBox="0 -960 960 960"
		width="24px"
		xmlns="http://www.w3.org/2000/svg"
		class="mr-2 flex-shrink-0"
		>
		<path
			d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm640-480L501-453q-5 3-10.5 4.5T480-447q-5 0-10.5-1.5T459-453L160-640v400h640v-400ZM480-520l320-200H160l320 200ZM160-640v10-59 1-32 32-.5 58.5-10 400-400Z"
			fill="white"
			stroke="white"
			stroke-width="2"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</svg>
	<svg v-else-if="name === 'toggle'"
		width="24"
		height="25"
		viewBox="0 0 24 25"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		class="mr-2 flex-shrink-0"
		>
		<path
			d="M8 18.5H16C19.3137 18.5 22 15.8137 22 12.5C22 9.18629 19.3137 6.5 16 6.5H8C4.68629 6.5 2 9.18629 2 12.5C2 15.8137 4.68629 18.5 8 18.5Z"
			stroke="white"
			stroke-width="2"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			d="M16 9.5C14.3431 9.5 13 10.8431 13 12.5C13 14.1569 14.3431 15.5 16 15.5C17.6569 15.5 19 14.1569 19 12.5C19 10.8431 17.6569 9.5 16 9.5Z"
			stroke="white"
			stroke-width="2"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</svg>
	<svg v-else-if="name === 'analytics'"
		xmlns="http://www.w3.org/2000/svg"
		height="24"
		viewBox="0 -960 960 960"
		width="24"
		class="mr-2 flex-shrink-0"
		fill="white"
		>
		<path
			d="M320-480q-17 0-28.5 11.5T280-440v120q0 17 11.5 28.5T320-280q17 0 28.5-11.5T360-320v-120q0-17-11.5-28.5T320-480Zm320-200q-17 0-28.5 11.5T600-640v320q0 17 11.5 28.5T640-280q17 0 28.5-11.5T680-320v-320q0-17-11.5-28.5T640-680ZM480-400q-17 0-28.5 11.5T440-360v40q0 17 11.5 28.5T480-280q17 0 28.5-11.5T520-320v-40q0-17-11.5-28.5T480-400ZM200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm0-560v560-560Zm280 280q17 0 28.5-11.5T520-520q0-17-11.5-28.5T480-560q-17 0-28.5 11.5T440-520q0 17 11.5 28.5T480-480Z"
			stroke-width="2"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</svg>
	<svg v-else-if="name === 'sandwich'"
		width="24"
		height="25"
		viewBox="0 0 24 25"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		class="mr-2 flex-shrink-0"
		>
		<path
			d="M6.5 19.5H17.5C17.9647 19.5 18.197 19.4999 18.3902 19.4614C19.1836 19.3036 19.8036 18.6836 19.9614 17.8902C19.9999 17.697 19.9999 17.4647 19.9999 17C19.9999 16.5353 19.9999 16.3031 19.9614 16.1099C19.8036 15.3165 19.1836 14.6962 18.3902 14.5384C18.197 14.5 17.9647 14.5 17.5 14.5H6.5C6.03534 14.5 5.80306 14.5 5.60986 14.5384C4.81648 14.6962 4.19624 15.3165 4.03843 16.1099C4 16.3031 4 16.5354 4 17C4 17.4647 4 17.6969 4.03843 17.8901C4.19624 18.6835 4.81648 19.3036 5.60986 19.4614C5.80306 19.4999 6.03535 19.5 6.5 19.5Z"
			stroke="white"
			stroke-width="1.5"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			d="M6.5 10.5H17.5C17.9647 10.5 18.197 10.4999 18.3902 10.4614C19.1836 10.3036 19.8036 9.68356 19.9614 8.89018C19.9999 8.69698 19.9999 8.46465 19.9999 8C19.9999 7.53535 19.9999 7.30306 19.9614 7.10986C19.8036 6.31648 19.1836 5.69624 18.3902 5.53843C18.197 5.5 17.9647 5.5 17.5 5.5H6.5C6.03534 5.5 5.80306 5.5 5.60986 5.53843C4.81648 5.69624 4.19624 6.31648 4.03843 7.10986C4 7.30306 4 7.53539 4 8.00004C4 8.4647 4 8.69694 4.03843 8.89014C4.19624 9.68352 4.81648 10.3036 5.60986 10.4614C5.80306 10.4999 6.03535 10.5 6.5 10.5Z"
			stroke="white"
			stroke-width="1.5"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</svg>
	<svg v-else-if="name === 'settings'"
		width="24"
		height="25"
		viewBox="0 0 24 25"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		class="flex-shrink-0"
		>
		<circle cx="12" cy="12" r="3" stroke="white" stroke-width="1.5"/>
		<path d="M13.7654 2.15224C13.3978 2 12.9319 2 12 2C11.0681 2 10.6022 2 10.2346 2.15224C9.74457 2.35523 9.35522 2.74458 9.15223 3.23463C9.05957 3.45834 9.0233 3.7185 9.00911 4.09799C8.98826 4.65568 8.70226 5.17189 8.21894 5.45093C7.73564 5.72996 7.14559 5.71954 6.65219 5.45876C6.31645 5.2813 6.07301 5.18262 5.83294 5.15102C5.30704 5.08178 4.77518 5.22429 4.35436 5.5472C4.03874 5.78938 3.80577 6.1929 3.33983 6.99993C2.87389 7.80697 2.64092 8.21048 2.58899 8.60491C2.51976 9.1308 2.66227 9.66266 2.98518 10.0835C3.13256 10.2756 3.3397 10.437 3.66119 10.639C4.1338 10.936 4.43789 11.4419 4.43786 12C4.43783 12.5581 4.13375 13.0639 3.66118 13.3608C3.33965 13.5629 3.13248 13.7244 2.98508 13.9165C2.66217 14.3373 2.51966 14.8691 2.5889 15.395C2.64082 15.7894 2.87379 16.193 3.33973 17C3.80568 17.807 4.03865 18.2106 4.35426 18.4527C4.77508 18.7756 5.30694 18.9181 5.83284 18.8489C6.07289 18.8173 6.31632 18.7186 6.65204 18.5412C7.14547 18.2804 7.73556 18.27 8.2189 18.549C8.70224 18.8281 8.98826 19.3443 9.00911 19.9021C9.02331 20.2815 9.05957 20.5417 9.15223 20.7654C9.35522 21.2554 9.74457 21.6448 10.2346 21.8478C10.6022 22 11.0681 22 12 22C12.9319 22 13.3978 22 13.7654 21.8478C14.2554 21.6448 14.6448 21.2554 14.8477 20.7654C14.9404 20.5417 14.9767 20.2815 14.9909 19.902C15.0117 19.3443 15.2977 18.8281 15.781 18.549C16.2643 18.2699 16.8544 18.2804 17.3479 18.5412C17.6836 18.7186 17.927 18.8172 18.167 18.8488C18.6929 18.9181 19.2248 18.7756 19.6456 18.4527C19.9612 18.2105 20.1942 17.807 20.6601 16.9999C21.1261 16.1929 21.3591 15.7894 21.411 15.395C21.4802 14.8691 21.3377 14.3372 21.0148 13.9164C20.8674 13.7243 20.6602 13.5628 20.3387 13.3608C19.8662 13.0639 19.5621 12.558 19.5621 11.9999C19.5621 11.4418 19.8662 10.9361 20.3387 10.6392C20.6603 10.4371 20.8675 10.2757 21.0149 10.0835C21.3378 9.66273 21.4803 9.13087 21.4111 8.60497C21.3592 8.21055 21.1262 7.80703 20.6602 7C20.1943 6.19297 19.9613 5.78945 19.6457 5.54727C19.2249 5.22436 18.693 5.08185 18.1671 5.15109C17.9271 5.18269 17.6837 5.28136 17.3479 5.4588C16.8545 5.71959 16.2644 5.73002 15.7811 5.45096C15.2977 5.17191 15.0117 4.65566 14.9909 4.09794C14.9767 3.71848 14.9404 3.45833 14.8477 3.23463C14.6448 2.74458 14.2554 2.35523 13.7654 2.15224Z" stroke="white" stroke-width="1.5"/>
	</svg>
	<svg v-else-if="name === 'support'"
		class="flex-shrink-0"
		xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24" :fill="'white'">
		<path d="M440-120v-80h320v-284q0-117-81.5-198.5T480-764q-117 0-198.5 81.5T200-484v244h-40q-33 0-56.5-23.5T80-320v-80q0-21 10.5-39.5T120-469l3-53q8-68 39.5-126t79-101q47.5-43 109-67T480-840q68 0 129 24t109 66.5Q766-707 797-649t40 126l3 52q19 9 29.5 27t10.5 38v92q0 20-10.5 38T840-249v49q0 33-23.5 56.5T760-120H440Zm-80-280q-17 0-28.5-11.5T320-440q0-17 11.5-28.5T360-480q17 0 28.5 11.5T400-440q0 17-11.5 28.5T360-400Zm240 0q-17 0-28.5-11.5T560-440q0-17 11.5-28.5T600-480q17 0 28.5 11.5T640-440q0 17-11.5 28.5T600-400Zm-359-62q-7-106 64-182t177-76q89 0 156.5 56.5T720-519q-91-1-167.5-49T435-698q-16 80-67.5 142.5T241-462Z"

		stroke-linecap="round" stroke-linejoin="round"
	/></svg>

	<svg v-else-if="name === 'ai-segments'" class="flex-shrink-0"
	xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px" fill="#FFFFFF"><path d="M40-272q0-34 17.5-62.5T104-378q62-31 126-46.5T360-440q66 0 130 15.5T616-378q29 15 46.5 43.5T680-272v32q0 33-23.5 56.5T600-160H120q-33 0-56.5-23.5T40-240v-32Zm800 112H738q11-18 16.5-38.5T760-240v-40q0-44-24.5-84.5T666-434q51 6 96 20.5t84 35.5q36 20 55 44.5t19 53.5v40q0 33-23.5 56.5T840-160ZM360-480q-66 0-113-47t-47-113q0-66 47-113t113-47q66 0 113 47t47 113q0 66-47 113t-113 47Zm400-160q0 66-47 113t-113 47q-11 0-28-2.5t-28-5.5q27-32 41.5-71t14.5-81q0-42-14.5-81T544-792q14-5 28-6.5t28-1.5q66 0 113 47t47 113ZM120-240h480v-32q0-11-5.5-20T580-306q-54-27-109-40.5T360-360q-56 0-111 13.5T140-306q-9 5-14.5 14t-5.5 20v32Zm240-320q33 0 56.5-23.5T440-640q0-33-23.5-56.5T360-720q-33 0-56.5 23.5T280-640q0 33 23.5 56.5T360-560Zm0 320Zm0-400Z"/></svg>

	<svg v-else-if="name === 'ai-strategist'"
	xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="M200-160h560v-80H200v80Zm132-160h296l-23-160H355l-23 160ZM160-80q-17 0-28.5-11.5T120-120v-120q0-33 23.5-56.5T200-320h52l22-160h-74q-17 0-28.5-11.5T160-520q0-17 11.5-28.5T200-560h560q17 0 28.5 11.5T800-520q0 17-11.5 28.5T760-480h-74l22 160h52q33 0 56.5 23.5T840-240v120q0 17-11.5 28.5T800-80H160Zm320-783q8 0 16 2.5t15 7.5q23 18 49 30t55 12q21 0 40-6t37-16q10-5 19.5-4.5T729-832q8 5 11.5 13.5T742-800l-53 240h-82l39-173-7.5 1q-7.5 1-23.5 1-36 0-70.5-11T480-773q-29 20-62.5 31T349-731q-18 0-26.5-1l-8.5-1 39 173h-82l-53-240q-2-11 1.5-19.5T231-833q8-5 18-5.5t19 5.5q18 10 37 16t40 6q29 0 55-12t49-30q7-5 15-7.5t16-2.5Zm0 383Zm0-80Zm0 400Z"/></svg>

	<svg v-else-if="name === 'planning'" class="flex-shrink-0" xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px" fill="#FFFFFF"><path d="M40-120v-80h880v80H40Zm120-120q-33 0-56.5-23.5T80-320v-440q0-33 23.5-56.5T160-840h640q33 0 56.5 23.5T880-760v440q0 33-23.5 56.5T800-240H160Zm0-80h640v-440H160v440Zm0 0v-440 440Z"/></svg>
	<svg v-else-if="name === 'command-center'" class="flex-shrink-0" xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px" fill="#FFFFFF"><path d="M240-40q-50 0-85-35t-35-85q0-50 35-85t85-35q14 0 26 3t23 8l57-71q-28-31-39-70t-5-78l-81-27q-17 25-43 40t-58 15q-50 0-85-35T0-580q0-50 35-85t85-35q50 0 85 35t35 85v8l81 28q20-36 53.5-61t75.5-32v-87q-39-11-64.5-42.5T360-840q0-50 35-85t85-35q50 0 85 35t35 85q0 42-26 73.5T510-724v87q42 7 75.5 32t53.5 61l81-28v-8q0-50 35-85t85-35q50 0 85 35t35 85q0 50-35 85t-85 35q-32 0-58.5-15T739-515l-81 27q6 39-5 77.5T614-340l57 70q11-5 23-7.5t26-2.5q50 0 85 35t35 85q0 50-35 85t-85 35q-50 0-85-35t-35-85q0-20 6.5-38.5T624-232l-57-71q-41 23-87.5 23T392-303l-56 71q11 15 17.5 33.5T360-160q0 50-35 85t-85 35ZM120-540q17 0 28.5-11.5T160-580q0-17-11.5-28.5T120-620q-17 0-28.5 11.5T80-580q0 17 11.5 28.5T120-540Zm120 420q17 0 28.5-11.5T280-160q0-17-11.5-28.5T240-200q-17 0-28.5 11.5T200-160q0 17 11.5 28.5T240-120Zm240-680q17 0 28.5-11.5T520-840q0-17-11.5-28.5T480-880q-17 0-28.5 11.5T440-840q0 17 11.5 28.5T480-800Zm0 440q42 0 71-29t29-71q0-42-29-71t-71-29q-42 0-71 29t-29 71q0 42 29 71t71 29Zm240 240q17 0 28.5-11.5T760-160q0-17-11.5-28.5T720-200q-17 0-28.5 11.5T680-160q0 17 11.5 28.5T720-120Zm120-420q17 0 28.5-11.5T880-580q0-17-11.5-28.5T840-620q-17 0-28.5 11.5T800-580q0 17 11.5 28.5T840-540ZM480-840ZM120-580Zm360 120Zm360-120ZM240-160Zm480 0Z"/></svg>
	<svg v-else-if="name === 'knowledge'" class="flex-shrink-0" xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px" fill="#FFFFFF"><path d="M480-120 200-272v-240L40-600l440-240 440 240v320h-80v-276l-80 44v240L480-120Zm0-332 274-148-274-148-274 148 274 148Zm0 241 200-108v-151L480-360 280-470v151l200 108Zm0-241Zm0 90Zm0 0Z"/></svg>
	<svg v-else-if="name === 'overview'" class="mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="m787-145 28-28-75-75v-112h-40v128l87 87Zm-587 25q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v268q-19-9-39-15.5t-41-9.5v-243H200v560h242q3 22 9.5 42t15.5 38H200Zm0-120v40-560 243-3 280Zm80-40h163q3-21 9.5-41t14.5-39H280v80Zm0-160h244q32-30 71.5-50t84.5-27v-3H280v80Zm0-160h400v-80H280v80ZM720-40q-83 0-141.5-58.5T520-240q0-83 58.5-141.5T720-440q83 0 141.5 58.5T920-240q0 83-58.5 141.5T720-40Z"/></svg>
	<svg v-else-if="name === 'signal-library'" class="mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="M400-400h160v-80H400v80Zm0-120h320v-80H400v80Zm0-120h320v-80H400v80Zm-80 400q-33 0-56.5-23.5T240-320v-480q0-33 23.5-56.5T320-880h480q33 0 56.5 23.5T880-800v480q0 33-23.5 56.5T800-240H320Zm0-80h480v-480H320v480ZM160-80q-33 0-56.5-23.5T80-160v-560h80v560h560v80H160Zm160-720v480-480Z"/></svg>
	<svg v-else-if="name === 'calendar'" class="flex-shrink-0" xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px" fill="#FFFFFF"><path d="M200-80q-33 0-56.5-23.5T120-160v-560q0-33 23.5-56.5T200-800h40v-40q0-17 11.5-28.5T280-880q17 0 28.5 11.5T320-840v40h320v-40q0-17 11.5-28.5T680-880q17 0 28.5 11.5T720-840v40h40q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-80H200Zm0-80h560v-400H200v400Zm0-480h560v-80H200v80Zm0 0v-80 80Zm280 240q-17 0-28.5-11.5T440-440q0-17 11.5-28.5T480-480q17 0 28.5 11.5T520-440q0 17-11.5 28.5T480-400Zm-160 0q-17 0-28.5-11.5T280-440q0-17 11.5-28.5T320-480q17 0 28.5 11.5T360-440q0 17-11.5 28.5T320-400Zm320 0q-17 0-28.5-11.5T600-440q0-17 11.5-28.5T640-480q17 0 28.5 11.5T680-440q0 17-11.5 28.5T640-400ZM480-240q-17 0-28.5-11.5T440-280q0-17 11.5-28.5T480-320q17 0 28.5 11.5T520-280q0 17-11.5 28.5T480-240Zm-160 0q-17 0-28.5-11.5T280-280q0-17 11.5-28.5T320-320q17 0 28.5 11.5T360-280q0 17-11.5 28.5T320-240Zm320 0q-17 0-28.5-11.5T600-280q0-17 11.5-28.5T640-320q17 0 28.5 11.5T680-280q0 17-11.5 28.5T640-240Z"/></svg>
	<svg v-else-if="name === 'switch-horizontal'"
		width="28"
		height="25"
		viewBox="-4 0 24 25"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		class="mr-2 flex-shrink-0"
		>
		<path
			stroke="white"
			stroke-linecap="round"
			stroke-linejoin="round"
			stroke-width="2"
			d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
		/>
	</svg>
	<svg v-else-if="name === 'integrations'" class="mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="M280-280q-83 0-141.5-58.5T80-480q0-83 58.5-141.5T280-680h80v80h-80q-50 0-85 35t-35 85q0 50 35 85t85 35h80v80h-80Zm120-160v-80h160v80H400Zm200 160v-80h80q50 0 85-35t35-85q0-50-35-85t-85-35h-80v-80h80q83 0 141.5 58.5T880-480q0 83-58.5 141.5T680-280h-80Z"/></svg>
	<svg v-else-if="name === 'more'" class="mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="M240-400q-33 0-56.5-23.5T160-480q0-33 23.5-56.5T240-560q33 0 56.5 23.5T320-480q0 33-23.5 56.5T240-400Zm240 0q-33 0-56.5-23.5T400-480q0-33 23.5-56.5T480-560q33 0 56.5 23.5T560-480q0 33-23.5 56.5T480-400Zm240 0q-33 0-56.5-23.5T640-480q0-33 23.5-56.5T720-560q33 0 56.5 23.5T800-480q0 33-23.5 56.5T720-400Z"/></svg>
	<svg v-else-if="name === 'billing'" class="mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="M560-440q-50 0-85-35t-35-85q0-50 35-85t85-35q50 0 85 35t35 85q0 50-35 85t-85 35ZM280-320q-33 0-56.5-23.5T200-400v-320q0-33 23.5-56.5T280-800h560q33 0 56.5 23.5T920-720v320q0 33-23.5 56.5T840-320H280Zm80-80h400q0-33 23.5-56.5T840-480v-160q-33 0-56.5-23.5T760-720H360q0 33-23.5 56.5T280-640v160q33 0 56.5 23.5T360-400Zm440 320H120q-33 0-56.5-23.5T40-160v-400q0-17 11.5-28.5T80-600q17 0 28.5 11.5T120-560v400h680q17 0 28.5 11.5T840-120q0 17-11.5 28.5T800-80Z"/></svg>
	<svg v-else-if="name === 'sign-out'" class="mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="M200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h280v80H200v560h280v80H200Zm440-160-55-58 102-102H360v-80h327L585-622l55-58 200 200-200 200Z"/></svg>
	<svg v-else-if="name === 'settings'" class="mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="m388-80-20-126q-19-7-40-19t-37-25l-118 54-93-164 108-79q-2-9-2.5-20.5T185-480q0-9 .5-20.5T188-521L80-600l93-164 118 54q16-13 37-25t40-18l20-127h184l20 126q19 7 40.5 18.5T669-710l118-54 93 164-108 77q2 10 2.5 21.5t.5 21.5q0 10-.5 21t-2.5 21l108 78-93 164-118-54q-16 13-36.5 25.5T592-206L572-80H388Zm48-60h88l14-112q33-8 62.5-25t53.5-41l106 46 40-72-94-69q4-17 6.5-33.5T715-480q0-17-2-33.5t-7-33.5l94-69-40-72-106 46q-23-26-52-43.5T538-708l-14-112h-88l-14 112q-34 7-63.5 24T306-642l-106-46-40 72 94 69q-4 17-6.5 33.5T245-480q0 17 2.5 33.5T254-413l-94 69 40 72 106-46q24 24 53.5 41t62.5 25l14 112Zm44-210q54 0 92-38t38-92q0-54-38-92t-92-38q-54 0-92 38t-38 92q0 54 38 92t92 38Zm0-130Z"/></svg>
</template>

  <script>
  export default {
	name: 'SidebarIcons',
	props: {
	  name: {
		type: String,
		required: true
	  }
	}
  }
  </script>

  <style scoped>
  svg {
	display: inline-block;
	vertical-align: middle;
  }
  </style>
