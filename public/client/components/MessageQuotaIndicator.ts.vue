<template>
  <div class="quota-indicator" @click="handleClick">
    <div class="quota-display">
      <span class="remaining-count" :class="{ 'animating': isAnimating }">{{ displayedTotal }}</span>
      <span class="quota-label">Messages Left</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted, onUnmounted} from '@vue/runtime-core';
import {fetchMessageQuota} from '../services/messageQuotaService';
import { customerIOTrackEvent } from '../services/customerio.js';

const emit = defineEmits(['click']);
const quota = ref({dailyLimit:0,dailyUsed:0,premium:0});
const dailyRemaining = computed(() => Math.max(0, quota.value.dailyLimit - quota.value.dailyUsed));
const totalAvailable = computed(() => dailyRemaining.value + quota.value.premium);
const displayedTotal = ref(0);
const isAnimating = ref(false);
let pollInterval: number | null = null;

async function load() {
  const data = await fetchMessageQuota();
  if (data) {
    const oldTotal = totalAvailable.value;
    quota.value = data;
    const newTotal = totalAvailable.value;

    // Track when quota hits 0
    if (newTotal === 0 && oldTotal > 0) {
      customerIOTrackEvent('Message Quota Depleted');
    }

    // Animate the change if there's a difference
    if (oldTotal !== newTotal && displayedTotal.value !== 0) {
      animateCounter(displayedTotal.value, newTotal);
    } else {
      displayedTotal.value = newTotal;
    }
  }
}

function animateCounter(from: number, to: number, duration: number = 800) {
  isAnimating.value = true;
  const startTime = Date.now();
  const difference = to - from;

  const animate = () => {
    const currentTime = Date.now();
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // Easing function for smoother animation
    const easeOutQuart = 1 - Math.pow(1 - progress, 4);

    displayedTotal.value = Math.round(from + (difference * easeOutQuart));

    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      isAnimating.value = false;
    }
  };

  requestAnimationFrame(animate);
}

function startPolling() {
  // Poll every 5 seconds
  pollInterval = window.setInterval(load, 5000);
}

function stopPolling() {
  if (pollInterval) {
    clearInterval(pollInterval);
    pollInterval = null;
  }
}

function handleClick() {
  customerIOTrackEvent('Viewed Messages');
  emit('click');
}

// Listen for quota update events
function handleQuotaUpdate() {
  load(); // Refresh quota data
}

onMounted(async () => {
  await load();
  displayedTotal.value = totalAvailable.value;
  startPolling();
  // Listen for custom events to update quota
  window.addEventListener('quotaUpdated', handleQuotaUpdate);
  window.addEventListener('messagesSent', handleQuotaUpdate);
});

onUnmounted(() => {
  stopPolling();
  window.removeEventListener('quotaUpdated', handleQuotaUpdate);
  window.removeEventListener('messagesSent', handleQuotaUpdate);
});
</script>

<style scoped>
.quota-indicator {
  cursor: pointer;
  padding: 10px 14px;
  background: linear-gradient(135deg, #2c2257 0%, #1e1941 100%);
  border: 1px solid #332567;
  border-radius: 10px;
  color: white;
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quota-indicator:hover {
  background: linear-gradient(135deg, #332567 0%, #2c2257 100%);
  border-color: #6e3ff2;
  box-shadow: 0 4px 8px rgba(110, 63, 242, 0.2);
  transform: translateY(-1px);
}

.quota-indicator:active {
  transform: translateY(0px);
  box-shadow: 0 2px 4px rgba(110, 63, 242, 0.3);
}

.quota-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
}

.remaining-count {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  line-height: 1;
}

.quota-label {
  font-size: 10px;
  color: #b4b4c5;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Subtle click indicator */
.quota-indicator::after {
  content: '';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 4px;
  height: 4px;
  background: #6e3ff2;
  border-radius: 50%;
  opacity: 0.6;
}

.quota-indicator:hover::after {
  opacity: 1;
  background: #8b5cf6;
}

/* Animation styles */
.remaining-count {
  transition: all 0.3s ease;
}

.remaining-count.animating {
  color: #6e3ff2;
  transform: scale(1.05);
  text-shadow: 0 2px 8px rgba(110, 63, 242, 0.3);
}
</style>
