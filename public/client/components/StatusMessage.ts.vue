<template>
	<div v-if="showMessage"
	class="flex justify-center items-center fixed bottom-0 left-0 mb-4 ml-4 p-4 rounded-lg shadow-xl transition-opacity duration-300"
	:class="{ 'opacity-0': !isVisible, 'text-white bg-ralsuccess-dark' : this.status == 'success', 'bg-ralerror-dark text-white' : this.status == 'fail' }"
	style="z-index: 1000;"
	>
		{{ message }}
		<div class="flex cursor-pointer ml-4 py-1 px-2 rounded-full transition-all duration-300 font-bold"
		:class="{'hover:bg-green-900' : this.status == 'success', 'hover:bg-amber-300' : this.status == 'fail'}"
		@click="closeMessage"
		>X</div>
	</div>
</template>

  <script>
  //When using this component, you must include the resetStatus call in the component to get messages to display properly. Eg: <StatusMessage message="Your branding updates are live!" :status=testMessage @resetStatus="testMessage = 'nope'"></StatusMessage>
  export default {
	name: 'StatusMessage',
	props: ['status', 'message'],
  	emits: [''],
	  data() {
			return {
				showMessage: false,
				isVisible: false,
				changeTracker: 0,
			};
		},
		methods: {
			closeMessage() {
				this.isVisible = false;
				setTimeout(() => {
					this.showMessage = false;
				}, 300);
				this.$emit('resetStatus');
			},
		},
		watch: {
			status(newStatus) {
				console.log("STATUS", newStatus)
				if (newStatus == 'success' || newStatus == 'fail') {
					this.showMessage = true;
					this.isVisible = true;
					setTimeout(() => {
						this.isVisible = false;
						setTimeout(() => {
						this.showMessage = false;
						}, 300);
						this.$emit('resetStatus');
					}, 6000);
				}
			}
		}
  };
  </script>
