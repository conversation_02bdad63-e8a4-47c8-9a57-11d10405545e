<template>
	<div
		class="header"
		:class="showHeaderBackground ? 'bg-white rounded-md shadow border border-white border-opacity-25' : ''"
	>
		<div
			class="flex px-7 py-3 justify-center items-stretch"
		>
			<div class="flex items-center">
				<div
					class="px-7 py-2 rounded-sm mb-0.5 last:mb-0 pl-3 bg-ralprimary-main mr-7"
					style="border-radius:5em; height: 69px; margin-left: -10px"
					:class="{ 'pr-3': collapseLogo }"
					:style="goHomeOnLogoClick ? 'cursor: pointer' : ''"

					@click="logoClick()"
				>
					<a
						class="
						block
						truncate
						transition
						duration-150
						opacity-100
						text-white
						text-white hover:text-slate-200 text-slate-200 opacity-100
						"
						:href="href" @click="navigate"
					>
						<div class="flex items-center justify-center overflow-hidden">

							<svg width="46" height="47" viewBox="0 0 46 47" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path fill-rule="evenodd" clip-rule="evenodd" d="M40.8771 28.9209L22.8967 6.25L4.9163 28.9209L8.2581 32.407L4.67188 36.7404V40.75H12.3552L22.8967 27.5731L33.4382 40.75H41.1215V36.7404L37.5353 32.407L40.8771 28.9209ZM22.8967 14.7187L10.0577 30.2325L8.63318 28.7466L22.8967 10.7621L37.1602 28.7466L35.7358 30.2325L22.8967 14.7187ZM7.47569 37.7501L22.8967 19.1164L38.3177 37.7501V37.9462H34.7858L22.8967 23.0848L11.0076 37.9462H7.47569V37.7501Z" fill="white"/>
								<path d="M30.7381 40.75L22.897 30.7704L15.0558 40.75H18.6216L22.897 35.3086L27.1723 40.75H30.7381Z" fill="white"/>
							</svg>

							<span style="font-size: 1.5em; margin-left: 0.5em; height: 1em; line-height: 1.2em; overflow: hidden; font-weight: 400;" v-if="!collapseLogo">R A L E O N</span>
							<!-- <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-left: 2.5px">
								<path
									d="M1 8.95222V14.3002C1 15.4203 1 15.9807 1.21799 16.4086C1.40973 16.7849 1.71547 17.0906 2.0918 17.2823C2.5192 17.5001 3.07899 17.5001 4.19691 17.5001H13.8031C14.921 17.5001 15.48 17.5001 15.9074 17.2823C16.2837 17.0906 16.5905 16.7849 16.7822 16.4086C17 15.9811 17 15.4216 17 14.3037V8.95222C17 8.41789 16.9995 8.15057 16.9346 7.90193C16.877 7.68159 16.7825 7.47307 16.6546 7.28464C16.5102 7.07201 16.3096 6.89569 15.9074 6.54383L11.1074 2.34383C10.3608 1.69054 9.98751 1.36406 9.56738 1.23982C9.19719 1.13035 8.80261 1.13035 8.43242 1.23982C8.01261 1.36397 7.63985 1.69014 6.89436 2.34244L2.09277 6.54383C1.69064 6.89569 1.49004 7.07201 1.3457 7.28464C1.21779 7.47307 1.12255 7.68159 1.06497 7.90193C1 8.15057 1 8.41788 1 8.95222Z"
									:stroke="isExactActive ? 'white' : 'black'"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
								></path>
							</svg>

							<span v-if="sidebarExpanded" class="
						text-sm
						font-medium
						ml-3
						lg:opacity-0 lg:sidebar-expanded:opacity-100
						2xl:opacity-100
						duration-200
					">Home</span> -->
						</div>
					</a>
				</div>
			</div>


			<div class="hidden lg:block w-0 border" style="margin: -12px 16px -12px -8px"></div>

			<div v-if="showBackButton" class="back-button mr-7" @click="backClicked()">
				<div class="arrow">
					<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
						<g opacity="0.5">
						<path d="M18.3333 15L13.3333 20M13.3333 20L18.3333 25M13.3333 20H26.6667M35 20C35 11.7157 28.2843 5 20 5C11.7157 5 5 11.7157 5 20C5 28.2843 11.7157 35 20 35C28.2843 35 35 28.2843 35 20Z" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
						</g>
					</svg>
				</div>
			</div>


			<div class="hidden lg:flex justify-center items-center flex-grow">
				<slot></slot>
			</div>
			<div class="lg:hidden flex-grow"></div>

			<div v-if="showCloseButton" class="close-button flex justify-center items-center" @click="backClicked()">
				<div class="">
					<div class="rounded-full border-2 border-zinc-300 hover:bg-ralprimary-main transition-colors ease-in-out duration-300">
						<svg width="64" height="64" viewBox="0 0 69 69" fill="none" xmlns="http://www.w3.org/2000/svg" class="svg-effect transition-all ease-in-out duration 300">

						<path class="transition-all ease-in-out duration-300" d="M34.5 37.8004L22.9487 49.3517C22.5165 49.7839 21.9664 50 21.2985 50C20.6305 50 20.0805 49.7839 19.6483 49.3517C19.2161 48.9195 19 48.3695 19 47.7015C19 47.0336 19.2161 46.4835 19.6483 46.0513L31.1996 34.5L19.6483 22.9487C19.2161 22.5165 19 21.9664 19 21.2985C19 20.6305 19.2161 20.0805 19.6483 19.6483C20.0805 19.2161 20.6305 19 21.2985 19C21.9664 19 22.5165 19.2161 22.9487 19.6483L34.5 31.1996L46.0513 19.6483C46.4835 19.2161 47.0336 19 47.7015 19C48.3695 19 48.9195 19.2161 49.3517 19.6483C49.7839 20.0805 50 20.6305 50 21.2985C50 21.9664 49.7839 22.5165 49.3517 22.9487L37.8004 34.5L49.3517 46.0513C49.7839 46.4835 50 47.0336 50 47.7015C50 48.3695 49.7839 48.9195 49.3517 49.3517C48.9195 49.7839 48.3695 50 47.7015 50C47.0336 50 46.4835 49.7839 46.0513 49.3517L34.5 37.8004Z"
						fill="#5A16C9"/>
						</svg>

					</div>
				</div>
			</div>
		</div>

		<div class="lg:hidden flex flex-col sm:flex-row justify-center items-center p-4 pr-0 shadow z-2 sm:shadow-none">
			<slot></slot>
		</div>
	</div>
</template>

<script>

	import * as Utils from '../../client-old/utils/Utils';


	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		props: ['collapseLogo', 'goHomeOnLogoClick', 'showHeaderBackground', 'showBackButton', 'showCloseButton', 'backButtonHref'],
		components: {
		},
		async mounted() {
		},
		data() {
			return {}
		},
		methods: {
			logoClick() {
				if (this.goHomeOnLogoClick) {
					let runOnboarding = localStorage.getItem('runOnboarding');
					if(runOnboarding === 'true' || runOnboarding === true)
						this.$router.push('/loyalty/quickstart');
					else
						this.$router.push('/');
				}
			},
			backClicked() {
				if (this.backButtonHref) {
					this.$router.push(this.backButtonHref);
				} else {
					history.back();
				}
			}
		}
	}
</script>
<style scoped>


.back-button {
	/* Auto layout */
	cursor: pointer;
	display: flex;
	flex-direction: row;
	justify-content: flex-end;
	align-items: center;
	padding: 14px 22px;
	gap: 10px;

	width: 84px;
	height: 68px;
	right: 1304px;
	top: calc(50% - 68px/2 - 396px);

	background: rgba(255, 255, 255, 0.1);
	border-radius: 110px;
}

.close-button {
	/* Auto layout */
	cursor: pointer;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 110px;
}



.back-button > .arrow {
	/* Arrow / Arrow_Circle_Left */

	width: 40px;
	height: 40px;

	opacity: 0.5;

	/* Inside auto layout */
	flex: none;
	order: 0;
	flex-grow: 0;
}

.back-button > .arrow > svg {
	/* Vector */

	left: 12.5%;
	right: 12.5%;
	top: 12.5%;
	bottom: 12.5%;
}

.svg-effect:hover path {
  fill: white;
}

</style>

