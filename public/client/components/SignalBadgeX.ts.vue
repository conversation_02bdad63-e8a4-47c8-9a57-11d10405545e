<template>
	<div
	  draggable="true"
	  :class="[
		'flex items-center gap-2 px-4 py-1.5 border rounded-full text-sm font-medium',
		'cursor-pointer mb-2 mr-2 inline-block',
		'transition-all duration-200 hover:shadow-md',
		typeClasses
	  ]"
	  @dragstart="handleDragStart"
	  @click="handleClick"
	>
	<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="opacity-50"><path d="M360-160q-33 0-56.5-23.5T280-240q0-33 23.5-56.5T360-320q33 0 56.5 23.5T440-240q0 33-23.5 56.5T360-160Zm240 0q-33 0-56.5-23.5T520-240q0-33 23.5-56.5T600-320q33 0 56.5 23.5T680-240q0 33-23.5 56.5T600-160ZM360-400q-33 0-56.5-23.5T280-480q0-33 23.5-56.5T360-560q33 0 56.5 23.5T440-480q0 33-23.5 56.5T360-400Zm240 0q-33 0-56.5-23.5T520-480q0-33 23.5-56.5T600-560q33 0 56.5 23.5T680-480q0 33-23.5 56.5T600-400ZM360-640q-33 0-56.5-23.5T280-720q0-33 23.5-56.5T360-800q33 0 56.5 23.5T440-720q0 33-23.5 56.5T360-640Zm240 0q-33 0-56.5-23.5T520-720q0-33 23.5-56.5T600-800q33 0 56.5 23.5T680-720q0 33-23.5 56.5T600-640Z"/></svg>
	  {{ signal.label }}
	</div>
  </template>

  <script>
  export default {
	name: 'SignalBadge',
	props: {
	  signal: {
		type: Object,
		required: true
	  },
	  section: {
		type: String,
		required: true
	  }
	},
	computed: {
	  typeClasses() {
		const classes = {
		  rule: 'bg-green-100 text-green-800 border-green-200 hover:bg-green-50 ',
		  model: 'bg-blue-100 border-blue-200 text-blue-800 hover:bg-blue-50',
		  raleon: 'bg-purple-100 border-purple-200 text-purple-800 hover:bg-purple-50'
		}
		return classes[this.signal.type] || 'bg-gray-100 text-gray-800 hover:bg-gray-200'
	  }
	},
	methods: {
	  handleDragStart(e) {
		e.dataTransfer.setData('signalId', this.signal.id)
		e.dataTransfer.setData('sourceSection', this.section)
	  },
	  handleClick(e) {
		e.stopPropagation(); // Add this
		this.$emit('click', { signal: this.signal, event: e })
	  }
	}
  }
  </script>
