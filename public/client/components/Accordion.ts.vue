<template>
	<div
		class="accordion bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 hover:border-opacity-90 hover:shadow-lg flex-grow transition-all duration-300 ease-in-out overflow-hidden"
		:class="[
			{'accordion-disabled': disabled},
			{'accordion-closed': !isOpen},
			{'accordion-open': isOpen},
			{'border-opacity-90' : isOpen }
		]"
		:style="{ height: isOpen ? (openHeight ? openHeight : '100%') : '3em'}"
	>
		<div class="flex items-center px-4 py-1 cursor-pointer" @click="toggle">
			<div class="text-zinc-900 text-xl font-normal font-['Open Sans'] leading-loose flex-grow truncate accordion-title">{{ title  }}</div>
			<div class="flex-grow"></div>
			<div class="text-zinc-900 text-l font-normal font-['Open Sans'] leading-loose truncate accordion-subtitle">{{ subtitle }}</div>
			<div v-if="isOpen" >
				<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M12 15.3751L6 9.3751L7.4 7.9751L12 12.5751L16.6 7.9751L18 9.3751L12 15.3751Z" fill="#9F96E4"/>
				</svg>
			</div>
			<div v-if="!isOpen">
				<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M15.375 12L9.37498 18L7.97498 16.6L12.575 12L7.97498 7.4L9.37498 6L15.375 12Z" fill="#989898"/>
				</svg>
			</div>
		</div>
		<div
			class="transition-all accordion-body"
			:class="isOpen ? 'opacity-100' : 'opacity-0'"
			:style="{ height: isOpen ? (openHeight ? openHeight : '100%') : '0'}"
		>
			<slot></slot>
		</div>
	</div>
</template>

<script>
	import * as Utils from '../../client-old/utils/Utils';


	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		props: ['title', 'subtitle', 'open', 'preopen', 'group', 'openHeight', 'disabled'],
		data() {
			return {
				_open: false,
				hasPreopened: false
			}
		},
		components: {
		},
		async mounted() {
		},
		computed: {
			isOpen() {
				if (this.open !== undefined) {
					return this.open;
				}

				if (this.preopen && !this.hasPreopened) {
					this._open = true;
					this.hasPreopened = true;
				}

				return this._open;
			}
		},
		methods: {
			toggle() {
				if(this.disabled) return;

				if (this.isOpen) {
					this._open = false;
					this.$emit('close');
				} else {
					this._open = true;
					this.$emit('open');
					this.$emit('opened', this.$el);
				}
			}
		}
	}
</script>

<style scoped>
	.accordion-disabled {
		opacity: 0.6;
		pointer-events: none;
	}
</style>
