<template>
	<!-- Modal backdrop -->
	<transition
	  enter-active-class="transition ease-out duration-200"
	  enter-from-class="opacity-0"
	  enter-to-class="opacity-100"
	  leave-active-class="transition ease-out duration-100"
	  leave-from-class="opacity-100"
	  leave-to-class="opacity-0"
	>
	  <div v-show="modalOpen" class="fixed inset-0 bg-slate-900 bg-opacity-30 z-50 transition-opacity" aria-hidden="true"></div>
	</transition>
	<!-- Modal dialog -->
	<transition
	  enter-active-class="transition ease-in-out duration-200"
	  enter-from-class="opacity-0 translate-y-4"
	  enter-to-class="opacity-100 translate-y-0"
	  leave-active-class="transition ease-in-out duration-200"
	  leave-from-class="opacity-100 translate-y-0"
	  leave-to-class="opacity-0 translate-y-4"
	>
	  <div v-show="modalOpen" :id="id" class="fixed inset-0 z-50 overflow-hidden flex items-center my-4 justify-center transform px-1 sm:px-6" role="dialog" aria-modal="true">
		<div ref="modalContent" class="bg-white rounded-2xl shadow-lg  overflow-auto max-w-lg w-full max-h-full"  :class="{ 'md:min-w-fit': fitContent  }">
		  <!-- Modal header -->
		  <div class="px-5 py-10">
			<div class="flex justify-between items-center">
			  <div class="text-neutral-700 text-3xl font-medium font-['Inter'] uppercase">{{ title }}</div>
			  <button class="text-slate-400 hover:text-slate-500" @click.stop="$emit('close-modal')">
				<div class="sr-only">Close</div>

				<svg width="54" height="54" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg">
				<circle cx="27" cy="27" r="26" fill="white" stroke="white" stroke-width="2"/>
				<path d="M26.9996 29.583L17.9594 38.6231C17.6212 38.9614 17.1907 39.1305 16.668 39.1305C16.1452 39.1305 15.7147 38.9614 15.3765 38.6231C15.0383 38.2849 14.8691 37.8544 14.8691 37.3317C14.8691 36.809 15.0383 36.3785 15.3765 36.0402L24.4167 27.0001L15.3765 17.9599C15.0383 17.6217 14.8691 17.1912 14.8691 16.6684C14.8691 16.1457 15.0383 15.7152 15.3765 15.377C15.7147 15.0387 16.1452 14.8696 16.668 14.8696C17.1907 14.8696 17.6212 15.0387 17.9594 15.377L26.9996 24.4172L36.0397 15.377C36.378 15.0387 36.8085 14.8696 37.3312 14.8696C37.8539 14.8696 38.2844 15.0387 38.6227 15.377C38.9609 15.7152 39.13 16.1457 39.13 16.6684C39.13 17.1912 38.9609 17.6217 38.6227 17.9599L29.5825 27.0001L38.6227 36.0402C38.9609 36.3785 39.13 36.809 39.13 37.3317C39.13 37.8544 38.9609 38.2849 38.6227 38.6231C38.2844 38.9614 37.8539 39.1305 37.3312 39.1305C36.8085 39.1305 36.378 38.9614 36.0397 38.6231L26.9996 29.583Z" fill="#8A8787"/>
				</svg>
			  </button>
			</div>
		  </div>
		  <div class="w-[calc(100%-2em)] mx-[1em] h-px border border-neutral-700 border-opacity-50 mb-4"></div>
		  <slot />
		</div>
	  </div>
	</transition>
  </template>

  <script>
  import { ref, onMounted, onUnmounted } from 'vue'

  export default {
	name: 'ModalBasic',
	props: ['id', 'modalOpen', 'title', 'fitContent'],
	emits: ['close-modal'],
	setup(props, { emit }) {

	  const modalContent = ref(null)

	  // close on click outside
	  const clickHandler = ({ target }) => {
		if (!props.modalOpen) {
		  emit('close-modal')
		}
	  }

	  // close if the esc key is pressed
	  const keyHandler = ({ keyCode }) => {
		if (!props.modalOpen || keyCode !== 27) return
		emit('close-modal')
	  }

	  onMounted(() => {
		document.addEventListener('click', clickHandler)
		document.addEventListener('keydown', keyHandler)
	  })

	  onUnmounted(() => {
		document.removeEventListener('click', clickHandler)
		document.removeEventListener('keydown', keyHandler)
	  })

	  return {
		modalContent,
	  }
	}
  }
  </script>
