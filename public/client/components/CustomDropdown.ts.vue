<!-- CustomDropdown.vue -->
<template>
	<div class="relative" v-click-outside="closeDropdown">
	  <!-- Trigger <PERSON>ton -->
	  <button
		@click="isOpen = !isOpen"
		class="flex items-center justify-between w-full px-4 py-3 bg-white border rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
	  >
		<span class="block truncate" :class="{'font-medium': selected}">
		  {{ selected ? selected.title : placeholder }}
		</span>
		<svg
		  class="w-5 h-5 ml-2 -mr-1 transition-transform duration-200"
		  :class="{ 'transform rotate-180': isOpen }"
		  xmlns="http://www.w3.org/2000/svg"
		  viewBox="0 0 20 20"
		  fill="currentColor"
		>
		  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
		</svg>
	  </button>

	  <!-- Dropdown Menu -->
	  <transition
		enter-active-class="transition duration-100 ease-out"
		enter-from-class="transform scale-95 opacity-0"
		enter-to-class="transform scale-100 opacity-100"
		leave-active-class="transition duration-75 ease-in"
		leave-from-class="transform scale-100 opacity-100"
		leave-to-class="transform scale-95 opacity-0"
	  >
		<div
		  v-if="isOpen"
		  class="absolute z-10 w-full mt-1 bg-white rounded-lg shadow-lg"
		>
		  <ul class="py-1 overflow-auto max-h-60">
			<li
			  v-for="option in options"
			  :key="option.id"
			  @click="selectOption(option)"
			  class="relative cursor-pointer select-none hover:bg-gray-50"
			>
			  <div class="px-4 py-3">
				<div class="flex items-center">
				  <span class="font-medium text-gray-900">
					{{ option.title }}
				  </span>
				  <span
					v-if="option === selected"
					class="absolute inset-y-0 right-0 flex items-center pr-4 text-blue-600"
				  >
					<svg class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
					  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
					</svg>
				  </span>
				</div>
				<p class="mt-1 text-sm text-gray-500">
				  {{ option.description }}
				</p>
			  </div>
			</li>
		  </ul>
		</div>
	  </transition>
	</div>
  </template>

  <script>
  export default {
	name: 'CustomDropdown',
	props: {
	  options: {
		type: Array,
		required: true,
		// Each option should have: id, title, description
	  },
	  placeholder: {
		type: String,
		default: 'Select an option'
	  },
	  modelValue: {
		type: Object,
		default: null
	  }
	},
	data() {
	  return {
		isOpen: false,
		selected: this.modelValue
	  }
	},
	watch: {
	  modelValue(newValue) {
		this.selected = newValue
	  }
	},
	methods: {
	  selectOption(option) {
		this.selected = option
		this.isOpen = false
		this.$emit('update:modelValue', option)
		this.$emit('change', option)
	  },
	  closeDropdown() {
		this.isOpen = false
	  }
	},
	directives: {
	  'click-outside': {
		mounted(el, binding) {
		  el.clickOutsideEvent = function(event) {
			if (!(el === event.target || el.contains(event.target))) {
			  binding.value(event)
			}
		  }
		  document.addEventListener('click', el.clickOutsideEvent)
		},
		unmounted(el) {
		  document.removeEventListener('click', el.clickOutsideEvent)
		}
	  }
	}
  }
  </script>
