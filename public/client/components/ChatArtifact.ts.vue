<template>
	<div class="fixed bg-white shadow-xl border-l-2 border-t border-b border-gray-200 rounded-l-xl flex flex-col my-8 mb-16 z-50"
		:style="{
			width: componentWidth + 'px',
			height: 'calc(100% - 6rem)',
			right: '0',
			top: '2rem',
			boxShadow: '0 20px 30px -10px rgba(0, 0, 0, 0.15), 0 10px 20px -5px rgba(0, 0, 0, 0.1), 0 0 10px rgba(0, 0, 0, 0.05)',
			backgroundColor: 'rgba(255, 255, 255, 0.98)'
		}">
		<!-- Resize Handle -->
		<div class="absolute left-0 top-0 bottom-0 w-2 cursor-col-resize hover:bg-purple-200 hover:opacity-50 transition-colors duration-200 rounded-l-xl z-10 flex items-center justify-center"
			@mousedown="startResize">
			<div class="h-16 w-1 bg-gray-300 rounded-full opacity-50 hover:opacity-100 transition-opacity duration-200">
			</div>
		</div>
		<!-- Artifact Top Bar -->
		<div
			class="flex items-center justify-between p-2 border-b border-gray-200 flex-shrink-0 bg-gradient-to-l from-purple-50 to-white rounded-tl-xl">
			<div class="flex items-center space-x-2">
				<!-- Center: Toggle -->
				<div class="inline-flex p-1 bg-purple-100 bg-opacity-50 rounded-lg justify-center mx-2">
					<button v-if="showBriefTab" @click="artifactView = 'brief'" :class="[
						'px-4 py-1.5 text-xs font-medium rounded-md transition-all duration-200 focus:outline-none',
						artifactView === 'brief' ? 'bg-white text-purple-700 shadow-sm' : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50'
					]">
						Brief
					</button>
					<button v-if="showEmailTab || (artifactView === 'brief' && localGeneratedEmailDesign !== null)" @click="artifactView = 'email'" :class="[
						'px-4 py-1.5 text-xs font-medium rounded-md transition-all duration-200 focus:outline-none',
						artifactView === 'email' ? 'bg-white text-purple-700 shadow-sm' : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50'
					]">
						Email
					</button>
					<button v-if="showPlanTab" @click="artifactView = 'plan'" :class="[
						'px-4 py-1.5 text-xs font-medium rounded-md transition-all duration-200 focus:outline-none',
						artifactView === 'plan' ? 'bg-white text-purple-700 shadow-sm' : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50'
					]">
						Plan
					</button>
				</div>
			</div>

			<div class="flex items-center space-x-2">
				<div class="flex-shrink-0 relative group"> <!-- Added group for tooltip functionality -->
					<button v-if="artifactView === 'plan'" @click="handleBuildPlanClick" :disabled="isBuildingPlan"
						:class="[
							'px-3 py-1.5 text-sm font-medium bg-purple-600 text-white rounded hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 transition-all duration-200',
							isBuildingPlan ? 'bg-gray-300 text-gray-600 cursor-not-allowed' : 'bg-purple-600 text-white hover:bg-purple-700 shadow-sm'
						]">
						<span class="flex items-center">
							<svg v-if="isBuildingPlan" class="animate-spin -ml-1 mr-2 h-3 w-3 text-white"
								xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
								<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
									stroke-width="4"></circle>
								<path class="opacity-75" fill="currentColor"
									d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
								</path>
							</svg>
							{{ isBuildingPlan ? 'Building...' : 'Build Plan' }}
						</span>
					</button>
					<button v-if="artifactView === 'brief' || artifactView === 'email'" @click="handleBuildCampaignClick" :disabled="isBuildingCampaign || !canBuildCampaign"
						:class="[
							'px-3 py-1.5 text-sm font-medium bg-purple-600 text-white rounded hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 transition-all duration-200',
							isBuildingCampaign ? 'bg-gray-300 text-gray-600 cursor-not-allowed' : 'bg-purple-600 text-white hover:bg-purple-700 shadow-sm'
						]">
						<span class="flex items-center">
							<svg v-if="isBuildingCampaign" class="animate-spin -ml-1 mr-2 h-3 w-3 text-white"
								xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
								<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
									stroke-width="4"></circle>
								<path class="opacity-75" fill="currentColor"
									d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
								</path>
							</svg>
							{{
                                isBuildingCampaign ? 'Building...' :
                                !canBuildCampaign ? 'Need Brief' :
                                'Build Campaign'
                            }}
						</span>
					</button>

					<!-- Tooltip that appears on hover -->
					<div v-if="canBuildCampaign && !isBuildingCampaign && (artifactView === 'brief' || artifactView === 'email')" class="absolute right-0 top-full mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ease-in-out pointer-events-none z-50 w-64">
						<div class="bg-gray-800 text-white text-xs rounded py-2 px-3 shadow-lg">
							<div class="flex items-start mb-1">
								<svg class="h-4 w-4 mr-1 text-purple-400 flex-shrink-0 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
									<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
								</svg>
								<div>
									<p class="font-medium">Ready to create your campaign?</p>
									<p class="mt-1">When you're happy with the brief, click to build your full campaign workflow.</p>
								</div>
							</div>
						</div>
						<div class="w-3 h-3 bg-gray-800 transform rotate-45 absolute -top-1 right-5"></div>
					</div>
				</div>
				<!-- Right: Close Button -->
				<button @click="$emit('close-artifact-drawer')"
					class="p-2 rounded-full hover:bg-purple-100 text-gray-500 hover:text-purple-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50">
					<svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 -960 960 960" width="20px"
						fill="currentColor">
						<path
							d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z" />
					</svg>
				</button>
			</div>
		</div>

		<!-- Artifact Content Area -->
		<div class="flex-1 overflow-y-auto">
			<!-- Display different content based on artifactView -->
			<div v-if="artifactView === 'brief'" class="p-4 space-y-3">
				<!-- Streaming Content -->
				<div v-if="streamingBriefContent">
					<div class="mb-3 flex items-center">
						<div class="animate-pulse mr-2 h-2 w-2 bg-purple-500 rounded-full"></div>
						<p class="text-xs text-purple-600 font-medium">Creating campaign brief...</p>
					</div>
					<div class="bg-gray-50 rounded-lg border border-gray-200 p-4 mb-4">
						<h3 class="text-sm uppercase tracking-wide text-gray-500 font-semibold mb-3">Draft Brief</h3>
						<div class="prose prose-sm max-w-none prose-headings:mt-2 prose-headings:mb-1 prose-p:my-1 bg-white p-3 border border-gray-100 rounded whitespace-pre-wrap text-xs">
							{{ streamingBriefContent }}
						</div>
					</div>
				</div>
				<!-- Final Parsed Brief Content -->
				<div v-else-if="parsedBrief && !streamingBriefContent">
					<!-- Campaign Details Card -->
					<div class="bg-gray-50 rounded-lg border border-gray-200 p-4 mb-4">
						<h3 class="text-sm uppercase tracking-wide text-gray-500 font-semibold mb-3">Campaign Details</h3>

						<div class="grid grid-cols-1 gap-3">
							<!-- Subject Line -->
							<div v-if="parsedBrief.subjectLine" class="flex flex-col">
								<span class="text-xs text-gray-500 uppercase tracking-wide">Subject Line</span>
								<span class="text-base font-bold text-gray-900">{{ parsedBrief.subjectLine }}</span>
							</div>

							<!-- Preview Text -->
							<div v-if="parsedBrief.previewText" class="flex flex-col">
								<span class="text-xs text-gray-500 uppercase tracking-wide">Preview Text</span>
								<span class="text-sm italic text-gray-700">{{ parsedBrief.previewText }}</span>
							</div>

							<!-- Target Audience -->
							<div v-if="parsedBrief.targetSegment" class="flex flex-col">
								<span class="text-xs text-gray-500 uppercase tracking-wide">Target Audience</span>
								<span class="text-sm text-gray-800">{{ parsedBrief.targetSegment }}</span>
							</div>

							<!-- Scheduled Date -->
							<div v-if="parsedBrief.targetSendDate" class="flex flex-col">
								<span class="text-xs text-gray-500 uppercase tracking-wide">Scheduled Date</span>
								<span class="text-sm text-gray-800">{{ formatDate(parsedBrief.targetSendDate) }}</span>
							</div>
						</div>
					</div>

					<!-- Brief Content -->
					<div v-if="parsedBrief.briefText" class="mb-4">
						<h3 class="text-sm uppercase tracking-wide text-gray-500 font-semibold mb-2">Brief Content</h3>
						<div class="prose prose-sm max-w-none prose-headings:mt-2 prose-headings:mb-1 prose-p:my-1 bg-white p-3 border border-gray-100 rounded"
							v-html="renderedBriefBody"></div>
					</div>
					<div
						v-if="!parsedBrief.subjectLine && !parsedBrief.previewText && !parsedBrief.briefText && localBriefMarkdownContent">
						<!-- Fallback for when parsing might have failed but content exists -->
						<div class="bg-gray-50 rounded-lg border border-gray-200 p-4 mb-4">
							<h3 class="text-sm uppercase tracking-wide text-gray-500 font-semibold mb-3">Raw Brief Content</h3>
							<div class="prose prose-sm max-w-none prose-headings:mt-2 prose-headings:mb-1 prose-p:my-1 bg-white p-3 border border-gray-100 rounded"
								v-html="marked(localBriefMarkdownContent)"></div>
						</div>
					</div>
				</div>
				<!-- Placeholder (only show if no final content and not streaming) -->
				<div v-else>
					<p class="text-sm text-gray-500 italic">Brief content will appear here...</p>
				</div>
			</div>

			<!-- Plan Preview Area -->
			<div v-if="artifactView === 'plan'" class="h-full flex flex-col">
				<!-- Existing Plan Content -->
				<div v-if="isGeneratingPlan || generatedPlanData || hasParseError"
					class="flex-1 overflow-auto artifact-scrollbar"> <!-- Added flex-1 and overflow -->
					<PlanArtifact :plan-data="generatedPlanData" :is-generating="isGeneratingPlan"
						:streaming-content="streamingPlanContent" :has-parse-error="hasParseError"
						:raw-json-content="rawPlanContent" @retry-parse="handleRetryParse" />
				</div>
				<div v-else class="flex-1 flex items-center justify-center text-gray-500 italic">
					Plan preview will appear here...
				</div>
			</div>

			<!-- Email Preview Area -->
			<div v-if="artifactView === 'email'" class="h-full border border-gray-200 rounded flex flex-col">
				<!-- Loading State -->
				<div v-if="localIsGeneratingEmail"
					class="flex-1 flex items-center justify-center flex-col space-y-4 text-gray-500 italic">
					<div class="animate-spin rounded-full h-10 w-10 border-4 border-purple-500 border-t-transparent">
					</div>
					<p>Loading email preview...</p>
				</div>

				<!-- Email Preview Iframe -->
				<div v-else-if="emailHtml" class="flex-1">
					<iframe :srcdoc="emailHtml" class="w-full h-full border-0"></iframe>
				</div>

				<!-- Placeholder -->
				<div v-else class="flex-1 flex items-center justify-center text-gray-500 italic">
					Email preview will appear here...
				</div>

				<!-- Hidden EmailEditor instance for HTML generation -->
				<div
					style="position: absolute; left: -9999px; top: -9999px; height: 1px; width: 1px; overflow: hidden;">
					<EmailEditor ref="emailEditorArtifact" :project-id="'267562'" :options="emailEditorOptions" style="height: 1px; width: 1px; border: none;" @load="editorLoaded" />
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import {defineComponent, computed, PropType, ref, onMounted, onUnmounted, watch} from '@vue/runtime-core'; // Added watch
import {useRouter} from 'vue-router';
import {EmailEditor} from 'vue-email-editor';
import {marked} from 'marked';
import {customerIOTrackEvent} from '../services/customerio.js';
import PlanArtifact from './PlanArtifact.ts.vue';
import axios from 'axios';
import * as Utils from '../../client-old/utils/Utils';
import * as OrganizationSettings from '../services/organization-settings';
import {ToolHandlerRegistry} from '../services/toolHandlerService';
import {initializeToolHandlers, toolEvents} from '../services/tools';

// Configure marked globally or ensure it's set before use
marked.setOptions({breaks: true, gfm: true});

export default defineComponent({
	name: 'ChatArtifact',
	components: {
		EmailEditor,
		PlanArtifact,
	},
	props: {
		briefMarkdownContent: {
			type: String as PropType<string | null>,
			default: null,
		},
		streamingBriefContent: {
			type: String,
			default: '',
		},
		generatedEmailDesign: {
			type: Object as PropType<any | null>, // Unlayer design JSON
			default: null,
		},
		isGeneratingEmail: {
			type: Boolean,
			default: false,
		},
		initialView: {
			type: String as PropType<'brief' | 'email' | 'plan'>,
			default: 'brief',
		},
		generatedPlanData: {
			type: Object as PropType<any | null>,
			default: null,
		},
		isGeneratingPlan: {
			type: Boolean,
			default: false,
		},
		streamingPlanContent: {
			type: String,
			default: '',
		},
		hasParseError: {
			type: Boolean,
			default: false,
		},
		rawPlanContent: {
			type: String,
			default: '',
		},
		chatId: {
			type: Number,
			default: null,
		},
	},
	emits: ['close-artifact-drawer', 'width-change', 'plan-built', 'retry-parse', 'campaign-built'],
	setup(props, {emit}) {
		const router = useRouter(); // Get router instance
		const artifactView = ref<'brief' | 'email' | 'plan'>(props.initialView);
		const emailHtml = ref('');
		const isBuildingPlan = ref(false);
		const isBuildingCampaign = ref(false);

		initializeToolHandlers(); // Initialize all tool handlers

		// Local reactive state for artifact content, mirroring props initially
		const localBriefMarkdownContent = ref<string | null>(props.briefMarkdownContent);
		const localStreamingBriefContent = ref<string>(props.streamingBriefContent);
		const localGeneratedEmailDesign = ref<any | null>(props.generatedEmailDesign);
		const localIsGeneratingEmail = ref<boolean>(props.isGeneratingEmail);
		const localGeneratedPlanData = ref<any | null>(props.generatedPlanData);
		const localIsGeneratingPlan = ref<boolean>(props.isGeneratingPlan);
		const localStreamingPlanContent = ref<string>(props.streamingPlanContent);
		
		// Font settings state
		const customFontUrl = ref('');
		const customFontName = ref('');
		const customFontValue = ref('');

		// Watch props and update local state if they change from parent
		watch(() => props.briefMarkdownContent, (newVal) => localBriefMarkdownContent.value = newVal);
		watch(() => props.streamingBriefContent, (newVal) => localStreamingBriefContent.value = newVal);
		watch(() => props.generatedEmailDesign, (newVal) => localGeneratedEmailDesign.value = newVal);
		watch(() => props.isGeneratingEmail, (newVal) => localIsGeneratingEmail.value = newVal);
		watch(() => props.generatedPlanData, (newVal) => localGeneratedPlanData.value = newVal);
		watch(() => props.isGeneratingPlan, (newVal) => localIsGeneratingPlan.value = newVal);
		watch(() => props.streamingPlanContent, (newVal) => localStreamingPlanContent.value = newVal);
		watch(() => props.initialView, (newVal) => artifactView.value = newVal); // Also watch initialView for artifactView

		// Load font settings function
		const loadFontSettings = async () => {
		  try {
			// Load custom font settings from organization settings
			customFontUrl.value = await OrganizationSettings.getOrganizationSetting('customFontUrl') || '';
			customFontName.value = await OrganizationSettings.getOrganizationSetting('customFontName') || '';
			customFontValue.value = await OrganizationSettings.getOrganizationSetting('customFontValue') || '';
			
			console.log('Loaded font settings for ChatArtifact:', {
			  url: customFontUrl.value,
			  name: customFontName.value,
			  value: customFontValue.value
			});
		  } catch (error) {
			console.error('Error loading font settings for ChatArtifact:', error);
		  }
		};
		
		// Computed property for EmailEditor options with custom fonts
		const emailEditorOptions = computed(() => {
		  const customFonts = [];
		  
		  // Add custom font if all required fields are provided
		  if (customFontUrl.value && customFontName.value && customFontValue.value) {
			const customFont = {
			  label: customFontName.value,
			  value: customFontValue.value,
			  url: customFontUrl.value,
			  weights: [
				{ label: 'Regular', value: 400 },
				{ label: 'Bold', value: 700 }
			  ]
			};
			
			customFonts.push(customFont);
		  }
		  
		  return {
			displayMode: 'email',
			features: {
			  preview: false,
			  imageEditor: false,
			  undoRedo: false,
			  stockImages: false,
			},
			tools: {
			  image: {enabled: false},
			  button: {enabled: false},
			  divider: {enabled: false},
			  text: {enabled: false},
			  html: {enabled: false},
			  menu: {enabled: false},
			  social: {enabled: false},
			  timer: {enabled: false},
			  video: {enabled: false},
			  form: {enabled: false},
			  row: {enabled: true}
			},
			fonts: {
			  showDefaultFonts: true,
			  customFonts: customFonts
			}
		  };
		});
		
		const defaultEmailDesign = ref({"counters": {}, "body": {"id": "YTs1S7srOa", "rows": [{"id": "-TaBEMMQpt", "cells": [1], "columns": [{"id": "ZhqnAP7TU9", "contents": [{"id": "phzB-G6w5X", "type": "image", "values": {"containerPadding": "10px", "anchor": "", "src": {"url": "https://dqpqjbq51w8fz.cloudfront.net/images/organization=2239/JM-Logo.png", "width": 315, "height": 47, "autoWidth": false, "maxWidth": "45%"}, "textAlign": "center", "altText": "Jake Madden Logo", "action": {"name": "web", "values": {"href": "", "target": "_blank"}}, "hideDesktop": false, "displayCondition": null, "_styleGuide": null, "_meta": {"htmlID": "u_content_image_3", "htmlClassNames": "u_content_image", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false, "pending": false}}], "values": {"backgroundColor": "", "padding": "0px", "border": {}, "borderRadius": "0px", "_meta": {"htmlID": "u_column_3", "htmlClassNames": "u_column", "description": ""}, "deletable": true, "locked": false}}], "values": {"displayCondition": null, "columns": false, "_styleGuide": null, "backgroundColor": "", "columnsBackgroundColor": "", "backgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "custom", "position": "center"}, "padding": "0px", "anchor": "", "hideDesktop": false, "_meta": {"htmlID": "u_row_3", "htmlClassNames": "u_row", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false}}, {"id": "l4C0rGfd_m", "cells": [1], "columns": [{"id": "pAFKNGjEZD", "contents": [{"id": "WyHqf8PGPj", "type": "divider", "values": {"width": "0%", "border": {"borderTopWidth": "210px", "borderTopStyle": "solid", "borderTopColor": "#BBBBBB"}, "textAlign": "center", "containerPadding": "10px", "anchor": "", "hideDesktop": false, "displayCondition": null, "_styleGuide": null, "_meta": {"htmlID": "u_content_divider_1", "htmlClassNames": "u_content_divider", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false}}, {"id": "c_wTydUAWX", "type": "heading", "values": {"containerPadding": "10px", "anchor": "", "headingType": "h1", "fontSize": "22px", "color": "#ffffff", "textAlign": "center", "lineHeight": "140%", "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkHoverColor": "#0000ee", "linkUnderline": true, "linkHoverUnderline": true}, "hideDesktop": false, "displayCondition": null, "_styleGuide": null, "_meta": {"htmlID": "u_content_heading_1", "htmlClassNames": "u_content_heading", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false, "text": "Step Up Your Game. Discover How Real Fans Rock Their Super Dunkers!", "_languages": {}}}, {"id": "ehwNmIAO8k", "type": "button", "values": {"href": {"name": "web", "values": {"href": "", "target": "_blank"}}, "buttonColors": {"color": "#000000", "backgroundColor": "#ffffff", "hoverColor": "#FFFFFF", "hoverBackgroundColor": "#3AAEE0"}, "size": {"autoWidth": true, "width": "100%"}, "fontSize": "14px", "lineHeight": "120%", "textAlign": "center", "padding": "10px 20px", "border": {}, "borderRadius": "4px", "hideDesktop": false, "displayCondition": null, "_styleGuide": null, "containerPadding": "10px", "anchor": "", "_meta": {"htmlID": "u_content_button_1", "htmlClassNames": "u_content_button", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false, "text": "Get Inspired", "_languages": {}, "calculatedWidth": 110, "calculatedHeight": 37}}, {"id": "M6dgBPTSqF", "type": "divider", "values": {"width": "0%", "border": {"borderTopWidth": "210px", "borderTopStyle": "solid", "borderTopColor": "#BBBBBB"}, "textAlign": "center", "containerPadding": "10px", "anchor": "", "hideDesktop": false, "displayCondition": null, "_styleGuide": null, "_meta": {"htmlID": "u_content_divider_2", "htmlClassNames": "u_content_divider", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false}}], "values": {"backgroundColor": "", "padding": "0px", "border": {}, "borderRadius": "0px", "_meta": {"htmlID": "u_column_1", "htmlClassNames": "u_column", "description": ""}, "deletable": true, "locked": false}}], "values": {"displayCondition": null, "columns": false, "_styleGuide": null, "backgroundColor": "", "columnsBackgroundColor": "", "backgroundImage": {"url": "https://dqpqjbq51w8fz.cloudfront.net/images/organization=2239/JM-Hero1.png", "fullWidth": true, "repeat": "no-repeat", "size": "custom", "position": "center", "width": 600, "height": 600}, "padding": "0px", "anchor": "", "hideDesktop": false, "_meta": {"htmlID": "u_row_1", "htmlClassNames": "u_row", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false, "pending": false}}, {"id": "DA5DrAIWDs", "cells": [1], "columns": [{"id": "l65W3PRhex", "contents": [{"id": "x14EZgunPk", "type": "heading", "values": {"containerPadding": "10px", "anchor": "", "headingType": "h1", "fontSize": "22px", "textAlign": "center", "lineHeight": "140%", "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkHoverColor": "#0000ee", "linkUnderline": true, "linkHoverUnderline": true}, "hideDesktop": false, "displayCondition": null, "_styleGuide": null, "_meta": {"htmlID": "u_content_heading_2", "htmlClassNames": "u_content_heading", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false, "text": "Super Dunkers: A Statement", "_languages": {}}}, {"id": "TLoDosVy2e", "type": "text", "values": {"containerPadding": "10px 40px", "anchor": "", "fontSize": "14px", "textAlign": "left", "lineHeight": "160%", "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkHoverColor": "#0000ee", "linkUnderline": true, "linkHoverUnderline": true}, "hideDesktop": false, "displayCondition": null, "_styleGuide": null, "_meta": {"htmlID": "u_content_text_1", "htmlClassNames": "u_content_text", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false, "text": "<p style=\"line-height: 160%;\">Super Dunkers are more than sneakers&amp;mdash;they&amp;rsquo;re a statement. Our customers round the globe effortlessly blend street edge with high-fashion flair, making every step a bold move. Join the movement and explore how these kicks go from city streets to everyday adventures. With versatile design and unstoppable style, Super Dunkers help you own every moment.</p>", "_languages": {}}}], "values": {"backgroundColor": "", "padding": "0px", "border": {}, "borderRadius": "0px", "_meta": {"htmlID": "u_column_2", "htmlClassNames": "u_column", "description": ""}, "deletable": true, "locked": false}}], "values": {"displayCondition": null, "columns": false, "_styleGuide": null, "backgroundColor": "", "columnsBackgroundColor": "#ffffff", "backgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "custom", "position": "center"}, "padding": "0px", "anchor": "", "hideDesktop": false, "_meta": {"htmlID": "u_row_2", "htmlClassNames": "u_row", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false}}, {"id": "q1p5XWGYHi", "cells": [1], "columns": [{"id": "080TwZwWMp", "contents": [{"id": "YG0Rafyw9R", "type": "image", "values": {"containerPadding": "10px", "anchor": "", "src": {"url": "https://dqpqjbq51w8fz.cloudfront.net/images/organization=2239/JM-TheLook.png", "width": 1024, "height": 1052, "autoWidth": false, "maxWidth": "85%"}, "textAlign": "center", "altText": "", "action": {"name": "web", "values": {"href": "", "target": "_blank"}}, "hideDesktop": false, "displayCondition": null, "_styleGuide": null, "_meta": {"htmlID": "u_content_image_3", "htmlClassNames": "u_content_image", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false, "pending": false}}], "values": {"backgroundColor": "", "padding": "0px", "border": {}, "borderRadius": "0px", "_meta": {"htmlID": "u_column_3", "htmlClassNames": "u_column", "description": ""}, "deletable": true, "locked": false}}], "values": {"displayCondition": null, "columns": false, "_styleGuide": null, "backgroundColor": "", "columnsBackgroundColor": "", "backgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "custom", "position": "center"}, "padding": "0px", "anchor": "", "hideDesktop": false, "_meta": {"htmlID": "u_row_3", "htmlClassNames": "u_row", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false}}, {"id": "np6VY9cFI4", "cells": [1], "columns": [{"id": "6IaPGxPzHK", "contents": [{"id": "sACftEWWQs", "type": "image", "values": {"containerPadding": "10px", "anchor": "", "src": {"url": "https://dqpqjbq51w8fz.cloudfront.net/images/organization=2239/JM-Products.png", "width": 597, "height": 594, "autoWidth": false, "maxWidth": "85%"}, "textAlign": "center", "altText": "", "action": {"name": "web", "values": {"href": "", "target": "_blank"}}, "hideDesktop": false, "displayCondition": null, "_styleGuide": null, "_meta": {"htmlID": "u_content_image_3", "htmlClassNames": "u_content_image", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false, "pending": false}}], "values": {"backgroundColor": "", "padding": "0px", "border": {}, "borderRadius": "0px", "_meta": {"htmlID": "u_column_3", "htmlClassNames": "u_column", "description": ""}, "deletable": true, "locked": false}}], "values": {"displayCondition": null, "columns": false, "_styleGuide": null, "backgroundColor": "", "columnsBackgroundColor": "", "backgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "custom", "position": "center"}, "padding": "0px", "anchor": "", "hideDesktop": false, "_meta": {"htmlID": "u_row_3", "htmlClassNames": "u_row", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false}}, {"id": "VfjCM6jn16", "cells": [1], "columns": [{"id": "EnMIzjNwBL", "contents": [{"id": "EJ2Mm51xh-", "type": "image", "values": {"containerPadding": "0px", "anchor": "", "src": {"url": "https://dqpqjbq51w8fz.cloudfront.net/images/organization=2239/JM-Pay.png", "width": 610, "height": 79, "autoWidth": false, "maxWidth": "100%"}, "textAlign": "center", "altText": "", "action": {"name": "web", "values": {"href": "", "target": "_blank"}}, "hideDesktop": false, "displayCondition": null, "_styleGuide": null, "_meta": {"htmlID": "u_content_image_3", "htmlClassNames": "u_content_image", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false, "pending": false}}], "values": {"backgroundColor": "", "padding": "0px", "border": {"borderTopWidth": "0px", "borderTopStyle": "solid", "borderLeftWidth": "0px", "borderLeftStyle": "solid", "borderRightWidth": "0px", "borderRightStyle": "solid", "borderBottomWidth": "0px", "borderBottomStyle": "solid"}, "borderRadius": "0px", "_meta": {"htmlID": "u_column_3", "htmlClassNames": "u_column", "description": ""}, "deletable": true, "locked": false}}], "values": {"displayCondition": null, "columns": false, "_styleGuide": null, "backgroundColor": "", "columnsBackgroundColor": "#000000", "backgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "custom", "position": "center"}, "padding": "0px", "anchor": "", "hideDesktop": false, "_meta": {"htmlID": "u_row_3", "htmlClassNames": "u_row", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false}}, {"id": "I6U_Vke__i", "cells": [1], "columns": [{"id": "NeNl32LMlP", "contents": [{"id": "bqD9cAKhxZ", "type": "social", "values": {"containerPadding": "10px", "anchor": "", "icons": {"iconType": "circle-white", "icons": [{"name": "Facebook", "url": "https://facebook.com/"}, {"name": "Instagram", "url": "https://instagram.com/"}, {"name": "X", "url": "https://x.com/"}, {"name": "YouTube", "url": "https://youtube.com/"}, {"name": "TikTok", "url": "https://tiktok.com/"}]}, "align": "center", "iconSize": 32, "spacing": 5, "hideDesktop": false, "displayCondition": null, "_styleGuide": null, "_meta": {"htmlID": "u_content_social_1", "htmlClassNames": "u_content_social", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false}}, {"id": "8Rynoy-Tc8", "type": "text", "values": {"containerPadding": "10px", "anchor": "", "fontSize": "14px", "color": "#ffffff", "textAlign": "center", "lineHeight": "140%", "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkHoverColor": "#0000ee", "linkUnderline": true, "linkHoverUnderline": true}, "hideDesktop": false, "displayCondition": null, "_styleGuide": null, "_meta": {"htmlID": "u_content_text_2", "htmlClassNames": "u_content_text", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false, "text": "<p style=\"line-height: 140%;\">Don't miss out! Add <a rel=\"noopener\" href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a> to your address book and make sure you receive our emails!</p>\\n<p style=\"line-height: 140%;\">&amp;nbsp;</p>\\n<p style=\"line-height: 140%;\">{% unsubscribe %}</p>", "_languages": {}}}], "values": {"backgroundColor": "", "padding": "0px", "border": {"borderTopWidth": "0px", "borderTopStyle": "solid", "borderLeftWidth": "0px", "borderLeftStyle": "solid", "borderRightWidth": "0px", "borderRightStyle": "solid", "borderBottomWidth": "0px", "borderBottomStyle": "solid"}, "borderRadius": "0px", "_meta": {"htmlID": "u_column_4", "htmlClassNames": "u_column", "description": ""}, "deletable": true, "locked": false}}], "values": {"displayCondition": null, "columns": false, "_styleGuide": null, "backgroundColor": "", "columnsBackgroundColor": "#959694", "backgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "custom", "position": "center"}, "padding": "0px", "anchor": "", "hideDesktop": false, "_meta": {"htmlID": "u_row_4", "htmlClassNames": "u_row", "description": ""}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "locked": false}}], "headers": [], "footers": [], "values": {"_styleGuide": null, "popupPosition": "center", "popupWidth": "600px", "popupHeight": "auto", "borderRadius": "10px", "contentAlign": "center", "contentVerticalAlign": "center", "contentWidth": 600, "fontFamily": {"label": "Arial", "value": "arial,helvetica,sans-serif"}, "textColor": "#000000", "popupBackgroundColor": "#FFFFFF", "popupBackgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "cover", "position": "center"}, "popupOverlay_backgroundColor": "rgba(0, 0, 0, 0.1)", "popupCloseButton_position": "top-right", "popupCloseButton_backgroundColor": "#DDDDDD", "popupCloseButton_iconColor": "#000000", "popupCloseButton_borderRadius": "0px", "popupCloseButton_margin": "0px", "popupCloseButton_action": {"name": "close_popup", "attrs": {"onClick": "document.querySelector('.u-popup-container').style.display = 'none';"}}, "language": {}, "backgroundColor": "#ffffff", "preheaderText": "", "linkStyle": {"body": true, "linkColor": "#1155cc", "linkHoverColor": "#0000ee", "linkUnderline": true, "linkHoverUnderline": true, "inherit": false}, "backgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "custom", "position": "center"}, "_meta": {"htmlID": "u_body", "htmlClassNames": "u_body", "description": ""}}}, "schemaVersion": 20});

		// Set initial width based on screen size
		const componentWidth = ref(window.innerWidth < 768 ? window.innerWidth : 900);
		let startX = 0;
		let initialWidth = 0;
		// Removed maxAllowedArtifactWidth from here, will calculate dynamically in resize

		const startResize = (event: MouseEvent) => {
			document.body.style.userSelect = 'none';
			document.body.style.cursor = 'col-resize';
			startX = event.clientX;
			initialWidth = componentWidth.value;

			window.addEventListener('mousemove', resize);
			window.addEventListener('mouseup', stopResize);
		};

		const resize = (event: MouseEvent) => {
			event.preventDefault(); // Prevent default browser drag behavior

			// On mobile, don't allow resizing
			if (window.innerWidth < 768) {
				return;
			}

			// Calculate how far the mouse has moved from the starting position
			const deltaX = event.clientX - startX;

			// Calculate new width based on the drag direction
			const newWidth = initialWidth - deltaX;

			// Set minimum and maximum constraints
			const minWidth = 400; // Minimum drawer width
			const maxWidth = Math.min(window.innerWidth * 0.6, window.innerWidth - 400);

			// Apply constraints
			const constrainedWidth = Math.max(minWidth, Math.min(newWidth, maxWidth));

			// Update width and notify parent
			componentWidth.value = constrainedWidth;
			emit('width-change', constrainedWidth);
		};

		const stopResize = () => {
			// Reset styles and remove event listeners
			document.body.style.userSelect = '';
			document.body.style.cursor = '';
			window.removeEventListener('mousemove', resize);
			window.removeEventListener('mouseup', stopResize);
		};

		// --- Window Resize Handling ---
		const handleResize = () => {
			// Check if screen is mobile (less than 768px)
			if (window.innerWidth < 768) {
				// Set to 100% width on mobile
				componentWidth.value = window.innerWidth;
				emit('width-change', componentWidth.value);
				return;
			}

			// Ensure the component doesn't exceed the available space on desktop
			const maxAllowedWidth = Math.min(window.innerWidth * 0.6, window.innerWidth - 400);

			// If current width exceeds max allowed, adjust it
			if (componentWidth.value > maxAllowedWidth) {
				componentWidth.value = Math.max(500, maxAllowedWidth);
				// Emit width change to parent
				emit('width-change', componentWidth.value);
			}
		};

		const handleChatUpdateSegment = (event: any) => {
			console.log('ChatArtifact received chat:update-segment:', event);
			const {toolTag, status, content, segmentId, error} = event; // Added error field

			switch (toolTag) {
				case 'brief':
					if (status === 'streaming') {
						localStreamingBriefContent.value = content;
						localBriefMarkdownContent.value = null; // Clear final content if streaming starts/continues
					} else if (status === 'complete') {
						localBriefMarkdownContent.value = content;
						localStreamingBriefContent.value = ''; // Clear streaming content
					}
					break;
				case 'email':
					console.log(`Email update: status=${status}, isGenerating=${localIsGeneratingEmail.value}`);
					if (status === 'generating' || status === 'streaming') {
						console.log('Setting email generating state to TRUE');
						localIsGeneratingEmail.value = true;
						// If we have streaming content for email, we could handle it here
					} else if (status === 'complete') {
						// Only update the design if we have content
						if (content) {
							console.log('Setting email design with content');
							localGeneratedEmailDesign.value = content; // Assuming content is the design JSON
						}
						console.log('Setting email generating state to FALSE');
						localIsGeneratingEmail.value = false;

						// Force rerender email view if needed
						if (artifactView.value === 'email') {
							console.log('Forcing email view refresh');
							// This is a trick to force re-evaluation of the component
							setTimeout(() => {
								const oldView = artifactView.value;
								artifactView.value = 'brief';
								setTimeout(() => {
									artifactView.value = oldView;
								}, 10);
							}, 10);
						}
					} else if (status === 'error') {
						console.error('Error generating email artifact:', error || content);
						console.log('Setting email generating state to FALSE due to error');
						localIsGeneratingEmail.value = false;
					}
					break;
				case 'plan':
					if (status === 'streaming') {
						localStreamingPlanContent.value = content;
						localGeneratedPlanData.value = null;
						localIsGeneratingPlan.value = true;
					} else if (status === 'complete') {
						localGeneratedPlanData.value = content; // Assuming content is the plan data
						localStreamingPlanContent.value = '';
						localIsGeneratingPlan.value = false;
					} else if (status === 'error') {
						localIsGeneratingPlan.value = false;
						localStreamingPlanContent.value = '';
						console.error('Error generating plan artifact:', error || content);
					}
					break;
				default:
					console.warn(`ChatArtifact: Unknown toolTag '${toolTag}' in chat:update-segment`);
			}

			// Log the state after update for debugging
			if (toolTag === 'email') {
				console.log('After email update - Current state:', {
					status,
					isGenerating: localIsGeneratingEmail.value,
					hasDesign: localGeneratedEmailDesign.value !== null,
					artifactView: artifactView.value
				});
			}
		};

		const handleArtifactOpen = (event: any) => {
			console.log('ChatArtifact received artifact:open:', event);
			const {view, data, autoOpen} = event;

			if (view) {
				console.log(`Setting artifactView to ${view} (previous: ${artifactView.value})`);
				// Always update the view when explicitly requested
				artifactView.value = view;
			}

			// Optionally, pre-fill data if provided, though chat:update-segment should handle most content
			if (data) {
				if (view === 'brief' && data.content) localBriefMarkdownContent.value = data.content;
				if (view === 'email' && data.design) localGeneratedEmailDesign.value = data.design;
				if (view === 'plan' && data.planData) localGeneratedPlanData.value = data.planData;
			}

			// Force a console log of the current state for debugging
			console.log('After artifact:open - Current state:', {
				artifactView: artifactView.value,
				showEmailTab: localGeneratedEmailDesign.value !== null || localIsGeneratingEmail.value || artifactView.value === 'email',
				isGeneratingEmail: localIsGeneratingEmail.value,
				hasEmailDesign: localGeneratedEmailDesign.value !== null
			});
		};

		onMounted(() => {
			// Add resize event listener
			window.addEventListener('resize', handleResize);
			handleResize(); // Initial check
			emit('width-change', componentWidth.value); // Emit initial width

			toolEvents.on('chat:update-segment', handleChatUpdateSegment);
			toolEvents.on('artifact:open', handleArtifactOpen);
		});

		onUnmounted(() => {
			// Remove event listeners
			window.removeEventListener('resize', handleResize);
			toolEvents.off('chat:update-segment', handleChatUpdateSegment);
			toolEvents.off('artifact:open', handleArtifactOpen);
		});
		// --- End Window Resize Handling ---


		// Load font settings on mount
		onMounted(() => {
		  loadFontSettings();
		});
		
		return {
			router,
			artifactView,
			emailHtml,
			isBuildingPlan,
			isBuildingCampaign,
			defaultEmailDesign,
			componentWidth,
			startResize,
			marked, // Return marked so it's accessible in the template
			// Return local reactive state variables
			localBriefMarkdownContent,
			localStreamingBriefContent,
			localGeneratedEmailDesign,
			localIsGeneratingEmail,
			localGeneratedPlanData,
			localIsGeneratingPlan,
			localStreamingPlanContent,
			// Font-related properties
			emailEditorOptions,
			loadFontSettings,
		};
	},
	computed: {
		canBuildCampaign() {
			// Check for valid brief text in parsed brief or streaming content
			const hasParsedBriefText = this.parsedBrief && this.parsedBrief.briefText && this.parsedBrief.briefText.trim() !== '';
			const hasStreamingBrief = this.localStreamingBriefContent && this.localStreamingBriefContent.trim() !== '';

			// Only brief text is required for UI purposes - email can be missing
			return hasParsedBriefText || hasStreamingBrief;
		},
		parsedBrief() {
			if (!this.localBriefMarkdownContent) { // Use local state via this
				return null;
			}
			try {
				// Extract only valid JSON by finding the first { and last }
				const content = this.localBriefMarkdownContent;
				const firstBrace = content.indexOf('{');
				const lastBrace = content.lastIndexOf('}');

				if (firstBrace === -1 || lastBrace === -1 || firstBrace >= lastBrace) {
					// No valid JSON structure found, treat entire content as briefText
					return {
						subjectLine: '',
						targetSegment: '',
						targetSendDate: '',
						previewText: '',
						briefText: content,
					};
				}

				// Extract just the JSON part
				const jsonStr = content.substring(firstBrace, lastBrace + 1);
				console.log('Extracted JSON part from brief content:',
					firstBrace > 0 ? `Found extra text before JSON: "${content.substring(0, firstBrace)}"` : 'No extra text before JSON');

				const parsed = JSON.parse(jsonStr);
				return {
					subjectLine: parsed.subjectLine || '',
					targetSegment: parsed.targetSegment || '',
					targetSendDate: parsed.targetSendDate || '',
					previewText: parsed.previewText || '',
					briefText: parsed.briefText || '',
				};
			} catch (e) {
				console.error('Error parsing briefMarkdownContent JSON:', e);
				console.error('Content that failed parsing:', this.localBriefMarkdownContent);
				return {
					subjectLine: '',
					targetSegment: '',
					targetSendDate: '',
					previewText: '',
					briefText: this.localBriefMarkdownContent, // Fallback to raw local content
				};
			}
		},
		renderedBriefBody() {
			if (this.parsedBrief && this.parsedBrief.briefText) {
				return this.marked(this.parsedBrief.briefText); // Access marked via this
			}
			return '';
		},
		renderedStreamingBrief() {
			return this.marked(this.localStreamingBriefContent || ''); // Access marked via this, use local state
		},
		showBriefTab() {
			// Show brief tab if brief exists, is streaming, or if artifactView is set to brief
			return this.localBriefMarkdownContent !== null ||
				this.localStreamingBriefContent !== '' ||
				this.artifactView === 'brief'; // Also check current view
		},
		showEmailTab() {
			// Show email tab if email is being generated, design exists, or if artifactView is set to email
			return this.localGeneratedEmailDesign !== null ||
				this.localIsGeneratingEmail ||
				this.artifactView === 'email'; // Also check current view
		},
		showPlanTab() {
			// Show plan tab if plan exists, is being generated, or if artifactView is set to plan
			return this.localGeneratedPlanData !== null ||
				this.localIsGeneratingPlan ||
				this.artifactView === 'plan'; // Also check current view
		},
	},
	watch: {
		localGeneratedEmailDesign: { // Watch local reactive state
			handler(newDesign) {
				if (newDesign) {
					console.log('Generated email design received, loading into editor...');
					const editor = (this.$refs.emailEditorArtifact as any)?.editor;
					if (editor) {
						try {
							editor.loadDesign(JSON.parse(JSON.stringify(newDesign)));
							console.log('Design loaded, exporting HTML...');
							editor.exportHtml((data: {design: object, html: string}) => {
								console.log('HTML Exported from generated design');
								this.emailHtml = data.html; // emailHtml is a local ref in setup, accessed via this
							}, {
								cleanup: true,
							});
						} catch (error) {
							console.error('Error loading or exporting generated design:', error);
							this.emailHtml = '<p>Error loading email preview.</p>';
						}
					} else {
						console.warn('Editor instance not ready when localGeneratedEmailDesign changed.');
					}
				}
			},
			deep: true
		},
		// Watch for initialView is handled in setup by watching props.initialView
	},
	methods: {
		formatDate(dateString) {
			// First handle if it's already formatted as just a date string like "2024-06-15"
			if (dateString && dateString.length <= 10 && dateString.includes('-')) {
				return dateString;
			}

			// Try to parse the date string - if it fails, return the original string
			try {
				const date = new Date(dateString);
				// Check if the date is valid
				if (isNaN(date.getTime())) {
					return dateString;
				}

				// Format as Month Day, Year (e.g. "June 15, 2024")
				return date.toLocaleDateString('en-US', {
					year: 'numeric',
					month: 'long',
					day: 'numeric'
				});
			} catch (error) {
				console.error('Error formatting date:', error);
				return dateString;
			}
		},
		async editorLoaded() {
			console.log('Artifact Email Editor Loaded');
			
			// Load font settings when editor is ready
			await this.loadFontSettings();
			
			const editor = (this.$refs.emailEditorArtifact as any)?.editor;
			if (editor) {
				console.log('Editor instance is ready.');
				try {
					if (this.localGeneratedEmailDesign) { // Use local state
						console.log('Loading generated design...');
						editor.loadDesign(JSON.parse(JSON.stringify(this.localGeneratedEmailDesign)));
					} else {
						console.log('No generated design found, loading default design.');
						editor.loadDesign(JSON.parse(JSON.stringify(this.defaultEmailDesign))); // defaultEmailDesign is from setup
					}

					console.log('Exporting HTML after initial load...');
					editor.exportHtml((data: {design: object, html: string}) => {
						console.log('HTML Exported after initial load');
						this.emailHtml = data.html; // emailHtml is from setup
					}, {
						cleanup: true,
					});

				} catch (error) {
					console.error('Error during initial editor loadDesign or exportHtml:', error);
					this.emailHtml = '<p>Error loading email preview.</p>';
				}
			} else {
				console.error('Could not find editor instance ref in editorLoaded.');
			}
		},
		handleRetryParse() {
			console.log('Retry parse button clicked!');
			this.$emit('retry-parse');
		},

		async handleBuildPlanClick() {
			console.log('Build Plan button clicked!');
			if (!this.localGeneratedPlanData) { // Use local state
				console.error("No generated plan data available to build.");
				alert("Error: No plan data is available to build.");
				return;
			}

			customerIOTrackEvent('Build Plan Clicked');
			this.isBuildingPlan = true; // isBuildingPlan is from setup

			console.log('Generated Plan Data:', this.localGeneratedPlanData); // Use local state

			try {
				const payload = {
					planName: this.localGeneratedPlanData.name || 'Generated Plan',
					planDescription: this.localGeneratedPlanData.description || '',
					planBusinessGoal: this.localGeneratedPlanData.goal || '',
					planStartDate: this.localGeneratedPlanData.startDate,
					planEndDate: this.localGeneratedPlanData.endDate,
					dataSummary: this.localGeneratedPlanData.dataSummary || '',
					versionPrompt: this.localGeneratedPlanData.versionPrompt || '',
					versionDescription: this.localGeneratedPlanData.versionDescription || 'Initial version generated from chat',

					campaigns: (this.localGeneratedPlanData.campaigns || []).map((camp: any) => ({
						name: camp.name || 'Generated Campaign',
						type: camp.campaignType || 'Unknown',
						description: camp.description || '',
						targetSegment: camp.targetSegment || 'General Audience',
						scheduledDate: camp.scheduledDate,
						businessGoal: camp.businessGoal || '',
						promotionTitle: camp.promotionTitle || '',
						promotionDescription: camp.promotionDescription || '',
						promotionType: camp.promotionType || '',
						whyText: camp.whyText || '',
						subjectLine: camp.subjectLine || '',
						previewText: camp.previewText || '',
						emailBriefText: camp.emailBriefText || '',
						emailJSON: camp.emailJSON || '',
						promotionSuggestionReason: camp.promotionSuggestionReason || '',
						taskType: camp.taskType || camp.campaignType,
					})),
				};

				if (!payload.planName) {
					throw new Error("Plan name is missing in generated data.");
				}
				if (!payload.planStartDate || !payload.planEndDate) {
					console.warn("Plan start date or end date might be missing in generated data.");
				}
				if (!payload.campaigns.every((c: any) => c.scheduledDate)) {
					throw new Error("One or more campaigns are missing a scheduled date.");
				}

				console.log('Sending payload to /api/v1/chat/plan/build:', JSON.stringify(payload, null, 2));

				// Use axios instance if available, otherwise fetch
				const api = axios.create({
					baseURL: Utils.URL_DOMAIN,
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
				});

				const response = await api.post('/chat/plan/build', payload);
				console.log('Response from build plan API:', response.data);

				if (response.data.success && response.data.planId) {
					const planId = response.data.planId;
					console.log('Plan built successfully! Plan ID:', planId);
					this.$emit('plan-built', {planId: planId, versionId: response.data.versionId});
					this.router.push(`/ai-strategist/planning/plan/${planId}`); // router is from setup
				} else {
					console.error('Backend indicated failure building plan:', response.data);
					alert(`Error building plan: ${response.data.message || 'Unknown error from server.'}`);
				}
			} catch (error: any) {
				const errorMessage = error.response?.data?.message || error.message || 'An unexpected error occurred.';
				console.error('Error calling build plan API:', error.response?.data || error.message);
				alert(`Error building plan: ${errorMessage}`);
			} finally {
				this.isBuildingPlan = false; // isBuildingPlan is from setup
			}
		},

		async handleBuildCampaignClick() {
			console.log('Build Campaign button clicked!');

			// Check if we have enough data to build a campaign
			const hasBriefContent = this.localBriefMarkdownContent !== null || this.localStreamingBriefContent !== '';

			if (!hasBriefContent) {
				console.error("No brief content available to build campaign.");
				alert("Error: Brief content is required to build a campaign.");
				return;
			}

			// If chatId not provided, use a default value - API requires it but it's not meaningful to users
			if (!this.chatId) {
				console.warn("No chat ID available for building campaign, using default value.");
				// For API purposes, we'll use a default ID if none provided
				// This could be replaced with getting the latest chat ID from a service
				this.chatId = 1; // Use default ID
			}

			customerIOTrackEvent('Build Campaign Clicked');
			this.isBuildingCampaign = true;

			try {
				// Always use the raw brief content directly without parsing
				let briefText = '';

				if (this.localBriefMarkdownContent) {
					// Use the full markdown content directly without parsing
					briefText = this.localBriefMarkdownContent;
				} else if (this.localStreamingBriefContent) {
					// Use streaming content if that's all we have
					briefText = this.localStreamingBriefContent;
				}

				// Define payload interface
				interface CampaignPayload {
					chatId: number;
					briefText: string;
					rawBriefContent: string | null;
					emailDesign?: string;
					emailHtml?: string;
				}

				// Prepare the payload for the API
				const payload: CampaignPayload = {
					chatId: this.chatId,
					briefText: briefText,
					rawBriefContent: this.localBriefMarkdownContent
				};

				// If we have email design/HTML, add them to the payload
				if (this.artifactView === 'email' && this.localGeneratedEmailDesign) {
					payload.emailDesign = JSON.stringify(this.localGeneratedEmailDesign);

					if (this.emailHtml) {
						payload.emailHtml = this.emailHtml;
					}
				}

				console.log('Sending payload to /api/v1/chat/plan/campaign:', JSON.stringify(payload, null, 2));

				// Make the API call
				const api = axios.create({
					baseURL: Utils.URL_DOMAIN,
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
				});

				const response = await api.post('/chat/plan/campaign', payload);
				console.log('Response from build campaign API:', response.data);

				if (response.data.success && response.data.campaignId) {
					const campaignId = response.data.campaignId;
					const taskId = response.data.taskId;

					console.log('Campaign built successfully! Campaign ID:', campaignId, 'Task ID:', taskId);
					this.$emit('campaign-built', {campaignId, taskId});

					// Navigate to the task detail page
					this.router.push(`/ai-strategist/tasks/${taskId}`);
				} else {
					console.error('Backend indicated failure building campaign:', response.data);
					alert(`Error building campaign: ${response.data.message || 'Unknown error from server.'}`);
				}
			} catch (error: any) {
				const errorMessage = error.response?.data?.message || error.message || 'An unexpected error occurred.';
				console.error('Error calling build campaign API:', error.response?.data || error.message);
				alert(`Error building campaign: ${errorMessage}`);
			} finally {
				this.isBuildingCampaign = false;
			}
		},
	},
});
</script>

<style scoped lang="css">
/* Add animation for the artifact drawer */
@keyframes slideInFromRight {
	0% {
		transform: translateX(20px);
		opacity: 0;
	}

	100% {
		transform: translateX(0);
		opacity: 1;
	}
}

/* Apply the animation to the component */
.relative {
	animation: slideInFromRight 0.3s ease-out forwards;
	backdrop-filter: blur(5px);
	-webkit-backdrop-filter: blur(5px);
}

.h-full {
	height: 100%;
}

.artifact-scrollbar {
	scrollbar-width: thin;
	scrollbar-color: #b1b2b4 #FFFFFF;
}

.artifact-scrollbar::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

.artifact-scrollbar::-webkit-scrollbar-track {
	background: #FFFFFF;
	border-radius: 9999px;
}

.artifact-scrollbar::-webkit-scrollbar-thumb {
	background-color: #8e9299;
	border-radius: 9999px;
	border: 2px solid transparent;
	background-clip: content-box;
}

.artifact-scrollbar::-webkit-scrollbar-thumb:hover {
	background-color: #8e9299;
}
</style>
