<template>
	<table class="min-w-full table-auto">
		<thead class="bg-gray-100">
			<tr>
				<th class="w-12 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
				</th>
				<th class="px-6 py-3 text-left text-xs font-medium font-['Inter'] text-gray-500 uppercase tracking-wider">
					{{ header1 }}
				</th>
				<th class="px-6 py-3 text-left text-xs font-medium font-['Inter'] text-gray-500 uppercase tracking-wider">
					{{ header2 }}
				</th>
				<th class="px-6 py-3 text-left text-xs font-medium font-['Inter'] text-gray-500 uppercase tracking-wider">
					{{ header3 }}
				</th>
				<th v-if="!hideSort" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
				</th>
			</tr>
		</thead>
		<tbody class="bg-white">
			<tr v-for="(row, index) in tableData" :key="index"
				:id="`${this.rowIdPrefix || 'row'}-${row.id}`"
				@click="onRowClick(row, index)"
				@mouseenter="onMouseEnter(index, row.id)"
				@mouseleave="onMouseLeave"
				:class="[
					'border-t border-b border-ralbackground-light-line hover:bg-indigo-50 hover:cursor-pointer focus:bg-indigo-50 focus:cursor-pointer',
					{'bg-white': `${this.rowIdPrefix || 'row'}-${row.id}` != highlightedRowId, 'bg-indigo-50': `${this.rowIdPrefix || 'row'}-${row.id}` == highlightedRowId}]">
				<td class="w-12 px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
					<div class="flex items-center justify-center">
						<div v-html="row.icon"></div>
					</div>
				</td>
				<td class="px-6 py-4 text-sm text-gray-500" style="max-width: 30rem;">
					<div class="text-sm font-semibold font-['Inter'] text-ralsecondary-start">{{ row.title }}</div>
					<div class="text-sm text-ralgray-dark">{{ row.subtitle }}</div>
				</td>
				<td class="px-6 py-4 whitespace-nowrap text-sm text-ralgray-dark">
					<div v-if="!hideStatusToggle" class="flex items-center justify-left">
						<ToggleItem
							@toggleChange="toggleRow(row, index)"
							:state="row.toggle"
							showLabel=true
							onLabel="Active"
							offLabel="Inactive"
							:isDisabled="row.configured === false"
							@click.stop>
						</ToggleItem>
					</div>
					<div v-else>
						{{  row.toggle ? 'Active' : 'Inactive' }}
					</div>
				</td>
				<td v-if="!hideSort" class="px-6 py-4 whitespace-nowrap text-sm text-ralgray-dark">
					<div class="flex items-center justify-left">
						<button @click.stop="moveRow(index, true)" :disabled="index === 0">
							<svg class="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
							</svg>
						</button>
						<button @click.stop="moveRow(index, false)" :disabled="index === tableData.length - 1">
							<svg class="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
							</svg>
						</button>
					</div>
				</td>
				<td class="w-40 px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
					<div class="flex justify-end align-center">
						<LightSecondaryButton cta="Edit" v-show="hoveredRowIndex === index"
							@click="onEditClick(row, index)"
						/>

						<div v-if="allowDelete && hoveredRowIndex === index" class="ml-3 mt-auto mb-auto">
							<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" class="cursor-pointer delete-icon" @click.stop="onDeleteClicked(row, index)">
								<path d="M280-120q-33 0-56.5-23.5T200-200v-520h-40v-80h200v-40h240v40h200v80h-40v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM360-280h80v-360h-80v360Zm160 0h80v-360h-80v360ZM280-720v520-520Z"/>
							</svg>
						</div>
						<!-- <svg class="ml-4" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M240-400q-33 0-56.5-23.5T160-480q0-33 23.5-56.5T240-560q33 0 56.5 23.5T320-480q0 33-23.5 56.5T240-400Zm240 0q-33 0-56.5-23.5T400-480q0-33 23.5-56.5T480-560q33 0 56.5 23.5T560-480q0 33-23.5 56.5T480-400Zm240 0q-33 0-56.5-23.5T640-480q0-33 23.5-56.5T720-560q33 0 56.5 23.5T800-480q0 33-23.5 56.5T720-400Z"/></svg> -->
					</div>
				</td>
			</tr>
		</tbody>
	</table>
</template>

<script>
import ToggleItem from './ToggleItem.ts.vue';
import LightSecondaryButton from './LightSecondaryButton.ts.vue';

export default {
	props: ['tableData', 'header1', 'header2', 'header3', 'hideStatusToggle', 'hideSort', 'allowDelete', 'rowIdPrefix', 'highlightedItemId'],
	components: {
		ToggleItem,
		LightSecondaryButton
	},
	emits: ['toggleChanged', 'editClicked', 'priorityChanged', 'deleteClicked'],
	data() {
		return {
			hoveredRowIndex: null,
			highlightedRowId: null,
		};
	},
	mounted() {},
	watch: {
		'highlightedItemId': {
			handler: function (newVal, oldVal) {
				this.highlightedRowId = newVal;
			},
		}
	},
	methods: {
		moveRow(index, moveUp) {
			if (moveUp && index > 0) {
				[this.tableData[index - 1], this.tableData[index]] = [this.tableData[index], this.tableData[index - 1]];
			} else if (!moveUp && index < this.tableData.length - 1) {
				[this.tableData[index + 1], this.tableData[index]] = [this.tableData[index], this.tableData[index + 1]];
			}
			this.tableData.forEach((item, idx) => item.priority = idx);
			const priorityUpdates = this.tableData.map(item => ({
				id: item.id,
				priority: item.priority
			}));
			this.$emit('priorityChanged', priorityUpdates);
		},
		toggleRow(rowData, index) {
			rowData.toggle = !rowData.toggle;
			this.$emit('toggleChanged', rowData);
		},
		onEditClick(rowData, index) {
			this.$emit('editClicked', rowData);
		},
		onDeleteClicked(rowData, index) {
			this.$emit('deleteClicked', rowData);
		},
		onRowClick(rowData, index) {
			this.$emit('editClicked', rowData);
		},
		onMouseEnter(index) {
			this.hoveredRowIndex = index;
			this.highlightedRowId = null;
		},
		onMouseLeave() {
			this.hoveredRowIndex = null;
		},
	}
};

</script>

<style>
.fade-enter-active,
.fade-leave-active {
	transition-property: opacity, transform;
	transition-duration: 500ms;
	transition-timing-function: ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
	opacity: 0;
	transform: translateX(10px);
}

.fade-enter-to,
.fade-leave-from {
	opacity: 1;
	transform: translateX(0);
}

.delete-icon {
	fill: #5f6368;
	transition: fill 0.5s;
}
.delete-icon:hover {
	fill: #DC2626;
}
</style>
