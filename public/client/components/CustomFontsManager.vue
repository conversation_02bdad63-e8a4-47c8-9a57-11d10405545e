<template>
	<!-- Custom Fonts Section -->
	<div class="bg-white rounded-lg border shadow-sm">
		<div class="p-6">
			<div class="flex items-center justify-between">
				<div>
					<h2 class="text-lg font-semibold">Custom Fonts</h2>
					<p class="text-sm text-gray-500">Add custom fonts for your email designs</p>
				</div>
				<button @click="showCustomFonts = !showCustomFonts" :class="[
					'px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200',
					showCustomFonts
						? 'bg-gray-100 text-gray-700 border-gray-200'
						: 'bg-purple-600 text-white border-purple-600 hover:bg-purple-700'
				]">
					<span v-if="!showCustomFonts">Add Custom Font</span>
					<span v-else>Hide Custom Font</span>
				</button>
			</div>
		</div>

		<!-- Collapsible Font Settings -->
		<div v-if="showCustomFonts" class="p-6 pt-0 space-y-6 border-t border-gray-100">

			<!-- Upload Method -->
			<div class="space-y-6">
				<!-- Font Family Name Input -->
				<div class="space-y-4">
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-2">Font Family Name</label>
						<input v-model="newFontFamily" type="text" placeholder="e.g., Montserrat, Custom Brand Font"
							class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500" />
						<p class="text-xs text-gray-500 mt-1">This will be the name used in the font dropdown</p>
					</div>

					<!-- Weight Selection -->
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-2">Font Weight</label>
						<select v-model="newFontWeight"
							class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
							<option v-for="option in fontWeightOptions" :key="option.value" :value="option.value">
								{{ option.label }} ({{ option.value }})
							</option>
						</select>
					</div>

					<!-- Fallback Font Input -->
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-2">Fallback Font</label>
						<input v-model="newFontFallback" type="text" placeholder="sans-serif"
							class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500" />
						<p class="text-xs text-gray-500 mt-1">Used when the custom font fails to load (e.g., sans-serif, serif, monospace)</p>
					</div>

					<!-- File Upload -->
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-2">Font File</label>
						<div v-if="!selectedFontFile"
							class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors duration-200">
							<input type="file" accept=".ttf,.otf,.woff,.woff2" @change="handleSingleFontUpload"
								class="hidden" ref="singleFontInput" />
							<div @click="$refs.singleFontInput.click()" class="cursor-pointer">
								<svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor"
									viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
										d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
								</svg>
								<p class="mt-2 text-sm text-gray-600">Click to upload font file</p>
								<p class="text-xs text-gray-500">TTF, OTF, WOFF, WOFF2 • Max 5MB</p>
							</div>
						</div>
						<!-- Show selected file -->
						<div v-else class="border-2 border-green-300 bg-green-50 rounded-lg p-4">
							<div class="flex items-center justify-between">
								<div class="flex items-center">
									<svg class="w-8 h-8 text-green-600 mr-3" fill="none" stroke="currentColor"
										viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
											d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
									</svg>
									<div>
										<p class="text-sm font-medium text-green-900">{{ selectedFontFile.name }}
										</p>
										<p class="text-xs text-green-700">{{ Math.round(selectedFontFile.size /
											1024) }} KB</p>
									</div>
								</div>
								<button @click="clearSelectedFile" class="text-green-600 hover:text-green-800">
									<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
											d="M6 18L18 6M6 6l12 12" />
									</svg>
								</button>
							</div>
						</div>
					</div>

					<!-- Upload Button -->
					<button @click="uploadNewFont" :disabled="!canUploadFont || isProcessingFonts" :class="[
						'w-full py-2 px-4 rounded-lg font-medium transition-colors duration-200',
						canUploadFont && !isProcessingFonts
							? 'bg-purple-600 text-white hover:bg-purple-700'
							: 'bg-gray-300 text-gray-500 cursor-not-allowed'
					]">
						<span v-if="isProcessingFonts" class="flex items-center justify-center">
							<svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
								<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
									stroke-width="4"></circle>
								<path class="opacity-75" fill="currentColor"
									d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
								</path>
							</svg>
							Uploading...
						</span>
						<span v-else>Add Font</span>
					</button>
				</div>
			</div>

			<!-- Existing Custom Fonts List -->
			<div v-if="customFonts.length > 0" class="space-y-4">
				<label class="text-sm font-medium text-gray-700">Current Custom Fonts</label>
				<div class="space-y-3">
					<div v-for="(font, index) in customFonts" :key="index"
						class="p-4 bg-gray-50 rounded-lg border border-gray-200">
						<div class="flex items-center justify-between">
							<div>
								<h4 class="font-medium text-gray-900">{{ font.name }}</h4>
								<p class="text-sm text-gray-600">{{ font.weights.length }} weight(s): {{
									font.weights.map(w => w.weight).join(', ') }}</p>
								<p class="text-xs text-gray-500">CSS: {{ font.cssValue }}</p>
								<p class="text-xs text-gray-400" v-if="font.fallback">Fallback: {{ font.fallback }}</p>
							</div>
							<button @click="removeCustomFont(index)" class="text-red-600 hover:text-red-800 p-1">
								<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
										d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
								</svg>
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';
import * as OrganizationSettings from '../services/organization-settings.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'CustomFontsManager',
	emits: ['status-update'],
	data() {
		return {
			customFonts: [], // Array of complete font configurations
			showCustomFonts: false,
			isProcessingFonts: false,
			newFontFamily: '',
			newFontWeight: 400,
			newFontFallback: 'sans-serif',
			selectedFontFile: null,
			fontWeightOptions: [
				{ label: 'Thin', value: 100 },
				{ label: 'Extra Light', value: 200 },
				{ label: 'Light', value: 300 },
				{ label: 'Regular', value: 400 },
				{ label: 'Medium', value: 500 },
				{ label: 'Semi Bold', value: 600 },
				{ label: 'Bold', value: 700 },
				{ label: 'Extra Bold', value: 800 },
				{ label: 'Black', value: 900 }
			],
		};
	},
	async mounted() {
		await this.loadCustomFonts();
	},
	computed: {
		canUploadFont() {
			return !!(this.newFontFamily.trim() && this.newFontWeight && this.selectedFontFile);
		}
	},
	methods: {
		async loadCustomFonts() {
			try {
				// Load custom fonts data
				const customFontsString = await OrganizationSettings.getOrganizationSetting('customFonts');
				if (customFontsString) {
					try {
						this.customFonts = JSON.parse(customFontsString);
						// Load CSS for all fonts
						this.customFonts.forEach(fontFamily => {
							if (fontFamily.cssUrl) {
								this.loadFontCSS(fontFamily.cssUrl);
							}
						});
					} catch (error) {
						console.error('Error parsing custom fonts:', error);
						this.customFonts = [];
					}
				}
			} catch (error) {
				console.error('Error loading custom fonts:', error);
				this.$emit('status-update', 'error', 'Failed to load custom fonts');
			}
		},
		handleSingleFontUpload(event) {
			console.log('handleSingleFontUpload called with event:', event);
			const file = event.target.files[0];
			console.log('Selected file:', file);
			if (!file) return;

			// Validate file type
			const allowedTypes = ['.ttf', '.otf', '.woff', '.woff2'];
			const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
			if (!allowedTypes.includes(fileExtension)) {
				this.$emit('status-update', 'error', `Invalid file type. Please upload a font file (${allowedTypes.join(', ')})`);
				event.target.value = '';
				return;
			}

			// Validate file size (max 5MB for fonts)
			const maxSize = 5 * 1024 * 1024; // 5MB
			if (file.size > maxSize) {
				this.$emit('status-update', 'error', 'Font file too large. Maximum size is 5MB.');
				event.target.value = '';
				return;
			}

			this.selectedFontFile = file;
			console.log('Font file selected successfully:', file.name, 'Size:', Math.round(file.size / 1024), 'KB');
			this.$emit('status-update', 'success', `Selected: ${file.name} (${Math.round(file.size / 1024)} KB)`);
		},
		clearSelectedFile() {
			this.selectedFontFile = null;
			if (this.$refs.singleFontInput) {
				this.$refs.singleFontInput.value = '';
			}
			this.$emit('status-update', '', '');
		},
		async uploadNewFont() {
			console.log('uploadNewFont called, canUploadFont:', this.canUploadFont);
			console.log('newFontFamily:', this.newFontFamily);
			console.log('newFontWeight:', this.newFontWeight);
			console.log('selectedFontFile:', this.selectedFontFile);

			if (!this.canUploadFont) {
				console.log('Cannot upload font - validation failed');
				return;
			}

			try {
				this.isProcessingFonts = true;
				this.$emit('status-update', 'info', `Uploading ${this.selectedFontFile.name}...`);

				// Create FormData for upload
				const formData = new FormData();
				formData.append('file', this.selectedFontFile);
				formData.append('weight', this.newFontWeight.toString());
				formData.append('fontFamily', this.newFontFamily);

				// Upload to S3
				const response = await fetch(`${URL_DOMAIN}/branding/fonts/upload`, {
					method: 'POST',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					},
					body: formData
				});

				if (!response.ok) {
					throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
				}

				const uploadResult = await response.json();

				// Find or create font family
				let fontFamily = this.customFonts.find(f => f.name === this.newFontFamily);
				if (!fontFamily) {
					// Create CSS value with fallback font
					const cssValue = `'${this.newFontFamily}', ${this.newFontFallback}`;
					fontFamily = {
						name: this.newFontFamily,
						cssValue: cssValue,
						cssUrl: '',
						weights: [],
						fallback: this.newFontFallback
					};
					this.customFonts.push(fontFamily);
				}

				// Add weight to font family
				const weightLabel = this.fontWeightOptions.find(w => w.value === this.newFontWeight)?.label || 'Custom';
				fontFamily.weights.push({
					name: weightLabel,
					weight: this.newFontWeight,
					url: uploadResult.url,
					filename: uploadResult.filename
				});

				// Generate CSS for this font family
				await this.generateFontFamilyCSS(fontFamily);

				this.$emit('status-update', 'success', `Added ${weightLabel} weight to ${this.newFontFamily}`);

				// Reset form
				this.newFontFamily = '';
				this.newFontWeight = 400;
				this.newFontFallback = 'sans-serif';
				this.selectedFontFile = null;
				if (this.$refs.singleFontInput) {
					this.$refs.singleFontInput.value = '';
				}

				// Save to organization settings
				await this.saveCustomFonts();

			} catch (error) {
				console.error('Error uploading font:', error);
				this.$emit('status-update', 'error', `Failed to upload font: ${error.message}`);
			} finally {
				this.isProcessingFonts = false;
			}
		},
		async generateFontFamilyCSS(fontFamily) {
			try {
				// Prepare font data for CSS generation
				const fontData = {
					fontFamily: fontFamily.name,
					fonts: {}
				};

				// Convert weights to object keyed by weight
				fontFamily.weights.forEach(weight => {
					fontData.fonts[weight.weight] = weight.url;
				});

				// Call backend to generate CSS
				const response = await fetch(`${URL_DOMAIN}/branding/fonts/generate-css`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(fontData)
				});

				if (!response.ok) {
					throw new Error(`CSS generation failed: ${response.status} ${response.statusText}`);
				}

				const result = await response.json();
				fontFamily.cssUrl = result.cssUrl;

				// Load the CSS for preview
				this.loadFontCSS(fontFamily.cssUrl);

			} catch (error) {
				console.error('Error generating CSS for font family:', error);
				throw error;
			}
		},
		loadFontCSS(cssUrl) {
			// Remove existing font stylesheets for this font
			const existingLinks = document.querySelectorAll(`link[data-font-css="${cssUrl}"]`);
			existingLinks.forEach(link => link.remove());

			// Add new font stylesheet
			const link = document.createElement('link');
			link.rel = 'stylesheet';
			link.href = cssUrl;
			link.setAttribute('data-font-css', cssUrl);
			document.head.appendChild(link);
		},
		removeCustomFont(index) {
			const fontFamily = this.customFonts[index];
			this.customFonts.splice(index, 1);

			// Remove CSS
			if (fontFamily.cssUrl) {
				const links = document.querySelectorAll(`link[data-font-css="${fontFamily.cssUrl}"]`);
				links.forEach(link => link.remove());
			}

			this.$emit('status-update', 'info', `Removed font family: ${fontFamily.name}`);

			// Save changes
			this.saveCustomFonts();
		},
		async saveCustomFonts() {
			try {
				// Save the custom fonts array to organization settings
				await OrganizationSettings.updateOrganizationSetting('customFonts', JSON.stringify(this.customFonts));
			} catch (error) {
				console.error('Error saving custom fonts:', error);
			}
		},
		// Expose method for parent to get current fonts for saving
		getCurrentFonts() {
			return this.customFonts;
		}
	}
};
</script>