<template>
	<div class="flex flex-wrap items-center -m-3">

		<div class="m-3 min-w-24">
			<!-- Start -->
			<div class="flex items-center">
				<div class="form-switch" @click="toggleState">
					<input type="checkbox" id="toggle1" class="sr-only" v-model="toggle" true-value="true"
						false-value="false" :disabled="disabledState" />
					<label class="bg-slate-400" for="toggle">
						<span class="bg-white shadow-sm" aria-hidden="true"></span>
						<span class="sr-only">Toggle</span>
					</label>
				</div>
				<div class="text-sm text-slate-400 italic ml-2" v-if="labelState">{{ toggleLabel }}</div>
			</div>
			<!-- End -->
		</div>
	</div>
</template>

<script>
export default {
	props: {
		state: {
			type: [Boolean, String],
			default: false
		},
		isDisabled: {
			type: [<PERSON>ole<PERSON>, String],
			default: false
		},
		showLabel: {
			type: Boolean,
			default: false
		},
		onLabel: {
			type: String,
			default: 'On'
		},
		offLabel: {
			type: String,
			default: 'Off'
		}
	},
	data() {
		return {
			toggle: false,
			disabledState: false,
			labelState: false
		};
	},
	watch: {
		state(newVal) {
			this.toggle = newVal;
		},
		isDisabled(newVal) {
			this.disabledState = newVal;
		},
		onLabel(newVal) {
			this.onLabel = newVal;
		},
		offLabel(newVal) {
			this.offLabel = newVal;
		},
	},
	computed: {
		toggleLabel() {
			//No idea where props are not workign here, and it always uses defaults
			return this.toggle ? this.onLabel : this.offLabel;
		}
	},
	methods: {
		toggleState() {
			if (this.disabledState)
				return;

			this.toggle = !this.toggle;
			this.$emit('toggleChange', this.toggle);
		},
	},
	mounted() {
		this.toggle = this.state === true || this.state === 'true';
		this.disabledState = this.isDisabled === true || this.isDisabled === 'true';
		this.labelState = this.showLabel;
	}
};
</script>

<style>
</style>
