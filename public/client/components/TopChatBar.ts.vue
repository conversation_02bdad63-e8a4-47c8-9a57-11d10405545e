<template>
  <div class="w-full bg-white shadow-sm px-4 py-2 flex items-center justify-between border-b border-gray-200">
    <!-- Left side: History Icon Button -->
    <button @click="$emit('toggle-drawer')" class="p-2 rounded-md hover:bg-gray-100 text-gray-600">
      <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="currentColor">
        <path d="M120-240v-80h720v80H120Zm0-200v-80h720v80H120Zm0-200v-80h720v80H120Z"/>
      </svg>
      <!-- We can add screen reader text here later -->
    </button>

    <!-- Right side: Connection Buttons & Artifact Toggle -->
    <div class="flex items-center space-x-3">
      <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1.5 rounded-md hover:bg-gray-200 transition-colors">
        Connect to Klaviyo
      </button>
      <!-- <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1.5 rounded-md hover:bg-gray-200 transition-colors">
        Connect to Shopify
      </button> -->
<!--
      <button @click="$emit('toggle-artifact-drawer')" class="p-2 rounded-md hover:bg-gray-100 text-gray-600">
        <svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 -960 960 960" width="20px" fill="currentColor">
          <path d="M480-320 280-520l56-58 144 144 144-144 56 58-200 200ZM200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm0 0v-560 560Z"/>
        </svg>
      </button> -->
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'TopChatBar',
  // Props, emits, methods for drawer and connections can be added later
});
</script>

<style scoped>
/* Add any specific styles if needed */
</style>
