<template>
  <div class="w-full bg-white shadow-sm px-4 py-2 flex items-center justify-between border-b border-gray-200">
    <!-- Left side: History Icon Button and Conversation Info -->
    <div class="flex items-center space-x-3">
      <button @click="$emit('toggle-drawer')" class="p-2 rounded-md hover:bg-gray-100 text-gray-600">
        <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="currentColor">
          <path d="M120-240v-80h720v80H120Zm0-200v-80h720v80H120Zm0-200v-80h720v80H120Z"/>
        </svg>
        <!-- We can add screen reader text here later -->
      </button>

      <!-- Conversation Info -->
      <div v-if="conversation" class="flex flex-col">
        <h2 class="text-sm font-medium text-gray-900 truncate max-w-xs">
          {{ conversation.name || 'Untitled Conversation' }}
        </h2>
        <div class="text-xs text-gray-500 flex items-center space-x-2">
          <span v-if="conversation.createdByUser">
            Created by {{ formatUserName(conversation.createdByUser) }}
          </span>
          <span v-if="conversation.createdByUser && conversation.createdAt">•</span>
          <span v-if="conversation.createdAt">
            {{ formatDate(conversation.createdAt) }}
          </span>
        </div>
      </div>
    </div>

    <!-- Right side: Connection Buttons & Artifact Toggle -->
    <div class="flex items-center space-x-3">
      <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1.5 rounded-md hover:bg-gray-200 transition-colors">
        Connect to Klaviyo
      </button>
      <!-- <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1.5 rounded-md hover:bg-gray-200 transition-colors">
        Connect to Shopify
      </button> -->
<!--
      <button @click="$emit('toggle-artifact-drawer')" class="p-2 rounded-md hover:bg-gray-100 text-gray-600">
        <svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 -960 960 960" width="20px" fill="currentColor">
          <path d="M480-320 280-520l56-58 144 144 144-144 56 58-200 200ZM200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm0 0v-560 560Z"/>
        </svg>
      </button> -->
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

interface ConversationUser {
  id: number;
  email: string;
  firstName?: string;
  lastName?: string;
}

interface ConversationInfo {
  id: number;
  name?: string;
  createdAt: string;
  createdByUser?: ConversationUser;
}

export default defineComponent({
  name: 'TopChatBar',
  props: {
    conversation: {
      type: Object as PropType<ConversationInfo>,
      default: null
    }
  },
  methods: {
    formatUserName(user?: ConversationUser): string {
      if (!user) return 'Unknown User';

      const firstName = user.firstName?.trim();
      const lastName = user.lastName?.trim();

      if (firstName && lastName) {
        return `${firstName} ${lastName}`;
      } else if (firstName) {
        return firstName;
      } else if (lastName) {
        return lastName;
      } else {
        // Fallback to email if no name is available
        return user.email;
      }
    },

    formatDate(dateString: string): string {
      const date = new Date(dateString);
      const now = new Date();
      const diffInMs = now.getTime() - date.getTime();
      const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

      if (diffInDays === 0) {
        return 'Today';
      } else if (diffInDays === 1) {
        return 'Yesterday';
      } else if (diffInDays < 7) {
        return `${diffInDays} days ago`;
      } else {
        return date.toLocaleDateString();
      }
    }
  }
});
</script>

<style scoped>
/* Add any specific styles if needed */
</style>
