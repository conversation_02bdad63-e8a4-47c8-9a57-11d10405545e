<template>
	<div class="w-full max-w-7xl mx-auto p-6">
	  <div class="mb-8">
		<h2 class="text-xl font-semibold text-gray-900 mb-2">
		  {{ chartTitle }}  </h2>
		<p class="text-gray-600">
		  Analyze how customers progress through multiple purchases
		</p>
	  </div>

	  <!-- Loading state -->
	  <div v-if="!data || isLoading" class="flex flex-col items-center justify-center py-12">
		<div class="spinner mb-4"></div>
		<p class="text-gray-600">Loading purchase path data...</p>
	  </div>

	  <!-- Content when data is available -->
	  <template v-else>
		<div class="flex items-center gap-4 mb-6">
		  <select
			v-model="selectedCategory"
			@change="handleCategoryChange"
			class="px-4 py-2 border rounded-lg bg-white min-w-[200px]"
		  >
			<option
			  v-for="category in categories"
			  :key="category"
			  :value="category"
			>
			  {{ category === 'ALL' ? 'All Products' : category }}
			</option>
		  </select>

		  <div v-if="selectedPath.length > 0" class="text-sm text-gray-600">
			Selected Path: {{ selectedPath.join(' → ') }}
		  </div>
		</div>

		<!-- Empty state when no products -->
		<div v-if="!allProductsData.length" class="flex flex-col items-center justify-center py-12">
			<div class="text-gray-400 mb-3">
			  <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4M12 20V4" />
			  </svg>
			</div>
			<p class="text-gray-600 mb-2">No purchase path data available</p>
			<p class="text-gray-500 text-sm">Try selecting a different category or check back later</p>
		  </div>

		<!-- Products list when data exists -->
		<div v-else class="flex gap-4 relative overflow-x-auto pb-4">
		  <template v-for="(columnData, columnIndex) in displayedColumns" :key="'column-'+columnIndex">
			<!-- Fixed width column container -->
			<div class="w-[300px] flex-shrink-0">
			  <div class="space-y-4">
				<div class="flex justify-between items-center">
				  <h3 class="font-medium text-gray-900">Purchase #{{ columnIndex + 1 }}</h3>
				  <span class="text-sm text-gray-500" v-if="columnData && columnData.length">{{ columnData.length }} products</span>
				</div>
				<!-- Product list container without scroll -->
				<div class="space-y-4">
				  <div
					v-for="item in columnData"
					:key="item.id"
					@click="selectItem(item, columnIndex)"
					class="bg-white rounded-lg p-4 cursor-pointer transition-all hover:shadow-lg"
					:class="{'border-2 border-blue-500': isSelected(item, columnIndex), 'border border-gray-200': !isSelected(item, columnIndex)}"
				  >
					<div class="space-y-3">
					  <div class="flex items-center gap-3">
						<div class="h-10 w-10 flex-shrink-0 bg-gray-100 rounded-lg" />
						<div class="min-w-0 flex-1">
							<h4 class="font-medium text-gray-900 line-clamp-2 min-h-[2.5rem]">{{ item.name }}</h4>
						  <div class="text-sm text-blue-600">
							{{ item.purchases }} purchases
						  </div>
						</div>
					  </div>
					  <div class="space-y-2">
						<div>
						  <div class="text-sm text-gray-600 mb-1">Conversion rate</div>
						  <div class="h-2 w-full bg-gray-100 rounded">
							<div
							  class="h-full bg-blue-600 rounded"
							  :style="{ width: item.conversionRate + '%' }"
							/>
						  </div>
						  <div class="text-right text-sm text-gray-900 mt-1">
							{{ item.conversionRate }}%
						  </div>
						</div>
						<div class="text-sm text-gray-900">
						  Revenue: {{ formatRevenue(item.revenue) }}
						</div>
						<div class="text-sm text-gray-900">
						  Sub Rate: {{ item.subscriptionRate }}%
						</div>
						<div class="text-sm text-gray-900">
						  Member Rate: {{ item.memberRate }}%
						</div>
					  </div>
					</div>
				  </div>
				</div>
			  </div>
			</div>

			<!-- Fixed width continuation indicator -->
			<div v-if="showContinuationIndicator(columnIndex)" class="flex items-center w-[80px] flex-shrink-0 justify-center">
			  <div class="flex items-center relative w-full">
				<div class="absolute w-full h-[2px] bg-gray-200"></div>
				<div class="relative bg-white px-3 py-1 rounded-full border border-gray-200 shadow-sm">
				  <span class="text-sm text-gray-600">{{ calculateContinuationConversion(columnIndex) }}%</span>
				  <svg xmlns="http://www.w3.org/2000/svg" class="inline-block w-4 h-4 text-gray-400 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
					<polyline points="9 18 15 12 9 6" />
				  </svg>
				</div>
			  </div>
			</div>
		  </template>
		</div>
	  </template>
	</div>
  </template>

  <script>
  // No need for axios here

  export default {
	name: 'PurchasePathAnalysis',
	props: {
	  data: {
		type: Object, // Changed from Array to Object
		required: true,
	  },
	  chartTitle: {
		type: String,
		default: 'Purchase Path Analysis', //  A default title.
	  },
	},
	data() {
	  return {
		categories: [],  //  Populate this from your data.
		selectedCategory: 'ALL',
		selectedPath: [],
		// allProductsData: [], //  No longer needed - use the prop directly.
		displayedColumns: [],  //  This will hold the data for each column.
		isLoading: true, // Start with loading true
	  };
	},
	computed: {
		allProductsData() {
			if (!this.data) return [];
			const selectedCategory = this.selectedCategory || 'ALL';
			const category = this.data[selectedCategory];
			if (!category || !category.products) return [];

			// Return products directly without mapping
			return category.products;
		}
	},
	methods: {
	  formatRevenue(value) {
		return new Intl.NumberFormat('en-US', {
		  style: 'currency',
		  currency: 'USD',
		  minimumFractionDigits: 0,
		  maximumFractionDigits: 0,
		}).format(value);
	  },
	  handleCategoryChange() {
		console.log('Category changed to:', this.selectedCategory);
		console.log('Data structure:', this.data);
		console.log('Selected category data:', this.data[this.selectedCategory]);

		this.resetData();
		this.displayedColumns = [this.getFirstColumnItems()];
		console.log('First column items after category change:', this.displayedColumns[0]);
	  },
	  resetData() {
		this.selectedPath = [];
		this.displayedColumns = []; // Clear all columns.
	  },
	  selectItem(item, columnIndex) {
		// Update selectedPath
		this.selectedPath = this.selectedPath.slice(0, columnIndex);
		this.selectedPath.push(item.name);

		// Calculate next column based on the selected item's nextpurchases
		if (item.nextpurchases && item.nextpurchases.length > 0) {
		  // Trim columns up to current index
		  this.displayedColumns = this.displayedColumns.slice(0, columnIndex + 1);
		  // Add the nextpurchases array as the next column
		  const nextColumn = item.nextpurchases.map(nextItem => ({
			...nextItem,
			// Ensure the item has all required properties
			purchases: nextItem.purchases || 0,
			quantity: nextItem.quantity || 0,
			conversionRate: nextItem.conversionRate || 0,
			revenue: nextItem.revenue || 0,
			subscriptionRate: nextItem.subscriptionRate || 0,
			memberRate: nextItem.memberRate || 0
		  }));
		  this.displayedColumns.push(nextColumn);
		} else {
		  // If no nextpurchases, remove subsequent columns
		  this.displayedColumns = this.displayedColumns.slice(0, columnIndex + 1);
		}
	  },
	  isSelected(item, columnIndex) {
		return this.selectedPath[columnIndex] === item.name;
	  },

	  getFirstColumnItems() {
		console.log('Getting first column items for category:', this.selectedCategory);
		const products = this.allProductsData;
		console.log('Raw products:', products);

		if (!products || products.length === 0) {
			console.log('No products found');
			return [];
		}

		// Return products directly without grouping
		return products;
	},

	  showContinuationIndicator(columnIndex) {
		// Show only if there's another column after this one
		return this.displayedColumns[columnIndex + 1] !== undefined;
	  },

	  calculateContinuationConversion(columnIndex) {
		// Get the currently selected item in this column
		const currentItem = this.displayedColumns[columnIndex].find(
		  item => item.name === this.selectedPath[columnIndex]
		);

		if (!currentItem || !currentItem.nextpurchases) return 0;

		// Total purchases of the current item
		const currentPurchases = currentItem.purchases;

		// Sum of purchases in nextpurchases
		const nextPurchasesTotal = currentItem.nextpurchases.reduce(
		  (sum, item) => sum + (item.purchases || 0),
		  0
		);

		// Calculate percentage
		return currentPurchases > 0
		  ? Math.round((nextPurchasesTotal / currentPurchases) * 100)
		  : 0;
	  },
	  mapProduct(product) {
		return {
		  id: product.id,
		  name: product.name,
		  purchases: product.purchases || 0,
		  quantity: product.quantity || 0,
		  conversionRate: product.conversionRate || 0,
		  revenue: product.revenue || 0,
		  subscriptionRate: product.subscriptionRate || 0,
		  memberRate: product.memberRate || 0,
		  nextpurchases: product.nextpurchases || []
		};
	  },
	  // No fetchData method needed
	},
	watch: {
		data: {
			handler: function(newData) {
				console.log('Data changed:', newData);
				if (!newData) {
					this.categories = [];
					this.displayedColumns = [];
					this.isLoading = true;
					return;
				}

				// Keep loading true until we process the data
				this.isLoading = true;

				// Get all categories including ALL
				const allCategories = Object.keys(newData);
				console.log('All categories:', allCategories);
				// Don't filter out ALL category
				this.categories = allCategories;
				console.log('Categories:', this.categories);

				// Default to ALL if it exists
				if (allCategories.includes('ALL')) {
					this.selectedCategory = 'ALL';
				} else if (allCategories.length > 0) {
					this.selectedCategory = allCategories[0];
				}
				console.log('Selected category:', this.selectedCategory);

				this.resetData();
				this.displayedColumns = [this.getFirstColumnItems()];
				console.log('First column items:', this.displayedColumns[0]);

				// Small delay to ensure smooth transition
				setTimeout(() => {
					this.isLoading = false;
				}, 100);
			},
			immediate: true, // Call the handler immediately when data changes
			deep: true //Watch for deep changes
		},
		selectedCategory: {
			handler: function(newCategory) {
			  console.log('Category changed to:', newCategory);
			  console.log('Current products:', this.allProductsData);
			  this.displayedColumns = [this.getFirstColumnItems()];
			}
		  }
	},
	created() {
		// Remove the initial category selection here since it's handled in the watcher
		if (this.data) {
		  console.log('Initial data:', this.data);
		}
	  },
	mounted() {
	  // No fetchData here
	  this.displayedColumns = [this.getFirstColumnItems()]; // Initialize first column
	  console.log('Initial displayedColumns:', this.displayedColumns);
	},
  };
  </script>

  <style scoped>
.spinner {
  border: 3px solid #f3f4f6; /* Gray-100 */
  border-left-color: #3b82f6; /* Blue-500 to match your design */
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
  </style>
