<template>
  <div v-if="show"
       class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
       tabindex="-1"
       role="dialog">
    <!-- Modal Content -->
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-auto animate-modal-appear"
         role="document"
         @click.stop>
      <div class="sticky top-0 bg-white p-6 border-b z-10 flex justify-between items-center">
        <h3 class="text-lg font-semibold">{{ isEditMode ? 'Edit Brand Image' : 'Save Brand Image' }}</h3>
        <button @click="closeModal" class="text-gray-400 hover:text-gray-600 transition">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Upload Form -->
      <div class="p-6">
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Image Name</label>
              <input
                v-model="imageData.name"
                type="text"
                placeholder="e.g. Main Logo"
                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                @input="validateImageData"
              />
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">AI Image Tag <span class="text-gray-400 text-xs">(optional)</span></label>
              <select
                v-model="imageData.imageType"
                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                @change="validateImageData"
              >
                <option value="">Exclude from AI</option>
                <!-- Default categories -->
                <option value="Logo">Logo</option>
                <option value="Hero Image">Hero Image</option>
                <option value="Banner">Banner</option>
                <option value="Product">Product</option>
                <option value="Background">Background</option>
                <option value="Icon">Icon</option>
                <option value="Reviews">Reviews</option>
                <!-- Custom categories -->
                <option v-for="category in customCategories" :key="category" :value="category">
                  {{ category }}
                </option>
              </select>
              <p class="text-xs text-gray-500 mt-1">Only categorized images will be available for AI-generated emails</p>
            </div>
          </div>

          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <label class="block text-sm font-medium">Describe when AI should use this</label>
              <button
                @click="generateDescription"
                class="flex items-center gap-2 px-3 py-1.5 text-sm rounded-lg text-purple-600 hover:bg-purple-50 border border-purple-200 transition-colors"
                :disabled="isGenerating"
              >
                <svg xmlns="http://www.w3.org/2000/svg" :class="['h-4 w-4', { 'animate-spin': isGenerating }]" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path>
                  <path d="M20 3v4"></path>
                  <path d="M22 5h-4"></path>
                  <path d="M4 17v2"></path>
                  <path d="M5 18H3"></path>
                </svg>
                <span>{{ isGenerating ? 'Generating...' : 'Generate AI Instructions' }}</span>
              </button>
            </div>
            <textarea
              v-model="imageData.description"
              rows="4"
              placeholder="Describe what this image is, and any limitations of when it should and shouldn't be used."
              class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
            ></textarea>
          </div>

          <!-- Image preview -->
          <div v-if="imageData.url" class="border rounded-lg overflow-hidden">
            <div class="relative bg-gray-50 flex items-center justify-center p-4" style="height: 200px;">
              <img :src="imageData.url" :alt="imageData.name" class="object-contain max-w-full max-h-full" />
            </div>
            <!-- Display dimensions for image -->
            <div class="px-3 py-2 text-xs text-gray-500 text-center border-t">
              Size: {{ imageData.width || '?' }}px × {{ imageData.height || '?' }}px
            </div>
          </div>

          <!-- Validation error message -->
          <div v-if="validationError" class="text-sm text-red-600 bg-red-50 p-3 rounded-lg">
            {{ validationError }}
          </div>

          <!-- Actions -->
          <div class="flex justify-end gap-3 pt-2">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 border rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="button"
              @click="saveImage"
              :disabled="!canSaveImage"
              :class="[
                'px-4 py-2 rounded-lg transition',
                canSaveImage
                  ? 'bg-purple-600 text-white hover:bg-purple-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              ]"
            >
              {{ isEditMode ? 'Update Image' : 'Save Image' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
  name: 'EditBrandImageModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    image: {
      type: Object,
      default: () => ({})
    },
    isEditMode: {
      type: Boolean,
      default: false
    },
    assetType: {
      type: String,
      default: 'email'
    },
    customCategories: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      imageData: {
        id: null,      // Add ID to track the image
        name: '',
        imageType: '',
        width: '',
        height: '',
        description: '',
        url: null,
        file: null
      },
      isGenerating: false,
      canSaveImage: false,
      validationError: ''
    };
  },
  watch: {
    show(newVal) {
      if (newVal && this.image) {
        this.initializeImageData();
      }
    },
    image(newVal) {
      if (newVal && this.show) {
        this.initializeImageData();
      }
    }
  },
  methods: {
    initializeImageData() {
      console.log('Initializing with isEditMode:', this.isEditMode, 'Image:', this.image); // Debug log

      // Initialize the form with image data
      this.imageData = {
        id: this.image.id, // Ensure we capture the ID
        name: this.image.name || this.image.friendlyname || '',
        imageType: this.image.imageType || '',
        width: this.image.width || '',
        height: this.image.height || '',
        description: this.image.description || '',
        url: this.image.url || '',
        file: null
      };
      console.log('Initializing image data with ID:', this.image.id); // Debug log
      this.validateImageData();
    },
    validateImageData() {
      // Reset validation error
      this.validationError = '';

      // Validate name field
      if (!this.imageData.name) {
        this.validationError = 'Please provide a name for the image';
        this.canSaveImage = false;
        return;
      }

      // Additional validation can be added here
      // For example, checking if the image URL is valid
      if (this.imageData.url && !this.isValidUrl(this.imageData.url)) {
        this.validationError = 'The image URL is invalid';
        this.canSaveImage = false;
        return;
      }

      // All validation passed
      this.canSaveImage = true;
    },

    isValidUrl(url) {
      try {
        new URL(url);
        return true;
      } catch (error) {
        return false;
      }
    },
    closeModal() {
      this.$emit('close');
    },
    async saveImage() {
      if (!this.canSaveImage) return;

      try {
        console.log('Saving image in mode:', this.isEditMode ? 'EDIT' : 'CREATE', 'ImageData:', this.imageData); // Debug log
        this.$emit('status-update', 'loading', 'Saving image...');

        // Prepare update data
        const updateData = {
          id: this.isEditMode ? this.image.id : this.imageData.id, // Use the original image ID for edit mode
          url: this.imageData.url,
          friendlyname: this.imageData.name,
          imageType: this.imageData.imageType,
          width: this.imageData.width,
          height: this.imageData.height,
          assetType: this.assetType,
          description: this.imageData.description
        };

        let endpoint;
        let method;

        if (this.isEditMode && this.image.imageId) {
          // Get the actual database ID from the image object
          const actualImageId = this.image.imageId;
          console.log('Updating existing image with ID:', actualImageId);
          console.log('Original image object:', this.image);

          // Use PATCH to update the existing image
          endpoint = `${URL_DOMAIN}/branding/images/${actualImageId}`;
          method = 'PATCH';

          console.log('Using PATCH to update existing image');
        } else {
          // Create a new image if not in edit mode or if it's a draft image
          console.log('Creating new image');
          endpoint = `${URL_DOMAIN}/branding/images/metadata`;
          method = 'POST';
        }

        // Make API call to update/create metadata
        const response = await fetch(endpoint, {
          method: method,
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updateData)
        });

        if (response.ok) {
          let resultId = null;

          // For PATCH requests (204 No Content), there's no response body to parse
          if (method === 'PATCH') {
            console.log('Image updated successfully (PATCH)');
            // For PATCH, we're updating an existing image, so use the existing ID
            resultId = this.image.imageId;
          } else {
            // For POST requests, we get a JSON response with the new image ID
            try {
              const contentType = response.headers.get('content-type');
              if (contentType && contentType.includes('application/json')) {
                const savedData = await response.json();
                console.log('Save response:', savedData);
                resultId = savedData.id || null;
                console.log('New image created with ID:', resultId);
              } else {
                console.log('Response is not JSON, using existing ID');
                resultId = this.image.imageId || null;
              }
            } catch (parseError) {
              console.warn('Error parsing JSON response:', parseError);
              // If we can't parse the response, use the existing ID
              resultId = this.image.imageId || null;
            }
          }

          this.$emit('status-update', 'success', 'Image saved successfully');
          this.$emit('save', {
            ...this.imageData,
            id: resultId,
            imageId: resultId // Make sure we pass the imageId too
          });
          this.closeModal();
        } else {
          const errorText = await response.text();
          console.error('Failed to save image:', errorText);
          this.$emit('status-update', 'error', `Failed to save image: ${errorText}`);
        }
      } catch (error) {
        console.error('Error saving image:', error);
        this.$emit('status-update', 'error', `Failed to save image: ${error.message}`);
      }
    },
    async generateDescription() {
      if (this.isGenerating) return;

      if (!this.imageData.url) {
        this.validationError = 'No image URL available for description generation';
        return;
      }

      if (!this.isValidUrl(this.imageData.url)) {
        this.validationError = 'The image URL is invalid';
        return;
      }

      this.isGenerating = true;
      this.$emit('status-update', 'loading', 'Generating AI description...');

      try {
        const response = await fetch(`${URL_DOMAIN}/branding/images/generate-description`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            imageUrl: this.imageData.url,
            imageType: this.imageData.imageType
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to generate description: ${response.status}`);
        }

        const data = await response.json();
        this.imageData.description = data.description;
        this.$emit('status-update', 'success', 'AI description generated successfully');
      } catch (error) {
        console.error('Error generating description:', error);
        this.$emit('status-update', 'error', 'Failed to generate image description: ' + error.message);
      } finally {
        this.isGenerating = false;
      }
    }
  }
};
</script>

<style scoped>
/* Modal animations */
.animate-modal-appear {
  animation: modal-appear 0.3s ease-out forwards;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
