<template>
	<div class="flex h-screen overflow-hidden bg-gray-50">
		<!-- Content area -->
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<ToastStatus :status="status" :text="statusText" @clear-status="clearStatus()" />
			<main>
				<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<!-- Page header -->
					<div class="mb-8 flex">
						<h1 class="text-3xl md:text-4xl text-gray-900 font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent">Currency Settings</h1>
					</div>

					<!-- Content -->
					<div class="bg-white shadow-xl rounded-2xl mb-8 border border-gray-100">
						<div class="flex flex-col md:flex-row md:-mr-px">
							<SettingsSidebar />

							<div class="grow">
								<!-- Panel body -->
								<div class="p-8 space-y-8">
									<section>
										<div class="mb-6">
											<label class="block text-lg font-semibold text-gray-900 mb-2">Primary Organization Currency</label>
											<div class="mt-2">
												<lv-dropdown v-model="primaryCurrency" :options="availableCurrencies" optionLabel="name" placeholder="Select a Currency" class="w-full" />
											</div>
										</div>
									</section>
									
									<section>
										<div class="bg-blue-50 border border-blue-200 rounded-2xl p-6">
											<div class="flex items-start mb-4">
												<svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
													<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
												</svg>
												<div>
													<h3 class="text-blue-900 text-base font-semibold mb-2">What happens when I change the currency here?</h3>
													<ul class="text-blue-800 text-sm space-y-2 list-disc list-inside">
														<li>Your Shopify Store Currency needs to match this currency setting for discount codes to work properly</li>
														<li>Customers will see currency values and symbols <strong>in their own currency</strong>, values will be converted in realtime using the current exchange rate in force in Markets settings</li>
														<li><strong>NOTE:</strong> if you have a currency selected here that is different from your store, coupons will be created in the currency of your Shopify store.</li>
													</ul>
												</div>
											</div>
										</div>
									</section>

									<section>
										<div class="mb-6">
											<label class="block text-lg font-semibold text-gray-900 mb-2">Primary Localization Language</label>
											<div class="mt-2">
												<lv-dropdown v-model="primaryLanguage" :options="availableLanguages" optionLabel="name" placeholder="Select a Language" class="w-full" />
											</div>
										</div>
									</section>

									<section>
										<div class="bg-blue-50 border border-blue-200 rounded-2xl p-6">
											<div class="flex items-start mb-4">
												<svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
													<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
												</svg>
												<div>
													<h3 class="text-blue-900 text-base font-semibold mb-2">What happens when I change the language here?</h3>
													<ul class="text-blue-800 text-sm space-y-2 list-disc list-inside">
														<li>Language Raleon uses like (Make 5 purchases, Join, Sign In, etc) will be translated for you for your shoppers.</li>
														<li><strong>NOTE:</strong> in the future we do plan to launch per field overrides, let us know if this is something you'd like to use.</li>
													</ul>
												</div>
											</div>
										</div>
									</section>
								</div>
								<footer class="border-t border-gray-100 bg-gray-50/50 rounded-b-2xl">
									<div class="flex flex-col px-8 py-6">
										<div class="flex justify-end gap-4">
											<button class="px-6 py-3 border border-gray-200 text-gray-600 font-medium rounded-xl hover:bg-gray-50 hover:border-gray-300 transition-all duration-200">
												Cancel
											</button>
											<button 
												@click="updateCurrencyChanges()"
												class="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200"
											>
												Save Changes
											</button>
										</div>
									</div>
								</footer>
							</div>
						</div>
					</div>

				</div>
			</main>
		</div>
	</div>
</template>

<script>
import { ref } from 'vue'
import Header from '../../client-old/partials/Header.vue'
import SettingsSidebar from '../../client-old/partials/settings/SettingsSidebar.vue'
import ToastStatus from '../../client-old/pages/component/ToastStatus.vue'
import LvDropdown from 'lightvue/dropdown';
import * as Utils from '../../client-old/utils/Utils';
import { getCurrentOrg } from '../services/organization';
import * as CurrencyUtils from '../services/currency.js';


const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'Account',
	components: {
		Header,
		SettingsSidebar,
		ToastStatus,
		LvDropdown
	},
	setup() {

	},
	async mounted() {
		const response = await fetch(`${URL_DOMAIN}/currency/supported-currencies`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		let jsonresponse = await response.json();
		this.availableCurrencies = jsonresponse;

		const response2 = await fetch(`${URL_DOMAIN}/organization/primary-currency`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		let jsonresponse2 = await response2.json();
		this.primaryCurrency = jsonresponse2;

		const organization = await getCurrentOrg();
		if(organization)
		{
			this.currentOrg = organization;
			console.log('Selected Org', this.currentOrg)
			this.primaryLanguage = this.availableLanguages.find(lang => lang.code === this.currentOrg.language);
		}


	},
	computed: {

	},
	data() {
		return {
			status: '',
			statusText: '',
			availableCurrencies: [],
			primaryCurrency: {
				name: '',
				conversionToUSD: 0},
			availableLanguages: [
				{ code: 'en', name: 'English (English)' },
				{ code: 'fr', name: 'Français (French)' },
				{ code: 'es', name: 'Español (Spanish)' },
				{ code: 'de', name: 'Deutsch (German)' },
				{ code: 'it', name: 'Italiano (Italian)' },
				{ code: 'hi', name: 'हिन्दी (Hindi)' },
				{ code: 'zh', name: '中文 (Chinese)' },
				{ code: 'ka', name: 'ქართული (Georgian)' },
				{ code: 'he', name: 'עברית (Hebrew)' }
			],
			primaryLanguage: { code: 'en', name: 'English' },
			currentOrg: {}
		}
	},
	methods: {
		clearStatus() {
			this.status = '';
		},
		logout() {
			localStorage.removeItem('token');
		},
		async updateCurrencyChanges() {
			console.log('Current Org', this.currentOrg)
			const response = await fetch(`${URL_DOMAIN}/organization/primary-currency`, {
				method: 'POST',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(this.primaryCurrency)
			});

			let jsonresponse = await response.json();
			if (jsonresponse.error) {
				this.status = 'error';
				this.statusText = 'Error updating currency.';
				return;
			}
			else {
				this.status = 'success';
				this.statusText = 'Currency updated successfully.';
			}

			CurrencyUtils.clearPrimaryCurrencyCache();

			if(this.currentOrg.language === this.primaryLanguage.code)
				return;

			const response2 = await fetch(`${URL_DOMAIN}/organizations/${this.currentOrg.id}`, {
				method: 'PATCH',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ language: this.primaryLanguage.code })
			});

			let jsonresponse2 = await response2.json();
			if (jsonresponse2.error) {
				this.status = 'error';
				this.statusText = 'Error updating primary language.';
				return;
			}
			else {
				this.status = 'success';
				this.statusText = 'primary language updated successfully.';
			}
		}
	}
}
</script>
<style scoped>
.toggle-checkbox {
	opacity: 0;
	width: 0;
	height: 0;
}

.toggle-label {
	display: block;
	overflow: hidden;
	cursor: pointer;
	height: 24px;
	/* Height of the toggle */
	padding: 0;
	line-height: 24px;
	background: #bbb;
	/* Background of the toggle */
	border-radius: 24px;
	transition: background-color 0.2s;
}

.toggle-label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 2px;
	width: 20px;
	/* Width of the toggle handle */
	height: 20px;
	/* Height of the toggle handle */
	border-radius: 20px;
	background: #fff;
	/* Background of the toggle handle */
	transition: 0.2s;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox:checked+.toggle-label:after {
	transform: translateX(18px);
}

.toggle-checkbox:checked {
	right: 0;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox {
	right: 4px;
}

.toggle-label {
	transition: background-color 0.2s;
}
</style>
