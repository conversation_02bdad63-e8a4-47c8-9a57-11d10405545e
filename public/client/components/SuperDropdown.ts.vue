<template>
	<div ref="dropdown">
		<div class="px-3 py-2 bg-opacity-50 rounded-full justify-end items-center gap-1 inline-flex"
			@click="disabled ? undefined : isDropdownOpen = !isDropdownOpen"
			:class="{invisible: isDropdownOpen, 'cursor-not-allowed': disabled, 'cursor-pointer': !disabled, 'text-grey': disabled}, outline ? 'border border-black' : 'border-0 bg-ralbackground-light-tertiary hover:bg-ralbackground-light-tertiary hover:bg-opacity-90 transition-all duration-300'">
			<div class="w-6 h-6 relative">
				<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M16 15.5002L12 19.5002L8 15.5002M8 9.50024L12 5.50024L16 9.50024"
						:stroke="disabled ? '#aaa 	' : '#414141'" stroke-width="2" stroke-linecap="round"
						stroke-linejoin="round" />
				</svg>
			</div>
			<div class="font-medium font-['Inter'] text-lg"
				:class="{'whitespace-normal': selectionWordWrap, 'whitespace-nowrap': !selectionWordWrap, 'text-ralbackground-dark-base': !disabled, 'text-neutral-400': disabled}, (textSizeClass || 'text-2xl')">
				{{ !isOptionSelected ? '(' : '' }}{{ selectedLabel }}{{ !isOptionSelected ? ')' : '' }}
			</div>
		</div>

		<transition name="fade" enter-active-class="transition ease-out duration-200"
			leave-active-class="transition ease-in duration-150" enter-class="opacity-0 scale-95"
			enter-to-class="opacity-100 scale-100" leave-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-95">
			<div class="w-96 bg-white absolute rounded-lg shadow border border-violet-200 mt-[-4rem] md:left-auto md:w-auto md:min-w-[30rem] left-0 w-[100%] z-10 overflow-auto"
				v-if="isDropdownOpen">
				<ul>
					<li
						:class="{'cursor-not-allowed text-gray-500 bg-gray-100': option.enabled === false, 'cursor-pointer hover:bg-indigo-100': option.enabled !== false}"
						class="pl-4 py-3 pr-7"
						v-for="option in options"
						@click="option.enabled !== false && selectOption(option)">
						<div v-if="optionSlotKey && option[optionSlotKey]">
							<slot :name="option[optionSlotKey]"></slot>
						</div>
						<div class="flex flex-col items-start justify-space-between">
							<div v-if="optionTopSlotKey && option[optionTopSlotKey]">
								<slot :name="option[optionTopSlotKey]"></slot>
							</div>
							<div class="flex items-center justify-space-between">
								<div v-if="optionLeftSlotKey && option[optionLeftSlotKey]">
									<slot :name="option[optionLeftSlotKey]"></slot>
								</div>
								<div class="flex flex-col items-start justify-space-between">
									<div class="text-lg font-bold">{{ getOptionLabel(option) }}</div>
									<div class="text-md">{{ getOptionSubtitle(option) }}</div>
								</div>
								<div v-if="optionRightSlotKey && option[optionRightSlotKey]">
									<slot :name="option[optionRightSlotKey]"></slot>
								</div>
							</div>
							<div v-if="optionBottomSlotKey && option[optionBottomSlotKey]">
								<slot :name="option[optionBottomSlotKey]"></slot>
							</div>
						</div>

					</li>
				</ul>
			</div>
		</transition>
	</div>
</template>

<script>

import * as Utils from '../../client-old/utils/Utils';


const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: [
		'disabled',
		'outline',
		'textSizeClass',
		'options',
		'optionValueKey',
		'optionLabelKey',
		'modelValue',
		'placeholder',
		'optionSubtitleKey',
		'unrestricted',
		'optionSlotKey',
		'optionLeftSlotKey',
		'optionBottomSlotKey',
		'optionTopSlotKey',
		'optionRightSlotKey',
		'selectionWordWrap'
	],
	emits: ['update:modelValue'],
	components: {
	},
	async mounted() {
		document.body.addEventListener('click', (clickEvent) => {
			if (!this.$refs.dropdown?.contains(clickEvent.target)) {
				this.isDropdownOpen = false;
			}
		})
	},
	data() {
		return {
			isDropdownOpen: false
		}
	},
	computed: {
		isOptionSelected() {
			return this.modelValue || this.modelValue === 0 || (this.options.includes(null) && this.modelValue === null) || (this.options.includes(undefined) && this.modelValue === undefined);
		},
		selectedOption() {
			if (!this.optionValueKey) {
				if (this.unrestricted || this.options.includes(this.modelValue)) {
					return this.modelValue;
				}

				return undefined;
			}

			return this.options.find(x => x[this.optionValueKey] == this.modelValue);
		},
		selectedLabel() {
			if (this.placeholder && !this.isOptionSelected) {
				return this.placeholder;
			}

			if (!this.optionLabelKey) {
				return this.selectedOption;
			}

			return this.selectedOption?.[this.optionLabelKey];
		}
	},
	methods: {
		selectOption(option) {
			console.log('selectOption', option)
			if (option.enabled !== false) {
				this.$emit('update:modelValue', this.getOptionValue(option));
				this.isDropdownOpen = false;
			}
		},
		getOptionLabel(option) {
			if (!this.optionLabelKey) {
				return option;
			}

			return option[this.optionLabelKey];
		},
		getOptionSubtitle(option) {
			if (!this.optionSubtitleKey) {
				return;
			}

			return option[this.optionSubtitleKey];
		},
		getOptionValue(option) {
			if (!this.optionValueKey) {
				return option;
			}

			return option[this.optionValueKey];
		}
	}
}
</script>
<style scoped>
</style>

