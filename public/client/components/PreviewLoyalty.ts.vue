<template>
	<div v-if="!checkingSnippet && !isDataLoading && !isCheckout">
		<!-- Mobile View: Show only "Preview" -->
		<a class="inline-flex text-ralprimary-main text-sm font-semibold gap-1 cursor-pointer whitespace-nowrap w-auto hover:underline sm:hidden"
			@click="handleClick">
			{{ snippetEnabled ? 'Preview' : 'Install Raleon' }}
			<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M3.75 5.25C3.55109 5.25 3.36032 5.32902 3.21967 5.46967C3.07902 5.61032 3 5.80109 3 6V14.25C3 14.4489 3.07902 14.6397 3.21967 14.7803C3.36032 14.921 3.55109 15 3.75 15H12C12.1989 15 12.3897 14.921 12.5303 14.7803C12.671 14.6397 12.75 14.4489 12.75 14.25V9.75C12.75 9.33579 13.0858 9 13.5 9C13.9142 9 14.25 9.33579 14.25 9.75V14.25C14.25 14.8467 14.0129 15.419 13.591 15.841C13.169 16.2629 12.5967 16.5 12 16.5H3.75C3.15326 16.5 2.58097 16.2629 2.15901 15.841C1.73705 15.419 1.5 14.8467 1.5 14.25V6C1.5 5.40326 1.73705 4.83097 2.15901 4.40901C2.58097 3.98705 3.15326 3.75 3.75 3.75H8.25C8.66421 3.75 9 4.08579 9 4.5C9 4.91421 8.66421 5.25 8.25 5.25H3.75Z"
					fill="#202020" fill-opacity="0.8" />
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M10.5 2.25C10.5 1.83579 10.8358 1.5 11.25 1.5H15.75C16.1642 1.5 16.5 1.83579 16.5 2.25V6.75C16.5 7.16421 16.1642 7.5 15.75 7.5C15.3358 7.5 15 7.16421 15 6.75V3H11.25C10.8358 3 10.5 2.66421 10.5 2.25Z"
					fill="#202020" fill-opacity="0.8" />
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M16.2803 1.71967C16.5732 2.01256 16.5732 2.48744 16.2803 2.78033L8.03033 11.0303C7.73744 11.3232 7.26256 11.3232 6.96967 11.0303C6.67678 10.7374 6.67678 10.2626 6.96967 9.96967L15.2197 1.71967C15.5126 1.42678 15.9874 1.42678 16.2803 1.71967Z"
					fill="#202020" fill-opacity="0.8" />
			</svg>
		</a>

		<!-- Desktop and Tablet View: Show "Preview in Your Store" -->
		<a class="hidden sm:inline-flex text-ralprimary-main text-sm font-semibold gap-1 cursor-pointer whitespace-nowrap w-auto hover:underline"
			@click="handleClick" v-if="previewStyle == null || previewStyle != 'button'">
			{{ snippetEnabled ? 'Preview in Your Store' : 'Enable To Preview in Your Store' }}
			<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M3.75 5.25C3.55109 5.25 3.36032 5.32902 3.21967 5.46967C3.07902 5.61032 3 5.80109 3 6V14.25C3 14.4489 3.07902 14.6397 3.21967 14.7803C3.36032 14.921 3.55109 15 3.75 15H12C12.1989 15 12.3897 14.921 12.5303 14.7803C12.671 14.6397 12.75 14.4489 12.75 14.25V9.75C12.75 9.33579 13.0858 9 13.5 9C13.9142 9 14.25 9.33579 14.25 9.75V14.25C14.25 14.8467 14.0129 15.419 13.591 15.841C13.169 16.2629 12.5967 16.5 12 16.5H3.75C3.15326 16.5 2.58097 16.2629 2.15901 15.841C1.73705 15.419 1.5 14.8467 1.5 14.25V6C1.5 5.40326 1.73705 4.83097 2.15901 4.40901C2.58097 3.98705 3.15326 3.75 3.75 3.75H8.25C8.66421 3.75 9 4.08579 9 4.5C9 4.91421 8.66421 5.25 8.25 5.25H3.75Z"
					fill="#202020" fill-opacity="0.8" />
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M10.5 2.25C10.5 1.83579 10.8358 1.5 11.25 1.5H15.75C16.1642 1.5 16.5 1.83579 16.5 2.25V6.75C16.5 7.16421 16.1642 7.5 15.75 7.5C15.3358 7.5 15 7.16421 15 6.75V3H11.25C10.8358 3 10.5 2.66421 10.5 2.25Z"
					fill="#202020" fill-opacity="0.8" />
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M16.2803 1.71967C16.5732 2.01256 16.5732 2.48744 16.2803 2.78033L8.03033 11.0303C7.73744 11.3232 7.26256 11.3232 6.96967 11.0303C6.67678 10.7374 6.67678 10.2626 6.96967 9.96967L15.2197 1.71967C15.5126 1.42678 15.9874 1.42678 16.2803 1.71967Z"
					fill="#202020" fill-opacity="0.8" />
			</svg>
		</a>
		<div v-if="previewStyle == 'button' && snippetEnabled">
			<LightSecondaryButton cta="Preview in Store" @click="openNewWindow" />
		</div>
		<div v-else-if="previewStyle == 'button' && !snippetEnabled">
			<LightSecondaryButton cta="Enable To Preview" @click="openNewWindow" />
		</div>
	</div>
	<div v-if="isCheckout">
		<!-- Mobile View: Show only "Preview" -->
		<a class="inline-flex text-ralprimary-main text-sm font-semibold gap-1 cursor-pointer whitespace-nowrap w-auto hover:underline sm:hidden"
			@click="handleClick">
			Preview
			<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M3.75 5.25C3.55109 5.25 3.36032 5.32902 3.21967 5.46967C3.07902 5.61032 3 5.80109 3 6V14.25C3 14.4489 3.07902 14.6397 3.21967 14.7803C3.36032 14.921 3.55109 15 3.75 15H12C12.1989 15 12.3897 14.921 12.5303 14.7803C12.671 14.6397 12.75 14.4489 12.75 14.25V9.75C12.75 9.33579 13.0858 9 13.5 9C13.9142 9 14.25 9.33579 14.25 9.75V14.25C14.25 14.8467 14.0129 15.419 13.591 15.841C13.169 16.2629 12.5967 16.5 12 16.5H3.75C3.15326 16.5 2.58097 16.2629 2.15901 15.841C1.73705 15.419 1.5 14.8467 1.5 14.25V6C1.5 5.40326 1.73705 4.83097 2.15901 4.40901C2.58097 3.98705 3.15326 3.75 3.75 3.75H8.25C8.66421 3.75 9 4.08579 9 4.5C9 4.91421 8.66421 5.25 8.25 5.25H3.75Z"
					fill="#202020" fill-opacity="0.8" />
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M10.5 2.25C10.5 1.83579 10.8358 1.5 11.25 1.5H15.75C16.1642 1.5 16.5 1.83579 16.5 2.25V6.75C16.5 7.16421 16.1642 7.5 15.75 7.5C15.3358 7.5 15 7.16421 15 6.75V3H11.25C10.8358 3 10.5 2.66421 10.5 2.25Z"
					fill="#202020" fill-opacity="0.8" />
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M16.2803 1.71967C16.5732 2.01256 16.5732 2.48744 16.2803 2.78033L8.03033 11.0303C7.73744 11.3232 7.26256 11.3232 6.96967 11.0303C6.67678 10.7374 6.67678 10.2626 6.96967 9.96967L15.2197 1.71967C15.5126 1.42678 15.9874 1.42678 16.2803 1.71967Z"
					fill="#202020" fill-opacity="0.8" />
			</svg>
		</a>

		<!-- Desktop and Tablet View: Show "Preview in Your Store" -->
		<a class="hidden sm:inline-flex text-ralprimary-main text-sm font-semibold gap-1 cursor-pointer whitespace-nowrap w-auto hover:underline"
			@click="handleClick" v-if="previewStyle == null || previewStyle != 'button'">
			Preview in Your Store
			<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M3.75 5.25C3.55109 5.25 3.36032 5.32902 3.21967 5.46967C3.07902 5.61032 3 5.80109 3 6V14.25C3 14.4489 3.07902 14.6397 3.21967 14.7803C3.36032 14.921 3.55109 15 3.75 15H12C12.1989 15 12.3897 14.921 12.5303 14.7803C12.671 14.6397 12.75 14.4489 12.75 14.25V9.75C12.75 9.33579 13.0858 9 13.5 9C13.9142 9 14.25 9.33579 14.25 9.75V14.25C14.25 14.8467 14.0129 15.419 13.591 15.841C13.169 16.2629 12.5967 16.5 12 16.5H3.75C3.15326 16.5 2.58097 16.2629 2.15901 15.841C1.73705 15.419 1.5 14.8467 1.5 14.25V6C1.5 5.40326 1.73705 4.83097 2.15901 4.40901C2.58097 3.98705 3.15326 3.75 3.75 3.75H8.25C8.66421 3.75 9 4.08579 9 4.5C9 4.91421 8.66421 5.25 8.25 5.25H3.75Z"
					fill="#202020" fill-opacity="0.8" />
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M10.5 2.25C10.5 1.83579 10.8358 1.5 11.25 1.5H15.75C16.1642 1.5 16.5 1.83579 16.5 2.25V6.75C16.5 7.16421 16.1642 7.5 15.75 7.5C15.3358 7.5 15 7.16421 15 6.75V3H11.25C10.8358 3 10.5 2.66421 10.5 2.25Z"
					fill="#202020" fill-opacity="0.8" />
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M16.2803 1.71967C16.5732 2.01256 16.5732 2.48744 16.2803 2.78033L8.03033 11.0303C7.73744 11.3232 7.26256 11.3232 6.96967 11.0303C6.67678 10.7374 6.67678 10.2626 6.96967 9.96967L15.2197 1.71967C15.5126 1.42678 15.9874 1.42678 16.2803 1.71967Z"
					fill="#202020" fill-opacity="0.8" />
			</svg>
		</a>

		<div v-if="previewStyle == 'button' && snippetEnabled">
			<LightSecondaryButton cta="Preview in Store" @click="openNewWindow" />
		</div>
	</div>
</template>


<script>
import * as Utils from '../utils/utils';
import { customerIOTrackEvent } from '../services/customerio.js';
import LightSecondaryButton from './LightSecondaryButton.ts.vue';
const URL_DOMAIN = Utils.URL_DOMAIN;
export default {
	name: 'PreviewLoyaltyProgram',
	props: ['previewStyle', 'isCheckout'],
	components: {
		LightSecondaryButton
	},
	data() {
		return {
			showOverlay: false,
			snippetEnabled: true,
			checkingSnippet: true,
			tooltipMessage: '',
			isDataLoading: true,
		};
	},
	async mounted() {
		window.addEventListener('message', this.receiveMessage, false);
		let snippetEnabledLocalStorage = localStorage.getItem('snippetEnabled');
		if(snippetEnabledLocalStorage) {
			this.snippetEnabled = snippetEnabledLocalStorage === 'true';
			this.checkingSnippet = false;
		}
		try {
			const organization = await this.getCurrentOrg();
			await this.checkIfSnippetIsEnabled();
			if (organization) {
				this.external_domain = organization.externalDomain;
				this.orgId = organization.id;
			}
		} finally {
			this.isDataLoading = false;
		}
		document.addEventListener('visibilitychange', this.handleVisibilityChange);
	},
	beforeDestroy() {
		console.log('beforeDestroy')
		window.removeEventListener('message', this.receiveMessage, false);
		document.removeEventListener('visibilitychange', this.handleVisibilityChange);
	},
	methods: {
		handleClick() {
			if (!this.isDataLoading) {
				this.openNewWindow();
			} else {
				// Optionally handle the case when data is still loading, like showing a message
				console.log('Data is still loading, please wait...');
			}
		},
		handleVisibilityChange() {
            if (document.visibilityState === 'visible') {
                console.log('Tab is now active');
                // Call method to check if the snippet is enabled
                this.checkIfSnippetIsEnabled();
            }
        },
		async enableSnippet() {
			const snippetId = localStorage.getItem('shopify_snippet_id');
			window.open(`https://${this.external_domain}/admin/themes/current/editor?context=apps&activateAppId=${snippetId}/app-embed`, '_blank');
		},
		async openNewWindow() {
			if (this.isCheckout) {
				const subdomain = this.external_domain?.replace('.myshopify.com', '');
				window.open(`https://admin.shopify.com/store/${subdomain}/settings/checkout`, '_blank');
				return;
			}
			if(!this.snippetEnabled) {
				this.enableSnippet();
				return;
			}
			customerIOTrackEvent('Preview In Store');
			var features = "menubar=no,location=no,resizable=yes,scrollbars=yes,status=yes";
			if (this.childWindow && !this.childWindow.closed) {
				// Close the existing window
				console.log('closing existing window', this.childWindow)
				this.childWindow.close();
				this.childWindow = null;
			}
			this.childWindow = window.open(`https://${this.external_domain}`, 'preview', features);
			this.childWindow.location.reload(true);
			console.log('this.childWindow', this.childWindow)
			await this.setOnboardState();
		},
		receiveMessage(event) {
			// Handle the message
			//console.log('Message received from child:', event.data);
			if (event.data?.action === 'should-preview-live-data-request') {
				console.log("CORRECT MESSAGE, sending response to child window", this.childWindow);
				this.childWindow.postMessage({
					action: 'preview-live-data'
				}, '*');
			}
		},
		async checkIfSnippetIsEnabled() {
			let response = {};
			let jsonresponse = {};
			try {
				const snippetId = localStorage.getItem('shopify_snippet_id');
				response = await fetch(`${Utils.URL_DOMAIN}/app-embed-status?appEmbedId=${snippetId}`, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json',
						'ngrok-skip-browser-warning': true
					}
				});
				jsonresponse = await response.json();
				console.log('checkIfSnippetIsEnabled', jsonresponse.active, jsonresponse)
				this.snippetEnabled = jsonresponse.active;
				console.log('snippetEnabled', this.snippetEnabled);
				if(this.snippetEnabled) {
					this.tooltipMessage = jsonresponse.message || 'Clicking this will show you show you the live preview of your loyalty program on your site.';
				} else {
					this.tooltipMessage = 'Please make sure you have installed the snippet on your site to preview the live program.';
				}
			} catch (err) {
				console.log("Error: " + JSON.stringify(err));
				this.snippetEnabled = true;
			}
			localStorage.setItem('snippetEnabled', this.snippetEnabled);
			this.checkingSnippet = false;
		},
		async getCurrentOrg() {
			let response = {};
			let jsonresponse = {};
			try {
				response = await fetch(`${Utils.URL_DOMAIN}/organization/current`, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json',
						'ngrok-skip-browser-warning': true
					}
				});
				jsonresponse = await response.json();
			} catch (err) {
				console.log("Error: " + JSON.stringify(err));
			}
			return jsonresponse;
		},
		async setOnboardState() {
			let response = {};
			let jsonresponse = {};
			let type = 'Preview Program';
			try {
				response = await fetch(`${Utils.URL_DOMAIN}/onboarding-tasks/${type}/states`, {
					method: 'PATCH',
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json',
						'ngrok-skip-browser-warning': true
					},
					body: JSON.stringify({
						state: 'Verified'
					})
				});
				jsonresponse = await response.json();
				console.log('setOnboardState', jsonresponse);
			} catch (err) {
				console.log("Error: " + JSON.stringify(err));
			}
			return jsonresponse;
		}
	}
}
</script>
<style scoped>
</style>
