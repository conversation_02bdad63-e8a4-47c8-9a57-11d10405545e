<template>
	<div class="outer-container" v-if="progress !== null && (!progress || progress < 100 || !klaviyoConnected || !shopifyConnected || !knowledgeBaseComplete || (klaviyoConnected && klaviyoSyncProgress < 100))">
		<div class="process-container" :class="{ 'collapsed': isCollapsed }" @click="isCollapsed ? expand() : null">
			<div v-if="!isCollapsed" class="close-button" @click.stop="collapse">×</div>

			<!-- Collapsed View -->
			<div v-if="isCollapsed" class="collapsed-summary">
				<div class="collapsed-content">
					<div class="compact-info">
						<h3>Step {{ currentStep }}/{{ totalSteps }}</h3>
						<p class="status" v-if="currentStep === 1">{{ progress }}% Complete</p>
						<p class="status" v-else-if="currentStep === 2">Connect to Shopify</p>
						<p class="status" v-else-if="currentStep === 3">Connect to Klaviyo</p>
						<p class="status" v-else-if="currentStep === 4">Build Knowledge base</p>
						<p class="status" v-else-if="currentStep === 5">Sync Klaviyo Segments</p>
					</div>
				</div>
			</div>

			<!-- Expanded View -->
			<template v-else>
				<!-- Process Customer Data -->
				<div class="process-card">
					<div class="icon-container">
						<svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" fill="none"
							stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
							<polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
						</svg>
					</div>
					<h3>1. Process Customer Data</h3>
					<p class="status">{{ progress }}% Complete</p>
					<p v-if="status" class="status-message">{{ status }}</p>
				</div>

				<!-- Connect Shopify -->
				<div class="process-card">
					<div class="icon-container">
						<svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" fill="none"
							stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
							<rect x="2" y="4" width="20" height="16" rx="2"></rect>
							<path d="M22 7l-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
						</svg>
					</div>
					<h3>2. Connect Shopify</h3>
					<p class="status" v-if="shopifyConnected">Connected</p>
					<a v-else href="/ai-segments/integrations" class="connect-link">
						Connect
						<svg class="arrow-icon" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M2.5 9.5L9.5 2.5M9.5 2.5H4M9.5 2.5V8" stroke="currentColor" stroke-width="1.5"
								stroke-linecap="round" stroke-linejoin="round" />
						</svg>
					</a>
				</div>

				<!-- Connect Klaviyo -->
				<div class="process-card">
					<div class="icon-container">
						<svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" fill="none"
							stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
							<rect x="2" y="4" width="20" height="16" rx="2"></rect>
							<path d="M22 7l-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
						</svg>
					</div>
					<h3>3. Connect Klaviyo</h3>
					<p class="status" v-if="klaviyoConnected">Connected</p>
					<a v-else href="/ai-segments/integrations" class="connect-link">
						Connect
						<svg class="arrow-icon" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M2.5 9.5L9.5 2.5M9.5 2.5H4M9.5 2.5V8" stroke="currentColor" stroke-width="1.5"
								stroke-linecap="round" stroke-linejoin="round" />
						</svg>
					</a>
				</div>

				<!-- Build Knowledge Base -->
				<div class="process-card">
					<div class="icon-container">
						<svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" fill="none"
							stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
							<path
								d="M12 2a9 9 0 0 1 9 9c0 3.14-2.3 5.8-4 7h-2a3 3 0 0 0-3 3M12 2a9 9 0 0 0-9 9c0 3.14 2.3 5.8 4 7h2a3 3 0 0 1 3 3">
							</path>
						</svg>
					</div>
					<h3>4. Build Knowledge Base</h3>
					<p class="status" v-if="knowledgeBaseComplete">Complete</p>
					<a v-else href="/ai-segments/knowledge" class="connect-link">
						Configure
						<svg class="arrow-icon" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M2.5 9.5L9.5 2.5M9.5 2.5H4M9.5 2.5V8" stroke="currentColor" stroke-width="1.5"
								stroke-linecap="round" stroke-linejoin="round" />
						</svg>
					</a>
				</div>

				<!-- Klaviyo Segment Sync -->
				<div class="process-card" v-if="klaviyoConnected">
					<div class="icon-container">
						<svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" fill="none"
							stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
							<circle cx="12" cy="12" r="10"></circle>
							<polyline points="12 6 12 12 16 14"></polyline>
						</svg>
					</div>
					<h3>5. Sync Klaviyo Segments</h3>
					<p class="status" v-if="klaviyoSyncProgress === 100">Complete</p>
					<p class="status" v-else-if="klaviyoSyncProgress !== null">{{ klaviyoSyncProgress }}% Complete</p>
					<p class="status" v-else>Checking progress...</p>
				</div>
			</template>
		</div>
	</div>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';
import * as OrgServices from '../services/organization';
import * as OrganizationSettings from '../services/organization-settings.js';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'DataProgress',
	data() {
		return {
			progress: 0,
			status: '',
			pollingInterval: null,
			isCollapsed: true,
			klaviyoConnected: false,
			shopifyConnected: true,
			knowledgeBaseComplete: false,
			currentStep: 1,
			totalSteps: 5,
			klaviyoSyncProgress: null,
		};
	},
	async mounted() {
		await this.fetchProgress();
		// Start polling every 30 seconds
		this.pollingInterval = setInterval(async () => {
			await this.fetchProgress();
			await this.checkKnowledgeBaseCompletion();
			await this.fetchKlaviyoSyncProgress();
			this.updateCurrentStep();
		}, 30000);

		// Check initial states
		const connectionResponse = await this.checkKlaviyoConnection();
		this.klaviyoConnected = connectionResponse.connected;
		const shopifyConnectionResponse = await this.checkShopifyConnection();
		this.shopifyConnected = shopifyConnectionResponse.connected;
		await this.checkKnowledgeBaseCompletion();
		await this.fetchKlaviyoSyncProgress();
		this.updateCurrentStep();
	},
	beforeDestroy() {
		// Clean up polling interval
		if (this.pollingInterval) {
			clearInterval(this.pollingInterval);
		}
	},
	methods: {
		async fetchProgress() {
			try {
				const response = await fetch(`${URL_DOMAIN}/metric-progress`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					}
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const data = await response.json();
				this.progress = 100; //data.body.progress;
				this.status = data.body.status;

				if (this.progress === 100) {
					clearInterval(this.pollingInterval);
				}
			} catch (error) {
				console.error('Failed to fetch progress:', error);
				this.status = 'Error fetching progress';
			}
		},
		async checkKlaviyoConnection() {
			const response = await fetch(`${URL_DOMAIN}/integration/klaviyo/connected`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				}
			});
			if (!response.ok) {
				throw new Error('Failed to check Klaviyo connection');
			}
			return response.json();
		},
		async checkShopifyConnection() {
			const response = await fetch(`${URL_DOMAIN}/integration/shopify/connected`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				}
			});
			if (!response.ok) {
				throw new Error('Failed to check Shopify connection');
			}
			return response.json();
		},
		async checkKnowledgeBaseCompletion() {
			try {
				// Fetch individual organization settings
				const customerProblems = await OrganizationSettings.getOrganizationSetting('customerProblems');
				const influencerCollaboration = await OrganizationSettings.getOrganizationSetting('influencerCollaboration');
				const hasSubscriptions = await OrganizationSettings.getOrganizationSetting('hasSubscriptions');

				// Fetch organization data
				const currentOrg = await OrgServices.getCurrentOrg();

				// Check if all required fields are present and non-empty
				const settingsComplete = [
					customerProblems,
					influencerCollaboration,
					hasSubscriptions
				].every(value => value && value.trim() !== '');

				const orgComplete = [
					currentOrg.description,
					currentOrg.sampleLanguage
				].every(value => value && value.trim() !== '');

				this.knowledgeBaseComplete = settingsComplete && orgComplete;

				// If everything is complete, collapse the component
				if (this.progress === 100 && this.klaviyoConnected && this.knowledgeBaseComplete && this.klaviyoSyncProgress === 100) {
					this.isCollapsed = true;
				}
			} catch (error) {
				console.error('Failed to check knowledge base completion:', error);
			}
		},
		async fetchKlaviyoSyncProgress() {
			if (!this.klaviyoConnected) return;

			try {
				const response = await fetch(`${URL_DOMAIN}/organization-segment/klaviyo-sync-progress`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					}
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const data = await response.json();
				this.klaviyoSyncProgress = data.progress;

				if (this.klaviyoSyncProgress === 100) {
					clearInterval(this.pollingInterval);
				}
			} catch (error) {
				console.error('Failed to fetch Klaviyo sync progress:', error);
			}
		},
		updateCurrentStep() {
			if (this.progress < 100) {
				this.currentStep = 1;
			} else if (!this.shopifyConnected) {
				this.currentStep = 2;
			} else if (!this.klaviyoConnected) {
				this.currentStep = 3;
			} else if (!this.knowledgeBaseComplete) {
				this.currentStep = 4;
			} else if (this.klaviyoSyncProgress !== null && this.klaviyoSyncProgress < 100) {
				this.currentStep = 5;
			}
		},
		expand() {
			this.isCollapsed = false;
		},
		collapse() {
			this.isCollapsed = true;
		},
	},
};
</script>

<style scoped>
.outer-container {
	padding: 0rem;
}

.process-container {
	display: flex;
	justify-content: space-around;
	padding: 20px;
	max-width: 48rem;
	gap: 2rem;
	background-color: white;
	border: 1px solid #e5e7eb;
	border-radius: 0.5rem;
	position: relative;
	transition: all 0.3s ease;
}

.process-container.collapsed {
	max-width: 24rem;
	cursor: pointer;
	padding: 8px 16px;
}

.process-container.collapsed:hover {
	background-color: #f9fafb;
}

.collapsed-summary {
	display: flex;
	align-items: center;
	width: 100%;
}

.collapsed-content {
	flex: 1;
	text-align: center;
}

.compact-info {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 1rem;
}

.compact-info h3 {
	margin: 0;
	font-size: 0.875rem;
}

.compact-info .status {
	margin: 0;
}

.close-button {
	position: absolute;
	top: 10px;
	right: 10px;
	width: 24px;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 20px;
	color: #6b7280;
	border-radius: 50%;
}

.close-button:hover {
	background-color: #f3f4f6;
}

.process-card {
	flex: 1;
	text-align: center;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.icon-container {
	width: 3rem;
	height: 3rem;
	background-color: rgb(243 232 255);
	border-radius: 0.5rem;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 0.75rem;
}

.icon {
	width: 1.5rem;
	height: 1.5rem;
	color: rgb(147 51 234);
}

.process-card h3 {
	font-size: 0.875rem;
	font-weight: 500;
	color: rgb(17 24 39);
	margin-bottom: 0.25rem;
}

.status {
	font-size: 0.75rem;
	color: rgb(217 119 6);
}

.status-message {
	font-size: 0.75rem;
	color: rgb(107 114 128);
	margin-top: 0.25rem;
}

.connect-link {
	font-size: 0.75rem;
	color: rgb(147 51 234);
	text-decoration: none;
	display: flex;
	align-items: center;
	gap: 0.25rem;
}

.arrow-icon {
	width: 0.75rem;
	height: 0.75rem;
}
</style>
