<template>
  <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
    <!-- Calendar navigation header -->
    <div class="bg-white px-4 py-3 flex items-center justify-between border-b border-gray-200">
      <div class="flex items-center">
        <h2 class="text-lg font-semibold text-gray-900">{{ calendarTitle }}</h2>
      </div>
      <div class="flex items-center space-x-2">
        <button
          @click="previousMonth"
          class="p-2 rounded-full hover:bg-gray-100 text-gray-600 focus:outline-none"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </button>
        <button
          @click="$emit('go-to-current')"
          class="px-3 py-1 text-sm font-medium rounded bg-purple-100 text-purple-700 hover:bg-purple-200 focus:outline-none"
        >
          Today
        </button>
        <button
          @click="nextMonth"
          class="p-2 rounded-full hover:bg-gray-100 text-gray-600 focus:outline-none"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Calendar grid -->
    <div class="bg-white">
      <!-- Days of week header -->
      <div class="grid grid-cols-7 gap-px bg-gray-200 text-sm font-semibold text-center text-gray-700">
        <div v-for="day in daysOfWeek" :key="day" class="py-2 bg-white">
          {{ day }}
        </div>
      </div>

      <!-- Calendar days -->
      <div class="grid grid-cols-7 gap-px bg-gray-200">
        <div
          v-for="day in calendarDays"
          :key="day.date"
          class="h-36 bg-white flex flex-col overflow-hidden"
          :class="{
            'bg-purple-50': day.isToday,
            'text-gray-400': !day.isCurrentMonth
          }"
        >
          <!-- Day number -->
          <div class="px-1 py-0.5 flex justify-between">
            <span
              class="text-sm font-medium h-5 w-5 flex items-center justify-center rounded-full"
              :class="{ 'bg-purple-600 text-white': day.isToday }"
            >
              {{ formatDayNumber(day.date) }}
            </span>
            <span v-if="day.totalEmails > 0" class="text-xs text-purple-600 font-medium">
              {{ day.totalEmails }}
            </span>
          </div>

          <!-- Emails for this day -->
          <div class="px-1 pb-1 space-y-0.5 flex-1 flex flex-col">
            <div
              v-for="(email, idx) in day.emails.slice(0, 3)"
              :key="idx"
              @click="$emit('navigate', email.id)"
              class="px-1.5 py-1 text-xs rounded bg-white border border-gray-200 hover:border-purple-300 hover:bg-purple-50 cursor-pointer"
            >
              <div class="flex flex-col">
                <div class="flex items-center gap-1.5">
                  <!-- Status color indicator dot -->
                  <div 
                    class="w-1.5 h-1.5 rounded-full flex-shrink-0" 
                    :style="{ backgroundColor: getStatusColor(email.status) }">
                  </div>
                  <div class="truncate font-medium leading-tight">{{ email.title }}</div>
                </div>
                <div class="flex justify-between items-center ml-3 mt-0.5">
                  <span class="text-[9px] text-gray-600 leading-none">
                    {{ getDisplayStatus(email.status) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Spacer to push the "more" indicator to the bottom when needed -->
            <div class="flex-grow" v-if="day.emails.length > 3"></div>

            <!-- "More" indicator if needed -->
            <div
              v-if="day.emails.length > 3"
              class="px-1.5 py-0.5 text-xs text-center text-purple-700 bg-purple-50 rounded hover:bg-purple-100 cursor-pointer"
              @click="$emit('show-day-detail', day)"
            >
              +{{ day.emails.length - 3 }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmailCalendarView',

  props: {
    currentMonth: {
      type: Number,
      required: true
    },
    currentYear: {
      type: Number,
      required: true
    },
    calendarDays: {
      type: Array,
      required: true
    },
    daysOfWeek: {
      type: Array,
      default: () => ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
    }
  },

  computed: {
    calendarTitle() {
      return new Date(this.currentYear, this.currentMonth).toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric'
      });
    }
  },

  methods: {
    previousMonth() {
      this.$emit('previous-month');
    },

    nextMonth() {
      this.$emit('next-month');
    },

    getStatusColor(status) {
      // Same color mapping as TopBar component
      const colors = {
        // Legacy status mappings to match TopBar color scheme
        'Ready': '#6366F1', // Indigo - maps to Campaign Ready
        'Not Started': '#6366F1', // Indigo - maps to Campaign Ready
        'In Progress': '#A78BFA', // Light purple - maps to In Copywriting
        'In Review': '#34D399', // Green - maps to In Review
        'Complete': '#059669', // Green - maps to Done/Complete
        'Archive': '#6B7280', // Slate - maps to Archive
        'Processing': '#9CA3AF', // Gray for processing
        // New status values (matching TopBar statusOptions)
        'Campaign Ready': '#6366F1', // Indigo
        'Ready for Copywriting': '#8B5CF6', // Purple
        'In Copywriting': '#A78BFA', // Light purple
        'Ready for Design': '#EC4899', // Pink
        'In Design': '#F472B6', // Light pink
        'Quality Check': '#F59E0B', // Amber
        'Ready for Review': '#10B981', // Emerald
        'Approved': '#3B82F6' // Blue
      };
      return colors[status] || '#9CA3AF'; // Default gray if not found
    },


    getDisplayStatus(status) {
      const statusMap = {
        'Ready': 'Not Started',
        'In Progress': 'In Progress',
        'In Review': 'In Review',
        'Not Started': 'Not Started',
        'Complete': 'Complete',
        'Processing': 'Processing'
      };
      return statusMap[status] || status;
    },

    formatDayNumber(date) {
      // Create a date object from the UTC string
      const formattedDate = new Date(date);

      // Use the UTC date methods to get the correct day number
      return formattedDate.getUTCDate();
    }
  },

  emits: ['previous-month', 'next-month', 'go-to-current', 'navigate', 'show-day-detail']
}
</script>
