<template>
  <!-- Modal backdrop -->
  <transition
    enter-active-class="transition ease-out duration-200"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition ease-out duration-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div v-if="modalOpen" class="fixed inset-0 bg-slate-900 bg-opacity-30 z-50 transition-opacity" aria-hidden="true"></div>
  </transition>
  <!-- Modal dialog -->
  <transition
    enter-active-class="transition ease-in-out duration-200"
    enter-from-class="opacity-0 translate-y-4"
    enter-to-class="opacity-100 translate-y-0"
    leave-active-class="transition ease-in-out duration-200"
    leave-from-class="opacity-100 translate-y-0"
    leave-to-class="opacity-0 translate-y-4"
  >
    <div v-if="modalOpen" :id="id" class="fixed inset-0 z-50 overflow-hidden flex items-center my-4 justify-center transform px-4 sm:px-6" role="dialog" aria-modal="true">
      <div ref="modalContent"
	  :class="{'bg-white rounded-lg shadow-lg overflow-auto max-w-2xl w-full max-h-full' : colorSet == 'light', 'bg-ralsecondary-start rounded-2xl border border-ralprimary-light' : colorSet == 'dark'}">
        <slot />
      </div>
    </div>
  </transition>

  <!-- Example -->
  <!--

			<ModalBlank id="info-modal" :modalOpen="testModalOpen" @close-modal="testModalOpen = false">
                      <div class="p-5 flex space-x-4">
                        <div class="w-10 h-10 rounded-full flex items-center justify-center shrink-0 bg-indigo-100">
                          <svg class="w-4 h-4 shrink-0 fill-current text-indigo-500" viewBox="0 0 16 16">
                            <path d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm1 12H7V7h2v5zM8 6c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1z" />
                          </svg>
                        </div>
                        <div>
                          <div class="mb-2">
                            <div class="text-lg font-semibold text-ralblack-primary">Create new Event?</div>
                          </div>
                          <div class="text-sm mb-10">
                            <div class="space-y-2">
                              <p class="text-ralblack-secondary">Semper eget duis at tellus at urna condimentum mattis pellentesque lacus suspendisse faucibus interdum.</p>
                            </div>
                          </div>
                          <div class="flex flex-wrap justify-end space-x-2">
                            <CancelButton @click="testModalOpen = false" cta="Cancel"></CancelButton>
                            <button class="btn-sm bg-ralerror-dark hover:bg-opacity-75 text-white rounded-full transitiona-all duration-300">Yes, Create it</button>
                          </div>
                        </div>
                      </div>
            </ModalBlank>
  -->
</template>

<script>
import { onMounted, onUnmounted, ref } from 'vue'

export default {
  name: 'ModalEmpty',
  props: ['id', 'modalOpen', 'color'],
  emits: ['close-modal'],
  data() {
	return {
		colorSet: "light",
	}
  },
  setup(props, { emit }) {

    const modalContent = ref(null)

    const clickHandler = ({ target }) => {
      if (!props.modalOpen) {
        emit('close-modal')
      }
    }

    // close if the esc key is pressed
    const keyHandler = ({ keyCode }) => {
      if (!props.modalOpen || keyCode !== 27) return
      emit('close-modal')
    }

    onMounted(() => {
      document.addEventListener('click', clickHandler)
      document.addEventListener('keydown', keyHandler)
    })

    onUnmounted(() => {
      document.removeEventListener('click', clickHandler)
      document.removeEventListener('keydown', keyHandler)
    })

    return {
      modalContent,
    }
  },
  mounted() {
	if(this.color == null || this.color == 'light')
		this.colorSet = 'light';
	  else
		this.colorSet = 'dark';
  }
}
</script>
