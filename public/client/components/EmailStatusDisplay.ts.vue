<template>
  <div
    class="flex flex-col items-start p-3 rounded-lg border border-gray-300 bg-gray-50 text-gray-700 max-w-xs"
  >
    <div v-if="segment.type === 'email_status' && segment.isGenerating" class="flex items-center space-x-2">
      <!-- Basic spinner example -->
      <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span>Generating Email...</span>
    </div>
    <!-- Removed v-else-if and v-else blocks as this component only shows the generating state -->
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

// Define local interface for the expected structure of the segment prop
// This avoids needing to import the full ChatMessageSegment if it's complex
// or defined far away, keeping the component self-contained for now.
interface ChatMessageSegmentForEmailStatus {
  type: 'email_status';
  isGenerating: boolean;
  // Include other fields from the actual ChatMessageSegment if they
  // become necessary for this component's logic or template.
}

export default defineComponent({
  name: 'EmailStatusDisplay',
  props: {
    segment: {
      type: Object as PropType<ChatMessageSegmentForEmailStatus>,
      required: true,
    },
  },
  setup() {
    // No specific setup logic needed for this basic display component yet
    return {};
  },
});
</script>

<style scoped>
/* Add any component-specific styles here if needed, */
/* although Tailwind should cover most cases. */
</style>
