<template>
	<div class="mt-6 relative">
		<div className="max-w-7xl mx-auto">
			<div className="flex gap-6 min-h-[60vh]">
				<div className="w-1/2 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
					<h3 className="text-lg font-medium text-gray-900 mb-4">Available Signals</h3>

					<div className="space-y-4">
						<div className="flex items-center justify-between">
							<div class="flex gap-1 rounded-lg backdrop-blur-sm">
								<div
									@click="activeFilterTab = 'all'"
									:class="{
										'bg-gray-100 text-gray-900': activeFilterTab === 'all',
										'text-gray-600 hover:bg-gray-100': activeFilterTab !== 'all'
									}"
									class="px-4 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 cursor-pointer"
								>
									All
								</div>
								<div
									@click="activeFilterTab = 'raleon'"
									:class="{
										'bg-gray-100 text-gray-900': activeFilterTab === 'raleon',
										'text-gray-600 hover:bg-gray-100': activeFilterTab !== 'raleon'
									}"
									class="px-4 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 cursor-pointer"
								>
									Raleon AI
								</div>
								<div
									@click="activeFilterTab = 'model'"
									:class="{
										'bg-gray-100 text-gray-900': activeFilterTab === 'model',
										'text-gray-600 hover:bg-gray-100': activeFilterTab !== 'model'
									}"
									class="px-4 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 cursor-pointer"
								>
									Raleon Insights
								</div>
								<div
									@click="activeFilterTab = 'rule'"
									:class="{
										'bg-gray-100 text-gray-900': activeFilterTab === 'rule',
										'text-gray-600 hover:bg-gray-100': activeFilterTab !== 'rule'
									}"
									class="px-4 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 cursor-pointer"
								>
									Rules
								</div>
							</div>
						</div>
					</div>

					<div class="relative group mt-4">
						<div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none text-gray-400">
							<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
								<circle cx="11" cy="11" r="8"></circle>
								<line x1="21" y1="21" x2="16.65" y2="16.65"></line>
							</svg>
						</div>
						<input
							type="text"
							v-model="searchQuery"
							placeholder="Search signals..."
							class="w-full pl-12 pr-4 py-2 text-sm rounded-xl border-0 bg-white shadow-sm ring-1 ring-gray-200 transition-shadow duration-200 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 hover:ring-gray-300"
						/>
					</div>

					<div>
						<signal-section
							class="min-h-[300px]"
							title="All Signals"
							section="all"
							:signals="filteredSignals"
							:is-dragging-over="isDraggingOver"
							@drop="handleDrop"
							@dragenter="handleDragEnter"
							@dragleave="handleDragLeave"
							@signal-click="handleSignalClick"
						/>
					</div>
				</div>

				<div className="w-1/2 space-y-6">
					<div
						className="bg-white p-6 rounded-lg shadow-sm border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors">
						<h3 className="text-lg font-medium text-gray-900 mb-4">Include Signals</h3>
						<signal-section title="Include Signals" section="positive" :signals="signals?.positive || []"
							:is-dragging-over="isDraggingOver" @drop="handleDrop" @dragenter="handleDragEnter"
							@dragleave="handleDragLeave" @signal-click="handleSignalClick" class="" />
					</div>

					<div
						className="bg-white p-6 rounded-lg shadow-sm border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors">
						<h3 className="text-lg font-medium text-gray-900 mb-4">Exclude Signals</h3>
						<signal-section title="Exclude Signals" section="negative" :signals="signals?.negative || []"
							:is-dragging-over="isDraggingOver" @drop="handleDrop" @dragenter="handleDragEnter"
							@dragleave="handleDragLeave" @signal-click="handleSignalClick" />
					</div>
				</div>
			</div>

			<SignalEditor v-if="activeSignal" :signal="activeSignal" :position="editorPosition" @close="closeEditor"
				@update="handleSignalUpdate" />
		</div>
	</div>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';
import SignalSection from './SignalSection.ts.vue'
import SignalEditor from './SignalEditor.ts.vue'
const URL_DOMAIN = Utils.URL_DOMAIN;
export default {
	name: 'SignalManager',
	components: {
		SignalSection,
		SignalEditor
	},
	emits: ['update:audience-size', 'calculating'],
	data() {
		return {
			isCalculating: false,
			enabledIntegrations: [],
			signals: {
				all: [
					{ id: 1, label: "At risk to churn", type: "raleon", size: 14831, desc: "Customers most likely predicted to churn based on the Raleon Churn Score." },
					{ id: 2, label: "Replenishment desire", type: "model", size: 1849, desc: "Customers who are likely to rebuy a product they have before in the next ~30 days.", adjuster: "Replenishment Score", thresholdLabel: " / 100" },
					{ id: 3, label: "Subscription upsell", type: "model", size: 931 },
					{ id: 4, label: "Subscription downsell", type: "model", size: 823 },
					{ id: 5, label: "Likely for repeat purchase", type: "model", size: 2344 },
					{ id: 6, label: "Likely for cross-sell", type: "model", size: 843 },
					{ id: 7, label: "Email Engaged", type: "rule", size: 21932 },
					{ id: 8, label: "Losing engagement", type: "raleon", size: 843 },
					{ id: 9, label: "Single-category buyer", type: "rule", size: 843 },
					{ id: 10, label: "Multi-category buyer", type: "rule", size: 843 },
					{ id: 11, label: "Discounter", type: "raleon", size: 843 },
					{ id: 12, label: "Heavy discounter", type: "raleon", size: 843 },
					{ id: 13, label: "Not price sensitive", type: "model", size: 843 },
					{ id: 14, label: "Shipping sensitive", type: "model", size: 843 },
					{ id: 15, label: "Seasonal", type: "model", size: 843 },
					{ id: 16, label: "Returner", type: "model", size: 843 },
					{ id: 17, label: "Abandoned Cart Recently", type: "rule", size: 843 },
				],
				positive: [],
				negative: []
			},
			activeFilterTab: 'all',
			searchQuery: '',
			isDraggingOver: null,
			activeSignal: null,
			editorPosition: { top: 0, left: 0 },
			calculationDebounce: null
		}
	},
	computed: {
		hasIntegrationSignals() {
			const allSignals = [...(this.signals.positive || []), ...(this.signals.negative || [])];
			return allSignals.some(signal => signal.integrationId != null);
		},
		filteredSignals() {
			// Explicitly depend on these reactive properties
			const signals = this.signals.all || [];
			const activeTab = this.activeFilterTab;
			const query = this.searchQuery ? this.searchQuery.toLowerCase() : '';

			// Apply all filters in sequence
			let filtered = signals;

			// 1. Filter by active tab
			if (activeTab !== 'all') {
				filtered = filtered.filter(signal => signal.type === activeTab);
			}

			// 2. Filter by search query
			if (query) {
				console.log("Query:", query);
				filtered = filtered.filter(signal => {
					const labelMatch = signal.name && signal.name.toLowerCase().includes(query);
					const descMatch = signal.description && signal.description.toLowerCase().includes(query);
					return labelMatch || descMatch;
				});
			}

			// 3. Filter by integration status
			filtered = filtered.filter(signal => {
				if (!signal.integrationId) return true;
				const integration = this.enabledIntegrations.find(i => i.id === signal.integrationId);
				return integration && integration.connected;
			});

			return filtered;
		},
		estimatedSize() {
			if (!this.signals?.positive?.length) {
				return null;
			}

			// Calculate base size from positive signals
			const baseSize = this.signals.positive.reduce((total, signal) => {
				return total + signal.size;
			}, 0);

			// Calculate negative impact
			const negativeImpact = this.signals.negative.reduce((total, signal) => {
				return total + signal.size;
			}, 0);

			// Adjust base size considering negative signals
			const adjustedSize = Math.max(0, baseSize - negativeImpact);

			// Calculate range (±15% for variation)
			const minSize = Math.floor(adjustedSize * 0.85);
			const maxSize = Math.ceil(adjustedSize * 1.15);

			// Format numbers with commas and add asterisk if needed
			const sizeText = `${minSize.toLocaleString()} - ${maxSize.toLocaleString()}`;
			return this.hasIntegrationSignals ? `${sizeText}*` : sizeText;
		}
	},
	async mounted() {
		await this.fetchEnabledIntegrations();
		this.signals.all = await this.fetchSignals();
	},
	watch: {
		'signals.positive': {
			deep: true,
			handler() {
				this.triggerSizeCalculation()
			}
		},
		'signals.negative': {
			deep: true,
			handler() {
				this.triggerSizeCalculation()
			}
		}
	},
	methods: {
		async fetchEnabledIntegrations() {
			try {
				const response = await fetch(`${URL_DOMAIN}/integrations`, {
					method: 'GET',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
				});

				if (response.ok) {
					const data = await response.json();
					this.enabledIntegrations = data;
				} else {
					console.error("Failed to fetch integrations:", response.statusText);
				}
			} catch (error) {
				console.error("Error fetching integrations:", error);
			}
		},
		async fetchSignals() {
			try {
				this.isLoading = true;
				const response = await fetch(`${URL_DOMAIN}/signals`, {
					method: 'GET',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
				});

				if (response.ok) {
					const data = await response.json();
					console.log("Signals:", data);
					return data;
				} else {
					console.error("Failed to fetch signals:", response.statusText);
				}
			} catch (error) {
				console.error("Error fetching signals:", error);
			} finally {
				this.isLoading = false;
			}
		},
		triggerSizeCalculation() {
			// Clear any existing calculation timeout
			if (this.calculationDebounce) {
				clearTimeout(this.calculationDebounce)
			}

			// Emit calculating state
			this.$emit('calculating', true)
			this.isCalculating = true

			// Debounce the calculation
			this.calculationDebounce = setTimeout(() => {
				this.calculateAndEmitSize()
			}, 600)
		},

		async calculateAndEmitSize() {
			if (!this.signals?.positive?.length) {
				this.$emit('update:audience-size', null)
				this.$emit('calculating', false)
				this.isCalculating = false
				return
			}

			// Adjust base size considering negative signals
			let adjustedSize = 0
			//api call to get size
			try {
				const { positive, negative } = this.signals;
				const body = {
					positive,
					negative
				};

				const response = await fetch(`${URL_DOMAIN}/organization-segment/calculate-size`, {
					method: 'POST',
					headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json'
					},
					body: JSON.stringify(body)
				});

				if (response.ok) {
					let calculatedSize = await response.json();
					console.log("Calculated size:", calculatedSize);
					adjustedSize = calculatedSize.totalSize;
				} else {
					console.error("Failed to save segment:", response.statusText);
				}
			} catch (error) {
			console.error("Error saving segment:", error);
			}

			// Calculate range (±15% for variation)
			const minSize = Math.floor(adjustedSize * 0.85)
			const maxSize = Math.ceil(adjustedSize * 1.15)

			// Emit the new size range
			this.$emit('update:audience-size', {
				min: minSize,
				max: maxSize,
				exact: adjustedSize,
				hasIntegrationSignals: this.hasIntegrationSignals
			})

			// Reset calculation state
			this.$emit('calculating', false)
			this.isCalculating = false
		},
		handleDrop({ signalId, sourceSection, targetSection }) {
			console.log("Dropped signal:", signalId, sourceSection, targetSection);

			// If dragging within the same section, do nothing
			if (sourceSection === targetSection) return;

			// Find the signal in the source section
			const sourceSignals = this.signals[sourceSection];
			const signal = sourceSignals.find(s => s.id === signalId);

			if (signal) {
				console.log("Signal found:", signal);

				// Remove the signal from the source section
				this.signals[sourceSection] = sourceSignals.filter(s => s.id !== signalId);

				// Add the signal to the target section
				this.signals[targetSection] = [...this.signals[targetSection], signal];
			} else {
				console.log("Signal not found in source section!");
			}

			// Reset dragging state
			this.isDraggingOver = null;

			// Recalculate audience size
			this.triggerSizeCalculation();
		},

		handleDragEnter(section) {
			this.isDraggingOver = section;
		},
		handleDragLeave() {
			this.isDraggingOver = null;
		},
		handleSignalClick({ signal, event }) {
			if (!signal) return;

			const EDITOR_WIDTH = 320;
			const EDITOR_HEIGHT = 300;
			const TOP_SPACING = 70;
			const BOTTOM_SPACING = 8;

			const badge = event.target.closest('[role="button"]') || event.target;
			const rect = badge.getBoundingClientRect();
			const viewportHeight = window.innerHeight;

			// Set left position to align with badge's left edge
			let left = rect.left;
			let top;

			// Check if editor would go below viewport
			if ((rect.bottom + BOTTOM_SPACING + EDITOR_HEIGHT) > viewportHeight) {
				// Position above the badge
				top = rect.top - EDITOR_HEIGHT + TOP_SPACING;
			} else {
				// Position below the badge
				top = rect.bottom + BOTTOM_SPACING;
			}

			this.editorPosition = {
				top: top + window.scrollY,
				left: Math.max(BOTTOM_SPACING, left)
			};

			this.activeSignal = this.activeSignal?.id === signal.id ? null : signal;
		},

		closeEditor() {
			this.activeSignal = null;
		},

		handleSignalUpdate(signal, updates) {
			const sections = ['all', 'positive', 'negative']
			sections.forEach(section => {
				const signalIndex = this.signals[section].findIndex(s => s.id === signal.id)
				if (signalIndex !== -1) {
					this.signals[section][signalIndex] = { ...signal, ...updates }
				}
			})

			this.triggerSizeCalculation()
		},
	}
}
</script>
