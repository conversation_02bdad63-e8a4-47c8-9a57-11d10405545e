<template>
	<div class="w-full flex flex-col">
		<div
			class="flex justify-start items-center overflow-x-auto overflow-y-hidden tab-name-scroll-container mb-4">
			<div class="flex justify-center items-center">
				<div v-if="!useLozengeTabs" v-for="(tab, index) in tabs"
					class="flex justify-center items-center ml-3 mr-4 tab-name-container cursor-pointer" :class="{
						'text-slate-400 opacity-40 cursor-not-allowed': tabsEnabled && !tabsEnabled[index] && index !== selectedTabIndex,
						'cursor-pointer': tabsEnabled && tabsEnabled[index] || index == selectedTabIndex,
						'cursor-pointer': tabsEnabled === undefined,
					}" :ref="'tab' + index" @click="selectTab(index);">
					<div class="hidden md:block"
						:class="index == selectedTabIndex ? 'underline' : ''"></div>
					<div class="text-ralbackground-dark-secondary text-xl md:text-3xl font-medium font-['Open-Sans'] leading-loose tracking-wide underline-offset-4 hover:underline "
						:class="{'underline': index == selectedTabIndex}">
						{{ tab.name }}
					</div>

					<svg xmlns="http://www.w3.org/2000/svg" height="21" viewBox="0 -960 960 960" width="21" class="ml-8"
						fill="#9D9D9D" v-if="index != (tabs.length - 1)">
						<path
							d="M579-480 285-774q-15-15-14.5-35.5T286-845q15-15 35.5-15t35.5 15l307 308q12 12 18 27t6 30q0 15-6 30t-18 27L356-115q-15 15-35 14.5T286-116q-15-15-15-35.5t15-35.5l293-293Z" />
					</svg>
				</div>

				<div v-if="useLozengeTabs" v-for="(tab, index) in tabs"
					class="flex justify-center items-center ml-3 mr-4 tab-name-container cursor-pointer hover:bg-zinc-300 transition-all duration-300 rounded-full"
					:ref="'tab' + index" @click="selectTab(index);">

					<div class="border border-zinc-300 px-8 py-3 rounded-full"
						:class="index == selectedTabIndex ? 'bg-zinc-300 text-black' : ''">{{ tab.name }}
					</div>

				</div>
			</div>
		</div>

		<div class="flex flex-col">
			<component :is="tabs[selectedTabIndex].component" v-bind="tabs[selectedTabIndex].props"
				v-on="tabs[selectedTabIndex].eventListeners" @next-tab="selectTab(selectedTabIndex + 1)">
			</component>
		</div>
	</div>
</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: ['tabs', 'useLozengeTabs', 'tabsEnabled'],
	components: {
	},
	async mounted() {
	},
	data() {
		return {
			selectedTabIndex: 0,
		}
	},
	methods: {
		selectTab(index) {
			if (this.tabsEnabled && !this.tabsEnabled[index]) {
				return;
			}

			this.selectedTabIndex = index;
			const ref = this.$refs[`tab` + index];
			if (ref) {
				ref[0].scrollIntoView({ behavior: 'smooth' });
			}
		},
	}
}
</script>
<style scoped>
.tab-name-scroll-container {
	max-width: 100vw;
}

.tab-name-container {
	text-wrap: nowrap;
}
</style>

