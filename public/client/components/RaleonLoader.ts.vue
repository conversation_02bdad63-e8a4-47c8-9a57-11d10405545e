<template>

	<div class="animation-container">
		<svg width="83" height="60" viewBox="0 0 834 605" fill="none" xmlns="http://www.w3.org/2000/svg" class="rtop">
		<path fill-rule="evenodd" clip-rule="evenodd" d="M417 0L833.341 523.732L755.961 604.265L714.292 554.032L747.276 519.704L417 104.237L86.7254 519.704L119.709 554.032L78.0404 604.265L0.660156 523.732L417 0Z" fill="#5A16C9"/>
		</svg>

		<svg width="84" height="60" viewBox="0 0 844 602" fill="none" xmlns="http://www.w3.org/2000/svg" class="rmid">
		<path fill-rule="evenodd" clip-rule="evenodd" d="M760.96 409.265L719.291 359.032L422 0.638977L124.709 359.032L83.0399 409.265L0 509.371V602H177.91L422 297.596L666.09 602H844V509.371L760.96 409.265ZM422 102.232L64.9231 532.698V537.228H146.706L422 193.909L697.294 537.228H779.077V532.698L422 102.232Z" fill="#5A16C9"/>
		</svg>

		<svg width="36" height="23" viewBox="0 0 364 231" fill="none" xmlns="http://www.w3.org/2000/svg" class="rbot">
		<path d="M363.569 231L182.006 0.456299L0.442383 231H83.0083L182.006 105.296L281.003 231H363.569Z" fill="#5A16C9"/>
		</svg>
	</div>

</template>

  <script>
  export default {
	name: 'RaleonLoader',
  };
  </script>

<style scoped>
.animation-container {
  position: relative;
}

@keyframes ralLoader {
	0%, 16.67%, 83.33%, 100% { opacity: 0; }
  16.67%, 33.33% { opacity: 1; } /* Bottom SVG fades in */
  25%, 50% { opacity: 1; } /* Middle SVG starts fading in before bottom finishes */
  41.67%, 66.67% { opacity: 1; } /* Top SVG starts fading in before middle finishes */
}

.rtop {
  opacity: 0;
  animation: ralLoader 3s ease-in-out infinite;
  animation-delay: 0.42s; /* Adjusted for 3s cycle */
}
.rmid {
  opacity: 0;
  position: absolute;
  top: 19px;
  animation: ralLoader 3s ease-in-out infinite;
  animation-delay: 0.25s; /* Adjusted for 3s cycle */
}
.rbot {
  opacity: 0;
  position: absolute;
  top: 55px;
  left: 24px;
  animation: ralLoader 3s ease-in-out infinite;
}

.rbot::after, .rmid::after, .rtop::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.5); /* Adjust color and opacity as needed */
  border-radius: 50%;
  transform: scale(0);
  opacity: 0;
  transition: all 0.6s ease-out;
  transform-origin: center;
}

.loader-visible::after {
  transform: scale(2); /* Adjust scale for desired ripple size */
  opacity: 0; /* Fades the ripple out */
}
</style>
