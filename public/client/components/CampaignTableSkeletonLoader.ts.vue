<template>
	<div role="status" class="w-full p-4 space-y-4 border bg-white border-ralprimary-light opacity-75 border-opacity-50 rounded-2xl shadow animate-pulse mt-4">
		<div class="flex items-center justify-between">
			<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
			<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
			<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
			<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
		</div>
	</div>
	<div role="status" class="w-full p-4 space-y-4 border bg-white border-ralprimary-light opacity-75 border-opacity-50 rounded-2xl shadow animate-pulse mt-2">
		<div class="flex items-center justify-between">
			<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
			<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
			<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
			<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
		</div>
	</div>
	<div role="status" class="w-full p-4 space-y-4 border bg-white border-ralprimary-light opacity-75 border-opacity-50 rounded-2xl shadow animate-pulse mt-2">
		<div class="flex items-center justify-between">
			<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
			<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
			<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
			<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
		</div>
	</div>
</template>

<script>
	export default { name: 'CampaignTableSkeletonLoader' }
</script>
