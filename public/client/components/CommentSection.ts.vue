<template>
  <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
        </svg>
        Comments ({{ comments.length }})
      </h3>
    </div>

    <div class="p-6 space-y-4" v-if="comments.length > 0">
      <!-- Comments list - only show top-level comments -->
      <div
        v-for="comment in topLevelComments"
        :key="comment.id"
        class="relative bg-white p-4 rounded-lg border border-gray-200 hover:border-purple-200 transition-colors"
        :class="{'bg-green-50 border-green-200': comment.resolved}"
      >
        <div class="relative">
          <!-- Resolve button (top right) -->
          <button
            @click="toggleResolve(comment)"
            class="absolute top-0 right-0 p-1 rounded hover:bg-green-50 mt-1 mr-1" 
            :class="comment.resolved ? 'text-blue-600' : 'text-green-600'"
            :title="comment.resolved ? 'Unresolve comment' : 'Resolve comment'"
          >
            <svg v-if="!comment.resolved" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>

          <div class="flex justify-between items-start gap-4">
            <!-- User info and comment content -->
            <div class="flex-1">
              <div class="flex items-center mb-2">
                <div class="h-8 w-8 rounded-full bg-purple-200 flex items-center justify-center text-purple-700 font-semibold mr-2">
                  {{ getInitials(comment.user?.firstName, comment.user?.lastName) }}
                </div>
                <div>
                  <p class="font-medium text-sm">
                    {{ getUserFullName(comment.user) || currentUser?.name || 'User' }}
                  </p>
                  <p class="text-xs text-gray-500">
                    {{ formatDate(comment.createdAt) }}
                  </p>
                </div>
                <!-- Status badges -->
                <div class="ml-2 flex gap-1">
                  <span v-if="comment.resolved" class="px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-700">Resolved</span>
                </div>
              </div>
              <p class="text-gray-700">{{ comment.content }}</p>
            </div>
          </div>

          <!-- Reply button (bottom right) -->
          <div class="flex justify-end mt-3">
            <button
              @click="setReply(comment)"
              class="p-1 rounded hover:bg-purple-50 text-purple-600"
              title="Reply to comment"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Replies section -->
        <div
          v-if="comment.replies && comment.replies.length"
          class="mt-3 ml-8 space-y-3 pt-3 border-t border-gray-100"
        >
          <div
            v-for="reply in comment.replies"
            :key="reply.id"
            class="bg-gray-50 p-3 rounded-lg border border-gray-200"
          >
            <div class="flex items-center mb-1">
              <div class="h-6 w-6 rounded-full bg-purple-100 flex items-center justify-center text-purple-700 font-semibold text-xs mr-2">
                {{ getInitials(reply.user?.firstName, reply.user?.lastName) }}
              </div>
              <p class="font-medium text-sm">
                {{ getUserFullName(reply.user) || currentUser?.name || 'User' }}
              </p>
              <p class="text-xs text-gray-500 ml-2">
                {{ formatDate(reply.createdAt) }}
              </p>
            </div>
            <p class="text-gray-600 text-sm ml-8">{{ reply.content }}</p>
          </div>
        </div>

        <!-- Reply form for this comment -->
        <div v-if="replyTo && replyTo.id === comment.id" class="mt-3 ml-8 pt-3 border-t border-gray-100">
          <div class="mb-2 flex items-center text-sm text-gray-600 bg-blue-50 p-2 rounded">
            <span>Replying to: {{ getUserFullName(replyTo.user) || 'User' }}</span>
            <button @click="clearReply" class="ml-2 text-red-500 hover:text-red-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div class="flex gap-2">
            <textarea
              v-model="newComment"
              rows="2"
              class="flex-1 border border-gray-300 rounded-lg p-3 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="Add a reply..."
            ></textarea>
            <button
              @click="submitComment"
              class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium flex items-center"
              :disabled="!newComment.trim()"
              :class="{'opacity-50 cursor-not-allowed': !newComment.trim()}"
            >
              <svg xmlns="http://www.w3.org/2000/svg" height="16px" viewBox="0 -960 960 960" width="16px" fill="currentColor" class="mr-1">
                <path d="M440-160v-487L216-423l-56-57 320-320 320 320-56 57-224-224v487h-80Z" />
              </svg>
              Reply
            </button>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="p-6 text-center text-gray-500">
      <p>No comments yet. Be the first to add one!</p>
    </div>

    <!-- Main comment form (only show when not replying) -->
    <div v-if="!replyTo" class="p-6 border-t border-gray-200 bg-gray-50">
      <div class="flex gap-2">
        <textarea
          v-model="newComment"
          rows="2"
          class="flex-1 border border-gray-300 rounded-lg p-3 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          placeholder="Add a comment..."
        ></textarea>
        <button
          @click="submitComment"
          class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium flex items-center"
          :disabled="!newComment.trim()"
          :class="{'opacity-50 cursor-not-allowed': !newComment.trim()}"
        >
          <svg xmlns="http://www.w3.org/2000/svg" height="16px" viewBox="0 -960 960 960" width="16px" fill="currentColor" class="mr-1">
            <path d="M440-160v-487L216-423l-56-57 320-320 320 320-56 57-224-224v487h-80Z" />
          </svg>
          Submit
        </button>
      </div>
    </div>
  </div>
</template>

<script>
// No external date formatting library needed
import * as Utils from '../../client-old/utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
  name: 'CommentSection',

  props: {
    planId: {
      type: [String, Number],
      required: true
    },
    urlDomain: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      comments: [],
      newComment: '',
      replyTo: null,
      currentUser: null
    };
  },

  computed: {
    topLevelComments() {
      return this.comments.filter(comment => !comment.parentId);
    }
  },

  mounted() {
    console.log('CommentSection component mounted with planId:', this.planId);
    this.fetchComments();
    this.getCurrentUser();
  },

  methods: {
    async getCurrentUser() {
      try {
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        this.currentUser = userInfo;
      } catch (err) {
        console.error('Error getting current user:', err);
      }
    },

    async fetchComments() {
      console.log('Fetching comments for plan:', this.planId);
      console.log('Using URL domain:', this.urlDomain);

      try {
        // Use the correct endpoint from the controller
        const url = `${URL_DOMAIN}/planner/plan/${this.planId}/comments`;
        console.log('Fetch URL:', url);

        const response = await fetch(url, {
          method: 'GET',
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        });

        if (response.ok) {
          const allComments = await response.json();
          console.log('Fetched all comments:', allComments);

          // Process comments to build the hierarchy
          this.processComments(allComments);
        } else {
          console.error('Failed to load comments. Server returned:', response.status);
        }
      } catch (err) {
        console.error('Failed to load comments:', err);
      }
    },

    processComments(allComments) {
      // Create a map of parent comments with their replies
      const commentMap = {};

      // Initialize all comments with empty replies array
      allComments.forEach(comment => {
        comment.replies = [];
        commentMap[comment.id] = comment;
      });

      // Organize replies under their parent comments
      allComments.forEach(comment => {
        if (comment.parentId && commentMap[comment.parentId]) {
          commentMap[comment.parentId].replies.push(comment);
        }
      });

      // Set the final comments array
      this.comments = allComments;
    },

    async submitComment() {
      if (this.newComment.trim() === '') return;

      try {
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const body = {
          content: this.newComment,
          userId: userInfo.id
        };

        if (this.replyTo) {
          body.parentId = this.replyTo.id;
        }

        const response = await fetch(`${URL_DOMAIN}/planner/plan/${this.planId}/comments`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body)
        });

        if (response.ok) {
          this.newComment = '';
          this.replyTo = null;
          await this.fetchComments();
        } else {
          console.error('Failed to submit comment. Server returned:', response.status);
        }
      } catch (err) {
        console.error('Failed to add comment:', err);
      }
    },

    setReply(comment) {
      this.replyTo = comment;
      // Focus the textarea
      this.$nextTick(() => {
        const textarea = document.querySelector('textarea');
        if (textarea) textarea.focus();
      });
    },

    clearReply() {
      this.replyTo = null;
    },

    async toggleResolve(comment) {
      try {
        const response = await fetch(`${URL_DOMAIN}/planner/comments/${comment.id}`, {
          method: 'PATCH',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ resolved: !comment.resolved })
        });

        if (response.ok) {
          comment.resolved = !comment.resolved;
        } else {
          console.error('Failed to toggle resolve status. Server returned:', response.status);
        }
      } catch (err) {
        console.error('Failed to toggle resolve status:', err);
      }
    },


    formatDate(dateString) {
      if (!dateString) return '';

      try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
          hour: 'numeric',
          minute: 'numeric'
        });
      } catch (err) {
        console.error('Error formatting date:', err);
        return dateString;
      }
    },

    getUserFullName(user) {
      if (!user) return '';

      if (user.firstName && user.lastName) {
        return `${user.firstName} ${user.lastName}`;
      } else if (user.firstName) {
        return user.firstName;
      } else if (user.lastName) {
        return user.lastName;
      }

      return '';
    },

    getInitials(firstName, lastName) {
      if (!firstName && !lastName) return '?';

      let initials = '';

      if (firstName) {
        initials += firstName[0].toUpperCase();
      }

      if (lastName) {
        initials += lastName[0].toUpperCase();
      }

      return initials || '?';
    }
  }
};
</script>

<style scoped>
.comment-transition-enter-active,
.comment-transition-leave-active {
  transition: all 0.3s ease;
}

.comment-transition-enter-from,
.comment-transition-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>
