<template>
  <div class="flex h-screen overflow-hidden bg-gray-50">
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
      <main>
        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
          <div class="mb-8 flex">
            <h1 class="text-3xl md:text-4xl text-gray-900 font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent">Data Settings</h1>
          </div>
          <div class="bg-white shadow-xl rounded-2xl mb-8 border border-gray-100">
            <div class="flex flex-col md:flex-row md:-mr-px">
              <SettingsSidebar />
              <div class="grow">
                <div class="p-8 space-y-8">
                  <!-- Customer Excluded Tags -->
                  <section>
                    <div class="mb-6">
                      <label for="tags" class="block text-lg font-semibold text-gray-900 mb-2">Customer Excluded Tags</label>
                      <p class="text-gray-600 leading-relaxed">Enter tags for customers to be excluded from the Loyalty Program, Metrics, and Segmentation. These should match your Shopify Customer Tags. Add one tag at a time.</p>
                    </div>
                    <div class="flex items-center gap-4 mb-4">
                      <input 
                        type="text" 
                        id="tags" 
                        v-model="tagInput" 
                        @keydown.enter.prevent="addTag" 
                        placeholder="Add tags" 
                        class="flex-1 px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200" 
                      />
                      <button 
                        @click.stop="addTag"
                        class="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200"
                      >
                        Add
                      </button>
                    </div>
                    <div class="flex flex-wrap gap-2">
                      <span v-for="tag in tags" :key="tag" class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-purple-100 text-purple-800 border border-purple-200">
                        {{ tag }}
                        <button type="button" @click="removeTag(tag)" class="ml-2 text-purple-600 hover:text-purple-800 transition-colors duration-200">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </span>
                    </div>
                  </section>

                  <!-- Order Excluded Tags -->
                  <section>
                    <div class="mb-6">
                      <label for="order-tags" class="block text-lg font-semibold text-gray-900 mb-2">Order Excluded Tags</label>
                      <p class="text-gray-600 leading-relaxed">Enter tags for orders to be excluded from the Loyalty Program, Metrics, and Segmentation. These should match your Shopify Order Tags. Add one tag at a time.</p>
                    </div>
                    <div class="flex items-center gap-4 mb-4">
                      <input 
                        type="text" 
                        id="order-tags" 
                        v-model="orderTagInput" 
                        @keydown.enter.prevent="addOrderTag" 
                        placeholder="Add order tags" 
                        class="flex-1 px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200" 
                      />
                      <button 
                        @click.stop="addOrderTag"
                        class="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200"
                      >
                        Add
                      </button>
                    </div>
                    <div class="flex flex-wrap gap-2">
                      <span v-for="tag in orderTags" :key="tag" class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-purple-100 text-purple-800 border border-purple-200">
                        {{ tag }}
                        <button type="button" @click="removeOrderTag(tag)" class="ml-2 text-purple-600 hover:text-purple-800 transition-colors duration-200">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </span>
                    </div>
                  </section>

                  <!-- Product Exclusions -->
                  <section>
                    <div class="mb-6">
                      <label class="block text-lg font-semibold text-gray-900 mb-2">Product Exclusions</label>
                      <p class="text-gray-600 leading-relaxed">Select products to exclude from replenishment and recommendations in AI Segmentation.</p>
                    </div>
                    <div class="mb-6">
                      <div class="relative">
                        <input 
                          type="text" 
                          v-model="productSearch" 
                          @input="searchProducts" 
                          placeholder="Search products..." 
                          class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200" 
                        />
                        <div v-if="searchResults.length > 0" class="absolute z-10 w-full mt-2 bg-white shadow-lg rounded-xl border border-gray-200">
                          <ul class="max-h-60 overflow-auto">
                            <li v-for="product in searchResults" :key="product.id" @click="addExcludedProduct(product)" class="px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-center transition-colors duration-200 first:rounded-t-xl last:rounded-b-xl">
                              <span class="text-gray-900">{{ product.title }}</span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div v-for="product in excludedProducts" :key="product.id" class="relative rounded-xl border border-gray-200 bg-white p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
                        <div class="flex items-center">
                          <div class="flex-grow">
                            <h4 class="text-sm font-medium text-gray-900">{{ product.title }}</h4>
                          </div>
                          <button @click="removeExcludedProduct(product)" class="text-gray-400 hover:text-red-500 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </section>
                </div>
                <footer class="border-t border-gray-100 bg-gray-50/50 rounded-b-2xl">
                  <div class="flex flex-col px-8 py-6">
                    <div class="flex justify-end gap-4">
                      <button class="px-6 py-3 border border-gray-200 text-gray-600 font-medium rounded-xl hover:bg-gray-50 hover:border-gray-300 transition-all duration-200">
                        Cancel
                      </button>
                      <button 
                        @click="updateDataSettings()"
                        class="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200"
                      >
                        Save Changes
                      </button>
                    </div>
                  </div>
                </footer>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
  <StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>
</template>

<script>
import SettingsSidebar from '../../client-old/partials/settings/SettingsSidebar.vue'
import StatusMessage from '../components/StatusMessage.ts.vue'
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue'
import * as Utils from '../../client-old/utils/Utils'

const URL_DOMAIN = Utils.URL_DOMAIN

export default {
  name: 'DataSettings',
  components: { SettingsSidebar, StatusMessage, LightSecondaryButton },
  async mounted() {
    try {
      const response = await fetch(`${URL_DOMAIN}/loyalty-programs`, {
        method: 'GET',
        credentials: 'omit',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })
      const json = await response.json()
      if (json && json[0]) {
        const program = json[0]
        this.programId = program.id
        if (program.exclusionTags) {
          this.tags = program.exclusionTags.split(',').map(t => t.trim())
        }
        if (program.orderExclusionTags) {
          this.orderTags = program.orderExclusionTags.split(',').map(t => t.trim())
        }
        if (program.excludedProductIds && program.excludedProductIds.length > 0) {
          const productsResponse = await fetch(`${URL_DOMAIN}/shop-products`, {
            method: 'GET',
            credentials: 'omit',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })
          if (!productsResponse.ok) throw new Error('Failed to fetch products')
          const productsData = await productsResponse.json()
          this.allProducts = productsData || []
          const excludedProductIds = program.excludedProductIds.split(',')
          this.excludedProducts = this.allProducts.filter(p => excludedProductIds.includes(p.id.toString()))
        }
      } else {
        // Create default loyalty program if none exists
        const payload = {
          name: "Default Program",
          active: false
        }
        const createResponse = await fetch(`${URL_DOMAIN}/loyalty-programs`, {
          method: 'POST',
          credentials: 'omit',
          mode: 'cors',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify(payload),
        })
        if (createResponse.ok) {
          const newProgram = await createResponse.json()
          this.programId = newProgram.id
        } else {
          throw new Error('Failed to create default loyalty program')
        }
      }
    } catch (err) {
      console.error('Error loading data settings:', err)
      this.status.type = 'fail'
      this.status.message = 'Failed to load data settings'
    }
  },
  data() {
    return {
      programId: -1,
      tags: [],
      tagInput: '',
      orderTags: [],
      orderTagInput: '',
      productSearch: '',
      allProducts: [],
      searchResults: [],
      excludedProducts: [],
      status: { message: '', type: 'nope' }
    }
  },
  methods: {
    addTag() {
      const newTags = this.tagInput.split(',').map(t => t.trim()).filter(t => t !== '')
      this.tags.push(...newTags)
      this.tagInput = ''
    },
    removeTag(tagToRemove) {
      this.tags = this.tags.filter(tag => tag !== tagToRemove)
    },
    addOrderTag() {
      const newTags = this.orderTagInput.split(',').map(tag => tag.trim()).filter(tag => tag !== '')
      this.orderTags.push(...newTags)
      this.orderTagInput = ''
    },
    removeOrderTag(tagToRemove) {
      this.orderTags = this.orderTags.filter(tag => tag !== tagToRemove)
    },
    async searchProducts() {
      if (this.allProducts.length === 0) {
        try {
          const response = await fetch(`${URL_DOMAIN}/shop-products`, {
            method: 'GET',
            credentials: 'omit',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })
          if (!response.ok) throw new Error('Failed to fetch products')
          const data = await response.json()
          this.allProducts = data || []
        } catch (err) {
          console.error('Error fetching products:', err)
          this.status.type = 'fail'
          this.status.message = 'Failed to load products'
          return
        }
      }
      if (!this.productSearch.trim()) {
        this.searchResults = []
        return
      }
      const searchTerm = this.productSearch.toLowerCase()
      this.searchResults = this.allProducts.filter(product => product.title.toLowerCase().includes(searchTerm))
    },
    addExcludedProduct(product) {
      if (!this.excludedProducts.some(p => p.id === product.id)) {
        this.excludedProducts.push(product)
      }
      this.productSearch = ''
      this.searchResults = []
    },
    removeExcludedProduct(product) {
      this.excludedProducts = this.excludedProducts.filter(p => p.id !== product.id)
    },
    async updateDataSettings() {
      const programDetails = {
        exclusionTags: this.tags.join(','),
        orderExclusionTags: this.orderTags.join(','),
        excludedProductIds: this.excludedProducts.map(p => p.id).join(',')
      }
      const response = await fetch(`${URL_DOMAIN}/loyalty-programs/${this.programId}`, {
        method: 'PATCH',
        credentials: 'omit',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(programDetails)
      })
      if (response.error) {
        this.status.type = 'fail'
        this.status.message = 'Failed to update data settings'
        return
      }
      this.status.type = 'success'
      this.status.message = 'Data settings updated successfully.'
    }
  }
}
</script>
<style scoped>
</style>
