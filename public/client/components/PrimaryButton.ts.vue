<template>

	<div class="inline-flex items-center sm:py-0 bg-gradient-button-primary transition-all duration-300 rounded-full cursor-not-allowed focus:outline-none focus:ring whitespace-nowrap"
		@click="click" :class="{
			'px-5 sm:px-5 py-1': getSize == 'normal',
			'px-5 sm:px-4 py-1': getSize == 'small',
			'px-1 sm:px-4 py-1': getSize == 'xs',
			'cursor-pointer': !disabled,
			'hover:opacity-95': !disabled,
			'opacity-50 cursor-not-allowed': disabled || isSaving,
			'bg-ralbutton-primary-light-deactivated': disabled || isSaving,
		}">

		<svg v-if="icon === 'true' && !showSpinner" :width="svgWidth" :height="svgHeight" viewBox="0 0 40 41"
			fill="none" xmlns="http://www.w3.org/2000/svg" class="mr-2">
			<path
				d="M13.3333 20.5H20M20 20.5H26.6667M20 20.5V27.1667M20 20.5V13.8333M20 35.5C11.7157 35.5 5 28.7843 5 20.5C5 12.2157 11.7157 5.5 20 5.5C28.2843 5.5 35 12.2157 35 20.5C35 28.7843 28.2843 35.5 20 35.5Z"
				stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
		</svg>
		<span class="text-white font-['Inter']" :class="{
			'text-2xl sm:text-5xl font-medium sm:leading-[65px]': getSize === 'normal',
			'text-xl sm:text-2xl font-medium sm:leading-[65px]': getSize === 'small',
			'text-md sm:text-md font-medium sm:leading-[35px]': getSize === 'xs'
		}">{{ showSpinner ? spinnerText || 'Saving...' : cta }}</span>
		<svg v-if="showSpinner" class="animate-spin mr-2" xmlns="http://www.w3.org/2000/svg" height="32"
			viewBox="0 0 24 24" width="32" fill="#FFFFFF">
			<path
				d="M12 22c5.421 0 10-4.579 10-10S17.421 2 12 2 2 6.579 2 12s4.579 10 10 10zm0-2C6.486 20 2 15.514 2 10S6.486 0 12 0s10 4.486 10 10-4.486 10-10 10z" />
		</svg>
	</div>
</template>

  <script>
  export default {
	name: 'PrimaryButton',
	props: ['cta', 'size', 'icon', 'disabled', 'showSpinner', 'spinnerText'],
  	emits: ['click'],

	computed: {
		getSize() {
			if(this.size == 'small') {
				return 'small'; }
			else if(this.size == 'xs') {
				return 'xs';
			}
			else {
				return 'normal';
			}
		},
		svgWidth() {
			if(this.getSize == 'normal')
				return '40';
			else if(this.getSize == 'xs')
				return '20';
			else
				return '30';
		},
		svgHeight() {
			if(this.getSize == 'normal')
				return '41';
			else if(this.getSize == 'xs')
				return '21';
			else
				return '31';
		}
	},
	methods: {
		click() {
			if (!this.disabled) {
				this.$emit('click');
			}
		}
	}
  };
  </script>
