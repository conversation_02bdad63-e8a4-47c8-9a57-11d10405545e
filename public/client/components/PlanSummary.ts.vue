<template>
  <div class="px-8 py-6 border-t border-b border-gray-100">
    <h3 class="text-lg font-medium text-gray-900 mb-2">Campaigns</h3>
    <p class="text-gray-600 leading-relaxed mb-2">
      {{ description }}
    </p>
    <div v-if="userPrompt" class="mb-2">
      <div class="flex items-center gap-2 cursor-pointer" @click="toggleUserPrompt">
        <svg :class="{'rotate-90': showUserPrompt}" class="w-4 h-4 transition-transform duration-200" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M7.293 4.293a1 1 0 011.414 0L14.414 10l-5.707 5.707a1 1 0 01-1.414-1.414L11.586 10 7.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
        <span class="text-sm font-medium text-gray-700">User Prompt</span>
      </div>
      <div v-show="showUserPrompt" class="mt-2 pl-6 text-sm text-gray-600 border-l-2 border-gray-200 space-y-2">
        <p>{{ userPrompt }}</p>
      </div>
    </div>
    <div v-if="dataSummary" class="mb-2">
      <div class="flex items-center gap-2 cursor-pointer" @click="toggleDataSummary">
        <svg :class="{'rotate-90': showDataSummary}" class="w-4 h-4 transition-transform duration-200" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M7.293 4.293a1 1 0 011.414 0L14.414 10l-5.707 5.707a1 1 0 01-1.414-1.414L11.586 10 7.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
        <span class="text-sm font-medium text-gray-700">Data Analyzed</span>
      </div>
      <div v-show="showDataSummary" class="mt-2 pl-6 text-sm text-gray-600 border-l-2 border-gray-200 space-y-2">
        <p>{{ dataSummary }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PlanSummary',

  props: {
    // The plan's description
    description: {
      type: String,
      required: true,
      default: ''
    },
    // The user prompt text
    userPrompt: {
      type: String,
      required: false,
      default: ''
    },
    // The data summary text
    dataSummary: {
      type: String,
      required: false,
      default: ''
    }
  },

  data() {
    return {
      // Local state to manage the visibility of the user prompt
      showUserPrompt: false,
      // Local state to manage the visibility of the data summary
      showDataSummary: false
    }
  },

  methods: {
    // Toggles the showUserPrompt state
    toggleUserPrompt() {
      this.showUserPrompt = !this.showUserPrompt;
    },
    // Toggles the showDataSummary state
    toggleDataSummary() {
      this.showDataSummary = !this.showDataSummary;
    }
  }
}
</script>

<style scoped>
/* Optional styles specific to this component */
.rotate-90 {
  transform: rotate(90deg);
}
</style>
