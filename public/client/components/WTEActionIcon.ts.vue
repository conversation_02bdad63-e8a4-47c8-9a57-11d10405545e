<template>
	<div>
		<div v-html="svgContent"></div>
	</div>
</template>

<script>
export default {
	props: ['iconType'],
	name: 'WTEActionIcon',
	computed: {
		svgContent() {
			return this.getActionIcon(this.iconType);
		},
	},
	methods: {
		getActionIcon(iconType) {
			switch (iconType) {
				case 'dollar-spent':
					return `<svg fill="#15803D" slot="dollar-spent" xmlns="http://www.w3.org/2000/svg"
								height="33" viewBox="0 -960 960 960" width="33">
								<path
									d="M237-120q-23 0-44.5-16T164-175q-25-84-41-145.5t-25.5-108Q88-475 84-511t-4-69q0-92 64-156t156-64h200q27-36 68.5-58t91.5-22q25 0 42.5 17.5T720-820q0 6-1.5 12t-3.5 11q-4 11-7.5 22t-5.5 24l91 91h47q17 0 28.5 11.5T880-620v210q0 13-7.5 23T852-372l-85 28-50 167q-8 26-29 41.5T640-120h-80q-33 0-56.5-23.5T480-200h-80q0 33-23.5 56.5T320-120h-83Zm3-80h80v-80h240v80h80l62-206 98-33v-141h-40L620-720q0-20 2.5-39t7.5-37q-29 8-51 27.5T547-720H300q-58 0-99 41t-41 99q0 41 21 140.5T240-200Zm400-320q17 0 28.5-11.5T680-560q0-17-11.5-28.5T640-600q-17 0-28.5 11.5T600-560q0 17 11.5 28.5T640-520Zm-160-80q17 0 28.5-11.5T520-640q0-17-11.5-28.5T480-680H360q-17 0-28.5 11.5T320-640q0 17 11.5 28.5T360-600h120Zm0 102Z" />
							</svg>`;
				case 'nth-purchase':
					return `<svg fill="#15803D" slot="nth-purchase" xmlns="http://www.w3.org/2000/svg"
								height="33" viewBox="0 -960 960 960" width="33">
								<path
									d="M240-80q-33 0-56.5-23.5T160-160v-480q0-33 23.5-56.5T240-720h80q0-66 47-113t113-47q66 0 113 47t47 113h80q33 0 56.5 23.5T800-640v480q0 33-23.5 56.5T720-80H240Zm0-80h480v-480h-80v80q0 17-11.5 28.5T600-520q-17 0-28.5-11.5T560-560v-80H400v80q0 17-11.5 28.5T360-520q-17 0-28.5-11.5T320-560v-80h-80v480Zm160-560h160q0-33-23.5-56.5T480-800q-33 0-56.5 23.5T400-720ZM240-160v-480 480Z" />
							</svg>`;
				case 'timed-purchase':
					return `<svg fill="#15803D" slot="timed-purchase" xmlns="http://www.w3.org/2000/svg"
								height="33" viewBox="0 -960 960 960" width="33">
								<path d="M360-840v-80h240v80H360Zm80 440h80v-240h-80v240Zm40 320q-74 0-139.5-28.5T226-186q-49-49-77.5-114.5T120-440q0-74 28.5-139.5T226-694q49-49 114.5-77.5T480-800q62 0 119 20t107 58l56-56 56 56-56 56q38 50 58 107t20 119q0 74-28.5 139.5T734-186q-49 49-114.5 77.5T480-80Zm0-80q116 0 198-82t82-198q0-116-82-198t-198-82q-116 0-198 82t-82 198q0 116 82 198t198 82Zm0-280Z"/>
							</svg>`;
				case 'ig-follow':
					return `<svg fill="#15803D" slot="ig-follow" xmlns="http://www.w3.org/2000/svg" height="33"
								width="33" viewBox="0 0 56.7 56.7">
								<g>
									<path d="M28.2,16.7c-7,0-12.8,5.7-12.8,12.8s5.7,12.8,12.8,12.8S41,36.5,41,29.5S35.2,16.7,28.2,16.7z M28.2,37.7
								c-4.5,0-8.2-3.7-8.2-8.2s3.7-8.2,8.2-8.2s8.2,3.7,8.2,8.2S32.7,37.7,28.2,37.7z" />
									<circle cx="41.5" cy="16.4" r="2.9" />
									<path d="M49,8.9c-2.6-2.7-6.3-4.1-10.5-4.1H17.9c-8.7,0-14.5,5.8-14.5,14.5v20.5c0,4.3,1.4,8,4.2,10.7c2.7,2.6,6.3,3.9,10.4,3.9
								h20.4c4.3,0,7.9-1.4,10.5-3.9c2.7-2.6,4.1-6.3,4.1-10.6V19.3C53,15.1,51.6,11.5,49,8.9z M48.6,39.9c0,3.1-1.1,5.6-2.9,7.3
								s-4.3,2.6-7.3,2.6H18c-3,0-5.5-0.9-7.3-2.6C8.9,45.4,8,42.9,8,39.8V19.3c0-3,0.9-5.5,2.7-7.3c1.7-1.7,4.3-2.6,7.3-2.6h20.6
								c3,0,5.5,0.9,7.3,2.7c1.7,1.8,2.7,4.3,2.7,7.2V39.9L48.6,39.9z" />
								</g>
							</svg>`;
				case 'tt-follow':
					return `<svg viewBox="0 0 256 256" fill="#15803D" width="33" height="33" slot="tt-follow"
								xmlns="http://www.w3.org/2000/svg">
								<rect fill="none" height="256" width="256" />
								<path
									d="M168,106a95.9,95.9,0,0,0,56,18V84a56,56,0,0,1-56-56H128V156a28,28,0,1,1-40-25.3V89.1A68,68,0,1,0,168,156Z"
									fill="none" stroke="#15803D" stroke-linecap="round" stroke-linejoin="round"
									stroke-width="16" />
							</svg>`;
				case 'fb-follow':
					return `<svg height="33" width="33" fill="#15803D" version="1.1" viewBox="0 0 512 512"
								xml:space="preserve" xmlns="http://www.w3.org/2000/svg"
								xmlns:xlink="http://www.w3.org/1999/xlink">
								<path
									d="M288,192v-38.1c0-17.2,3.8-25.9,30.5-25.9H352V64h-55.9c-68.5,0-91.1,31.4-91.1,85.3V192h-45v64h45v192h83V256h56.4l7.6-64  H288z M330.2,240h-41.1H272v15.5V432h-51V255.5V240h-14.9H176v-32h30.1H221v-16.5v-42.2c0-24.5,5.4-41.2,15.5-51.8  C247.7,85.5,267.6,80,296.1,80H336v32h-17.5c-12,0-27.5,1.1-37.1,11.7c-8.1,9-9.4,20.1-9.4,30.1v37.6V208h17.1H334L330.2,240z"
									stroke="#15803D" stroke-width="10" />
							</svg>`;
				case 'product-review':
					return `<svg xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"
								fill="#15803D" slot="product-review">
								<path
									d="m480-461 76 46q11 7 22-.5t8-20.5l-20-87 68-59q10-9 6-21.5T622-617l-89-7-35-82q-5-12-18-12t-18 12l-35 82-89 7q-14 1-18 13.5t6 21.5l68 59-20 87q-3 13 8 20.5t22 .5l76-46ZM240-240l-92 92q-19 19-43.5 8.5T80-177v-623q0-33 23.5-56.5T160-880h640q33 0 56.5 23.5T880-800v480q0 33-23.5 56.5T800-240H240Zm-34-80h594v-480H160v525l46-45Zm-46 0v-480 480Z" />
							</svg>`;
				case 'product-photo-review':
					return `<svg xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 0 32 32" width="33"
								fill="#15803D" slot="product-photo-review">
								<path d="M30 2.497h-28c-1.099 0-2 0.901-2 2v23.006c0 1.099 0.9 2 2 2h28c1.099 0 2-0.901 2-2v-23.006c0-1.099-0.901-2-2-2zM30 27.503l-28-0v-5.892l8.027-7.779 8.275 8.265c0.341 0.414 0.948 0.361 1.379 0.035l3.652-3.306 6.587 6.762c0.025 0.025 0.053 0.044 0.080 0.065v1.85zM30 22.806l-5.876-6.013c-0.357-0.352-0.915-0.387-1.311-0.086l-3.768 3.282-8.28-8.19c-0.177-0.214-0.432-0.344-0.709-0.363-0.275-0.010-0.547 0.080-0.749 0.27l-7.309 7.112v-14.322h28v18.309zM23 12.504c1.102 0 1.995-0.894 1.995-1.995s-0.892-1.995-1.995-1.995-1.995 0.894-1.995 1.995c0 1.101 0.892 1.995 1.995 1.995z"></path>
							</svg>`;
				case 'welcome-bonus':
					return `<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="33" fill="#15803D" slot="welcome-bonus"><path d="m558-144 238-74q-5-9-14.5-15.5T760-240H558q-27 0-43-2t-33-8l-57-19q-16-5-23-20t-2-31q5-16 19.5-23.5T450-346l42 14q17 5 38.5 8t58.5 4h11q0-11-6.5-21T578-354l-234-86h-64v220l278 76Zm-21 78-257-72q-8 26-31.5 42T200-80h-80q-33 0-56.5-23.5T40-160v-280q0-33 23.5-56.5T120-520h224q7 0 14 1.5t13 3.5l235 87q33 12 53.5 42t20.5 66h80q50 0 85 33t35 87q0 22-11.5 34.5T833-145L583-67q-11 4-23 4t-23-3Zm-417-94h80v-280h-80v280Zm520-312q-15 0-29.5-5.5T584-494L474-602q-31-30-52.5-66.5T400-748q0-55 38.5-93.5T532-880q32 0 60 13.5t48 36.5q20-23 48-36.5t60-13.5q55 0 93.5 38.5T880-748q0 43-21 79.5T807-602L696-494q-12 11-26.5 16.5T640-472Zm0-80 109-107q19-19 35-40.5t16-48.5q0-22-15-37t-37-15q-14 0-26.5 5.5T700-778l-29 35q-12 14-31 14t-31-14l-29-35q-9-11-21.5-16.5T532-800q-22 0-37 15t-15 37q0 27 16 48.5t35 40.5l109 107Zm0-154Z"/></svg>`;
				case 'birthday-bonus':
					return `<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="33" fill="#15803D" slot="birthday-bonus"><path d="M160-80q-17 0-28.5-11.5T120-120v-200q0-33 23.5-56.5T200-400v-160q0-33 23.5-56.5T280-640h160v-58q-18-12-29-29t-11-41q0-15 6-29.5t18-26.5l56-56 56 56q12 12 18 26.5t6 29.5q0 24-11 41t-29 29v58h160q33 0 56.5 23.5T760-560v160q33 0 56.5 23.5T840-320v200q0 17-11.5 28.5T800-80H160Zm120-320h400v-160H280v160Zm-80 240h560v-160H200v160Zm80-240h400-400Zm-80 240h560-560Zm560-240H200h560Z"/></svg>`;
				case 'milestone-subscription-purchase':
					return `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#15803D" height="33" width="33" version="1.1" id="Capa_1" viewBox="0 0 495.099 495.099" xml:space="preserve">
								<g id="XMLID_73_">
									<g id="XMLID_75_">
										<path id="XMLID_76_" d="M203.82,1.475c-1.28,1.472-1.422,3.624-0.328,5.247l28.608,42.45    C129.497,57.098,48.389,142.944,48.389,247.546c0,59.917,26.716,113.6,68.714,150.149c3.524-4.587,7.842-8.597,13.274-11.282    l34.04-16.833c-38.96-26.623-64.605-71.382-64.605-122.034c0-75.819,57.44-138.424,131.083-146.733l-27.394,40.658    c-1.096,1.625-0.962,3.774,0.327,5.249c1.289,1.473,3.405,1.891,5.154,1.028c35.723-17.665,103.131-51.013,135.047-66.803    c5.734-2.837,5.722-11.012-0.013-13.846c-92.208-45.566-75.115-37.037-135.041-66.653C207.225-0.418,205.108,0.001,203.82,1.475z"/>
									</g>
									<path id="XMLID_74_" d="M446.71,247.546c0-59.758-26.582-113.314-68.403-149.856c-3.507,4.494-7.734,8.445-12.957,10.998   l-34.415,17.015c38.817,26.649,64.353,71.309,64.353,121.844c0,75.585-57.089,138.005-130.397,146.634l27.335-40.551   c1.096-1.624,0.954-3.773-0.326-5.248c-1.289-1.473-3.408-1.891-5.156-1.028c-51.816,25.61-17.87,8.829-141.178,69.776   c-1.472,0.727-2.401,2.225-2.401,3.867v0.007c0,1.65,0.929,3.139,2.401,3.858c0,0,95.723,47.322,141.169,69.794   c1.748,0.862,3.867,0.443,5.156-1.028c1.289-1.475,1.423-3.626,0.326-5.249l-28.615-42.475   C365.919,437.667,446.71,351.946,446.71,247.546z"/>
								</g>
							</svg>`
				case 'first-subscription-purchase':
					return `<svg xmlns="http://www.w3.org/2000/svg" height="33" fill="#15803D" viewBox="0 -960 960 960" width="33"><path d="M189.058-73.304q-30.994 0-53.374-22.38-22.38-22.38-22.38-53.374v-595.218q0-31.059 22.38-53.486 22.38-22.427 53.374-22.427h54.275v-29.688q0-15.554 10.795-26.266 10.794-10.712 26.294-10.712 15.783 0 26.619 10.712t10.836 26.266v29.688h324.246v-29.688q0-15.554 10.795-26.266 10.794-10.712 26.294-10.712 15.783 0 26.619 10.712t10.836 26.266v29.688h54.275q31.06 0 53.486 22.427 22.427 22.427 22.427 53.486v240.291q0 16-11.066 26.938-11.065 10.939-27.101 10.939t-26.891-10.939q-10.855-10.938-10.855-26.938v-62.682H189.058v417.609h260.537q16 0 26.938 10.972 10.938 10.972 10.938 27.022 0 16.05-10.938 26.905t-26.938 10.855H189.058ZM760 8.529q-63.442 0-113.703-34.304-50.261-34.305-73.536-89.152-5.877-11.877 2.619-23.573 8.497-11.695 22.759-11.695 16.441 0 30.492 9.177 14.05 9.178 24.398 21.938 16.486 26.899 44.66 42.71Q725.862-60.558 760-60.558q55.832 0 95.365-39.533 39.533-39.534 39.533-95.365 0-56.005-39.485-95.372-39.486-39.367-95.413-39.367-27.225 0-50.518 9.471-23.293 9.472-40.627 26.623h22.42q13.75 0 23.071 9.536 9.321 9.536 9.321 25.167 0 14.674-9.948 24.608-9.948 9.935-24.755 9.935h-99.616q-16 0-26.939-10.938-10.938-10.939-10.938-26.939v-99.775q0-14.738 10.333-24.641 10.334-9.903 27.638-9.903 13.34 0 22.228 8.933 8.888 8.934 8.888 22.343v21.5q26.92-25.203 62.801-40.185 35.88-14.982 76.641-14.982 84.821 0 144.404 59.585 59.582 59.584 59.582 144.409 0 84.586-59.582 144.281Q844.821 8.53 760 8.53ZM189.058-633.333h581.884v-110.943H189.058v110.943Zm0 0v-110.943 110.943Z"/></svg>`;
				default:
					return `<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33">
						<path
							d="M480-165q-17 0-33-7.5T419-194L113-560q-9-11-13.5-24T95-611q0-9 1.5-18.5T103-647l75-149q11-20 29.5-32t41.5-12h462q23 0 41.5 12t29.5 32l75 149q5 8 6.5 17.5T865-611q0 14-4.5 27T847-560L541-194q-12 14-28 21.5t-33 7.5Zm-95-475h190l-60-120h-70l-60 120Zm55 347v-267H218l222 267Zm80 0 222-267H520v267Zm144-347h106l-60-120H604l60 120Zm-474 0h106l60-120H250l-60 120Z" />
						</svg >`;
			}
		}
	}
};
</script>
