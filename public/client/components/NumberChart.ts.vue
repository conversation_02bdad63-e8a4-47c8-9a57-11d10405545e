<template>
	<div class="min-w-1/2 mx-auto bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md mb-2">
		<!-- Card Header -->
		<div class="px-4 pt-4 flex justify-between items-center">
			<div v-if="isLoading" class="w-24 h-6 bg-gray-200 animate-pulse rounded-lg"></div>
			<div v-else class="title">{{ title }}</div>

			<div class="w-10 h-10 flex items-center justify-center z-1000 max-w-50">
				<Tooltip bg="dark" size="md" position="left">
					<div v-if="tip" class="text-xs text-white">{{tip}}</div>
				</Tooltip>
			</div>
		</div>

		<div class="px-4 pb-6 flex justify-start items-left flex-col sm:items-center sm:flex-row">
			<div class="flex flex-col justify-start mb-2">
				<div v-if="isLoading" class="w-32 h-12 bg-gray-200 animate-pulse rounded-lg"></div>
				<span v-else class="number" :style="numberStyles">{{ number }}</span>
			</div>
			<div v-if="change" class="flex items-center sm:ml-4">
				<span v-if="!isLoading" class="change text-white text-xs font-bold px-3 py-1 rounded-full">
					{{ change }}
				</span>
				<div v-else class="w-16 h-6 bg-gray-200 animate-pulse rounded-full"></div>
			</div>
		</div>
	</div>
</template>

<script>
	import Tooltip from './Tooltip.ts.vue';

	export default {
		props: {
			number: { type: String, default: '' },
			change: { type: String, default: '' },
			title: { type: String, default: '' },
			numberStyles: { type: String, default: '' },
			tip: { type: String, default: '' },
			isLoading: { type: Boolean, default: false }
		},
		components: {
			Tooltip,
		}
	}
</script>

<style scoped>
	.row-1 {
		justify-content: space-between;
	}

	.title {
		color: #191919;
		font-family: Open Sans;
		font-size: 20px;
		font-style: normal;
		font-weight: 400;
		line-height: normal;
	}

	.tooltip {
		background: #E5E7EB;
	}

	.tooltip span {
		opacity: 0.5;
		color: rgb(75, 85, 99);
	}

	.row-2 {
		align-items: center;
	}

	.change {
		background:#15803D;
	}

	.change svg {
		stroke: white;
	}

	.change div {
		color:#F8F9FC;
		font-family: Inter;
		font-size: 14px;
		font-style: normal;
		font-weight: 600;
		line-height: normal;
		text-transform: uppercase;
	}

	.number {
		color: rgba(32, 32, 32, 0.80);
		font-family: Inter;
		font-size: 3rem;
		font-style: normal;
		font-weight: 600;
		line-height: normal;
		letter-spacing: -2.4px;
		text-transform: uppercase;
	}
</style>
