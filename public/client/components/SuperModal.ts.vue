<template>

	<div class="super-modal">
		<slot></slot>
	</div>
</template>

<script>

	import * as Utils from '../../client-old/utils/Utils';


	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		components: {
		},
		async mounted() {
		},
		data() {
			return {}
		}
	}
</script>
<style scoped>
.super-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 10000;

	display: flex;
	flex-direction: column;

	background: linear-gradient(144deg, #FAF8F5 20.89%, #E2E8F8 53.59%, #BCCAFD 88.28%);
}
</style>

