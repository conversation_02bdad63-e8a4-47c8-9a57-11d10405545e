<template>
    <Teleport to="body">
        <Transition name="modal-fade">
            <div v-if="modelValue" class="fixed inset-0 z-50 flex items-center justify-center">
                <!-- Backdrop -->
                <div class="absolute inset-0 bg-black bg-opacity-40" @click="$emit('update:modelValue', false)"></div>
                
                <!-- Modal -->
                <div class="relative bg-white rounded-xl shadow-lg max-w-md w-full mx-4 overflow-hidden z-10">
                    <!-- Header with gradient -->
                    <div class="extra-smooth-gradient-text px-6 pt-6 pb-3">
                        <h3 class="text-xl font-semibold">{{ title }}</h3>
                    </div>
                    
                    <!-- Content -->
                    <div class="px-6 py-4">
                        <p class="text-gray-700">{{ message }}</p>
                    </div>
                    
                    <!-- Actions -->
                    <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                        <!-- Cancel button -->
                        <button 
                            @click="$emit('update:modelValue', false)"
                            class="px-4 py-2 text-sm font-medium rounded-full bg-white border border-gray-200 hover:bg-gray-50 text-gray-700 transition-colors duration-150 shadow-sm">
                            {{ cancelText }}
                        </button>
                        
                        <!-- Confirm button -->
                        <button 
                            @click="confirm"
                            class="px-4 py-2 text-sm font-medium rounded-full bg-gradient-to-r from-[#6E41FF] to-[#8A4FFF] text-white hover:opacity-90 transition-colors duration-150 shadow-sm">
                            {{ confirmText }}
                        </button>
                    </div>
                </div>
            </div>
        </Transition>
    </Teleport>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
    name: 'ConfirmModal',
    props: {
        modelValue: {
            type: Boolean,
            required: true
        },
        title: {
            type: String,
            default: 'Confirm Action'
        },
        message: {
            type: String,
            default: 'Are you sure you want to proceed?'
        },
        confirmText: {
            type: String,
            default: 'Confirm'
        },
        cancelText: {
            type: String,
            default: 'Cancel'
        }
    },
    emits: ['update:modelValue', 'confirm'],
    setup(props, { emit }) {
        const confirm = () => {
            emit('confirm');
            emit('update:modelValue', false);
        };

        return {
            confirm
        };
    }
});
</script>

<style scoped>
.modal-fade-enter-active,
.modal-fade-leave-active {
    transition: opacity 0.2s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
    opacity: 0;
}

.extra-smooth-gradient-text {
    background: linear-gradient(90deg, #6E41FF 0%, #8A4FFF 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
}
</style>