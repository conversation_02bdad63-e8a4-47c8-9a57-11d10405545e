import { ref, computed } from 'vue';
import { URL_DOMAIN } from '../utils/utils';

export interface MessageQuota {
  dailyLimit: number;
  dailyUsed: number;
  premium: number;
  claimedRewards?: string[];
}

export interface PlanDetails {
  id: number;
  name: string;
  price: number;
  hasRevenueBasedPricing: boolean;
}

export interface PlanInfo {
  currentPlan: PlanDetails | null;
  upgradePlan: PlanDetails | null;
  shouldShowUpgrade: boolean;
  inEffect: boolean;
  startDate: string;
  daysLeft: number;
  endDate: string;
  hasActivePlan: boolean;
  hasPaidPlan: boolean;
}

// Reactive quota state shared across components
const quotaState = ref<MessageQuota | null>(null);
const planInfoState = ref<PlanInfo | null>(null);

export async function fetchMessageQuota(): Promise<MessageQuota | null> {
  const token = localStorage.getItem('token');
  if (!token) return null;
  const res = await fetch(`${URL_DOMAIN}/messages/quota`, {
    headers: { Authorization: `Bearer ${token}` },
  });
  if (!res.ok) return null;
  return await res.json();
}

export async function fetchPlanInfo(): Promise<PlanInfo | null> {
  const token = localStorage.getItem('token');
  if (!token) return null;
  const res = await fetch(`${URL_DOMAIN}/free-trial-info`, {
    headers: { Authorization: `Bearer ${token}` },
  });
  if (!res.ok) return null;
  return await res.json();
}

// Reactive quota service
export const useQuotaService = () => {
  const quota = computed(() => quotaState.value);
  const planInfo = computed(() => planInfoState.value);
  
  const canSendMessage = computed(() => {
    if (!quotaState.value) return true;
    return Math.max(0, quotaState.value.dailyLimit - quotaState.value.dailyUsed) + quotaState.value.premium > 0;
  });

  const refreshQuota = async () => {
    try {
      const newQuota = await fetchMessageQuota();
      quotaState.value = newQuota;
      return newQuota;
    } catch (error) {
      console.error('Error refreshing quota:', error);
      return null;
    }
  };

  const refreshPlanInfo = async () => {
    try {
      const newPlanInfo = await fetchPlanInfo();
      planInfoState.value = newPlanInfo;
      return newPlanInfo;
    } catch (error) {
      console.error('Error refreshing plan info:', error);
      return null;
    }
  };

  const addMessage = (count: number = 1) => {
    if (quotaState.value) {
      quotaState.value = {
        ...quotaState.value,
        dailyUsed: quotaState.value.dailyUsed + count
      };
    }
  };

  const addPremiumMessages = (count: number) => {
    if (quotaState.value) {
      quotaState.value = {
        ...quotaState.value,
        premium: quotaState.value.premium + count
      };
    }
  };

  const initializeQuota = async () => {
    if (!quotaState.value) {
      await refreshQuota();
      await refreshPlanInfo();
    }
  };

  return {
    quota,
    planInfo,
    canSendMessage,
    refreshQuota,
    refreshPlanInfo,
    addMessage,
    addPremiumMessages,
    initializeQuota
  };
};

// Global debug function for testing quota updates
if (typeof window !== 'undefined') {
  (window as any).testAddPremiumMessages = (count: number = 10) => {
    const service = useQuotaService();
    service.addPremiumMessages(count);
    console.log('Added', count, 'premium messages. Total available:', 
      Math.max(0, service.quota.value?.dailyLimit - service.quota.value?.dailyUsed || 0) + (service.quota.value?.premium || 0));
  };
}
