/**
 * Service for generating tool message content for chat displays
 */
export class ToolMessageService {
  /**
   * Get a random message for a specific tool type
   */
  static getRandomMessage(toolName: string): string {
    const genericMessages = [
      'Looked up data',
      'Called an AI subagent',
      'Processed your request',
      'Analyzed information',
      'Worked on that for you',
      'Fetched details',
      'Searched through data',
      'Gathered information'
    ];

    const toolSpecificMessages: { [key: string]: string[] } = {
      product_lookup: [
        'Looked up product details',
        'Searched product catalog',
        'Fetched product information',
        'Checked inventory data'
      ],
      image_lookup: [
        'Searched for images',
        'Looked through image library',
        'Found the perfect image',
        'Browsed visual assets'
      ],
      image_info: [
        'Analyzed image content',
        'Extracted text from image',
        'Processed visual information',
        'Read image details',
        'Analyzed text content',
        'Scanned image elements'
      ],
      customer_lookup: [
        'Looked up customer data',
        'Searched customer records',
        'Fetched customer insights',
        'Analyzed customer behavior'
      ],
      data_lookup: [
        'Searched database',
        'Looked up data records',
        'Fetched information',
        'Queried data sources'
      ]
    };

    const toolMessages = toolSpecificMessages[toolName] || genericMessages;
    return toolMessages[Math.floor(Math.random() * toolMessages.length)];
  }
}