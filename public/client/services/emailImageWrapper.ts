/**
 * Service for wrapping email HTML with interactive image editing capabilities
 */

interface ImageClickEvent {
  type: 'image-clicked';
  imageUrl: string;
  imageAlt: string;
}

export class EmailImageWrapper {
  /**
   * Wraps email HTML with interactive functionality for image editing
   */
  static wrapEmailHtml(originalHtml: string): string {
    if (!originalHtml || originalHtml.trim() === '') {
      return originalHtml;
    }

    const styles = this.extractOriginalStyles(originalHtml);
    const bodyContent = this.extractBodyContent(originalHtml);
    const script = this.generateInteractiveScript();
    const hoverStyles = this.generateHoverStyles();

    return `
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          ${styles}
          <style>
            ${hoverStyles}
          </style>
        </head>
        <body>
          ${bodyContent}
          <script>
            ${script}
          </script>
        </body>
      </html>
    `;
  }

  /**
   * Extract original styles from the HTML
   */
  private static extractOriginalStyles(html: string): string {
    const styleMatches = html.match(/<style[^>]*>([\s\S]*?)<\/style>/gi);
    const linkMatches = html.match(/<link[^>]*rel=['"]stylesheet['"][^>]*>/gi);
    
    let styles = '';
    
    if (styleMatches) {
      styles += styleMatches.join('\n');
    }
    
    if (linkMatches) {
      styles += linkMatches.join('\n');
    }

    // Also check for inline styles in the head
    const headMatch = html.match(/<head[^>]*>([\s\S]*?)<\/head>/i);
    if (headMatch && headMatch[1]) {
      const headContent = headMatch[1];
      const additionalStyles = headContent.match(/<style[^>]*>([\s\S]*?)<\/style>/gi);
      const additionalLinks = headContent.match(/<link[^>]*>/gi);
      
      if (additionalStyles) {
        styles += additionalStyles.join('\n');
      }
      
      if (additionalLinks) {
        styles += additionalLinks.join('\n');
      }
    }

    return styles;
  }

  /**
   * Extract body content from the HTML
   */
  private static extractBodyContent(html: string): string {
    // Try to extract body content first
    const bodyMatch = html.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (bodyMatch && bodyMatch[1]) {
      return bodyMatch[1];
    }

    // If no body tags, try to extract everything between html tags
    const htmlMatch = html.match(/<html[^>]*>([\s\S]*?)<\/html>/i);
    if (htmlMatch && htmlMatch[1]) {
      // Remove head content if present
      const withoutHead = htmlMatch[1].replace(/<head[^>]*>[\s\S]*?<\/head>/i, '');
      return withoutHead;
    }

    // Fallback: return the original HTML (might be just body content)
    return html;
  }

  /**
   * Generate CSS styles for image hover effects
   */
  private static generateHoverStyles(): string {
    return `
      /* Image hover effects for editing */
      img {
        transition: all 0.3s ease;
        position: relative;
      }
      
      /* Create a wrapper div for each image to contain the overlay */
      .image-edit-wrapper {
        position: relative;
        display: inline-block;
        overflow: hidden;
        line-height: 0;
      }
      
      .image-edit-wrapper img {
        display: block;
        transition: all 0.3s ease;
        vertical-align: top;
      }
      
      /* Sci-fi overlay that appears on hover */
      .image-edit-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at center, rgba(0, 255, 255, 0.1), rgba(107, 70, 193, 0.8));
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.4s ease;
        border-radius: 4px;
        cursor: pointer;
        backdrop-filter: blur(2px);
      }
      
      /* Sci-fi edit icon container */
      .edit-icon-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        border: 2px solid rgba(255, 255, 255, 0.9);
        border-radius: 12px;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(4px);
        box-shadow: 
          0 0 15px rgba(0, 255, 255, 0.8),
          inset 0 0 15px rgba(255, 255, 255, 0.1);
      }
      
      /* Clear edit icon */
      .edit-icon {
        width: 24px;
        height: 24px;
        fill: #FFFFFF;
        filter: drop-shadow(0 0 4px rgba(0, 255, 255, 0.6));
        animation: iconFloat 3s ease-in-out infinite;
      }
      
      /* Hover effects */
      .image-edit-wrapper:hover .image-edit-overlay {
        opacity: 1;
      }
      
      .image-edit-wrapper:hover img {
        transform: scale(1.03);
        filter: brightness(1.2) saturate(1.1);
        border-radius: 4px;
        box-shadow: 
          0 0 30px rgba(0, 255, 255, 0.4),
          0 8px 25px rgba(107, 70, 193, 0.3);
      }
      
      /* Sci-fi pulsing animation */
      @keyframes sciFiPulse {
        0%, 100% {
          border-color: rgba(255, 255, 255, 0.9);
          box-shadow: 
            0 0 15px rgba(0, 255, 255, 0.8),
            inset 0 0 15px rgba(255, 255, 255, 0.1);
          transform: scale(1);
        }
        50% {
          border-color: rgba(0, 255, 255, 1);
          box-shadow: 
            0 0 20px rgba(0, 255, 255, 1),
            0 0 30px rgba(255, 255, 255, 0.5),
            inset 0 0 20px rgba(0, 255, 255, 0.2);
          transform: scale(1.05);
        }
      }
      
      /* Icon floating animation */
      @keyframes iconFloat {
        0%, 100% {
          transform: translateY(0px);
          filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.8));
        }
        50% {
          transform: translateY(-4px);
          filter: drop-shadow(0 0 12px rgba(0, 255, 255, 1));
        }
      }
      
      /* Scanning line effect */
      .image-edit-overlay::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 30%;
        height: 100%;
        background: linear-gradient(90deg, 
          transparent, 
          rgba(255, 255, 255, 0.3), 
          transparent
        );
        animation: scanLine 4s infinite;
        border-radius: 4px;
      }
      
      .image-edit-wrapper:hover .image-edit-overlay::before {
        animation-duration: 3s;
      }
      
      @keyframes scanLine {
        0% {
          left: -30%;
        }
        100% {
          left: 100%;
        }
      }
      
      /* Holographic grid overlay */
      .image-edit-overlay::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: 
          linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
        background-size: 20px 20px;
        opacity: 0;
        transition: opacity 0.3s ease;
        border-radius: 4px;
        pointer-events: none;
      }
      
      .image-edit-wrapper:hover .image-edit-overlay::after {
        opacity: 0.3;
      }
    `;
  }

  /**
   * Generate JavaScript for interactive functionality
   */
  private static generateInteractiveScript(): string {
    return `
      (function() {
        // Clear edit icon SVG - pencil with square
        const editIconSVG = '<div class="edit-icon-container"><svg class="edit-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25z"/><path d="M2 22h20v2H2z" opacity="0.3"/></svg></div>';

        // Wait for DOM to be fully loaded
        function initImageInteractivity() {
          const images = document.querySelectorAll('img');
          
          images.forEach(function(img) {
            // Skip if already wrapped
            if (img.closest('.image-edit-wrapper')) {
              return;
            }
            
            // Store original image styles
            const originalPosition = window.getComputedStyle(img).position;
            const originalDisplay = window.getComputedStyle(img).display;
            
            // Create wrapper div
            const wrapper = document.createElement('div');
            wrapper.className = 'image-edit-wrapper';
            
            // Copy all image dimensions and positioning to wrapper
            const imgRect = img.getBoundingClientRect();
            const imgStyles = window.getComputedStyle(img);
            
            // Apply styles to wrapper to match image
            wrapper.style.width = img.offsetWidth + 'px';
            wrapper.style.height = img.offsetHeight + 'px';
            wrapper.style.maxWidth = imgStyles.maxWidth;
            wrapper.style.maxHeight = imgStyles.maxHeight;
            
            // Create overlay div that covers the entire image
            const overlay = document.createElement('div');
            overlay.className = 'image-edit-overlay';
            overlay.innerHTML = editIconSVG;
            
            // Insert wrapper before the image
            img.parentNode.insertBefore(wrapper, img);
            
            // Move image into wrapper
            wrapper.appendChild(img);
            
            // Ensure image fills the wrapper
            img.style.width = '100%';
            img.style.height = '100%';
            img.style.objectFit = 'cover';
            
            // Add overlay to wrapper
            wrapper.appendChild(overlay);
            
            // Remove any existing click listeners
            img.removeEventListener('click', handleImageClick);
            overlay.removeEventListener('click', handleOverlayClick);
            
            // Add click listeners to both image and overlay
            img.addEventListener('click', handleImageClick);
            overlay.addEventListener('click', handleOverlayClick);
            
            // Add error handling for broken images
            img.addEventListener('error', function() {
              console.warn('Failed to load image:', img.src);
            });
          });
        }
        
        function handleImageClick(event) {
          handleClick(event, event.target);
        }
        
        function handleOverlayClick(event) {
          handleClick(event, event.target.closest('.image-edit-wrapper').querySelector('img'));
        }
        
        function handleClick(event, img) {
          event.preventDefault();
          event.stopPropagation();
          
          const imageUrl = img.src || img.getAttribute('src') || '';
          const imageAlt = img.alt || img.getAttribute('alt') || '';
          
          // Send message to parent window
          if (window.parent && window.parent !== window) {
            window.parent.postMessage({
              type: 'image-clicked',
              imageUrl: imageUrl,
              imageAlt: imageAlt
            }, '*');
          }
        }
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', initImageInteractivity);
        } else {
          initImageInteractivity();
        }
        
        // Re-initialize if new images are added dynamically
        const observer = new MutationObserver(function(mutations) {
          let shouldReinit = false;
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  if (node.tagName === 'IMG' || node.querySelector('img')) {
                    shouldReinit = true;
                  }
                }
              });
            }
          });
          
          if (shouldReinit) {
            setTimeout(initImageInteractivity, 100);
          }
        });
        
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      })();
    `;
  }

  /**
   * Check if HTML contains images
   */
  static hasImages(html: string): boolean {
    const imgRegex = /<img[^>]+>/gi;
    return imgRegex.test(html);
  }

  /**
   * Extract all image URLs from HTML
   */
  static extractImageUrls(html: string): string[] {
    const imgRegex = /<img[^>]+src=['"]([^'"]+)['"][^>]*>/gi;
    const urls: string[] = [];
    let match: RegExpExecArray | null;

    while ((match = imgRegex.exec(html)) !== null) {
      urls.push(match[1]);
    }

    return urls;
  }
}

export type { ImageClickEvent };