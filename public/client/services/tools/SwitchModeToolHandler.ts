import { BaseTool<PERSON>and<PERSON> } from '../toolHandlerService';
import { Emitter } from 'mitt';

interface SwitchModeData {
  message: string;
  mode: string;
  summary: string;
}

export class SwitchModeToolHandler extends BaseToolHandler {
  public readonly tag = 'switch_mode'; // Unique tag for this tool

  constructor(emitter: Emitter<any>) {
    super(emitter);
    this.register(); // Explicitly register after tag is initialized
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId;
    this.streamingContent = initialContent;
    console.log(`${this.tag} tool started. PlaceholderId: ${this.placeholderId}`);
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) return;

    // Log chunk contents for debugging
    console.log(`${this.tag} tool received chunk: "${contentChunk}"`);

    // Accumulate content
    this.streamingContent += contentChunk;

    // Log total accumulated content length
    console.log(`${this.tag} tool: Accumulated content length: ${this.streamingContent.length}`);
  }

  onEnd(): void {
    if (!this.placeholderId) {
      console.warn(`${this.tag} handler received end signal but was not started or already ended.`);
      return;
    }

    // Get the raw content and make a simplified version for debugging
    const rawContent = this.streamingContent.trim();
    const debugContent = rawContent.length > 100 ?
      rawContent.substring(0, 50) + '...' + rawContent.substring(rawContent.length - 50) :
      rawContent;

    console.log(`${this.tag} tool: Processing raw content (${rawContent.length} chars):`, debugContent);

    // Initialize switch mode data
    let switchModeData: SwitchModeData | null = null;

    // Enhanced extraction logic to parse JSON content
    try {
      // First try to parse the content as JSON directly
      try {
        // Clean up the content to make it more JSON-friendly
        let cleanedContent = rawContent.trim();

        // Check if it starts with { and ends with } for an object
        if (cleanedContent.startsWith('{') && cleanedContent.endsWith('}')) {
          console.log(`${this.tag} tool: Content appears to be a JSON object, attempting to parse`);
          const parsedData = JSON.parse(cleanedContent);

          if (typeof parsedData === 'object' && parsedData !== null) {
            // Validate required fields
            const message = parsedData.message || '';
            const mode = parsedData.mode || '';
            const summary = parsedData.summary || '';

            if (message && mode) {
              switchModeData = {
                message,
                mode,
                summary
              };
              console.log(`${this.tag} tool: Successfully parsed switch mode data:`, switchModeData);
            } else {
              console.error(`${this.tag} tool: Missing required fields. Message: "${message}", Mode: "${mode}"`);
            }
          }
        }
      } catch (directJsonError) {
        console.log(`${this.tag} tool: Direct JSON parsing failed, trying regex approaches`, directJsonError);
      }

      // If direct JSON parsing didn't work, try regex approaches
      if (!switchModeData) {
        // Look for JSON structure in the content using regex
        const jsonRegex = /\{[^{}]*"message"\s*:\s*"[^"]*"[^{}]*"mode"\s*:\s*"[^"]*"[^{}]*\}/g;
        const matches = rawContent.match(jsonRegex);

        if (matches && matches.length > 0) {
          console.log(`${this.tag} tool: Found potential JSON via regex`);
          
          for (const match of matches) {
            try {
              console.log(`${this.tag} tool: Attempting to parse regex match:`, match);
              const parsedData = JSON.parse(match);
              
              const message = parsedData.message || '';
              const mode = parsedData.mode || '';
              const summary = parsedData.summary || '';

              if (message && mode) {
                switchModeData = {
                  message,
                  mode,
                  summary
                };
                console.log(`${this.tag} tool: Successfully parsed switch mode data from regex:`, switchModeData);
                break;
              }
            } catch (parseError) {
              console.log(`${this.tag} tool: Failed to parse regex match:`, parseError);
            }
          }
        }
      }

      // If still no data found, try extracting individual fields
      if (!switchModeData) {
        const messageMatch = rawContent.match(/"message"\s*:\s*"([^"]*)"/);
        const modeMatch = rawContent.match(/"mode"\s*:\s*"([^"]*)"/);
        const summaryMatch = rawContent.match(/"summary"\s*:\s*"([^"]*)"/);

        if (messageMatch && modeMatch) {
          switchModeData = {
            message: messageMatch[1],
            mode: modeMatch[1],
            summary: summaryMatch ? summaryMatch[1] : ''
          };
          console.log(`${this.tag} tool: Extracted switch mode data from individual fields:`, switchModeData);
        }
      }
    } catch (extractionError) {
      console.error(`${this.tag} tool: Error extracting switch mode data:`, extractionError);
    }

    // If we found valid switch mode data, emit the navigation event
    if (switchModeData && switchModeData.message && switchModeData.mode) {
      console.log(`${this.tag} tool: Emitting switch mode navigation for mode: ${switchModeData.mode}`);

      // Emit the switch mode event with navigation data
      this.emitter.emit('chat:switch-mode', {
        type: 'switch_mode',
        sender: 'ai',
        switchModeData: switchModeData,
        timestamp: new Date(),
        placeholderId: this.placeholderId,
      });

      // Also emit as a text message to show what's happening
      this.emitter.emit('chat:update-segment', {
        type: 'text',
        sender: 'ai',
        content: `Switching to ${switchModeData.mode} mode: ${switchModeData.message}`,
        timestamp: new Date(),
      });

      console.log(`${this.tag} tool: Successfully emitted switch mode events`);
    } else {
      console.error(`${this.tag} tool: Could not extract valid switch mode data from content`);

      // Emit fallback error message
      this.emitter.emit('chat:update-segment', {
        type: 'text',
        sender: 'ai',
        content: `Error: Failed to parse switch mode data. Expected format: {"message": "...", "mode": "...", "summary": "..."}`,
        timestamp: new Date(),
      });

      // Also emit the raw content as a text message for debugging
      this.emitter.emit('chat:update-segment', {
        type: 'text',
        sender: 'ai',
        content: `Debug: Raw switch_mode content: ${rawContent}`,
        timestamp: new Date(),
      });
    }

    // Clean up and complete
    this.emitter.emit(`${this.tag}:complete`, {
      placeholderId: this.placeholderId,
      finalContent: switchModeData,
      success: switchModeData !== null
    });

    // Reset state
    this.placeholderId = null;
    this.streamingContent = '';
  }
}