import { <PERSON><PERSON>ool<PERSON>and<PERSON> } from '../toolHandlerService';
import { Emitter } from 'mitt';
import { getOrganizationSetting, updateOrganizationSetting } from '../organization-settings.js';

interface MemoryPayload {
  category: string;
  info: string;
}

interface StoredMemory {
  type: string;
  content: string;
  timestamp: string;
}

export class MemoryToolHandler extends BaseToolHandler {
  public readonly tag = 'memory';

  constructor(emitter: Emitter<any>) {
    super(emitter);
    this.register();
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId;
    this.streamingContent = initialContent;
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) return;
    this.streamingContent += contentChunk;
  }

  private async saveMemoryToStorage(memory: MemoryPayload): Promise<boolean> {
    try {
      // Get existing memories
      const existingMemoriesString = await getOrganizationSetting('brandMemories');
      let existingMemories: StoredMemory[] = [];

      if (existingMemoriesString) {
        try {
          existingMemories = JSON.parse(existingMemoriesString);
        } catch (parseError) {
          console.warn('Failed to parse existing memories, starting fresh:', parseError);
          existingMemories = [];
        }
      }

      // Normalize content for duplicate detection (trim whitespace, ignore case)
      const normalizedNewContent = memory.info.trim().toLowerCase();

      // Check for duplicates - exact match on normalized content
      const isDuplicate = existingMemories.some(existing =>
        existing.content.trim().toLowerCase() === normalizedNewContent
      );

      if (isDuplicate) {
        console.log('MemoryToolHandler: Skipping duplicate memory:', memory.info);
        return false; // Indicate duplicate was skipped
      }

      // Map category to our new simplified types
      const typeMapping: Record<string, string> = {
        'info': 'info',
        'information': 'info',
        'brand info': 'info',
        'general': 'info',
        'about': 'info',
        'company': 'info',
        'text': 'text',
        'content': 'text',
        'messaging': 'text',
        'brand voice': 'preferences',
        'brand': 'preferences',
        'voice': 'preferences',
        'preference': 'preferences',
        'preferences': 'preferences',
        'style': 'preferences',
        'promotion': 'promotion',
        'promotional': 'promotion',
        'campaign': 'promotion',
        'offer': 'promotion',
        'discount': 'promotion',
        'email': 'email',
        'email template': 'email',
        'email design': 'email',
        'subject line': 'email',
        'brief': 'brief',
        'campaign brief': 'brief',
        'content brief': 'brief',
        'strategy': 'brief'
      };

      // Determine memory type from category
      const memoryType = typeMapping[memory.category.toLowerCase()] || 'info';

      // Create new memory object
      const newMemory: StoredMemory = {
        type: memoryType,
        content: memory.info.trim(),
        timestamp: new Date().toISOString()
      };

      // Add to existing memories (most recent first)
      existingMemories.unshift(newMemory);

      // Limit to last 50 memories to prevent bloat
      const MAX_MEMORIES = 50;
      if (existingMemories.length > MAX_MEMORIES) {
        existingMemories = existingMemories.slice(0, MAX_MEMORIES);
      }

      // Save back to organization settings
      await updateOrganizationSetting('brandMemories', JSON.stringify(existingMemories));
      console.log('MemoryToolHandler: Successfully saved memory to storage:', newMemory);
      return true; // Indicate successful save
    } catch (error) {
      console.error('MemoryToolHandler: Failed to save memory to storage:', error);
      return false; // Indicate save failed
    }
  }

  private fixInvalidJson(jsonString: string): string {
    try {
      // First try to parse as-is
      JSON.parse(jsonString);
      return jsonString;
    } catch (e) {
      console.log('MemoryToolHandler: Attempting to fix invalid JSON format');

      // Fix common JSON issues:
      // 1. Unquoted property names
      let fixed = jsonString
        .replace(/(\w+):/g, '"$1":')  // Add quotes around property names
        .replace(/,\s*}/g, '}')       // Remove trailing commas
        .replace(/,\s*]/g, ']');      // Remove trailing commas in arrays

      try {
        JSON.parse(fixed);
        console.log('MemoryToolHandler: Successfully fixed JSON format');
        return fixed;
      } catch (e2) {
        console.log('MemoryToolHandler: Could not fix JSON, attempting manual parsing');

        // Manual parsing as last resort
        const categoryMatch = jsonString.match(/category:\s*["']([^"']+)["']/i);
        const infoMatch = jsonString.match(/info:\s*["']([^"']+)["']/i);

        if (categoryMatch && infoMatch) {
          const manualJson = {
            category: categoryMatch[1],
            info: infoMatch[1]
          };
          console.log('MemoryToolHandler: Manual parsing successful:', manualJson);
          return JSON.stringify(manualJson);
        }

        // If all else fails, return original
        throw new Error('Could not parse or fix JSON');
      }
    }
  }

  async onEnd(): Promise<void> {
    if (!this.placeholderId) return;
    let rawContent = this.streamingContent.trim();
    
    // Strip <memory> tags if they exist in the content
    rawContent = rawContent.replace(/^<memory>\s*/, '').replace(/\s*<\/memory>$/, '');
    
    try {
      // Try to fix and parse the JSON
	  console.log('MemoryToolHandler received raw content:', rawContent);
      const fixedJson = this.fixInvalidJson(rawContent);
      const memory: MemoryPayload = JSON.parse(fixedJson);
      console.log('MemoryToolHandler received memory:', memory);

      // Validate that we have the required fields
      if (!memory.category || !memory.info) {
        throw new Error('Missing required fields: category and info');
      }

      // Try to save to storage
      const saved = await this.saveMemoryToStorage(memory);

      if (saved) {
        // Emit success message for UI display
        this.emitter.emit('chat:update-segment', {
          type: 'memory',
          sender: 'ai',
          memory,
          timestamp: new Date(),
          placeholderId: this.placeholderId,
        });
      } else {
        // Memory was a duplicate or failed to save
        this.emitter.emit('chat:update-segment', {
          type: 'text',
          sender: 'ai',
          content: 'Memory already exists in your brand knowledge base.',
          timestamp: new Date(),
          placeholderId: this.placeholderId,
        });
      }
    } catch (e) {
      console.error('MemoryToolHandler failed to parse content:', e);
      console.error('Raw content was:', rawContent);
      this.emitter.emit('chat:update-segment', {
        type: 'text',
        sender: 'ai',
        content: `[Error parsing memory: ${e.message}]`
      });
    }
    this.emitter.emit(`${this.tag}:complete`, { placeholderId: this.placeholderId });
    this.placeholderId = null;
    this.streamingContent = '';
  }
}
