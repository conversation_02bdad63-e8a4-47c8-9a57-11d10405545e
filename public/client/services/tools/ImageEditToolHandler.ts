import { BaseTool<PERSON>andler } from '../toolHandlerService';
import { Emitter } from 'mitt';

export class ImageEditToolHandler extends BaseToolHandler {
  public readonly tag = 'image_edit'; // Unique tag for this tool

  constructor(emitter: Emitter<any>) {
    super(emitter);
    this.register(); // Explicitly register after tag is initialized
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId;
    this.streamingContent = initialContent;
    // No initial chat segment is created by this handler directly.
    // The segment will be created when onEnd is called with the edited image data.
    console.log(`${this.tag} tool started. PlaceholderId: ${this.placeholderId}`);
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) return;
    // Image edit content is typically not streamed character by character.
    // It's usually a single block of JSON containing the result.
    // We'll accumulate it here and process fully in onEnd.
    this.streamingContent += contentChunk;
    console.log(`${this.tag} tool received content chunk. Current accumulated: ${this.streamingContent.substring(0,50)}...`);
  }

  onEnd(): void {
    if (!this.placeholderId) {
      console.warn(`${this.tag} handler received end signal but was not started or already ended.`);
      return;
    }

    let editedImageUrl = '';
    let originalUrl = '';
    let appliedText = '';
    const rawContent = this.streamingContent.trim();

    try {
      if (rawContent.startsWith('{') && rawContent.endsWith('}')) {
        const editResult = JSON.parse(rawContent);
        
        if (editResult.status === 'success' && editResult.edited_image_urls && editResult.edited_image_urls.length > 0) {
          const editedImageUrls = editResult.edited_image_urls;
          originalUrl = editResult.original_url || '';
          appliedText = editResult.applied_text || '';
          const variationsCount = editResult.variations_count || editedImageUrls.length;
          
          // Emit a success message with multiple edited images
          if (editedImageUrls.length === 1) {
            // Single image - use regular image format
            this.emitter.emit('chat:update-segment', {
              type: 'image',
              sender: 'ai',
              imageUrl: editedImageUrls[0],
              imageName: `Edited Image: ${appliedText}`,
              timestamp: new Date(),
            });
          } else {
            // Multiple images - use multiimage format
            this.emitter.emit('chat:update-segment', {
              type: 'multiimage',
              sender: 'ai',
              imageUrls: editedImageUrls,
              imageName: `${variationsCount} Edited Variations: ${appliedText}`,
              timestamp: new Date(),
            });
          }
          
          console.log(`${this.tag} tool ended. Successfully edited image with text: ${appliedText}`);
        } else {
          // Handle error case
          const errorMessage = editResult.message || 'Failed to edit image';
          this.emitter.emit('chat:update-segment', {
            type: 'text',
            sender: 'ai',
            content: `[Error: ${errorMessage}]`,
            timestamp: new Date(),
          });
          
          console.error(`${this.tag} tool: Error in response:`, editResult);
        }
      } else {
        // Invalid response format
        console.error(`${this.tag} tool: Invalid response format:`, rawContent);
        this.emitter.emit('chat:update-segment', {
          type: 'text',
          sender: 'ai',
          content: `[Error: Invalid response format from image edit tool]`,
          timestamp: new Date(),
        });
      }
    } catch (e) {
      console.error(`${this.tag} tool: Error parsing content:`, e, "Raw content:", rawContent);
      this.emitter.emit('chat:update-segment', {
        type: 'text',
        sender: 'ai',
        content: `[Error: Failed to process image edit result]`,
        timestamp: new Date(),
      });
    }

    this.emitter.emit(`${this.tag}:complete`, { 
      placeholderId: this.placeholderId, 
      finalContent: {
        editedImageUrls: editedImageUrl ? [editedImageUrl] : [], // Maintain backward compatibility
        originalUrl,
        appliedText
      } 
    });
    
    this.placeholderId = null;
    this.streamingContent = '';
  }
}