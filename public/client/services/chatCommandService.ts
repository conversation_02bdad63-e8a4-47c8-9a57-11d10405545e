import { URL_DOMAIN } from '../utils/utils';

export interface Command {
  name: string;
  description: string;
  execute: (args?: any) => Promise<any>;
}

const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};

const commands: Command[] = [
  {
    name: '/clear',
    description: 'Clear the chat history and start a new conversation.',
    execute: async (args: { 
      campaignId: string; 
      conversationId?: string | number; 
      taskId?: number; 
      name?: string; 
      promptTemplateId?: number; 
    }) => {
      if (!args.campaignId) {
        throw new Error('campaignId is required to clear the chat.');
      }

      const response = await fetch(`${URL_DOMAIN}/chat/conversations/new`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ 
          campaignId: args.campaignId,
          oldConversationId: args.conversationId,
          taskId: args.taskId,
          name: args.name,
          promptTemplateId: args.promptTemplateId,
          clearOnly: true, // Don't create conversation immediately
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to clear conversation');
      }

      return await response.json();
    },
  },
  {
    name: '/compress',
    description: 'Condense this conversation, can help with quality output.',
    execute: async (args: { conversationId: string }) => {
      if (!args.conversationId) {
        throw new Error('conversationId is required to compress the chat.');
      }

      const response = await fetch(`${URL_DOMAIN}/chat/conversations/${args.conversationId}/compress`, {
        method: 'POST',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to compress conversation');
      }

      return await response.json();
    },
  },
];

export const chatCommandService = {
  getCommands: () => commands,
  executeCommand: async (commandName: string, args?: any) => {
    const command = commands.find(c => c.name === commandName);
    if (!command) {
      throw new Error(`Unknown command: ${commandName}`);
    }
    return await command.execute(args);
  },
};
