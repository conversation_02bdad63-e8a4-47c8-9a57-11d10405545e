// stores/sessionStore.js
import {defineStore} from 'pinia';
import * as Utils from '../utils/utils.js';

export const useSessionStore = defineStore('session', {
	state: () => ({
		loggedIn: false,
		sessionInterval: null,
		initialized: false,
	}),
        actions: {
                async checkSession() {
                        const token = localStorage.getItem('token');
                        console.log('checking session', token);
                        if (token) {
                                // Check if token is in JWT format (has 3 parts separated by dots)
                                const tokenParts = token.split('.');
                                if (tokenParts.length === 3) {
                                        try {
                                                const tokenData = JSON.parse(atob(tokenParts[1]));
                                                const exp = tokenData.exp;
                                                if (exp && exp * 1000 < Date.now()) {
                                                        localStorage.removeItem('token');
                                                        this.loggedIn = false;
                                                        this.initialized = true;
                                                        console.log('JWT token expired');
                                                        return;
                                                }
                                        } catch (error) {
                                                console.warn('Failed to parse JWT token:', error);
                                                // Continue to API validation if JWT parsing fails
                                        }
                                }

                                try {
                                        const response = await fetch(`${Utils.URL_DOMAIN}/users/who-am-i`, {
                                                method: 'GET',
                                                headers: {
                                                        'Authorization': `Bearer ${token}`,
                                                        'Content-Type': 'application/json',
                                                        'ngrok-skip-browser-warning': 'true'
                                                }
                                        });

                                        if (!response.ok) {
                                                localStorage.removeItem('token');
                                                this.loggedIn = false;
                                        } else {
                                                this.loggedIn = true;
                                        }
                                } catch (err) {
                                        console.error('Token validation failed:', err);
                                        localStorage.removeItem('token');
                                        this.loggedIn = false;
                                }
                        } else {
                                this.loggedIn = false;
                        }

                        this.initialized = true;
                },
                startSessionCheck() {
                        this.sessionInterval = setInterval(async () => {
                                await this.checkSession();
                        }, 300000);
                },
		stopSessionCheck() {
			clearInterval(this.sessionInterval);
		},
		logIn() {
			this.loggedIn = true;
			this.initialized = true;
			this.stopSessionCheck();
			this.startSessionCheck();
		},
		logOut() {
			this.loggedIn = false;
			this.initialized = true;
			this.stopSessionCheck();
		},
	},
	getters: {
		isLoggedIn() {
			return this.loggedIn;
		},
		isInitialized() {
			return this.initialized;
		}
	},
});
