<template>
	<div class="w-[100%] lg:w-auto mb-8">
		<AccordionGroup
			:accordions="accordions"
			initialOpenAccordionKey="program"
			@open="handleAccordionOpen">
			<template v-slot:program>
				<div
				>
					<div class="ml-4 mb-8 text-black text-sm font-normal font-['Inter']">The referral program is what a customer
						or guest would see in the Loyalty Panel. <LearnMoreText text="What's a Loyalty Panel?"
							url="https://docs.raleon.io/docs/the-loyalty-panel"></LearnMoreText>
					</div>
					<div class="mx-4 mb-4">
						<div class="mt-2">
							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Referral
								title</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">What a customer sees on the
								launcher, as shown in the preview above. Be short and succinct. You have a
								max of 40 characters.</div>
							<div
								class="mb-2 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40" v-model="branding.referrals.title" />
								</div>
							</div>


							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Referral
								call to action</div>
							<div class="mb-3 text-black text-sm font-normal font-['Inter']">This can be a little longer description of why a person should refer friends!</div>
							<div
								class="w-full mb-8 h-28 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-start gap-2 inline-flex">
								<div class="w-full h-full self-stretch justify-start items-center gap-2 flex">
									<textarea
										class="no-focus-outline border-none w-full h-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal border-none p-0 resize-none focus:outline-none"
										v-model="branding.referrals.callToAction"
										@input="branding.referrals.callToAction = $event.target.value.replace(/\n/g, '')"></textarea>
								</div>
							</div>


							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Referral
								Text Color</div>
							<div class="mt-4">
								<LvColorPicker
									class="mx-2 md:mx-6 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
									:key="'background-launcher-styling-textColor-' + brandingUpdateCounter"
									v-model="computedReferralTextColor" :clearable="false" :value="computedReferralTextColor"
									:bottomBar="false" />
							</div>

							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Referral
								Button Text Color</div>
							<div class="mt-4">
								<LvColorPicker
									class="mx-2 md:mx-6 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
									:key="'background-launcher-styling-referralButtonTextColor-' + brandingUpdateCounter"
									v-model="computedReferralButtonTextColor" :clearable="false" :value="computedReferralButtonTextColor"
									:bottomBar="false" />
							</div>

							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Referral
								Button Background Color</div>
							<div class="mt-4">
								<LvColorPicker
									class="mx-2 md:mx-6 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
									:key="'background-launcher-styling-referralButtonBackgroundColor-' + brandingUpdateCounter"
									v-model="computedReferralButtonBackgroundColor" :clearable="false" :value="computedReferralButtonBackgroundColor"
									:bottomBar="false" />
							</div>

							<div class="mt-4  text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
								Referral Social Link Background Color</div>
							<div class="mt-4">
								<LvColorPicker
									class="mx-2 md:mx-6 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
									:key="'background-launcher-styling-backgroundColor-' + brandingUpdateCounter"
									v-model="computedReferralFillBackgroundColor" :clearable="false"
									:value="computedReferralFillBackgroundColor" :bottomBar="false" />
							</div>

							<div class="mt-4  text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
								Referral Social Link Fill Color</div>
							<div class="mt-4">
								<LvColorPicker
									class="mx-2 md:mx-6 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
									:key="'background-launcher-styling-socialFillColor-' + brandingUpdateCounter"
									v-model="computedReferralSocialLinkFillColor" :clearable="false"
									:value="computedReferralSocialLinkFillColor" :bottomBar="false" />
							</div>
						</div>
					</div>

				</div>
			</template>
			<template v-slot:sharing>
				<div>
					<div class="ml-4 mb-8 text-black text-sm font-normal font-['Inter']">Customize the link sharing experience
						by turning share targets on and off, and customizing the default share text.</div>
						<div class="ml-4 mb-8 text-black text-sm font-['Inter']">
							You can enable up to 6 share targets.
							{{ enabledShareTargetsCount }} / 6 share targets are currently enabled.
							<div
								v-if="enabledShareTargetsCount >= 6"
								:class="{ 'font-bold': enabledShareTargetsCount >= 6 }">
								You must disable at least one share target to enable more.
							</div>
						</div>
						<div class="ml-4 mb-8 text-black text-sm font-normal font-['Inter']">For share targets that support pre-populating share text, you can customize the link share text by using the <pre>{ REFERRAL_LINK }</pre> token to control where the link goes in the text</div>
					<div class="mx-4 mb-4">

						<div class="mt-8" v-if="branding?.referrals?.shareSettings?.facebook">
							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Facebook</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Enable Facebook sharing</div>
							<div
								class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="toggle"
									class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
									v-model="branding.referrals.shareSettings.facebook.enabled"
									:disabled="!branding.referrals.shareSettings.facebook.enabled && enabledShareTargetsCount > 5" />
								<label for="toggle"
									class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>

							<div v-if="branding.referrals.shareSettings.facebook.enabled">
								<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Default share text</div>
								<div class="mb-2 w-full h-12 px-4 py-3 rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex bg-gray-200 text-gray-400 cursor-not-allowed">
									<div class="w-full self-stretch justify-start items-center gap-2 flex">
										<input type="text"
											class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none cursor-not-allowed"
											:class="{ 'bg-gray-200 text-gray-400': true }"
											maxlength="140" value="{ REFERRAL_LINK }" disabled/>
									</div>
								</div>
								<p class="text-gray-600 text-sm italic">This text can't be customized because Facebook does not support pre-populating the share text.</p>
							</div>
						</div>

						<div class="mt-8" v-if="branding?.referrals?.shareSettings?.twitter">
							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Twitter</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Enable Twitter sharing</div>
							<div
								class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="toggle-twitter"
									class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
									v-model="branding.referrals.shareSettings.twitter.enabled"
									:disabled="!branding.referrals.shareSettings.twitter.enabled && enabledShareTargetsCount > 5" />
								<label for="toggle-twitter"
									class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>

							<div v-if="branding.referrals.shareSettings.twitter.enabled">
								<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Default share text</div>
								<div class="mb-2 w-full bg-white h-12 px-4 py-3 rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
									<div class="w-full self-stretch justify-start items-center gap-2 flex">
										<input type="text"
											class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
											maxlength="140" v-model="branding.referrals.shareSettings.twitter.text"/>
									</div>
								</div>
							</div>
						</div>

						<div class="mt-8" v-if="branding?.referrals?.shareSettings?.whatsapp">
							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">WhatsApp</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Enable WhatsApp sharing</div>
							<div
								class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="toggle-whatsapp"
									class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
									v-model="branding.referrals.shareSettings.whatsapp.enabled"
									:disabled="!branding.referrals.shareSettings.whatsapp.enabled && enabledShareTargetsCount > 5"  />
								<label for="toggle-whatsapp"
									class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>

							<div v-if="branding.referrals.shareSettings.whatsapp.enabled">
								<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Default share text</div>
								<div class="mb-2 w-full bg-white h-12 px-4 py-3 rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
									<div class="w-full self-stretch justify-start items-center gap-2 flex">
										<input type="text"
											class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
											maxlength="140" v-model="branding.referrals.shareSettings.whatsapp.text"/>
									</div>
								</div>
							</div>
						</div>

						<div class="mt-8" v-if="branding?.referrals?.shareSettings?.fbmessenger">
							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Facebook Messenger</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Enable Messenger sharing</div>
							<div
								class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="toggle-messenger"
									class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
									v-model="branding.referrals.shareSettings.fbmessenger.enabled"
									:disabled="!branding.referrals.shareSettings.fbmessenger.enabled && enabledShareTargetsCount > 5" />
								<label for="toggle-messenger"
									class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>

							<div v-if="branding.referrals.shareSettings.fbmessenger.enabled">
								<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Default share text</div>
								<div class="mb-2 w-full h-12 px-4 py-3 rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex bg-gray-200 text-gray-400 cursor-not-allowed">
									<div class="w-full self-stretch justify-start items-center gap-2 flex">
										<input type="text"
											class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none cursor-not-allowed"
											:class="{ 'bg-gray-200 text-gray-400': true }"
											maxlength="140" value="{ REFERRAL_LINK }" disabled/>
									</div>
								</div>
								<p class="text-gray-600 text-sm italic">This text can't be customized because Facebook Messenger does not support pre-populating the share text.</p>
							</div>
						</div>


						<div class="mt-8" v-if="branding?.referrals?.shareSettings?.email">
							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Email</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Enable Email sharing</div>
							<div
								class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="toggle-email"
									class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
									v-model="branding.referrals.shareSettings.email.enabled"
									:disabled="!branding.referrals.shareSettings.email.enabled && enabledShareTargetsCount > 5"  />
								<label for="toggle-email"
									class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>

							<div v-if="branding.referrals.shareSettings.email.enabled">
								<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Body text</div>
								<div class="mb-2 w-full bg-white h-12 px-4 py-3 rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
									<div class="w-full self-stretch justify-start items-center gap-2 flex">
										<input type="text"
											class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
											maxlength="140" v-model="branding.referrals.shareSettings.email.text"/>
									</div>
								</div>
							</div>

							<div v-if="branding.referrals.shareSettings.email.enabled">
								<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Subject text</div>
								<div class="mb-2 w-full bg-white h-12 px-4 py-3 rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
									<div class="w-full self-stretch justify-start items-center gap-2 flex">
										<input type="text"
											class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
											maxlength="140" v-model="branding.referrals.shareSettings.email.subject"/>
									</div>
								</div>
							</div>
						</div>

						<!-- Enabling the option to take an email Input box-->
						<div class="mt-8" v-if="branding?.referrals?.shareSettings?.email">
							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Show Friend Email Input Box</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Enable Send Referral Through Klaviyo, this will place an email input box in the referral component.</div>
							<div
								class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="toggle-email-input"
										class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
										v-model="branding.referrals.shareSettings.email.showEmailInput"  />
								<label for="toggle-email-input"
										class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>
						</div>

						<div class="mt-8" v-if="branding?.referrals?.shareSettings?.pinterest">
							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Pinterest</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Enable Pinterest sharing</div>
							<div
								class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="toggle-pinterest"
									class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
									v-model="branding.referrals.shareSettings.pinterest.enabled"
									:disabled="!branding.referrals.shareSettings.pinterest.enabled && enabledShareTargetsCount > 5"  />
								<label for="toggle-pinterest"
									class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>

							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Default share text</div>
							<div class="mb-2 w-full h-12 px-4 py-3 rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex bg-gray-200 text-gray-400 cursor-not-allowed">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none cursor-not-allowed"
										:class="{ 'bg-gray-200 text-gray-400': true }"
										maxlength="140" value="{ REFERRAL_LINK }" disabled/>
								</div>
							</div>
							<p class="text-gray-600 text-sm italic">This text can't be customized because Pinterest does not support pre-populating the share text.</p>
						</div>

						<div class="mt-8" v-if="branding?.referrals?.shareSettings?.instagram">
							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Instagram</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Enable Instagram sharing</div>
							<div
								class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="toggle-instagram"
									class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
									v-model="branding.referrals.shareSettings.instagram.enabled"
									:disabled="!branding.referrals.shareSettings.instagram.enabled && enabledShareTargetsCount > 5"  />
								<label for="toggle-instagram"
									class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>

							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Default share text</div>
							<div class="mb-2 w-full h-12 px-4 py-3 rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex bg-gray-200 text-gray-400 cursor-not-allowed">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none cursor-not-allowed"
										:class="{ 'bg-gray-200 text-gray-400': true }"
										maxlength="140" value="{ REFERRAL_LINK }" disabled/>
								</div>
							</div>
							<p class="text-gray-600 text-sm italic">This text can't be customized because Instagram does not support pre-populating the share text.</p>
						</div>

						<div class="mt-8" v-if="branding?.referrals?.shareSettings?.tiktok">
							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">TikTok</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Enable TikTok sharing</div>
							<div
								class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="toggle-tiktok"
									class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
									v-model="branding.referrals.shareSettings.tiktok.enabled"
									:disabled="!branding.referrals.shareSettings.tiktok.enabled && enabledShareTargetsCount > 5"  />
								<label for="toggle-tiktok"
									class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>

							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Default share text</div>
							<div class="mb-2 w-full h-12 px-4 py-3 rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex bg-gray-200 text-gray-400 cursor-not-allowed">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none cursor-not-allowed"
										:class="{ 'bg-gray-200 text-gray-400': true }"
										maxlength="140" value="{ REFERRAL_LINK }" disabled/>
								</div>
							</div>
							<p class="text-gray-600 text-sm italic">This text can't be customized because TikTok does not support pre-populating the share text.</p>
						</div>


						<div class="mt-8" v-if="branding?.referrals?.shareSettings?.sms">
							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">SMS</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Enable SMS sharing</div>
							<div
								class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="toggle-sms"
									class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
									v-model="branding.referrals.shareSettings.sms.enabled"
									:disabled="!branding.referrals.shareSettings.sms.enabled && enabledShareTargetsCount > 5"  />
								<label for="toggle-sms"
									class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>

							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Default share text</div>
							<div class="mb-2 w-full h-12 px-4 py-3 rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex bg-white">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="140" v-model="branding.referrals.shareSettings.sms.text"/>
								</div>
							</div>
						</div>
					</div>

				</div>
			</template>

		</AccordionGroup>
	</div>
</template>

<script>
import * as Utils from '../../../client-old/utils/Utils';
import AccordionGroup from '../../components/AccordionGroup.ts.vue';
import LearnMoreText from '../../components/LearnMoreText.ts.vue';
import SuperDropdown from '../../components/SuperDropdown.ts.vue';
import ImageUpload from '../../components/ImageUpload.ts.vue';
import LvColorPicker from 'lightvue/color-picker';


const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: [
		'branding',
		'previewType',
		'isLogoUploading',
		'isHeroImageUploading',
		'launcherActive'
	],
	emits: ['fileUploaded', 'switchPreview', 'fileUploading', 'brandingChanged'],
	created() {
		this.debouncedBrandingChanged = this.debounce(this.handleBrandingChanged, 1000);
	},
	data() {
		return {
			accordions: [{
				key: 'program',
				title: "Referral Program",
				height: '1140px'
			}, {
				key: 'sharing',
				title: "Customize the link sharing experience",
				height: '2425px'
			}],
			launcherSizes: [],
			launcherRadii: [],
			debouncedBrandingChanged: null,
			brandingUpdateCounter: 0
		}
	},
	watch: {
		'branding.colors.backgroundColor': function (newValue) {
			this.branding.colors.backgroundColor = this.convertToHex(newValue);
		},
		'branding.colors.buttonBackgroundColor': function (newValue) {
			this.branding.colors.buttonBackgroundColor = this.convertToHex(newValue);
		},
		'branding.colors.buttonTextColor': function (newValue) {
			this.branding.colors.buttonTextColor = this.convertToHex(newValue);
		},
		'branding.colors.linkColor': function (newValue) {
			this.branding.colors.linkColor = this.convertToHex(newValue);
		},
		'branding.colors.accentColor.from': function (newValue) {
			this.branding.colors.accentColor.from = this.convertToHex(newValue);
		},
		'branding.colors.accentColor.to': function (newValue) {
			this.branding.colors.accentColor.to = this.convertToHex(newValue);
		},
		'branding.colors.secondaryColor': function (newValue) {
			this.branding.colors.secondaryColor = this.convertToHex(newValue);
		},
		'branding.colors.warningColor': function (newValue) {
			this.branding.colors.warningColor = this.convertToHex(newValue);
		},
		'branding.referrals.styling.textColor': function (newValue) {
			this.branding.referrals = this.branding.referrals || {};
			this.branding.referrals.styling = this.branding.referrals.styling || {};
			this.branding.referrals.styling.textColor = this.convertToHex(newValue);
		},
		'branding.referrals.styling.fillBorderColor': function (newValue) {
			this.branding.referrals = this.branding.referrals || {};
			this.branding.referrals.styling = this.branding.referrals.styling || {};
			this.branding.referrals.styling.fillBorderColor = this.convertToHex(newValue);
		},
		'branding.launcher.styling.backgroundColor': function (newValue) {
			this.branding.launcher.styling.backgroundColor = this.convertToHex(newValue);
		},

		'branding': {
			handler(newVal) {
				this.debouncedBrandingChanged(newVal);
			},
			deep: true
		}
	},
	components: {
		AccordionGroup,
		SuperDropdown,
		ImageUpload,
		LearnMoreText,
		LvColorPicker
	},
	async mounted() {
	},
	computed: {
		enabledShareTargetsCount: function() {
			return Object.values(this.branding.referrals.shareSettings).reduce((count, setting) => {
				return count + (setting.enabled ? 1 : 0);
			}, 0);
		},
		computedReferralSocialLinkFillColor: {
			get() {
				return this.branding.referrals?.styling?.socialFillColor;
			},
			set(value) {
				this.branding.referrals = this.branding.referrals || {};
				this.branding.referrals.styling = this.branding.referrals.styling || {};
				this.branding.referrals.styling.socialFillColor = value;
			}
		},
		computedReferralButtonBackgroundColor: {
			get() {
				return this.branding.referrals?.styling?.buttonBackgroundColor;
			},
			set(value) {
				this.branding.referrals = this.branding.referrals || {};
				this.branding.referrals.styling = this.branding.referrals.styling || {};
				this.branding.referrals.styling.buttonBackgroundColor = value;
			}
		},
		computedReferralTextColor: {
			get() {
				return this.branding.referrals?.styling?.textColor;
			},
			set(value) {
				this.branding.referrals = this.branding.referrals || {};
				this.branding.referrals.styling = this.branding.referrals.styling || {};
				this.branding.referrals.styling.textColor = value;
			}
		},
		computedReferralFillBackgroundColor: {
			get() {
				return this.branding.referrals?.styling?.fillBorderColor;
			},
			set(value) {
				this.branding.referrals = this.branding.referrals || {};
				this.branding.referrals.styling = this.branding.referrals.styling || {};
				this.branding.referrals.styling.fillBorderColor = value;
			}
		},
		computedReferralButtonTextColor: {
			get() {
				return this.branding.referrals?.styling?.buttonTextColor;
			},
			set(value) {
				this.branding.referrals = this.branding.referrals || {};
				this.branding.referrals.styling = this.branding.referrals.styling || {};
				this.branding.referrals.styling.buttonTextColor = value;
			}
		},
		accordionPreviewTypeName() {
			if (this.previewType == 'launcher') {
				return 'launcher';
			} else if (this.previewType == 'landing-page-guest') {
				return 'guest';
			} else if (this.previewType == 'landing-page-member') {
				return 'member';
			}
		}
	},
	methods: {
		preventNewLines(event) {
			if (event.keyCode === 13) { // 13 is the key code for Enter
				event.preventDefault();
			}
		},
		handleBrandingChanged(newBranding) {
			this.brandingUpdateCounter++;
			this.$emit('brandingChanged', newBranding);
		},
		debounce(func, delay) {
			let debounceTimer;
			return function () {
				const context = this;
				const args = arguments;
				clearTimeout(debounceTimer);
				debounceTimer = setTimeout(() => func.apply(context, args), delay);
			};
		},
		convertToHex(color) {
			// Check if color is already in hex format with or without '#'
			const hexColorPattern = /^#?([a-fA-F0-9]{3})([a-fA-F0-9]{3})?$/;
			const match = color.match(hexColorPattern);
			if (match) {
				// Reformat if '#' is missing
				return `#${match[1]}${match[2] ? match[2] : ''}`;
			}

			// Assuming the color is in RGB format, e.g., 'rgb(255, 0, 0)'
			if (/rgb/.test(color)) {
				const rgb = color.match(/\d+/g);
				return `#${rgb.map(x => {
					const hex = parseInt(x).toString(16);
					return hex.length === 1 ? '0' + hex : hex;
				}).join('')}`;
			}

			// Return original color if none of the above cases match
			return color;
		},
		handleAccordionOpen(accordionKey) {
			switch (accordionKey) {
				case 'launcher':
					return this.$emit('switchPreview', 'launcher');
				case 'guest':
					return this.$emit('switchPreview', 'landing-page-guest');
				case 'member':
					return this.$emit('switchPreview', 'landing-page-member');
				default:
					break;
			}
		},
	}
}
</script>

<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}
.toggle-checkbox {
	opacity: 0;
	width: 0;
	height: 0;
}

.toggle-label {
	display: block;
	overflow: hidden;
	cursor: pointer;
	height: 24px;
	/* Height of the toggle */
	padding: 0;
	line-height: 24px;
	background: #bbb;
	/* Background of the toggle */
	border-radius: 24px;
	transition: background-color 0.2s;
}

.toggle-label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 2px;
	width: 20px;
	/* Width of the toggle handle */
	height: 20px;
	/* Height of the toggle handle */
	border-radius: 20px;
	background: #fff;
	/* Background of the toggle handle */
	transition: 0.2s;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}
.toggle-checkbox:disabled+.toggle-label {
	background-color: #eee;
	cursor: not-allowed;
}


.toggle-checkbox:checked+.toggle-label:after {
	transform: translateX(18px);
}

.toggle-checkbox:checked {
	right: 0;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox {
	right: 4px;
}

.toggle-label {
	transition: background-color 0.2s;
}
</style>
