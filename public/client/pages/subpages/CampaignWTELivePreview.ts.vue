<template>
	<div class="flex flex-col">
		<div class="flex ml-5 mr-5 items-start">
			<div class="w-full bg-white bg-opacity-75 rounded-2xl border border-violet-200 p-2 md:p-7">
				<TabSwitcher :useLozengeTabs="true" :tabs="tabs" class="mt-6" />
			</div>

			<!-- <WTESummary v-if="!isShopReward"
				:wteData="wteSummaryData.wte"
				:rewardListData="wteSummaryData.rewards"
				class="hidden md:block"/> -->
		</div>

		<div class="flex justify-end flex-wrap items-center mt-4 mb-4">
			<a
			class="pl-2 pr-4 text-ralprimary-main text-sm justify-start font-semibold items-center gap-1 inline-flex cursor-pointer hover:underline ml-2"
			href="https://docs.raleon.io/docs/setting-up-a-new-campaign"
			target="_blank"
				>
				<svg class="w-4 h-4 fill-current text-ralrpimary-ultralight mr-2 opacity-50" viewBox="0 0 16 16">
				<path d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 12c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1zm1-3H7V4h2v5z" />
				</svg>
				What is styling a reward?
			</a>
			<div class="flex-grow"></div>
			<LightSecondaryButton
				cta="Save"
				@click="$emit('enable')"
				:isDisabled="!allComplete"
				class="mr-4 sm:mr-0"
			></LightSecondaryButton>
		</div>
	</div>

</template>

<script>

import * as Utils from '../../../client-old/utils/Utils';
import TabSwitcher from '../../components/TabSwitcher.ts.vue';
import RewardForm from './CampaignWTERewardView.ts.vue'
import WayToEarnForm from './CampaignWTEEarnView.ts.vue'
import ShopItemForm from './CampaignShopItemView.ts.vue'
import LightSecondaryButton from '../../components/LightSecondaryButton.ts.vue';
import ImageUpload from '../../components/ImageUpload.ts.vue';
import WTESummary from '../../components/WTESummary.ts.vue';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: ['campaign', 'earn', 'isShopReward', 'wteSummaryData'],
	components: {
		TabSwitcher,
		WayToEarnForm,
		RewardForm,
		LightSecondaryButton,
		ImageUpload,
		WTESummary
	},
	data() {
		return {
			isEarnImageUploading: false,
			isRewardImageUploading: false,
			isShopItemImageUploading: false,
			wteComplete: false,
			rewardsComplete: {},
			allComplete: false,
		}
	},
	watch: {
		'earn.rewards': {
			deep: true,
			handler(newVal) {
				console.log(`earn changed ${JSON.stringify(newVal)}`)
			}
		}
	},
	computed: {
		tabs() {
			let tabs;
			if(!this.isShopReward) {
				tabs = [
					{
						name: 'Way To Earn',
						component: WayToEarnForm,
						props: {
							earn: this.earn,
							isEarnImageUploading: this.isEarnImageUploading,
							formValid: this.formValid,
						},
						eventListeners: {
							imageSelected: (event) => {
								this.earn.imageURL = event.url;
							},
							isComplete: (isComplete) => {
								this.wteComplete = isComplete;
								const allRewardsComplete =
									Object.values(this.rewardsComplete).every(x => x) &&
									this.earn.rewards?.length === Object.keys(this.rewardsComplete).length;

								this.allComplete = this.wteComplete && allRewardsComplete;
							}
						}
					},
				]

				if (this.earn.rewards) {
					console.log(`pushing tabs ${JSON.stringify(this.earn.rewards)}`)
					const rewards = this.earn.rewards.map((reward, index) => ({
						name: `Reward ${index + 1}`,
						component: RewardForm,
						props: {
							reward,
							index,
							isRewardImageUploading: this.isRewardImageUploading,
						},
						eventListeners: {
							imageSelected: (event, index) => {
								this.earn.rewards[index].imageURL = event.url;
							},
							isComplete: (isComplete) => {
								this.rewardsComplete[index] = isComplete;

								const allRewardsComplete =
									Object.values(this.rewardsComplete).every(x => x) &&
									this.earn.rewards.length === Object.keys(this.rewardsComplete).length;

								this.allComplete = this.wteComplete && allRewardsComplete;
							}
						}
					}))
					if (rewards) {
						tabs.push(...rewards);
					}
				}
				return tabs;
			}
			else {
				return [
					{
						name: 'Reward',
						component: ShopItemForm,
						props: {
							earn: this.earn,
							isShopItemImageUploading: this.isShopItemImageUploading,
						},
						eventListeners: {
							imageSelected: (event) => {
								this.earn.shopItem.imageURL = event.url;
							},
							isComplete: (isComplete) => {
								this.allComplete = isComplete;
							}
						}
					}
				]
			}
		},
	},
	methods: {
	}
}
</script>
<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}
</style>

