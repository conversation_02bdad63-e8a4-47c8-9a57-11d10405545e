<template>
	<div class="mt-8 ml-2 mr-2 mb-8 w-full">
		<!-- ... existing HTML structure ... -->
		<div class="mb-3 text-slate-800 text-base font-semibold font-['Open Sans'] leading-normal">
			Point Shop Reward
			<span class="text-rose-500">*</span>
		</div>
		<div class="mb-3 text-black text-sm font-normal font-['Inter']">
			This is what the item will be called in the Loyalty Shop!
		</div>
		<div v-if="earn.shopItemPredictionRunning" role="status" class="w-full mb-8 flex p-3 space-y-4 justify-end bg-gray-300 dark:bg-gray-600  rounded-2xl shadow animate-pulse">
			<img src="../../images/magic.svg" width="20" height="60" />
		</div>
		<div v-if="!earn.shopItemPredictionRunning"
			class="mb-8 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"
			:class="{
				'border-ralerror-dark': v$.earn.shopItem.name.$dirty && v$.earn.shopItem.name.$invalid,
				'border-gray-400': !v$.earn.shopItem.name.length || !v$.earn.shopItem.name.$dirty,
			}">
			<div class="w-full self-stretch justify-start items-center gap-2 flex">
				<input
					class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Open Sans'] leading-normal appearance-none outline-none"
					maxlength="40"
					v-model="v$.earn.shopItem.name.$model"
				/>
			</div>
		</div>
		<div class="mb-3 text-slate-800 text-base font-semibold font-['Open Sans'] leading-normal">
			Reward Description
			<span class="text-rose-500">*</span>
		</div>
		<div class="mb-3 text-black text-sm font-normal font-['Inter']">
			This will be shown as the description of the purchased item.
		</div>
		<div v-if="earn.shopItemPredictionRunning" role="status" class="w-full mb-8 flex p-3 space-y-4 justify-end bg-gray-300 dark:bg-gray-600  rounded-2xl shadow animate-pulse">
			<img src="../../images/magic.svg" width="20" height="60" />
		</div>
		<div
			v-if="!earn.shopItemPredictionRunning"
			class="mb-8 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"
			:class="{
				'border-ralerror-dark': v$.earn.shopItem.description.$dirty && v$.earn.shopItem.description.$invalid,
				'border-gray-400': !v$.earn.shopItem.description.length || !v$.earn.shopItem.description.$dirty,
			}">
			<div class="w-full self-stretch justify-start items-center gap-2 flex">
				<input
					class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Open Sans'] leading-normal appearance-none outline-none"
					maxlength="80"
					v-model="v$.earn.shopItem.description.$model" />
			</div>
		</div>
		<div class="mb-3 text-slate-800 text-base font-semibold font-['Open Sans'] leading-normal">
			Upload Reward Image
			<span class="text-rose-500">*</span>
		</div>
		<div class="mt-3 text-slate-800 text-xs font-normal font-['Open Sans'] leading-none">
			Will be shown in the Loyalty Panel
		</div>
		<div class="flex flex-wrap mt-6 items-center">
			<div class="image-wrapper">
                <div class="image-container">
                    <img
						class="rounded-3xl border border-black object-center object-cover"
						:class="{
							'border-ralerror-dark': v$.earn.shopItem?.imageURL.$dirty && v$.earn.shopItem?.imageURL.$errors.length,
							'border-gray-400': !v$.earn.shopItem?.imageURL.$errors.length || !v$.earn.shopItem?.imageURL.$dirty,
						}"
						:src="v$.earn.shopItem?.imageURL.$model || 'https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg'"
					/>
                </div>
            </div>
			<div class="flex flex-col flex-grow items-start justify-center">
				<div class="w-full">
					<ImageUpload @imageSelected="(event) => $emit('imageSelected', event)"/>
				</div>
			</div>
		</div>
	</div>
</template>

<script>

import { useVuelidate } from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import ImageUpload from '../../components/ImageUpload.ts.vue';

export default {
	props: ['earn', 'isShopItemImageUploading'],
	emits: ['isComplete', 'imageSelected'],
	components: {
		ImageUpload
	},
	mounted() {
	},
	setup() {
		return {
			v$: useVuelidate(),
		}
	},
	watch: {
		'v$.earn.$invalid'(newValue) {
			this.$emit('isComplete', !newValue);
		}
	},
	methods: {},
	validations() {
		return {
			earn: {
				shopItem: {
					name: { required },
					description: { required },
					imageURL: { required },
				}

			}
		}
	}
}
</script>

<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

.image-wrapper {
    width: 100%;
    max-width: 200px;
    margin: auto;
}

.image-container {
    position: relative;
    width: 100%;
    padding-top: 100%; /* Maintains a 1:1 aspect ratio */
}

.image-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.upload-button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-top: 10px;
}

@media (min-width: 500px) {
    .upload-button-container {
        margin-left: 12px; /* Add left padding back for larger screens */
        text-align: left; /* Align left on larger screens */
		width: 100%;
		justify-content: flex-start;
    }
}
</style>
