<template>
	<div class="w-[100%] lg:w-auto">
		<AccordionGroup :accordions="filteredAccordions" :preview-type="accordionPreviewTypeName"
			:initialOpenAccordionKey="defaultOpenName" @open="handleAccordionOpen">
			<template v-slot:header>
				<div class="p-4">
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Text</label>
						<div class="">
							<input v-model="branding.headerStyle.title"
								class="rounded-lg no-focus-outline border border-gray-400 w-full text-gray-600 text-base font-['Inter'] leading-normal appearance-none outline-none max-w-sm"
								type="text" />

						</div>
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Logo</label>
						<div class="flex">
							<img class="w-10 h-10 rounded-md border border-gray-400 mr-2" :src="branding.logoUrl"
								v-if="branding.logoUrl != null" />
							<img class="w-10 h-10 rounded-md border border-gray-400 mr-2"
								src="https://dqpqjbq51w8fz.cloudfront.net/images/organization=1000508/icon_h6pfwzwj75iy28odbmty.png"
								v-if="branding.logoUrl == null" />
							<ImageUpload @imageSelected="uploadLogo" />
						</div>

					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Background Color</label>
						<LvColorPicker v-model="branding.headerStyle.backgroundColor" :clearable="false"
							:key="'header-color-backgroundColor-' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedHeaderBackgroundColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Text Color</label>
						<LvColorPicker v-model="branding.headerStyle.textColor" :clearable="false"
							:key="'header-color-textColor-' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedHeaderTextColor" :bottomBar="false" />
					</div>
				</div>
			</template>

			<template v-slot:home>
				<div class="p-4">
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Background Color</label>
						<LvColorPicker v-model="branding.home.backgroundColor" :clearable="false"
							:key="'home-color-backgroundColor-' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedHomeBackgroundColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Text Color (Default)</label>
						<LvColorPicker v-model="branding.home.textColor" :clearable="false"
							:key="'home-color-textColor-' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedHomeTextColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Points Color</label>
						<LvColorPicker v-model="branding.home.pointsColor" :clearable="false"
							:key="'home-color-pointsColor-' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedHomePointsColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Link Color</label>
						<LvColorPicker v-model="branding.home.linkColor" :clearable="false"
							:key="'home-color-linkColor-' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedHomeLinkColor" :bottomBar="false" />
					</div>
				</div>
			</template>
			<template v-slot:menu>
				<div class="p-4">
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Text Color</label>
						<LvColorPicker v-model="branding.menu.textColor" :clearable="false"
							:key="'waysToEarn-color-menuTextColor' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedMenuTextColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Active Color</label>
						<LvColorPicker v-model="branding.menu.underlineColor" :clearable="false"
							:key="'waysToEarn-color-menuUnderlineColor' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedMenuUnderLineColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<div class="mb-4 flex items-center">
							<div
								class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="show-history-toggle"
									class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
									v-model="branding.menu.showHistory" />
								<label for="show-history-toggle"
									class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>
							<span class="ml-2 text-sm font-['Inter']">Show History Option</span>
						</div>
					</div>
				</div>
			</template>

			<template v-slot:waysToEarn>
				<div class="p-4">
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Section Title</label>
						<input v-model="branding.waysToEarn.sectionTitle"
							class="rounded-lg no-focus-outline border border-gray-400 w-full text-gray-600 text-base font-['Inter'] leading-normal appearance-none outline-none max-w-sm"
							type="text" />
					</div>
					<div class="mb-4 max-w-sm">
						<label class="block text-sm font-['Inter'] font-semibold mb-2">Style</label>
						<div class="space-y-2">
							<!-- Card Option -->
							<div @click="branding.waysToEarn.style = 'card'"
								class="flex items-center p-3 border border-ralprimary-dark hover:bg-ralsecondary-start hover:text-white rounded-lg cursor-pointer transition-colors duration-200 ease-in-out"
								:class="[branding.waysToEarn.style === 'card' ? 'bg-ralsecondary-start text-white' : 'bg-white text-ralprimary-dark border-gray-300 hover:bg-gray-50']">
								<div class="w-5 h-5 mr-3 rounded-full border-2 flex items-center justify-center"
									:class="[branding.waysToEarn.style === 'card' ? 'border-white' : 'border-gray-300']">
									<div class="w-2.5 h-2.5 rounded-full transition-all duration-200 ease-in-out"
										:class="[branding.waysToEarn.style === 'card' ? 'bg-white' : 'bg-transparent']">
									</div>
								</div>
								Card
							</div>

							<!-- Floating Option -->
							<div @click="branding.waysToEarn.style = 'floating'"
								class="flex items-center p-3 border border-ralprimary-dark hover:bg-ralsecondary-start hover:text-white rounded-lg cursor-pointer transition-colors duration-200 ease-in-out"
								:class="[branding.waysToEarn.style === 'floating' ? 'bg-ralsecondary-start text-white' : 'bg-white text-ralprimary-dark border-gray-300 hover:bg-gray-50']">
								<div class="w-5 h-5 mr-3 rounded-full border-2 flex items-center justify-center"
									:class="[branding.waysToEarn.style === 'floating' ? 'border-white' : 'border-gray-300']">
									<div class="w-2.5 h-2.5 rounded-full transition-all duration-200 ease-in-out"
										:class="[branding.waysToEarn.style === 'floating' ? 'bg-white' : 'bg-transparent']">
									</div>
								</div>
								Floating
							</div>
						</div>
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Background Color</label>
						<LvColorPicker v-model="branding.waysToEarn.backgroundColor" :clearable="false"
							:key="'waysToEarn-color-backgroundColor-' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedWaysToEarnBackgroundColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Border Color</label>
						<LvColorPicker v-model="branding.waysToEarn.borderColor" :clearable="false"
							:key="'waysToEarn-color-borderColor-' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedWaysToEarnBorderColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Text Color</label>
						<LvColorPicker v-model="branding.waysToEarn.textColor" :clearable="false"
							:key="'waysToEarn-color-textColor-' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedWaysToEarnTextColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Instruction Text Color</label>
						<LvColorPicker v-model="branding.waysToEarn.instructionTextColor" :clearable="false"
							:key="'waysToEarn-color-pointPillInstructionText' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedWayToEarnInstructionTextColor" :bottomBar="false" />
					</div>
					<h3 class="text-lg font-semibold font-['Inter'] mb-2">Point Pill</h3>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Background Color</label>
						<LvColorPicker v-model="branding.waysToEarn.pointPillBackgroundColor" :clearable="false"
							:key="'waysToEarn-color-pointPillBackgroundColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedWaysToEarnPointPillBackgroundColor"
							:bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Border Color</label>
						<LvColorPicker v-model="branding.waysToEarn.pointPillBorderColor" :clearable="false"
							:key="'waysToEarn-color-pointPillBorderColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedWaysToEarnPointPillBorderColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Text Color</label>
						<LvColorPicker v-model="branding.waysToEarn.pointPillTextColor" :clearable="false"
							:key="'waysToEarn-color-pointPillPointPillTextColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedWaysToEarnPointPillTextColor" :bottomBar="false" />
					</div>
					<h3 class="text-lg font-semibold font-['Inter'] mb-2">Effects</h3>
					<div class="mb-4 flex items-center">
						<div
							class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in">
							<input type="checkbox" id="hover-effect-toggle"
								class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
								v-model="branding.waysToEarn.hoverEffect" />
							<label for="hover-effect-toggle"
								class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
						</div>
						<span class="ml-2 text-sm font-['Inter']">Enable hover effect</span>
					</div>

					<div class="mb-4 flex items-center">
						<div
							class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in">
							<input type="checkbox" id="background-blur-toggle"
								class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
								v-model="branding.waysToEarn.backgroundBlur" />
							<label for="background-blur-toggle"
								class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
						</div>
						<span class="ml-2 text-sm font-['Inter']">Enable background blur</span>
					</div>
				</div>
			</template>


			<template v-slot:rewards>
				<div class="p-4">
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Section Title</label>
						<input v-model="branding.rewards.sectionTitle"
							class="rounded-lg no-focus-outline border border-gray-400 w-full text-gray-600 text-base font-['Inter'] leading-normal appearance-none outline-none max-w-sm"
							type="text" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Text Color</label>
						<LvColorPicker v-model="branding.rewards.textColor" :clearable="false"
							:key="'waysToEarn-color-rewardsTextColor' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedRewardsTextColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Hover Text Color</label>
						<LvColorPicker v-model="branding.rewards.hoverTextColor" :clearable="false"
							:key="'waysToEarn-color-rewardsHoverTextColor' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedRewardsHoverTextColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Button Background Color</label>
						<LvColorPicker v-model="branding.rewards.buttonBackgroundColor" :clearable="false"
							:key="'waysToEarn-color-rewardsButtonBackgroundColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedRewardsButtonBackgroundColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Button Text Color</label>
						<LvColorPicker v-model="branding.rewards.buttonTextColor" :clearable="false"
							:key="'waysToEarn-color-rewardsButtonTextColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedRewardsButtonTextColor" :bottomBar="false" />
					</div>
					<h3 class="text-lg font-semibold font-['Inter'] mb-2">Point Pill</h3>

					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Background Color</label>
						<LvColorPicker v-model="branding.rewards.pointPillBackgroundColor" :clearable="false"
							:key="'waysToEarn-color-rewardsPointPillBackgroundColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedRewardsPointPillBackgroundColor"
							:bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Border Color</label>
						<LvColorPicker v-model="branding.rewards.pointPillBorderColor" :clearable="false"
							:key="'waysToEarn-color-rewardsPointPillBorderColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedRewardsPointPillBorderColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Text Color</label>
						<LvColorPicker v-model="branding.rewards.pointPillTextColor" :clearable="false"
							:key="'waysToEarn-color-rewardsPointPillTextColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedRewardsPointPillTextColor" :bottomBar="false" />
					</div>


					<h3 class="text-lg font-semibold font-['Inter'] mb-2">Effects</h3>
					<div class="mb-4 flex items-center">
						<div
							class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in">
							<input type="checkbox" id="blur-effect-toggle-rewards"
								class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
								v-model="branding.rewards.backgroundBlur" />
							<label for="blur-effect-toggle-rewards"
								class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
						</div>
						<span class="ml-2 text-sm font-['Inter']">Enable Background Blur</span>
					</div>
				</div>
			</template>
			<template v-slot:vip>
				<div class="p-4">
					<div class="mb-4">
						<div class="mb-4 flex items-center">
							<div
								class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="show-vip-toggle"
									class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
									v-model="branding.vip.showProgress" />
								<label for="show-vip-toggle"
									class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>
							<span class="ml-2 text-sm font-['Inter']">Show VIP Progress (VIP must be enabled)</span>
						</div>
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Text Color</label>
						<LvColorPicker v-model="branding.vip.textColor" :clearable="false"
							:key="'waysToEarn-color-vipTextColor' + brandingUpdateCounter" class="font-['Inter']"
							:value="computedVIPTextColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Progress Background Color</label>
						<LvColorPicker v-model="branding.vip.progressBackgroundColor" :clearable="false"
							:key="'waysToEarn-color-vipProgressBackgroundColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedProgressBackgroundColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Progress Fill Color</label>
						<LvColorPicker v-model="branding.vip.progressFillColor" :clearable="false"
							:key="'waysToEarn-color-vipProgressFillColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedProgressFillColor" :bottomBar="false" />
					</div>
				</div>
			</template>
			<template v-slot:guestView>
				<div class="p-4">
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Hero Title</label>
						<input v-model="branding.guestView.heroTitle"
							class="rounded-lg no-focus-outline border border-gray-400 w-full text-gray-600 text-base font-['Inter'] leading-normal appearance-none outline-none max-w-sm"
							type="text" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-['Inter'] font-semibold mb-1">Hero Description</label>
						<textarea
							v-model="branding.guestView.heroDescription"
							@input="branding.guestView.heroDescription = $event.target.value.replace(/\n/g, '')"
							class="rounded-lg border border-gray-400 w-full text-gray-600 text-base font-['Inter'] leading-normal appearance-none outline-none max-w-sm p-2 resize-none"
							rows="3"
						></textarea>
					</div>
					<div class="mb-4">
						<label class="block text-sm font-medium mb-1">Banner Image</label>
						<div class="flex flex-col items-start gap-4">
							<div class="w-full md:w-[420px]">
								<img class="rounded-3xl border border-black object-center h-[240px] w-full object-cover"
									:src="branding.guestView.heroImageUrl || 'https://raleoncdn.s3.us-east-1.amazonaws.com/DefaultBanner.jpg'" />
							</div>
							<div class="w-full md:w-[420px]">
								<ImageUpload :aspectRatio="1.75" @imageUploading="uploadingHeroImage"
									@imageSelected="uploadHeroImage" :show-close-button="true"
									container-classes="w-full"
									popover-classes="w-full md:w-auto h-auto lg:max-h-[100vh] lg:max-h-[50vw] lg:w-[30vw]" />
							</div>
						</div>
					</div>
					<div class="mb-4">
						<label class="block text-sm font-medium mb-1">Text Color</label>
						<LvColorPicker v-model="branding.guestView.textColor" :clearable="false"
							:key="'waysToEarn-color-vipGuestViewTextColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedGuestViewTextColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-medium mb-1">Background Color</label>
						<LvColorPicker v-model="branding.guestView.backgroundColor" :clearable="false"
							:key="'waysToEarn-color-vipGuestViewBackgroundColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedGuestViewBackgroundColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-medium mb-1">Button Background Color</label>
						<LvColorPicker v-model="branding.guestView.buttonBackgroundColor" :clearable="false"
							:key="'waysToEarn-color-vipGuestViewButtonBackgroundColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedGuestViewButtonBackgroundColor" :bottomBar="false" />
					</div>
					<div class="mb-4">
						<label class="block text-sm font-medium mb-1">Button Text Color</label>
						<LvColorPicker v-model="branding.guestView.buttonTextColor" :clearable="false"
							:key="'waysToEarn-color-vipGuestViewButtonTextColor' + brandingUpdateCounter"
							class="font-['Inter']" :value="computedGuestViewButtonTextColor" :bottomBar="false" />
					</div>

					<div class="mb-4">
						<div class="mb-4 flex items-center">
							<div
								class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="show-guestView-joinToggle"
									class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
									v-model="branding.guestView.showJoinButton" />
								<label for="show-guestView-joinToggle"
									class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>
							<span class="ml-2 text-sm font-['Inter']">Show Join Button</span>
						</div>
					</div>
				</div>
			</template>

			<template v-slot:launcher>
				<div class="ml-4 mb-8 text-black text-sm font-normal font-['Inter']">The loyalty launcher is what a
					customer
					or guest would click on to open the Loyalty Panel. <LearnMoreText text="What's a Loyalty Panel?"
						url="https://docs.raleon.io/docs/the-loyalty-panel"></LearnMoreText>
				</div>
				<div class="mx-4">
					<div class="mt-8 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
						Upload your logo</div>
					<div class="mt-3 text-black text-sm font-normal font-['Inter']">We use your logo in
						different parts of your loyalty experience. It should be a 72x72 square and under 50kb to keep
						site
						performance high.</div>
					<div class="flex mt-6">
						<img class="w-16 h-16 rounded-3xl border border-black" :src="branding.logoUrl"
							v-if="branding.logoUrl != null" />
						<img class="w-16 h-16 rounded-3xl border border-black"
							src="http://localhost:3030/client/images/RaleonSharkAvatar.png"
							v-if="branding.logoUrl == null" />

						<div class="flex flex-col flex-grow items-start justify-center pl-12">
							<ImageUpload @imageUploading="uploadingLogo" @imageSelected="uploadLogo"
								:show-close-button="true" container-classes="absolute"
								popover-classes="fixed w-[100vw] md:w-[100vw] h-[100vh] max-h-[100vh] left-0 top-0 lg:max-h-[50vw] lg:absolute lg:right-auto lg:w-[30vw] lg:h-auto" />
						</div>
					</div>

					<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
						Launcher size
					</div>
					<div class="mt-1 text-black text-sm font-normal font-['Inter']">Set the launcher size below.</div>
					<!-- <div
						class="w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"> -->
					<div class="w-full self-stretch justify-start items-center gap-2 flex mt-1">
						<select
							class="mb-2 w-52 h-10 px-2 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex"
							v-model="branding.launcher.size">
							<option v-for="size in this.launcherSizes" :value="size.value">
								{{ size.label }}
							</option>
						</select>
					</div>
					<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
						Rounded Corners
					</div>
					<div class="mt-1 text-black text-sm font-normal font-['Inter']">Set the launcher corner border
						radius below.</div>
					<!-- <div
						class="w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"> -->
					<div class="w-full self-stretch justify-start items-center gap-2 flex mt-1">
						<select
							class="mb-2 w-52 h-10 px-2 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex"
							v-model="branding.launcher.radius">
							<option v-for="radius in this.launcherRadii" :value="radius.value">
								{{ radius.label }}
							</option>
						</select>
					</div>
					<!-- </div> -->
					<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
						Launcher position</div>
					<div class="mt-1 text-black text-sm font-normal font-['Inter']">Adjust which side
						of your store the launcher appears on for computers, tablets, and mobile.</div>
					<select
						class="mb-2 w-52 h-10 px-2 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex"
						v-model="branding.launcher.launcherPosition">
						<option v-for="position in [
			{value: 'left', label: 'Bottom Left'},
			{value: 'right', label: 'Bottom Right'},
			{value: 'middle-left', label: 'Middle Left'},
			{value: 'middle-right', label: 'Middle Right'},
		]" :value="position.value">
							{{ position.label }}
						</option>
					</select>
					<div class="mt-2">
						<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Loyalty
							call to action</div>
						<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">What a customer sees on the
							launcher, as shown in the preview above. Be short and succinct. You have a
							max of 40 characters.</div>
						<div
							class="mb-2 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input type="text"
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									maxlength="40" v-model="branding.launcher.callToAction" />
							</div>
						</div>

						<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Loyalty
							Launcher Text Color</div>
						<div class="mt-4">
							<LvColorPicker
								class="mx-2 md:mx-6 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
								:key="'background-launcher-styling-textColor-' + brandingUpdateCounter"
								v-model="computedLauncherTextColor" :clearable="false"
								:value="computedLauncherTextColor" :bottomBar="false" />
						</div>

						<div class="mt-4  text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
							Loyalty Launcher Background Color</div>
						<div class="mt-4 mb-4">
							<LvColorPicker
								class="mx-2 md:mx-6 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
								:key="'background-launcher-styling-backgroundColor-' + brandingUpdateCounter"
								v-model="computedLauncherBackgroundColor" :clearable="false"
								:value="computedLauncherBackgroundColor" :bottomBar="false" />
						</div>
					</div>
				</div>
			</template>

			<template v-slot:advanced>
				<div class="p-4">
					<div class="my-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
						Custom Styling
					</div>
					<div class="mb-3 text-black text-sm font-normal font-['Inter']">Add custom css to override default styles</div>
					<textarea class="w-full h-28 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-start gap-2 inline-flex"
						v-model="branding.custom"
						@input="branding.custom = $event.target.value.replace(/\n/g, '')">
					</textarea>
				</div>
			</template>
		</AccordionGroup>
	</div>
</template>
<script>
import * as Utils from '../../../client-old/utils/Utils';
import AccordionGroup from '../../components/AccordionGroup.ts.vue';
import LearnMoreText from '../../components/LearnMoreText.ts.vue';
import SuperDropdown from '../../components/SuperDropdown.ts.vue';
import ImageUpload from '../../components/ImageUpload.ts.vue';
import LvColorPicker from 'lightvue/color-picker';
import { useRoute } from 'vue-router';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: [
		'branding',
		'previewType',
		'isLogoUploading',
		'isHeroImageUploading',
		'launcherActive',
	],
	emits: ['fileUploaded', 'switchPreview', 'fileUploading', 'brandingChanged'],
	created() {
		this.debouncedBrandingChanged = this.debounce(this.handleBrandingChanged, 1000);
	},
	setup() {
		const route = useRoute();
		return { route };
	},
	data() {
		return {
			accordionsBase: [
				{ key: 'header', title: "Header", height: '450px' },
				{ key: 'home', title: "Home", height: '450px' },
				{ key: 'menu', title: "Menu", height: '300px' },
				{ key: 'waysToEarn', title: "Ways to Earn", height: '1100px' },
				{ key: 'rewards', title: "Rewards", height: '870px' },
				{ key: 'vip', title: "VIP", height: '400px' },
				{ key: 'guestView', title: "Guest View", height: '1000px' },
				{ key: 'advanced', title: "Advanced", height: '300px' },
			],
			launcherSizes: [
				{ label: 'Small Logo Only', value: 'small', selected: true },
				{ label: 'Small Logo with Text', value: 'small-text' },
				{ label: 'Small Text Only', value: 'small-text-only' },
				{ label: 'Medium Logo Only', value: 'medium' },
				{ label: 'Medium Logo with Text', value: 'medium-text' },
				{ label: 'Medium Text Only', value: 'medium-text-only' },
				{ label: 'Large Logo Only', value: 'large' },
				{ label: 'Large Logo with Text', value: 'large-text' },
				{ label: 'Large Text Only', value: 'large-text-only' },
			],
			launcherRadii: [
				{ label: 'None', value: '0px' },
				{ label: 'Small', value: '5px' },
				{ label: 'Medium', value: '10px' },
				{ label: 'Large', value: '20px' },
				{ label: 'Extra large', value: '40px' },
				{ label: 'Full', value: '50%', selected: true },
			],
			debouncedBrandingChanged: null,
			brandingUpdateCounter: 0
		}
	},
	watch: {
		'branding.launcher.styling.textColor': function (newValue) {
			this.branding.launcher.styling.textColor = this.convertToHex(newValue);
		},
		'branding.launcher.styling.backgroundColor': function (newValue) {
			this.branding.launcher.styling.backgroundColor = this.convertToHex(newValue);
		},

		'branding': {
			handler(newVal) {
				this.debouncedBrandingChanged(newVal);
			},
			deep: true
		},
	},
	components: {
		AccordionGroup,
		SuperDropdown,
		ImageUpload,
		LearnMoreText,
		LvColorPicker
	},
	async mounted() { },
	computed: {
		filteredAccordions() {
			const tab = this.route.query.tab;

			if (tab === 'launcher') {
				return [{
					key: 'launcher',
					title: "Customize your loyalty launcher",
					height: '1200px',
				}];
			} else if (tab === 'sidebar') {
				return this.accordionsBase;
			} else {
				let result = [...this.accordionsBase];
				if (this.launcherActive) {
					result.splice(1, 0, {
						key: 'launcher',
						title: "Customize your loyalty launcher",
						height: 'auto'
					});
				}
				return result;
			}
		},
		computedRewardsHoverTextColor: {
			get() {
				return this.branding.rewards.hoverTextColor;
			},
			set(value) {
				this.branding.rewards.hoverTextColor = value;
			}
		},
		computedGuestViewButtonBackgroundColor: {
			get() {
				return this.branding.guestView.buttonBackgroundColor;
			},
			set(value) {
				this.branding.guestView.buttonBackgroundColor = value;
			}
		},
		computedGuestViewButtonTextColor: {
			get() {
				return this.branding.guestView.buttonTextColor;
			},
			set(value) {
				this.branding.guestView.buttonTextColor = value;
			}
		},
		computedGuestViewTextColor: {
			get() {
				return this.branding.guestView.textColor;
			},
			set(value) {
				this.branding.guestView.textColor = value;
			}
		},
		computedGuestViewBackgroundColor: {
			get() {
				return this.branding.guestView.backgroundColor;
			},
			set(value) {
				this.branding.guestView.backgroundColor = value;
			}
		},
		computedVIPTextColor: {
			get() {
				return this.branding.vip.textColor;
			},
			set(value) {
				this.branding.vip.textColor = value;
			}
		},
		computedProgressBackgroundColor: {
			get() {
				return this.branding.vip.progressBackgroundColor;
			},
			set(value) {
				this.branding.vip.progressBackgroundColor = value;
			}
		},
		computedProgressFillColor: {
			get() {
				return this.branding.vip.progressFillColor;
			},
			set(value) {
				this.branding.vip.progressFillColor = value;
			}
		},
		computedMenuTextColor: {
			get() {
				return this.branding.menu.textColor;
			},
			set(value) {
				this.branding.menu.textColor = value;
			}
		},
		computedMenuUnderLineColor: {
			get() {
				return this.branding.menu.underlineColor;
			},
			set(value) {
				this.branding.menu.underlineColor = value;
			}
		},
		computedHomeLinkColor: {
			get() {
				return this.branding.home.linkColor;
			},
			set(value) {
				this.branding.home.linkColor = value;
			}
		},
		computedRewardsPointPillTextColor: {
			get() {
				return this.branding.rewards.pointPillTextColor;
			},
			set(value) {
				this.branding.rewards.pointPillTextColor = value;
			}
		},
		computedRewardsPointPillBorderColor: {
			get() {
				return this.branding.rewards.pointPillBorderColor;
			},
			set(value) {
				this.branding.rewards.pointPillBorderColor = value;
			}
		},
		computedRewardsPointPillBackgroundColor: {
			get() {
				return this.branding.rewards.pointPillBackgroundColor;
			},
			set(value) {
				this.branding.rewards.pointPillBackgroundColor = value;
			}
		},
		computedRewardsTextColor: {
			get() {
				return this.branding.rewards.textColor;
			},
			set(value) {
				this.branding.rewards.textColor = value;
			}
		},
		computedRewardsButtonBackgroundColor: {
			get() {
				return this.branding.rewards.buttonBackgroundColor;
			},
			set(value) {
				this.branding.rewards.buttonBackgroundColor = value;
			}
		},
		computedRewardsButtonTextColor: {
			get() {
				return this.branding.rewards.buttonTextColor;
			},
			set(value) {
				this.branding.rewards.buttonTextColor = value;
			}
		},
		computedWaysToEarnPointPillTextColor: {
			get() {
				return this.branding.waysToEarn.pointPillTextColor;
			},
			set(value) {
				this.branding.waysToEarn.pointPillTextColor = value;
			}
		},
		computedWaysToEarnPointPillBorderColor: {
			get() {
				return this.branding.waysToEarn.pointPillBorderColor;
			},
			set(value) {
				this.branding.waysToEarn.pointPillBorderColor = value;
			}
		},
		computedWaysToEarnPointPillBackgroundColor: {
			get() {
				return this.branding.waysToEarn.pointPillBackgroundColor;
			},
			set(value) {
				this.branding.waysToEarn.pointPillBackgroundColor = value;
			}
		},
		computedWayToEarnInstructionTextColor: {
			get() {
				return this.branding.waysToEarn.instructionTextColor;
			},
			set(value) {
				this.branding.waysToEarn.instructionTextColor = value;
			}
		},
		computedWaysToEarnTextColor: {
			get() {
				return this.branding.waysToEarn.textColor;
			},
			set(value) {
				this.branding.waysToEarn.textColor = value;
			}
		},
		computedWaysToEarnBorderColor: {
			get() {
				return this.branding.waysToEarn.borderColor;
			},
			set(value) {
				this.branding.waysToEarn.borderColor = value;
			}
		},
		computedWaysToEarnBackgroundColor: {
			get() {
				return this.branding.waysToEarn.backgroundColor;
			},
			set(value) {
				this.branding.waysToEarn.backgroundColor = value;
			}
		},
		computedHomePointsColor: {
			get() {
				return this.branding.home.pointsColor;
			},
			set(value) {
				this.branding.home.pointsColor = value;
			}
		},
		computedHomeBackgroundColor: {
			get() {
				return this.branding.home.backgroundColor;
			},
			set(value) {
				this.branding.home.backgroundColor = value;
			}
		},
		computedHomeTextColor: {
			get() {
				return this.branding.home.textColor;
			},
			set(value) {
				this.branding.home.textColor = value;
			}
		},
		computedHeaderBackgroundColor: {
			get() {
				return this.branding.headerStyle.backgroundColor;
			},
			set(value) {
				this.branding.headerStyle.backgroundColor = value;
			}
		},
		computedHeaderTextColor: {
			get() {
				return this.branding.headerStyle.textColor;
			},
			set(value) {
				this.branding.headerStyle.textColor = value;
			}
		},
		computedLauncherTextColor: {
			get() {
				return this.branding.launcher.styling.textColor;
			},
			set(value) {
				this.branding.launcher.styling.textColor = value;
			}
		},
		computedLauncherBackgroundColor: {
			get() {
				return this.branding.launcher.styling.backgroundColor;
			},
			set(value) {
				this.branding.launcher.styling.backgroundColor = value;
			}
		},
		computedMemberBenefitsBackgroundColor: {
			get() {
				return this.branding.guest.content.benefitsBackgroundColor || '#C9C3C3';
			},
			set(value) {
				this.branding.guest.content.benefitsBackgroundColor = value;
			}
		},
		accordionPreviewTypeName() {
			const tab = this.route.query.tab;
			console.log('accordionPreviewTypeName', this.previewType, tab);
			if (this.previewType == 'launcher') {
				return 'launcher';
			} else if (this.previewType == 'landing-page-guest') {
				return 'guestView';
			} else if (this.previewType == 'landing-page-member') {
				return 'header';
			}
		},
		defaultOpenName() {
			const tab = this.route.query.tab;
			if (tab === 'launcher') {
				return 'launcher';
			} else if (tab === 'sidebar') {
				return 'header';
			} else {
				return 'header';
			}
		},
		accordions() {
			let result = [...this.accordionsBase];
			if (this.launcherActive) {
				result.splice(1, 0, {
					key: 'launcher',
					title: "Customize your loyalty launcher",
					height: 'auto'
				});
			}
			return result;
		}
	},
	methods: {
		preventNewLines(event) {
			if (event.keyCode === 13) { // 13 is the key code for Enter
				event.preventDefault();
			}
		},
		handleBrandingChanged(newBranding) {
			this.brandingUpdateCounter++;
			this.$emit('brandingChanged', newBranding);

			if (this.route.query.tab === 'launcher') {
				this.$emit('switchPreview', 'launcher');
			}
		},
		debounce(func, delay) {
			let debounceTimer;
			return function () {
				const context = this;
				const args = arguments;
				clearTimeout(debounceTimer);
				debounceTimer = setTimeout(() => func.apply(context, args), delay);
			};
		},
		convertToHex(color) {
			// Check if color is already in hex format with or without '#'
			const hexColorPattern = /^#?([a-fA-F0-9]{3})([a-fA-F0-9]{3})?$/;
			const match = color.match(hexColorPattern);
			if (match) {
				// Reformat if '#' is missing
				return `#${match[1]}${match[2] ? match[2] : ''}`;
			}

			// Assuming the color is in RGB format, e.g., 'rgb(255, 0, 0)'
			if (/rgb/.test(color)) {
				const rgb = color.match(/\d+/g);
				return `#${rgb.map(x => {
					const hex = parseInt(x).toString(16);
					return hex.length === 1 ? '0' + hex : hex;
				}).join('')}`;
			}

			// Return original color if none of the above cases match
			return color;
		},
		handleAccordionOpen(accordionKey) {
			console.log("Switching preview to", accordionKey);
			switch (accordionKey) {
				case 'launcher':
					return this.$emit('switchPreview', 'launcher');
				case 'guestView':
					return this.$emit('switchPreview', 'landing-page-guest');
				case 'home':
					return this.$emit('switchPreview', 'landing-page-member');
				default:
					return this.$emit('switchPreview', 'landing-page-member');
			}
		},
		async uploadingHeroImage() {

			console.log("Uploading Hero Image");
			this.$emit('fileUploading', {
				type: 'hero-image'
			});

		},
		async uploadHeroImage(event) {
			this.$nextTick(() => {
				this.$emit('fileUploaded', {
					...event,
					type: 'hero-image'
				});
			});
		},
		async uploadingLogo(event) {
			console.log("Starting logo updating");
			this.$emit('fileUploading', {
				type: 'logo'
			});
		},
		async uploadLogo(event) {
			this.$nextTick(() => {
				this.$emit('fileUploaded', {
					...event,
					type: 'logo'
				});
			});
		},
	}
}
</script>

<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

.toggle-checkbox {
	opacity: 0;
	width: 0;
	height: 0;
}

.toggle-label {
	display: block;
	overflow: hidden;
	cursor: pointer;
	height: 24px;
	/* Height of the toggle */
	padding: 0;
	line-height: 24px;
	background: #bbb;
	/* Background of the toggle */
	border-radius: 24px;
	transition: background-color 0.2s;
}

.toggle-label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 2px;
	width: 20px;
	/* Width of the toggle handle */
	height: 20px;
	/* Height of the toggle handle */
	border-radius: 20px;
	background: #fff;
	/* Background of the toggle handle */
	transition: 0.2s;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox:disabled+.toggle-label {
	background-color: #eee;
	cursor: not-allowed;
}


.toggle-checkbox:checked+.toggle-label:after {
	transform: translateX(18px);
}

.toggle-checkbox:checked {
	right: 0;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox {
	right: 4px;
}

.toggle-label {
	transition: background-color 0.2s;
}

@media (max-width: 768px) {
	.flex-shrink-0 {
		width: 100%;
	}
}
</style>
