<template>
	<div class="w-128 bg-white bg-opacity-75 rounded-2xl shadow border border-violet-200"
		:class="focusReferral ? 'min-h-[750px]' : ''">
		<div class="flex items-center mt-4 mb-4 justify-center">
			<PreviewLoyaltyProgram :isCheckout="previewType === 'checkout'" />
		</div>
		<div class="flex items-center m-6 mt-2"  @click="save" :disabled="isSaving">
			<div :class="{
			'h-14 px-4 py-2 bg-gradient-to-bl from-indigo-800 to-indigo-600 rounded-full justify-center items-center gap-2 inline-flex mr-4 cursor-pointer': !isSaving,
			'h-14 px-4 py-2 rounded-full justify-center items-center gap-2 inline-flex mr-4 cursor-not-allowed opacity-50 bg-ralbutton-primary-light-deactivated': isSaving
		}" style="width: 400px;">
				<div v-if="!isSaving"
					class="text-white text-center text-3xl font-normal font-['Open Sans'] leading-none">Save Branding
				</div>
				<div v-else class="text-white text-center text-3xl font-normal font-['Open Sans'] leading-none">
					Saving...</div>
				<svg v-if="!isSaving" xmlns="http://www.w3.org/2000/svg" height="32" viewBox="0 -960 960 960" width="32"
					fill="#FFFFFF">
					<path
						d="M320-273v-414q0-17 12-28.5t28-11.5q5 0 10.5 1.5T381-721l326 207q9 6 13.5 15t4.5 19q0 10-4.5 19T707-446L381-239q-5 3-10.5 4.5T360-233q-16 0-28-11.5T320-273Zm80-207Zm0 134 210-134-210-134v268Z" />
				</svg>
				<svg v-else class="animate-spin" xmlns="http://www.w3.org/2000/svg" height="32" viewBox="0 0 24 24"
					width="32" fill="#FFFFFF">
					<path
						d="M12 22c5.421 0 10-4.579 10-10S17.421 2 12 2 2 6.579 2 12s4.579 10 10 10zm0-2C6.486 20 2 15.514 2 10S6.486 0 12 0s10 4.486 10 10-4.486 10-10 10z" />
				</svg>
			</div>
		</div>

		<div class="flex m-6 mx-10 items-center" v-if="!focusReferral && !relativeNotifications">
			<div
				class="text-center text-black text-xs font-normal font-['Open Sans'] leading-none tracking-tight mr-4 w-90%">
				PREVIEW</div>
			<select class="w-4/5 text-xs lg:text-md" v-model="selectedItem" @change="previewTypeChanged">
				<option v-if="route.query.tab === 'launcher'" value="launcher">Loyalty Launcher</option>
				<template v-else>
					<option value="landing-page-guest">Loyalty Panel (Guest Sign-In View)</option>
					<option value="landing-page-member">Loyalty Panel (Member View)</option>
				</template>
			</select>
		</div>
		<div class="flex justify-center" v-if="previewType !== 'checkout'">
			<div id="external-script-container" class="mb-6 w-full" :class="focusReferral ? 'short' : ''"></div>
			<div v-if="focusReferral" id="external-script-curtain"></div>
		</div>
	</div>
</template>

<script>
import * as Utils from '../../../client-old/utils/Utils';
import PreviewLoyaltyProgram from '../../components/PreviewLoyalty.ts.vue';
import { customerIOTrackEvent } from '../../services/customerio.js';
import { useRoute } from 'vue-router';


const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: ['branding', 'previewType', 'launcherActive', 'focusReferral', 'setIsLoggedIn', 'relativeNotifications', 'translations', 'flavor'],
	emits: ['previewTypeChanged', 'saveStatus'],
	data() {
		return {
			isDirty: false,
			selectedItem: this.previewType || 'landing-page-guest',
			hasBrandingChanged: false,
			debounceTimer: null,
			raleonSnippetLoaded: false,
			isLoading: true,
			isSaving: false,
		}
	},
	setup() {
		const route = useRoute();
		return { route };
	},
	components: {
		PreviewLoyaltyProgram
	},
	async mounted() {
		this.loadRaleonLoyaltyScript();
		this.$watch('branding', (newVal, oldVal) => {
			this.debounce(() => {
				this.postBrandingChanged();
			}, 250);
		}, { deep: true });

		if (this.focusReferral) {
			setInterval(() => {
				this.$nextTick(() => {
					const container = document.getElementById('external-script-container');
					if (container) {
						container.scrollTo(0, 100000);
					}
				});
			}, 100);
		}

	},
	computed: {},
	watch: {
		previewType(newVal, oldVal) {
			this.debounce(() => {
				if (this.selectedItem === newVal) {
					return;
				}
				this.selectedItem = newVal;
				this.previewTypeChanged();
			}, 250);
		},
	},
	methods: {
		debounce(func, delay) {
			clearTimeout(this.debounceTimer);
			this.debounceTimer = setTimeout(func, delay);
		},
		getBranding() {
			if (this.branding.useDefaults) {
				return this.branding.defaults;
			}
			return this.branding;
		},
		postBrandingChanged() {
			window.postMessage({
				type: 'raleon-branding-update',
				loggedIn: this.selectedItem == 'landing-page-member' || this.setIsLoggedIn,
				relativeNotifications: this.relativeNotifications,
				branding: JSON.parse(JSON.stringify(this.branding)),
				defaultOpenChat: this.selectedItem !== 'launcher',
			}, '*');
		},
		async reset() {
			const response = await fetch(`${URL_DOMAIN}/branding`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
					'ngrok-skip-browser-warning': true,
				}
			});

			if (!response.ok || response.status < 200 || response.status >= 300) {
				throw new Error('Failed to retrieve branding');
			}

			const data = await response.json();

			for (const key in data) {
				if (Object.prototype.hasOwnProperty.call(data, key)) {
					this.branding[key] = data[key];
				}
			}

		},
		async save() {
			if (this.isSaving) {
				return;
			}
			this.isSaving = true;
			this.$emit('saveStatus', { isSaving: true });
			const response = await fetch(`${URL_DOMAIN}/branding?flavor=${this.flavor || ''}`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				body: JSON.stringify(this.branding),
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				}
			});

			if (!response.ok || response.status < 200 || response.status >= 300) {
				this.$emit('saveStatus', { type: 'fail', message: "We couldn't make your branding live. Please try again." });
				this.isSaving = false;
				throw new Error('Failed to save branding');
			}
			this.$emit('saveStatus', { type: 'success', message: 'Branding saved.' });
			customerIOTrackEvent('Branding Updated');


			const dirtyTranslations = (this.translations || [])?.filter?.(x => x.value !== x.originalValue);
			console.log('dirtyTranslations', dirtyTranslations);
			const translationResponse = await fetch(`${URL_DOMAIN}/translation-overrides`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				body: JSON.stringify(dirtyTranslations),
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				}
			});

			if (!translationResponse.ok || translationResponse.status < 200 || translationResponse.status >= 300) {
				this.$emit('saveStatus', { type: 'fail', message: "We couldn't make your branding live. Please try again." });
				this.isSaving = false;
				throw new Error('Failed to save translations');
			}
			this.isSaving = false;

		},
		loadRaleonLoyaltyScript() {
			const isProd = location.hostname === 'app.raleon.io';
			const scriptSrc = isProd ?
				'https://dqpqjbq51w8fz.cloudfront.net/raleon-loyalty.min.js' :
				'https://d2gcw94pe4tjqh.cloudfront.net/raleon-loyalty.min.js'
			const script = document.createElement('script');
			script.src = scriptSrc;
			//script.src = 'http://localhost:8080/output/raleon-loyalty.min.js'
			script.onload = () => this.initializeRaleonLoyalty();
			document.getElementById('external-script-container').appendChild(script);
			this.isLoading = false;
		},
		initializeRaleonLoyalty() {
			const loyaltyParams = {
				orgId: localStorage.getItem('userOrgId') || 1,
				enableQuests: true,
				defaultOpenChat: this.selectedItem !== 'launcher',
				userLoggedIn: this.selectedItem === 'landing-page-member' || this.setIsLoggedIn,
				relativeNotifications: this.relativeNotifications,
				customerId: '123',
				branding: this.branding,
				parentElementId: 'external-script-container',
			};
			window.setupRaleonLoyalty(loyaltyParams);
		},
		previewTypeChanged() {
			this.$emit('previewTypeChanged', this.selectedItem);
			window.postMessage({
				type: 'raleon-view-changed',
				view: this.selectedItem,
			}, '*');
		},
	},
}

export const branding = {
	"useDefaults": false,
	"logoUrl": "https://d3q4ufbgs1i4ak.cloudfront.net/23e5257a-dfe1-414a-afab-cbd31b869957.png",
	"header": "Smart Shoes Loyalty Program",
	"defaults": {
		"colors": {
			"useBackgroundColor": true,
			"backgroundColor": "#000000",
			"textColor": "#ffffff",
			"buttonTextColor": "#000000",
			"buttonBackgroundColor": "#ffffff",
			"linkColor": "#aaaaaa",
			"accentColor": {
				"from": "#f0abfc",
				"to": "#f87171"
			},
			"secondaryColor": "#BBF7D0",
			"warningColor": "#EFC030"
		}
	},
	"colors": {
		"useBackgroundColor": true,
		"backgroundColor": "#bd3232",
		"textColor": "#ffffff",
		"buttonTextColor": "#000000",
		"buttonBackgroundColor": "#ffffff",
		"linkColor": "#aaaaaa",
		"accentColor": {
			"from": "#d900ff",
			"to": "#000000"
		},
		"secondaryColor": "#00ff59",
		"warningColor": "#000000"
	},
	"launcher": {
		"logoUrl": "",
		"launcherPosition": "left",
		"callToAction": "Snowboarders Central",
		"styling": {
			"textColor": "#ffffff",
			"backgroundColor": "#399d72"
		}
	},
	"guest": {
		"heroImageUrl": "",
		"content": {
			"title": "JOIN COMMUNITY",
			"subtitle": "You receive rewards by completing ways to earn and making purchases. Collect XP to move across levels or spend it on extra rewards!",
			"benefitsTitle": "Member Benefits",
			"benefitsList": []
		}
	},
	"member": {
		"content": {
			"rewardsTitle": "Your Rewards",
			"wteTitle": "Ways to Earn",
			"rewardShopTitle": "Available Rewards"
		}
	}
}
</script>

<style scoped>
.loyalty-ui {
	max-width: 380px;
}

#external-script-container.short {
	max-height: 650px;
	overflow-y: hidden;
}

#external-script-curtain {
  position: absolute;
  width: 414px;
  height: 375px;
  background: linear-gradient(to bottom, rgba(255,255,255,1) 0%,rgba(255,255,255,0) 100%);
}
</style>
