<template>
	<div class="w-[100%] lg:w-auto">
		<AccordionGroup
			:accordions="filteredAccordions"
			:preview-type="accordionPreviewTypeName"
			@open="handleAccordionOpen">
			<template v-slot:colors>
				<div class="p-4">
					<div>
						<div
							class="opacity-75 text-slate-800 text-base font-semibold font-['Inter'] leading-normal tracking-wide">
							Set Background Color</div>
						<div class="mt-3 mb-3 text-slate-800 text-sm font-normal font-['Inter']">Used in the
							background of all parts of the Loyalty Panel.</div>
						<LvColorPicker
							class="mx-2 md:mx-6 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
							:key="'background-color-picker-' + brandingUpdateCounter" label="Background Color"
							v-model="computedBackgroundColor" :clearable="false" :value="computedBackgroundColor"
							:bottomBar="false" />
						<div
							class="mt-4 opacity-75 text-slate-800 text-base font-semibold font-['Inter'] leading-normal tracking-wide">
							Set Action Colors</div>
						<div class="mt-2 mb-3 text-slate-800 text-sm font-normal font-['Inter']">Used in
							buttons, links, and text.</div>
						<div class="mt-2 mx-2 md:mx-6 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
							Primary Colors</div>
						<LvColorPicker
							class="mx-2 md:mx-8 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
							label="Text Color" v-model="computedTextColor" :clearable="false"
							:key="'background-color-textColor-' + brandingUpdateCounter" :value="computedTextColor"
							:bottomBar="false" />

						<div class="mt-4">
							<LvColorPicker
								class="mx-2 md:mx-8 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
								label="Button Background Color" v-model="computedButtonBackgroundColor" :clearable="false"
								:key="'background-color-button-backgroundColor-' + brandingUpdateCounter"
								:value="computedButtonBackgroundColor" :bottomBar="false" />
						</div>

						<div class="mt-4">
							<LvColorPicker
								class="mx-2 md:mx-8 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
								label="Button Text Color" v-model="computedButtonTextColor" :clearable="false"
								:key="'background-color-buttonTextColor-' + brandingUpdateCounter"
								:value="computedButtonTextColor" :bottomBar="false" />
						</div>

						<div class="mt-4">
							<LvColorPicker
								class="mx-2 md:mx-8 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
								label="Link Text Color" :key="'background-color-linkColor-' + brandingUpdateCounter"
								v-model="computedLinkColor" :clearable="false" :value="computedLinkColor"
								:bottomBar="false" />
						</div>

						<div class="mt-4 mx-2 md:mx-6 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
							Accent Colors</div>
						<div class="mt-3 mx-2 md:mx-8">From:
							<LvColorPicker
								class="mx-3 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
								:key="'background-color-accentColor-from-' + brandingUpdateCounter"
								v-model="computedAccentColorFrom" :clearable="false" :value="computedAccentColorFrom"
								:bottomBar="false" />
						</div>
						<div class="mt-3 mx-2 md:mx-8">To:
							<LvColorPicker
								class="mx-3 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
								:key="'background-color-accentColor-to-' + brandingUpdateCounter"
								v-model="computedAccentColorTo" :clearable="false" :value="computedAccentColorTo"
								:bottomBar="false" />
						</div>

						<div class="mt-4 mx-2 md:mx-6 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
							Secondary Color</div>
						<div class="mt-4">
							<LvColorPicker
								class="mx-2 md:mx-8 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
								:key="'background-color-secondaryColor-' + brandingUpdateCounter"
								v-model="computedSecondaryColor" :clearable="false" :value="computedSecondaryColor"
								:bottomBar="false" />
						</div>

						<div class="mt-4 mx-6 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
							Warning Color</div>
						<div class="mt-4">
							<LvColorPicker
								class="mx-2 md:mx-8 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
								:key="'background-color-warningColor-' + brandingUpdateCounter"
								v-model="computedWarningColor" :clearable="false" :value="computedWarningColor"
								:bottomBar="false" />
						</div>

						<div class="mt-4 mx-6 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
							Disable Image Background Blur
						</div>
						<div class="mt-4 mx-6 text-black text-sm font-normal font-['Inter']">Turn this on to disable image background blur, this is useful when using icons for reward or wte images. </div>
						<div class="mt-4 mx-6">
							<div
								class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in">
								<input type="checkbox" id="toggle"
									class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
									v-model="branding.disableBackgroundBlur" />
								<label for="toggle"
									class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
							</div>
						</div>

					</div>
				</div>
			</template>
			<template v-slot:launcher>
				<div class="ml-4 mb-8 text-black text-sm font-normal font-['Inter']">The loyalty launcher is what a customer
					or guest would click on to open the Loyalty Panel. <LearnMoreText text="What's a Loyalty Panel?"
						url="https://docs.raleon.io/docs/the-loyalty-panel"></LearnMoreText>
				</div>
				<div class="mx-4">
					<div class="mt-8 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
						Upload your logo</div>
					<div class="mt-3 text-black text-sm font-normal font-['Inter']">We use your logo in
						different parts of your loyalty experience. It should be a 72x72 square and under 50kb to keep site
						performance high.</div>
					<div class="flex mt-6">
						<img class="w-16 h-16 rounded-3xl border border-black" :src="branding.logoUrl"
							v-if="branding.logoUrl != null" />
						<img class="w-16 h-16 rounded-3xl border border-black"
							src="http://localhost:3030/client/images/RaleonSharkAvatar.png"
							v-if="branding.logoUrl == null" />

						<div class="flex flex-col flex-grow items-start justify-center pl-12">
							<ImageUpload @imageUploading="uploadingLogo" @imageSelected="uploadLogo"
								:show-close-button="true" container-classes="absolute"
								popover-classes="fixed w-[100vw] md:w-[100vw] h-[100vh] max-h-[100vh] left-0 top-0 lg:max-h-[50vw] lg:absolute lg:right-auto lg:w-[30vw] lg:h-auto" />
						</div>
					</div>

					<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
						Launcher size
					</div>
					<div class="mt-1 text-black text-sm font-normal font-['Inter']">Set the launcher size below.</div>
					<!-- <div
						class="w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"> -->
						<div class="w-full self-stretch justify-start items-center gap-2 flex mt-1">
							<select
								class="mb-2 w-52 h-10 px-2 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex"
								v-model="branding.launcher.size">
								<option
									v-for="size in this.launcherSizes"
									:value="size.value"
								>
									{{ size.label }}
								</option>
							</select>
						</div>
					<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
						Rounded Corners
					</div>
					<div class="mt-1 text-black text-sm font-normal font-['Inter']">Set the launcher corner border radius below.</div>
					<!-- <div
						class="w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"> -->
						<div class="w-full self-stretch justify-start items-center gap-2 flex mt-1">
							<select
								class="mb-2 w-52 h-10 px-2 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex"
								v-model="branding.launcher.radius">
								<option
									v-for="radius in this.launcherRadii"
									:value="radius.value"
								>
									{{ radius.label }}
								</option>
							</select>
						</div>
					<!-- </div> -->
					<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
						Launcher position</div>
					<div class="mt-1 text-black text-sm font-normal font-['Inter']">Adjust which side
						of your store the launcher appears on for computers, tablets, and mobile.</div>
						<select
							class="mb-2 w-52 h-10 px-2 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex"
							v-model="branding.launcher.launcherPosition">
							<option
								v-for="position in [
									{value: 'left', label: 'Bottom Left'},
									{value: 'right', label: 'Bottom Right'},
									{value: 'middle-left', label: 'Middle Left'},
									{value: 'middle-right', label: 'Middle Right'},
								]"
								:value="position.value"
							>
								{{ position.label }}
							</option>
						</select>
					<div class="mt-2">
						<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Loyalty
							call to action</div>
						<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">What a customer sees on the
							launcher, as shown in the preview above. Be short and succinct. You have a
							max of 40 characters.</div>
						<div
							class="mb-2 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input type="text"
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									maxlength="40" v-model="branding.launcher.callToAction" />
							</div>
						</div>

						<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Loyalty
							Launcher Text Color</div>
						<div class="mt-4">
							<LvColorPicker
								class="mx-2 md:mx-6 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
								:key="'background-launcher-styling-textColor-' + brandingUpdateCounter"
								v-model="computedLauncherTextColor" :clearable="false" :value="computedLauncherTextColor"
								:bottomBar="false" />
						</div>

						<div class="mt-4  text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
							Loyalty Launcher Background Color</div>
						<div class="mt-4 mb-4">
							<LvColorPicker
								class="mx-2 md:mx-6 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
								:key="'background-launcher-styling-backgroundColor-' + brandingUpdateCounter"
								v-model="computedLauncherBackgroundColor" :clearable="false"
								:value="computedLauncherBackgroundColor" :bottomBar="false" />
						</div>
					</div>
				</div>
			</template>
			<template v-slot:guest>
				<div class="ml-4 mb-8 text-black text-sm font-normal font-['Inter']">The guest view is what a non-signed in
					customer (non-member) would see about your loyalty program.</div>
				<div class="m-4">
					<div class="mb-8">
						<div class="mb-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Loyalty Panel
							Header</div>
						<div class="mb-3 text-black text-sm font-normal font-['Inter']">This will show in the header when
							your shopper opens the Loyalty Panel.</div>
						<div
							class="w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input type="text"
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									maxlength="40" v-model="branding.header" />
							</div>
						</div>
					</div>

					<div class="mb-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
						Guest Banner</div>
					<div class="mb-3 text-black text-sm font-normal font-['Inter']">The guest banner photo,
						art, or animation. It goes at the top of your Loyalty Panel and brings attention and style to what
						your
						guests see before becoming a member. Ideal image is a 5:3 (1000 x 600 px) aspect ratio. Note some resizing does occur to fit smaller screens.
					</div>

					<div class="flex flex-col md:flex-row mt-6 space-y-4 md:space-y-0 md:space-x-4">
						<div class="md:flex md:flex-shrink-0">
							<img class="rounded-3xl border border-black object-center h-[240px] w-[420px] object-cover"
								:src="branding.guest.heroImageUrl || 'https://raleoncdn.s3.us-east-1.amazonaws.com/DefaultBanner.jpg'" />
						</div>
						<div class="flex flex-col items-center md:items-start justify-center w-full">
							<ImageUpload :aspectRatio="1.75" @imageUploading="uploadingHeroImage" @imageSelected="uploadHeroImage"
								:show-close-button="true" container-classes="absolute"
								popover-classes="w-full md:w-auto h-auto md:max-h-[100vh] lg:max-h-[50vw] lg:absolute lg:right-auto lg:w-[30vw] lg:h-auto" />
						</div>
					</div>


					<div class="mt-8 ml-2">
						<div class="mb-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Title
						</div>
						<div class="mb-3 text-black text-sm font-normal font-['Inter']">Be short and succinct. You have a
							max of 40 characters.</div>
						<div
							class="mb-8 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									maxlength="40" v-model="branding.guest.content.title" />
							</div>
						</div>

						<div class="mb-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Subtitle
						</div>
						<div class="mb-3 text-black text-sm font-normal font-['Inter']">This can be a little longer description of why a person should sign up!</div>
						<div
							class="w-full mb-8 h-28 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-start gap-2 inline-flex">
							<div class="w-full h-full self-stretch justify-start items-center gap-2 flex">
								<textarea
									class="no-focus-outline border-none w-full h-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal border-none p-0 resize-none focus:outline-none"
									v-model="branding.guest.content.subtitle"
									@input="branding.guest.content.subtitle = $event.target.value.replace(/\n/g, '')"></textarea>
							</div>
						</div>

						<div class="mb-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
							Member Benefits Title
						</div>
						<div class="mb-3 text-black text-sm font-normal font-['Inter']">Be short and succinct. You have a
							max of 40 characters.</div>
						<div
							class="mb-8 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									maxlength="40"
									v-model="branding.guest.content.benefitsTitle" />
							</div>
						</div>

						<div class="mb-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
							Member Benefits Background Color
						</div>
						<div class="mb-3 text-black text-sm font-normal font-['Inter']">Choose a background color for the benefits section.</div>
						<LvColorPicker
							class="justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter'] mb-3"
							:key="'benefits-backgroundColor-' + brandingUpdateCounter"
							v-model="computedMemberBenefitsBackgroundColor"
							:clearable="false"
							:value="computedMemberBenefitsBackgroundColor"
							:bottomBar="false"
						/>
						<div class="mb-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Hide Join Button</div>
						<div class="mb-3 text-black text-sm font-normal font-['Inter']">This is useful for new user accounts that don't need a join and sign in option</div>
						<div
							class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in">
							<input type="checkbox" id="hide-join-btn-toggle"
								class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
								v-model="branding.guest.hideJoinButton" />
							<label for="hide-join-btn-toggle"
								class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
						</div>

						<div class="my-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
							Custom Styling
						</div>
						<div class="mb-3 text-black text-sm font-normal font-['Inter']">Add custom css to override default styles</div>
						<textarea class="w-full h-28 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-start gap-2 inline-flex"
							v-model="branding.custom"
							@input="branding.custom = $event.target.value.replace(/\n/g, '')">
						</textarea>

						<!--
						<div class="mb-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Benefits
							Title</div>
						<div class="mb-3 text-black text-sm font-normal font-['Inter']">Be short and succinct. You have a
							max of 40 characters.</div>
						<div
							class="mb-8 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									maxlength="40" v-model="branding.guest.content.benefitsTitle" />
							</div>
						</div>
						-->
					</div>
				</div>
			</template>
			<template v-slot:member>
				<div class="ml-4 mb-8 text-black text-sm font-normal font-['Inter']">The member view is what a signed in
					customer (member) would see in the loyalty program.
					<br />
					The ways to earn and rewards in the preview are for preview purposes only and do not represent your
					store.
				</div>
				<div class="m-4">

					<div class="mt-8">
						<div class="mb-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Show History</div>
						<div class="mb-3 text-black text-sm font-normal font-['Inter']">This will turn on the history tab such that shoppers can see
							a log of points earned and redeemed. (English) only right now.</div>
						<div
							class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in">
							<input type="checkbox" id="show-history-toggle"
								class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
								v-model="branding.member.enableHistory" />
							<label for="show-history-toggle"
								class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
						</div>


						<!-- <div class="mt-6 mb-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Title for
							Rewards</div>
						<div class="mb-3 text-black text-sm font-normal font-['Inter']">These are rewards a member has
							redeemed but not used yet.</div>
						<div
							class="mb-8 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									maxlength="40" v-model="branding.member.content.rewardsTitle" />
							</div>
						</div> -->

						<div class="mt-6 mb-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Title for
							Ways to Earn</div>
						<div class="mb-3 text-black text-sm font-normal font-['Inter']">These are the activities a member
							can complete to earn rewards.</div>
						<div
							class="mb-8 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									maxlength="40" v-model="branding.member.content.wteTitle" />
							</div>
						</div>

						<div class="mb-3 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Title for
							rewards a member can get</div>
						<div class="mb-3 text-black text-sm font-normal font-['Inter']">These are rewards a member can spend
							points on to get.</div>
						<div
							class="mb-8 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									maxlength="40" v-model="branding.member.content.rewardShopTitle" />
							</div>
						</div>
					</div>
				</div>
			</template>
		</AccordionGroup>
	</div>
</template>

<script>
import * as Utils from '../../../client-old/utils/Utils';
import AccordionGroup from '../../components/AccordionGroup.ts.vue';
import LearnMoreText from '../../components/LearnMoreText.ts.vue';
import SuperDropdown from '../../components/SuperDropdown.ts.vue';
import ImageUpload from '../../components/ImageUpload.ts.vue';
import LvColorPicker from 'lightvue/color-picker';
import { useRoute } from 'vue-router';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: [
		'branding',
		'previewType',
		'isLogoUploading',
		'isHeroImageUploading',
		'launcherActive',
	],
	emits: ['fileUploaded', 'switchPreview', 'fileUploading', 'brandingChanged'],
	created() {
		this.debouncedBrandingChanged = this.debounce(this.handleBrandingChanged, 1000);
	},
	setup() {
		const route = useRoute();
		return { route };
	},
	data() {
		return {
			accordionsBase: [
				{ key: 'colors', title: "Set your brand colors", height: 'auto' },
				{ key: 'guest', title: "Customize guest view", height: 'auto' },
				{ key: 'member', title: "Customize member view", height: 'auto' }
        	],
			launcherSizes: [
				{label: 'Small Logo Only', value: 'small', selected: true},
				{label: 'Small Logo with Text', value: 'small-text'},
				{label: 'Small Text Only', value: 'small-text-only'},
				{label: 'Medium Logo Only', value: 'medium'},
				{label: 'Medium Logo with Text', value: 'medium-text'},
				{label: 'Medium Text Only', value: 'medium-text-only'},
				{label: 'Large Logo Only', value: 'large'},
				{label: 'Large Logo with Text', value: 'large-text'},
				{label: 'Large Text Only', value: 'large-text-only'},
			],
			launcherRadii: [
				{label: 'None', value: '0px'},
				{label: 'Small', value: '5px'},
				{label: 'Medium', value: '10px'},
				{label: 'Large', value: '20px'},
				{label: 'Extra large', value: '40px'},
				{label: 'Full', value: '50%', selected: true},
			],
			debouncedBrandingChanged: null,
			brandingUpdateCounter: 0
		}
	},
	watch: {
		'branding.colors.backgroundColor': function (newValue) {
			this.branding.colors.backgroundColor = this.convertToHex(newValue);
		},
		'branding.colors.buttonBackgroundColor': function (newValue) {
			this.branding.colors.buttonBackgroundColor = this.convertToHex(newValue);
		},
		'branding.colors.buttonTextColor': function (newValue) {
			this.branding.colors.buttonTextColor = this.convertToHex(newValue);
		},
		'branding.colors.linkColor': function (newValue) {
			this.branding.colors.linkColor = this.convertToHex(newValue);
		},
		'branding.colors.accentColor.from': function (newValue) {
			this.branding.colors.accentColor.from = this.convertToHex(newValue);
		},
		'branding.colors.accentColor.to': function (newValue) {
			this.branding.colors.accentColor.to = this.convertToHex(newValue);
		},
		'branding.colors.secondaryColor': function (newValue) {
			this.branding.colors.secondaryColor = this.convertToHex(newValue);
		},
		'branding.colors.warningColor': function (newValue) {
			this.branding.colors.warningColor = this.convertToHex(newValue);
		},
		'branding.launcher.styling.textColor': function (newValue) {
			this.branding.launcher.styling.textColor = this.convertToHex(newValue);
		},
		'branding.launcher.styling.backgroundColor': function (newValue) {
			this.branding.launcher.styling.backgroundColor = this.convertToHex(newValue);
		},
		'branding.colors.textColor': function (newValue) {
			this.branding.colors.textColor = this.convertToHex(newValue);
		},

		'branding': {
			handler(newVal) {
				this.debouncedBrandingChanged(newVal);
			},
			deep: true
		},
	},
	components: {
		AccordionGroup,
		SuperDropdown,
		ImageUpload,
		LearnMoreText,
		LvColorPicker
	},
	async mounted() {},
	computed: {
		filteredAccordions() {
			const tab = this.route.query.tab;

			if (tab === 'launcher') {
				return [{
					key: 'launcher',
					title: "Customize your loyalty launcher",
					height: 'auto',
					isOpen: true // Force this accordion to be open
				}];
			} else if (tab === 'sidebar') {
				return this.accordionsBase;
			} else {
				let result = [...this.accordionsBase];
				if (this.launcherActive) {
				result.splice(1, 0, {
					key: 'launcher',
					title: "Customize your loyalty launcher",
					height: 'auto'
				});
				}
				return result;
			}
		},

		accordionPreviewTypeName() {
			const tab = this.route.query.tab;
			if (tab === 'launcher') {
				return 'launcher';
			} else if (this.previewType == 'launcher') {
				return 'launcher';
			} else if (this.previewType == 'landing-page-guest') {
				return 'guest';
			} else if (this.previewType == 'landing-page-member') {
				return 'member';
			}
		},
		computedBackgroundColor: {
			get() {
				return this.branding.colors.backgroundColor;
			},
			set(value) {
				this.branding.colors.backgroundColor = value;
			}
		},
		computedButtonBackgroundColor: {
			get() {
				return this.branding.colors.buttonBackgroundColor;
			},
			set(value) {
				this.branding.colors.buttonBackgroundColor = value;
			}
		},
		computedButtonTextColor: {
			get() {
				return this.branding.colors.buttonTextColor;
			},
			set(value) {
				this.branding.colors.buttonTextColor = value;
			}
		},
		computedTextColor: {
			get() {
				return this.branding.colors.textColor;
			},
			set(value) {
				this.branding.colors.textColor = value;
			}
		},
		computedLinkColor: {
			get() {
				return this.branding.colors.linkColor;
			},
			set(value) {
				this.branding.colors.linkColor = value;
			}
		},
		computedAccentColorFrom: {
			get() {
				return this.branding.colors.accentColor.from;
			},
			set(value) {
				this.branding.colors.accentColor.from = value;
			}
		},
		computedAccentColorTo: {
			get() {
				return this.branding.colors.accentColor.to;
			},
			set(value) {
				this.branding.colors.accentColor.to = value;
			}
		},
		computedSecondaryColor: {
			get() {
				return this.branding.colors.secondaryColor;
			},
			set(value) {
				this.branding.colors.secondaryColor = value;
			}
		},
		computedWarningColor: {
			get() {
				return this.branding.colors.warningColor;
			},
			set(value) {
				this.branding.colors.warningColor = value;
			}
		},
		computedLauncherTextColor: {
			get() {
				return this.branding.launcher.styling.textColor;
			},
			set(value) {
				this.branding.launcher.styling.textColor = value;
			}
		},
		computedLauncherBackgroundColor: {
			get() {
				return this.branding.launcher.styling.backgroundColor;
			},
			set(value) {
				this.branding.launcher.styling.backgroundColor = value;
			}
		},
		computedMemberBenefitsBackgroundColor: {
			get() {
				return this.branding.guest.content.benefitsBackgroundColor || '#C9C3C3';
			},
			set(value) {
				this.branding.guest.content.benefitsBackgroundColor = value;
			}
		},
		accordionPreviewTypeName() {
			if (this.previewType == 'launcher') {
				return 'launcher';
			} else if (this.previewType == 'landing-page-guest') {
				return 'guest';
			} else if (this.previewType == 'landing-page-member') {
				return 'member';
			}
		},
		accordions() {
			let result = [...this.accordionsBase];
			if (this.launcherActive) {
				result.splice(1, 0, {
					key: 'launcher',
					title: "Customize your loyalty launcher",
					height: 'auto'
				});
			}
			return result;
		}
	},
	methods: {
		preventNewLines(event) {
			if (event.keyCode === 13) { // 13 is the key code for Enter
				event.preventDefault();
			}
		},
		handleBrandingChanged(newBranding) {
			this.brandingUpdateCounter++;
			this.$emit('brandingChanged', newBranding);
		},
		debounce(func, delay) {
			let debounceTimer;
			return function () {
				const context = this;
				const args = arguments;
				clearTimeout(debounceTimer);
				debounceTimer = setTimeout(() => func.apply(context, args), delay);
			};
		},
		convertToHex(color) {
			// Check if color is already in hex format with or without '#'
			const hexColorPattern = /^#?([a-fA-F0-9]{3})([a-fA-F0-9]{3})?$/;
			const match = color.match(hexColorPattern);
			if (match) {
				// Reformat if '#' is missing
				return `#${match[1]}${match[2] ? match[2] : ''}`;
			}

			// Assuming the color is in RGB format, e.g., 'rgb(255, 0, 0)'
			if (/rgb/.test(color)) {
				const rgb = color.match(/\d+/g);
				return `#${rgb.map(x => {
					const hex = parseInt(x).toString(16);
					return hex.length === 1 ? '0' + hex : hex;
				}).join('')}`;
			}

			// Return original color if none of the above cases match
			return color;
		},
		handleAccordionOpen(accordionKey) {
			switch (accordionKey) {
				case 'launcher':
					return this.$emit('switchPreview', 'launcher');
				case 'guest':
					return this.$emit('switchPreview', 'landing-page-guest');
				case 'member':
					return this.$emit('switchPreview', 'landing-page-member');
				default:
					break;
			}
		},
		async uploadingHeroImage() {

			console.log("Uploading Hero Image");
			this.$emit('fileUploading', {
				type: 'hero-image'
			});

		},
		async uploadHeroImage(event) {
			this.$nextTick(() => {
				this.$emit('fileUploaded', {
					...event,
					type: 'hero-image'
				});
			});
		},
		async uploadingLogo(event) {
			console.log("Starting logo updating");
			this.$emit('fileUploading', {
				type: 'logo'
			});
		},
		async uploadLogo(event) {
			this.$nextTick(() => {
				this.$emit('fileUploaded', {
					...event,
					type: 'logo'
				});
			});
		},
	}
}
</script>

<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}
.toggle-checkbox {
	opacity: 0;
	width: 0;
	height: 0;
}

.toggle-label {
	display: block;
	overflow: hidden;
	cursor: pointer;
	height: 24px;
	/* Height of the toggle */
	padding: 0;
	line-height: 24px;
	background: #bbb;
	/* Background of the toggle */
	border-radius: 24px;
	transition: background-color 0.2s;
}

.toggle-label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 2px;
	width: 20px;
	/* Width of the toggle handle */
	height: 20px;
	/* Height of the toggle handle */
	border-radius: 20px;
	background: #fff;
	/* Background of the toggle handle */
	transition: 0.2s;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}
.toggle-checkbox:disabled+.toggle-label {
	background-color: #eee;
	cursor: not-allowed;
}


.toggle-checkbox:checked+.toggle-label:after {
	transform: translateX(18px);
}

.toggle-checkbox:checked {
	right: 0;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox {
	right: 4px;
}

.toggle-label {
	transition: background-color 0.2s;
}
</style>
