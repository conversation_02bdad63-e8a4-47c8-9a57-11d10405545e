
<template>
	<div class="w-full">
		<div class="flex text-ralsecondary-start text-2xl font-normal font-['Inter'] mb-1 ml-4 mr-4">
			<div
				class="w-full md:w-[650px] bg-white bg-opacity-75 rounded-2xl border border-violet-200 p-2 md:p-7"
				:class="{'mb-10': isSingleReward}">
				<div v-if="!isPoints" class="flex text-ralsecondary-start text-2xl font-normal font-['Inter'] mb-4">
					<span>Choose a <span v-if="!isPerkReward">Reward</span><span v-if="isPerkReward">Perk</span></span>
					<div class="ml-auto">
						<a
						class="pl-2 pr-4 text-ralprimary-main text-sm justify-start font-semibold items-center gap-1 inline-flex cursor-pointer hover:underline ml-2"
						:href="isPerkReward ? 'https://docs.raleon.io/docs/setting-up-vip' : 'https://docs.raleon.io/docs/setting-up-a-new-campaign'"
						target="_blank"
							>
							<svg class="w-4 h-4 fill-current text-ralrpimary-ultralight mr-2 opacity-50" viewBox="0 0 16 16">
							<path d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 12c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1zm1-3H7V4h2v5z" />
							</svg>
							How do I setup a <span v-if="!isPerkReward">reward</span><span v-if="isPerkReward">perk</span>?
						</a>
					</div>
				</div>
				<div v-else class="flex text-ralsecondary-start text-2xl font-normal font-['Inter'] mb-4">
					<span>Points Program</span>
				</div>
				<div v-if="this.isLoading || loading" class="w-full flex items-start justify-center flex-col mt-4 animate-pulse mt-2">
						<div class="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-full"></div>
						<div class="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-full mt-4"></div>
				</div>
				<div v-if="!this.isLoading && !loading" class="flex flex-wrap justify-start gap-2">
					<div
						v-if="!this.isPoints"
						v-for="(reward, index) in this.rewardOptions"
						:key="index"
						class="cursor-not-allowed flex items-center justify-center gap-4 w-[100px] h-[100px] md:w-[140px] md:h-[116px] rounded-md border-2"
						:class="{
							'border-ralprimary-light': reward.type == this.editingReward?.type,
							'border-ralgray-light': reward.type !== this.editingReward?.type,
							'shadow-xl': reward.type == this.editingReward?.type,
							'shadow-sm': reward.type !== this.editingReward?.type,
							'border-none': !reward.enabled,
							'bg-ralbutton-primary-light-deactivated': !reward.enabled,
							'cursor-pointer': reward.enabled
						}"
						:disabled="reward.enabled === false"
						@click="reward.enabled && switchRewardType(reward.type)"
					>

						<div class="flex flex-col items-center">
							<WTETypeIcon :icon-type="reward.imageSlotKey"></WTETypeIcon>
							<div class="text-black text-sm font-semibold font-['Inter'] text-center w-full px-2">
								{{ reward.title }}
							</div>
						</div>
						<div v-if="!reward.enabled" class="absolute top-0 right-0 p-1">
							<Tooltip bg="dark" size="md" position="left" maxWidth="50px">
								<div class="text-xs text-white">
									You need a higher plan for this.
								</div>
							</Tooltip>
						</div>
					</div>
				</div>

				<div class="mt-7" v-if="this.editingReward?.type">
					<template v-if="this.editingReward?.type != 'free-shipping' && this.editingReward?.type != 'static-text'">
						<div class="flex flex-col justify-start">
							<div class="flex items-center justify-ste">
								<div class="text-ralsecondary-start text-sm font-bold font-['Inter'] mr-2"><span v-if="!isPerkReward">Reward</span><span v-if="isPerkReward">Perk</span></div>
								<Tooltip bg="dark" size="md" position="bottom">
									<div class="text-xs whitespace-nowrap text-white">Specify the details of the <span v-if="!isPerkReward">reward</span><span v-if="isPerkReward">perk</span> for the customer.</div>
								</Tooltip>
							</div>
							<div class="text-ralsecondary-start text-sm font-normal font-['Inter'] mt-2">
								{{ this.editingReward?.subtitle }}
							</div>
						</div>

						<div class="my-3">
							<div class="flex flex-col sm:flex-row items-center justify-between rounded-xl border border-ralbackground-light-line mt-5 p-3">
								<div class="text-ralgray-dark text-sm font-medium font-['Inter']">
									{{  processedLabel }}
									<span class="text-xs text-rose-500">*</span>
								</div>
								<div class="flex-grow"></div>
								<div
									class="w-full sm:w-36 h-10 px-2 py-3 mt-2 bg-white rounded-lg border flex justify-center items-center gap-2.5"
									:class="{
										'border-ralerror-dark': v$.editingReward.amount.$dirty && v$.editingReward.amount.$errors.length,
										'border-ralbackground-dark-line border-opacity-20': !v$.editingReward.amount.$errors.length || !v$.editingReward.amount.$dirty
									}"
								>
									<div class="justify-start items-center gap-2 inline-flex">
										<input
											v-if="this.editingReward?.type == 'static-text-click'"
											type="text"
											class="no-focus-outline w-full sm:w-36 text-neutral-700 text-base font-medium font-['Inter'] appearance-none outline-none bg-transparent border-none"
											v-model="v$.editingReward.externalLink.$model"
										/>
										<input
											v-else-if="!isTargetingAProduct || shouldShowNewLineForProduct"
											type="number"
											class="no-focus-outline w-full sm:w-36 text-neutral-700 text-base font-medium font-['Inter'] appearance-none outline-none bg-transparent border-none"
											v-model="v$.editingReward.amount.$model"
										/>

										<div
											v-else
											class="no-focus-outline w-full sm:w-36 text-neutral-700 text-base font-medium font-['Inter'] appearance-none outline-none bg-transparent border-none placeholder-gray-300"
										>
											<LvDropdown
												class="border-neutral-700 border-opacity-20"
												v-model="v$.editingReward.externalId.$model"
												@update:model-value="(newVal) => onDropdownChange(newVal, editingReward.type)"
												optionLabel="name"
												optionValue="code"
												placeholder="Select Option"
												emptyFilterMessage="No result found"
												icon-right="light-icon-arrow-down-circle"
												filterPlaceholder="Search"
												:options="dropdownOptions"
												:filter="true"
												:bottom-bar="true"
											/>
										</div>
									</div>
								</div>

								<div v-if="editingReward.showPointsEquivalency" class="ml-2">= $<span v-if="editingReward.amount">{{ editingReward.amount / earn.campaign?.loyaltyProgram?.loyaltyCurrencies?.[0]?.conversionToUSD}}</span>
								</div>

							</div>
							<div v-if="shouldShowNewLineForProduct" class="flex flex-col sm:flex-row items-center justify-between rounded-xl border border-ralbackground-light-line mt-5 p-3">
								<div class="text-ralgray-dark text-sm font-medium font-['Inter']">
									Select A Product
									<span class="text-xs text-rose-500">*</span>
								</div>
								<div class="flex-grow"></div>
								<div v-if="loadingProducts" class="text-ralgray-dark text-sm font-medium font-['Inter'] mr-3">
									Loading Products...
								</div>
								<div v-else
									class="w-full sm:w-36 h-10 px-2 py-3 mt-2 bg-white rounded-lg border flex justify-center items-center gap-2.5"
									:class="{
										'border-ralerror-dark': v$.editingReward.amount.$dirty && v$.editingReward.amount.$errors.length,
										'border-ralbackground-dark-line border-opacity-20': !v$.editingReward.amount.$errors.length || !v$.editingReward.amount.$dirty
									}"
								>
									<div class="justify-start items-center gap-2 inline-flex">
										<div
											class="no-focus-outline w-full sm:w-36 text-neutral-700 text-base font-medium font-['Inter'] appearance-none outline-none bg-transparent border-none placeholder-gray-300"
										>
											<LvDropdown
												class="border-neutral-700 border-opacity-20"
												v-model="v$.editingReward.externalId.$model"
												@update:model-value="(newVal) => onDropdownChange(newVal, editingReward.type)"
												optionLabel="name"
												optionValue="code"
												placeholder="Select Option"
												emptyFilterMessage="No result found"
												icon-right="light-icon-arrow-down-circle"
												filterPlaceholder="Search"
												:options="dropdownOptions"
												:filter="true"
												:bottom-bar="true"
											/>
										</div>
									</div>
								</div>

								<div v-if="editingReward.showPointsEquivalency" class="ml-2">= $<span v-if="editingReward.amount">{{ editingReward.amount / earn.campaign?.loyaltyProgram?.loyaltyCurrencies?.[0]?.conversionToUSD}}</span>
								</div>

							</div>
							<div v-if="shouldShowNewLineForProduct  && secondaryDropdownOptions.length" class="flex flex-col sm:flex-row items-center justify-between rounded-xl border border-ralbackground-light-line mt-5 p-3">
								<div class="text-ralgray-dark text-sm font-medium font-['Inter']">
									Optionally Select A Variant
									<div class="text-ralgray-light text-xs font-medium font-['Inter']">
										Don't select a variant if you want all variants to be included.
									</div>
								</div>
								<div class="w-full sm:w-36 h-10 px-2 py-3 mt-2 bg-white rounded-lg border flex justify-center items-center gap-2.5">
									<div class="justify-start items-center gap-2 inline-flex">
										<div
											class="no-focus-outline w-full sm:w-36 text-neutral-700 text-base font-medium font-['Inter'] appearance-none outline-none bg-transparent border-none placeholder-gray-300"
										>
											<LvDropdown
												class="border-neutral-700 border-opacity-20"
												v-model="editingReward.secondaryExternalId"
												@update:model-value="(newVal) => onSecondaryDropdownChange(newVal, editingReward.type)"
												optionLabel="title"
												optionValue="id"
												placeholder="Select Option"
												emptyFilterMessage="No result found"
												icon-right="light-icon-arrow-down-circle"
												filterPlaceholder="Search"
												:options="secondaryDropdownOptions"
												:filter="true"
												:bottom-bar="true"
											/>
										</div>
									</div>
								</div>

								<div v-if="editingReward.showPointsEquivalency" class="ml-2">= $<span v-if="editingReward.amount">{{ editingReward.amount / earn.campaign?.loyaltyProgram?.loyaltyCurrencies?.[0]?.conversionToUSD}}</span>
								</div>

							</div>
						</div>
					</template>
					<div v-if="this.isPoints">
						<label class="block text-sm font-medium mb-1">Include Taxes in points calculation</label>
						<div
							class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
							<input
								type="checkbox"
								id="toggle"
								v-model="editingReward.includeTaxes"
								class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
							<label
								for="toggle"
								class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer">
							</label>
						</div>
						<label for="toggle2" class="block text-sm font-medium mb-1">Include Shipping in points calculation</label>
						<div
							v-if="this.isPoints"
							class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
							<input
								type="checkbox"
								id="toggle2"
								v-model="editingReward.includeShipping"
								class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
							<label
								for="toggle2"
								class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer">
							</label>
						</div>
					</div>

					<div
						v-if="showCouponCombinesWith && skioIntegrationConnected && !isPerkReward">
						<div class="flex items-center justify-ste text-ralsecondary-start text-sm font-bold font-['Inter'] mr-2">
							<span class="mr-2">Applies to Subscription Purchases</span>
							<Tooltip bg="dark" size="md" position="bottom" class="z-10">
								<div class="text-xs whitespace-nowrap text-white">Controls whether this coupon applies to subscription purchases.</div>
							</Tooltip>
						</div>

						<div class="lg:flex lg:gap-6 mt-2">
							<div>
								<div
									class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
									<input
										type="checkbox"
										id="toggle"
										v-model="editingReward.appliesOnSubscriptions"
										class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
									<label
										for="toggle"
										class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer">
									</label>
								</div>
							</div>
						</div>
					</div>

					<div
						v-if="showCouponCombinesWith && !isPerkReward">
						<div class="flex items-center justify-ste text-ralsecondary-start text-sm font-bold font-['Inter'] mr-2">
							<span class="mr-2">Combines With</span>
							<Tooltip bg="dark" size="md" position="bottom" class="z-10">
								<div class="text-xs whitespace-nowrap text-white">Controls whether this coupon can be combined with the selected options.</div>
							</Tooltip>
						</div>

						<div class="lg:flex lg:gap-6 mt-2">
							<div>
								<label for="combinesWithOrders" class="block text-sm font-medium mb-1">Order Discounts</label>
								<div
									class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
									<input
										type="checkbox"
										id="combinesWithOrders"
										v-model="editingReward.combinesWithOrders"
										class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
									<label
										for="combinesWithOrders"
										class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer">
									</label>
								</div>
							</div>
							<div>
								<label for="combinesWithProducts" class="block text-sm font-medium mb-1">Product Discounts</label>
								<div class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
									<input
										type="checkbox"
										id="combinesWithProducts"
										v-model="editingReward.combinesWithProducts"
										class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
									<label
										for="combinesWithProducts"
										class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer">
									</label>
								</div>
							</div>
							<div>
								<label for="combinesWithShipping" class="block text-sm font-medium mb-1">Shipping Discounts</label>
								<div class="relative inline-block w-10 mr-2 mb-4 align-middle select-none transition duration-200 ease-in">
									<input
										type="checkbox"
										id="combinesWithShipping"
										v-model="editingReward.combinesWithShipping"
										class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
									<label
										for="combinesWithShipping"
										class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer">
									</label>
								</div>
							</div>
						</div>
					</div>
					<div v-if="this.editingReward.hasRestrictions" class="mt-4 mb-4 h-px border border-ralbackground-light-line"></div>

					<div class="my-3" v-if="this.editingReward.hasRestrictions">
						<div class="flex items-center justify-ste">
							<div class="text-ralsecondary-start text-sm font-bold font-['Inter']  mr-2"><span v-if="!isPerkReward">Reward</span><span v-if="isPerkReward">Perk</span> Restrictions</div>
							<Tooltip bg="dark" size="md" position="bottom">
								<div class="text-xs whitespace-nowrap text-white"><span v-if="!isPerkReward">Reward</span><span v-if="isPerkReward">Perk</span> restrictions let you control details like minimum order amount, or expiration.</div>
							</Tooltip>
						</div>

						<div v-for="(restriction, index) of editingReward.restrictions" :key="index" class="flex items-center justify-end rounded-xl border border-zinc-300 mt-3 p-1 pt-0 flex-wrap">
							<div class="mt-1">
								<select
									class="w-52 h-10 px-2 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex"
									v-model="restriction.type">
									<option
										v-for="restrictionType of this.editingReward.uiRewardRestrictions"
										:key="restrictionType.fieldOnDefinition"
										:value="restrictionType.fieldOnDefinition"
									>
										{{ restrictionType.name }}
									</option>
								</select>
							</div>
							<div class="flex-grow"></div>
							<div v-if="restriction.type" class="w-52 h-10 px-2 py-3 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex mt-1" :class="{'border-red-500': restriction.error}">
								<div class="justify-start items-center gap-2 inline-flex">
									<input
										class="no-focus-outline text-ralsecondary-start text-base font-medium font-['Inter'] bg-transparent appearance-none outline-none border-none"
										v-model="restriction.amount"
										@change="updateRewardRestriction(restriction)" />
								</div>
							</div>
							<div v-if="restriction.error" class="text-xs text-red-500 mt-1 mr-6">
								{{ restriction.error }}
							</div>
							<div v-if="!restriction.required" class="w-6 h-6 relative opacity-40 ml-2 mt-1 cursor-pointer" @click="removeRestriction(editingReward, restriction)">
								<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<g><path d="M8 12H16M12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21Z" stroke="#4D4D4D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></g>
								</svg>
							</div>
						</div>

						<div class="flex items-center justify-end mt-3">
							<div
								class="h-10 px-3 py-2 bg-opacity-50 border-0 rounded-full bg-ralbackground-light-tertiary hover:bg-ralbackground-light-tertiary hover:bg-opacity-90 transition-all duration-300rounded-full justify-end items-center gap-1 inline-flex cursor-pointer"
								@click="editingReward.restrictions.push({})">
								<div class="w-6 h-6 relative opacity-50">
									<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
										<path d="M8 12H12M12 12H16M12 12V16M12 12V8M12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21Z" stroke="#414141" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
									</svg>
								</div>
								<div class="text-background-dark-base text-base font-medium font-['Inter'] ">Add Restriction</div>
							</div>
						</div>
					</div>

					<div v-if="this.editingReward.hasRestrictions" class="mt-4 mb-4 h-px border border-ralbackground-light-line"></div>

					<div v-if="this.editingReward.limitTypes?.length" class="mt-3 h-px border border-ralbackground-dark-line"></div>

					<div class="my-3" v-if="this.editingReward.limitTypes?.length">
						<div class="flex items-center justify-ste">
							<div class="text-ralsecondary-start text-sm font-bold font-['Inter']  mr-2">Reward Limits</div>
							<div class="w-5 h-5 relative opacity-50">
								<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
								<g opacity="0.5">
								<path d="M10 9.16667V13.3333M10 17.5C5.85786 17.5 2.5 14.1421 2.5 10C2.5 5.85786 5.85786 2.5 10 2.5C14.1421 2.5 17.5 5.85786 17.5 10C17.5 14.1421 14.1421 17.5 10 17.5ZM10.0415 6.66667V6.75L9.9585 6.75016V6.66667H10.0415Z" stroke="#414141" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
								</g>
								</svg>
							</div>
						</div>

						<div class="w-[100%] my-3 h-12 px-2 py-3 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-start items-start gap-2.5 inline-flex cursor-pointer" @click="editingReward.limits = !editingReward.limitsEnabled">
							<div class="justify-start items-center gap-2 inline-flex">
								<div class="w-6 h-6 border-ralbackground-dark-line border rounded-md" :class="editingReward.limitsEnabled ? 'bg-neutral-700' : ''"></div>
								<div class="text-ralsecondary-start text-base font-medium font-['Inter']">Enable Reward Limits</div>
							</div>
						</div>

						<div v-for="limitType of this.editingReward.limitTypes" :key="limitType.label" class="flex items-center justify-space-between my-3 p-1">
							<div class="text-ralsecondary-start text-sm font-medium font-['Inter']">{{ limitType.label }}</div>
							<div class="flex-grow"></div>
							<div class="w-52 h-10 px-2 py-3 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex">
								<div class="justify-start items-center gap-2 inline-flex">
									<input class="no-focus-outline w-52 text-ralsecondary-start text-base font-medium font-['Inter'] bg-transparent appearance-none outline-none" v-model="limitType.amount"/>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="w-full mt-10">
					<div class="flex items-center justify-ste">
						<div class="text-ralsecondary-start text-sm font-bold font-['Inter'] mr-2">
							<span v-if="!isPerkReward">Reward</span><span v-if="isPerkReward">Perk</span> Branding
						</div>
						<Tooltip bg="dark" size="md" position="bottom">
							<div class="text-xs whitespace-nowrap text-white">
								We use AI to give you ideas on text and imagery here!
								<a
									class="pl-2 pr-4 items-center gap-1 inline-flex cursor-pointer hover:underline ml-2"
									href="https://docs.raleon.io/docs/setting-up-a-new-campaign"
									target="_blank">
									What is branding?
								</a>
							</div>
						</Tooltip>
						<PrimaryButton
							class="text-sm ml-auto"
							cta="Smart Suggest"
							size="xs"
							:disabled="predictionRunning"
							@click="predictRewardBranding"
						/>
					</div>
					<div class="mt-6 text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide">
						<span v-if="!isPerkReward">Reward</span><span v-if="isPerkReward">Perk</span> Name
						<span class="text-rose-500">*</span>
					</div>
					<div class="mb-3 text-ralgray-dark text-xs font-normal font-['Inter']">
						This will be shown to describe the particular <span v-if="!isPerkReward">reward</span><span v-if="isPerkReward">perk</span> to the shopper!
					</div>
					<div class="mb-8">
						<div v-if="predictionRunning || loading || isLoading" role="status" class="w-full flex p-3 space-y-4 justify-end bg-gray-300 dark:bg-gray-600  rounded-2xl shadow animate-pulse">
							<img src="../../images/magic.svg" width="20" height="60" />
						</div>
						<div v-if="!predictionRunning && !loading && !isLoading"
							class="w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"
							:class="{
								'border-ralerror-dark': v$.editingReward.name.$dirty && v$.editingReward.name.$errors.length,
								'border-gray-400': !v$.editingReward.name.$errors.length || !v$.editingReward.name.$dirty,
							}"
						>
							<div class="w-full mr-3 self-stretch justify-start items-center gap-2 flex">
								<input
									class="no-focus-outline border-none w-full text-gray-600 text-sm font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									:class="{'animate-pulse bg-gray-300 dark:bg-gray-600': predictionRunning}"
									maxlength="40"
									v-model="v$.editingReward.name.$model"
									:disabled="predictionRunning"
								/>
								<img src="../../images/magic.svg" width="20" height="60" />
							</div>
						</div>
					</div>
					<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide">
						<span v-if="!isPerkReward">Reward</span><span v-if="isPerkReward">Perk</span> Description
						<span class="text-rose-500">*</span>
					</div>
					<div class="mb-3 text-ralgray-dark text-xs font-normal font-['Inter'] leading-none">
						Describe the <span v-if="!isPerkReward">reward</span><span v-if="isPerkReward">perk</span> to provide the customer more details about what they're receiving.
					</div>
					<div class="mb-8">
						<div v-if="predictionRunning || loading || isLoading" role="status" class="w-full flex p-3 space-y-4 justify-end bg-gray-300 dark:bg-gray-600  rounded-2xl shadow animate-pulse">
							<img src="../../images/magic.svg" width="20" height="60" />
						</div>
						<div v-if="!predictionRunning && !loading && !isLoading"
							class="w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"
							:class="{
								'border-ralerror-dark': v$.editingReward.description.$dirty && v$.editingReward.description.$errors.length,
								'border-gray-400': !v$.editingReward.description.$errors.length || !v$.editingReward.description.$dirty,
							}"
						>
							<div class="w-full mr-3 self-stretch justify-start items-center gap-2 flex">
								<input
									class="no-focus-outline border-none w-full text-gray-600 text-sm font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									:class="{'animate-pulse bg-gray-300 dark:bg-gray-600': predictionRunning}"
									maxlength="80"
									:disabled="predictionRunning"
									v-model="v$.editingReward.description.$model"
								/>
								<img src="../../images/magic.svg" width="20" height="60" />
							</div>
						</div>
					</div>
					<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide">
						Upload <span v-if="!isPerkReward">Reward</span><span v-if="isPerkReward">Perk</span> Image
						<span class="text-rose-500">*</span>
					</div>
					<div class="text-ralgray-dark text-xs font-normal font-['Inter'] leading-none">
						This image will be shown to the customer to represent the <span v-if="!isPerkReward">reward</span><span v-if="isPerkReward">perk</span>.
					</div>
					<div class="flex flex-wrap mt-3 items-center">
						<div class="reward-image-wrapper">
							<div class="reward-image-container">
								<img v-if="predictionRunning || loading || isLoading"
									class="rounded-3xl border object-center object-cover"
									:class="{
										'animate-pulse bg-gray-300 dark:bg-gray-600': predictionRunning
									}"
									src="../../images/magic.svg" width="20" height="20" />

								<!-- When imageURL is invalid (null or empty) -->
								<img v-else-if="v$.editingReward.imageURL.$model == null || v$.editingReward.imageURL.$model == ''"
									class="rounded-3xl border object-center object-cover border-red-500"
									src="https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg" width="20" height="20" />

								<!-- Default case: Display the actual image -->
								<img v-else
									class="rounded-3xl border object-center object-cover"
									:class="{
										'border-ralerror-dark': v$.editingReward.imageURL.$dirty && v$.editingReward.imageURL.$errors.length,
										'border-gray-400': !v$.editingReward.imageURL.$dirty && !v$.editingReward.imageURL.$errors.length
									}"
									:src="v$.editingReward.imageURL.$model" />
							</div>
						</div>
						<div class="flex flex-col flex-grow items-start justify-center">
							<div class="w-full ml-4">
								<ImageUpload @imageSelected="(event) => v$.editingReward.imageURL.$model = event.url"/>
							</div>
						</div>
					</div>

					<div v-if="this.index > 0" class="flex justify-center">
						<LightSecondaryButton
							cta="Remove Reward"
							@click="deleteReward"
							class="mr-4 sm:mr-0"
						></LightSecondaryButton>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>

	import * as Utils from '../../../client-old/utils/Utils';
	import SuperDropdown from '../../components/SuperDropdown.ts.vue';
	import LightSecondaryButton from '../../components/LightSecondaryButton.ts.vue';
	import LvDropdown  from 'lightvue/dropdown';
	import Tooltip from '../../components/Tooltip.ts.vue'
	import ImageUpload from '../../components/ImageUpload.ts.vue';
	import WTETypeIcon from '../../components/WTETypeIcon.ts.vue';
	import RewardForm from './CampaignWTERewardView.ts.vue'
	import PrimaryButton from '../../components/PrimaryButton.ts.vue';
	import CancelButton from '../../components/CancelButton.ts.vue';
	import { useVuelidate } from '@vuelidate/core'
	import { required, helpers } from '@vuelidate/validators'
	import * as CurrencyUtils from '../../services/currency.js';
	import { SkioService } from '../../components/integrations/skio-service';

	const URL_DOMAIN = Utils.URL_DOMAIN;

	const amountValidator = (value, siblings, vm) => {
		if (vm.editingReward.type === 'free-shipping') {
			return true;
		}
		if (value == undefined || value == null || value == '' || !helpers.req(value)) return false;

		if (vm.editingReward.type === 'percent-discount') {
			return value > 0 && value <= 100;
		}

		return value >= 0;
	}

	const externalIdValidator = (value, siblings, vm) => {
		if (vm.editingReward.type === 'free-product') {
			return value != null && value != '';
		}
		return true;
	}

	const externalLinkValidator =(value, siblings, vm) => {
		if (vm.editingReward.type === 'static-text-click') {
			return value != null && value != '';
		}
		return true;
	}

	export default {
		props: ['campaign', 'earn', 'reward', 'isSingleReward', 'index', 'loading', 'isPoints', 'isPerkReward', 'isGiveaway'],
		emits: ['reward-edited', 'reward-deleted', 'reward-image-selected'],
		components: {
			SuperDropdown,
			LightSecondaryButton,
			LvDropdown,
			CancelButton,
			Tooltip,
			WTETypeIcon,
			RewardForm,
			ImageUpload,
			PrimaryButton,
		},
		data() {
			return {
				editingReward: this.reward,
				rewardOptions: [],
				dropdownOptions: [],
				selectedDropdownOption: null,
				secondaryDropdownOptions: [],
				selectedSecondaryDropdownOption: null,
				v$: useVuelidate(),
				isLoading: true,
				isComplete: false,
				loadingProducts: false,
				predictionRunning: false,
				processedLabel: '',
				skioIntegrationConnected: false,
			}
		},
		async mounted() {
			const options = await this.getRewardOptions();
			this.skioIntegrationConnected = await SkioService.isConnected();
			this.rewardOptions = options.sort((a, b) => {
				if (a.enabled === b.enabled) return 0;
				if (a.enabled) return -1;
				return 1;
			});

			if (this.reward && (this.reward.type || (this.reward.name && this.rewardOptions?.find?.(x => x.type === 'static-text')))) {
				const selectedOption = this.reward.name && !this.reward.type
					? this.rewardOptions?.find?.(x => x.type === 'static-text')
					: this.rewardOptions.find(rewardType => rewardType.type === this.reward.type);
				if (selectedOption) {
					this.editingReward = {
						...selectedOption,
						...this.reward,
					}

					if (this.isTargetingAProduct) {
						await this.fetchDropdownOptions();
					}
				}
				return;
			}

			if (this.earn?.condition?.type === 'milestone-subscription-purchase') {
				this.editingReward.appliesOnSubscriptions = true;
			}
			await this.switchRewardType(this.rewardOptions[0]?.type);
		},
		computed: {
			showRewardInput() {
				return this.editingReward?.type !== 'free-shipping';
			},
			isTargetingAProduct() {
				return this.editingReward?.type === 'free-product' || this.editingReward?.type === 'percent-off-product' || this.editingReward?.type === 'dollar-off-product';
			},
			shouldShowNewLineForProduct() {
				return this.isTargetingAProduct && this.editingReward?.type !== 'free-product';
			},
			showCouponCombinesWith() {
				return this.editingReward?.type !== 'points' &&
					this.editingReward?.type !== 'points-per-dollar' &&
					this.editingReward?.type !== 'giveaway-entry';
			},
		},
		watch: {
			'editingReward.label': {
				immediate: true,
				async handler(newLabel) {
					await this.processRewardLabel();
				}
			},
			'editingReward.type': {
				async handler(newValue, oldValue) {
					if (newValue === oldValue) {
						return;
					}
					if (newValue.type) {
						let rewardOptions = this.rewardOptions || await this.getRewardOptions();
						const rewardType = rewardOptions.find(rewardType => rewardType.type === newValue.type);
						if (!rewardType.enabled) return;
						this.editingReward = {
							...rewardType,
							...newValue,
						}

						if (this.isTargetingAProduct) {
							await this.fetchDropdownOptions();
						}

						this.$emit('reward-edited', this.editingReward, this.index);
						if (this.editingReward.type === 'free-shipping') {
							this.$emit('isComplete', true);
						}
						this.$emit('isComplete', !this.v$.editingReward.$invalid);

					}
				},
			},
			'v$.editingReward.externalLink.$model'(newValue, oldValue) {
				this.$emit('reward-edited', this.editingReward, this.index);
				this.$emit('isComplete', !this.v$.editingReward.$invalid);
			},
			'v$.editingReward.amount.$model'(newValue, oldValue) {
				this.$emit('reward-edited', this.editingReward, this.index);
				this.$emit('isComplete', !this.v$.editingReward.$invalid);
			},
			'v$.editingReward.name.$model'(newValue, oldValue) {
				this.$emit('reward-edited', this.editingReward, this.index);
				this.$emit('isComplete', !this.v$.editingReward.$invalid);
			},
			'v$.editingReward.description.$model'(newValue, oldValue) {
				this.$emit('reward-edited', this.editingReward, this.index);
				this.$emit('isComplete', !this.v$.editingReward.$invalid);
			},
			'v$.editingReward.imageURL.$model'(newValue, oldValue) {
				this.$emit('reward-edited', this.editingReward, this.index);
				this.$emit('isComplete', !this.v$.editingReward.$invalid);
			},
			'v$.editingReward.externalId.$model'(newValue, oldValue) {
				this.$emit('reward-edited', this.editingReward, this.index);
				this.$emit('isComplete', !this.v$.editingReward.$invalid);
			},
			'editingReward.includeTaxes'(newValue, oldValue) {
				this.$emit('reward-edited', this.editingReward, this.index);
			},
			'editingReward.includeShipping'(newValue, oldValue) {
				this.$emit('reward-edited', this.editingReward, this.index);
			},
			'editingReward.combinesWithOrders'(newValue, oldValue) {
				this.$emit('reward-edited', this.editingReward, this.index);
			},
			'editingReward.combinesWithProducts'(newValue, oldValue) {
				this.$emit('reward-edited', this.editingReward, this.index);
			},
			'editingReward.combinesWithShipping'(newValue, oldValue) {
				this.$emit('reward-edited', this.editingReward, this.index);
			},
			'editingReward.appliesOnSubscriptions'(newValue, oldValue) {
				this.$emit('reward-edited', this.editingReward, this.index);
			},
			'editingReward.restrictions': {
					handler(newValue, oldValue) {
					let isValid = true;
					for( const val in newValue)	{
						if(newValue[val].error) {
							isValid = false;
							break;
						}
					}
					this.$emit('isComplete', isValid);
					},
					deep: true // Enable deep watching
				}
			},
		methods: {
			async processRewardLabel() {
				if (this.editingReward && this.editingReward.label) {
					this.processedLabel = await CurrencyUtils.replaceCurrencyTags(this.editingReward.label);
				}
			},
			replaceCurrencyTagsSync(text) {
				return CurrencyUtils.replaceCurrencyTagsSync(text);
			},
			async getRewardOptions() {
				if(this.isSingleReward) {
					const endpoint = this.isPerkReward
						? 'perks/rewards'
						: 'shopitems/rewards';

					const response = await fetch(`${URL_DOMAIN}/${endpoint}`, {
						method: 'GET',
						credentials: 'omit',
						mode: 'cors',
						headers: {
							'Content-Type': 'application/json',
							Authorization: `Bearer ${localStorage.getItem('token')}`,
						},
					});

					const result = await response.json();
					this.rewardOptions = result;
					if(!this.earn.rewards || this.earn.rewards.length == 0) {
						this.$emit('reward-edited', {restrictions: []}, this.index);
					}
					for(let i = 0; i < this.rewardOptions.length; i++) {
						this.rewardOptions[i].title = await CurrencyUtils.replaceCurrencyTags(this.rewardOptions[i].title);
						if (this.isPerkReward) {
							this.rewardOptions[i].title = this.rewardOptions[i].title.replace(/Coupon$/, 'Perk');
						}
						this.rewardOptions[i].subtitle = await CurrencyUtils.replaceCurrencyTags(this.rewardOptions[i].subtitle);
						this.rewardOptions[i].label = await CurrencyUtils.replaceCurrencyTags(this.rewardOptions[i].label);
						if (this.isPerkReward) {
							this.rewardOptions[i].label = this.rewardOptions[i].label.replace(/\bCoupon\b/g, 'Perk');
						}
					}
					this.isLoading = false;
					return result;
				} else {
					let payload = {
						actions: [
							this.earn?.condition?.id
						]
					}

					const response = await fetch(`${URL_DOMAIN}/wte/customer-rewards`, {
						method: 'POST',
						credentials: 'omit',
						mode: 'cors',
						headers: {
							'Content-Type': 'application/json',
							Authorization: `Bearer ${localStorage.getItem('token')}`,
						},
						body: JSON.stringify(payload),
					});

					let result = await response.json();
					result = result.filter(x => this.isGiveaway
						? x.showInGiveawayConfig
						: !x.hideFromWteConfig
					);
					this.rewardOptions = result;
					for(let i = 0; i < this.rewardOptions.length; i++) {
						this.rewardOptions[i].title = await CurrencyUtils.replaceCurrencyTags(this.rewardOptions[i].title);
						// if (this.isPerkReward) {
						// 	this.rewardOptions[i].title = this.rewardOptions[i].title.replace(/Coupon$/, 'Perk');
						// }
						this.rewardOptions[i].subtitle = await CurrencyUtils.replaceCurrencyTags(this.rewardOptions[i].subtitle);
						this.rewardOptions[i].label = await CurrencyUtils.replaceCurrencyTags(this.rewardOptions[i].label);
						// if (this.isPerkReward) {
						// 	this.rewardOptions[i].label = this.rewardOptions[i].label.replace(/\bCoupon\b/g, 'Perk');
						// }
					}
					this.isLoading = false;
					return result
				}
			},
			addReward() {
				this.editingReward = {
					restrictions: [],
					externalLink: '',
				};
			},
			deleteReward() {
				this.$emit('reward-deleted', this.index);

				if (this.earn.rewards.length == 0) {
					this.$emit('isComplete', false);
				}
			},
			updateRewardRestriction(restriction) {
				if (restriction.amount < restriction.minimumAmount) {
					restriction.error = `Amount should not be less than ${restriction.minimumAmount}`;
					return;
				} else {
					restriction.error = null;
				}

				this.$emit('reward-edited', this.editingReward, this.index || 0);
			},
			isExisting(reward) {
				this.earn.rewards = this.earn.rewards || [];

				return this.earn.rewards.indexOf(reward) != -1;
			},
			async predictRewardBranding() {
				this.predictionRunning = true;
				const data = this.editingReward;

				if (this.isTargetingAProduct) {
					data.externalCollectionName = this.selectedDropdownOption?.name;
				}

				const response = await fetch(`${URL_DOMAIN}/wte/reward/predict`, {
					method: 'POST', credentials: 'omit', mode: 'cors',
					headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${localStorage.getItem('token')}`, },
					body: JSON.stringify(data),
				});

				const result = await response.json();
				this.editingReward.name = result.name;
				this.editingReward.description = result.description;
				if (result.imageURL) {
					this.editingReward.imageURL = result.imageURL;
				}
				this.predictionRunning = false;
			},
			removeRestriction(reward, restriction) {
				const index = reward.restrictions.indexOf(restriction);
				if (index < 0) {
					return;
				}
				reward.restrictions.splice(index, 1);
			},
			async switchRewardType(rewardTypeValue) {
				if (rewardTypeValue === this.editingReward?.type) {
					return;
				}
				let rewardOptions = this.rewardOptions || await this.getRewardOptions();
				let rewardType = rewardOptions.find(rewardType => rewardType.type === rewardTypeValue);
				if (!rewardType) rewardType = rewardOptions[0]?.type;
				if (!rewardType?.enabled) return;

				const appliesOnSubscriptions =
					this.editingReward?.appliesOnSubscriptions ||
					this.earn?.condition?.type === 'milestone-subscription-purchase' ||
					this.earn?.condition?.type === 'first-subscription-purchase' ||
					this.earn?.condition?.type === 'subscription-purchase';

				const restrictions = [];
				if (rewardType.hasRestrictions) {
					const restriction = rewardType.uiRewardRestrictions.find(x => x.fieldOnDefinition === 'expiresInDays');
					if (restriction) {
						restrictions.push({
							type: restriction.fieldOnDefinition,
							amount: 30,
							required: restriction.required,
							minimumAmount: restriction.minimumAmount,
						});
					}
				}

				this.editingReward = {
					...rewardType,
					restrictions,
					appliesOnSubscriptions,
					externalLink: '',
				};

				if (this.isTargetingAProduct) {
					await this.fetchDropdownOptions();
				}

				this.$emit('reward-edited', this.editingReward, this.index);

				if (this.isSingleReward) {
					this.$emit('reward-edited', this.editingReward, this.index || 0);
				}
			},
			onDropdownChange(newVal, rewardType) {
				if (!rewardType) {
					return;
				}
				this.selectedDropdownOption = newVal;
				this.editingReward.externalId = `${newVal}`;
				this.editingReward.amount = this.editingReward.type == 'free-product' ? 100 : this.editingReward.amount;

				let selectedOption = this.dropdownOptions.find(x => x.code === newVal);
				this.editingReward.externalName = selectedOption.name;
				this.editingReward.externalLink = selectedOption.handle;
				if(selectedOption?.image) {
					this.editingReward.imageURL = selectedOption?.image;
				}

				if (selectedOption && selectedOption.variants) {
					if (selectedOption.variants.length > 1) {
						this.secondaryDropdownOptions = selectedOption.variants;
					}
					else if (selectedOption.variants.length == 1) {
						this.secondaryDropdownOptions = [];
						this.editingReward.secondaryExternalId = selectedOption.variants[0].id;
					}
				}
			},
			onSecondaryDropdownChange(newVal) {
				this.selectedSecondaryDropdownOption = newVal;
				this.editingReward.secondaryExternalId = `${newVal}`;
			},
			async fetchDropdownOptions() {
				if (!this.editingReward?.type && !this.isTargetingAProduct) return;
				this.loadingProducts = true;
				let url = this.editingReward.type == 'free-product' ? `${URL_DOMAIN}/shop-collections` : `${URL_DOMAIN}/shop-products`;
				const response = await fetch(url, {
					method: 'GET', credentials: 'omit', mode: 'cors',
					headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${localStorage.getItem('token')}`, },
				});
				const result = await response.json();
				console.log(`products`, result);
				this.dropdownOptions = result.map(x => ({ name: x.title, code: `${x.id}`, handle: x.handle, variants: x.variants, }));
				if (this.editingReward.externalId) {
					this.selectedDropdownOption = this.dropdownOptions.find(x => x.code === this.editingReward.externalId);

					if (this.selectedDropdownOption && this.selectedDropdownOption.variants) {
						if(this.selectedDropdownOption.variants.length > 1){
							this.secondaryDropdownOptions = this.selectedDropdownOption.variants;
							if (this.editingReward.secondaryExternalId && this.secondaryDropdownOptions) {
								this.selectedSecondaryDropdownOption = this.selectedDropdownOption.variants.find(x => x.id.includes(this.editingReward.secondaryExternalId));
							}
						}
						else if(this.selectedDropdownOption.variants.length == 1){
							this.secondaryDropdownOptions = [];
							this.editingReward.secondaryExternalId = this.selectedDropdownOption.variants[0].id;
						}
					}
				}
				this.loadingProducts = false;
			},
		},
		validations() {
			return {
				editingReward: {
					amount: { amountValidator },
					description: { required },
					name: { required },
					imageURL: { required },
					externalId: { externalIdValidator },
					externalLink: { externalLinkValidator },
				}
			}
		}
	}
</script>
<style scoped>
	.no-focus-outline {
		box-shadow: none !important;
		outline: none !important;
	}

	.reward-image-wrapper {
		width: 100%;
		max-width: 150px;
		margin: auto;
	}

	.reward-image-container {
		position: relative;
		width: 100%;
		padding-top: 100%;
		/* Maintains a 1:1 aspect ratio */
	}

	.reward-image-container img {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.reward-upload-button-container {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		margin-top: 10px;
	}

	@media (min-width: 500px) {
		.reward-upload-button-container {
			margin-left: 12px;
			/* Add left padding back for larger screens */
			text-align: left;
			/* Align left on larger screens */
			width: 100%;
			justify-content: flex-start;
		}
	}
	.toggle-checkbox {
		opacity: 0;
		width: 0;
		height: 0;
	}

	.toggle-label {
		display: block;
		overflow: hidden;
		cursor: pointer;
		height: 24px;
		/* Height of the toggle */
		padding: 0;
		line-height: 24px;
		background: #bbb;
		/* Background of the toggle */
		border-radius: 24px;
		transition: background-color 0.2s;
	}

	.toggle-label:after {
		content: "";
		position: absolute;
		top: 2px;
		left: 2px;
		width: 20px;
		/* Width of the toggle handle */
		height: 20px;
		/* Height of the toggle handle */
		border-radius: 20px;
		background: #fff;
		/* Background of the toggle handle */
		transition: 0.2s;
	}

	.toggle-checkbox:checked+.toggle-label {
		background-color: #68d391;
	}
	.toggle-checkbox:disabled+.toggle-label {
		background-color: #eee;
		cursor: not-allowed;
	}


	.toggle-checkbox:checked+.toggle-label:after {
		transform: translateX(18px);
	}

	.toggle-checkbox:checked {
		right: 0;
	}

	.toggle-checkbox:checked+.toggle-label {
		background-color: #68d391;
	}

	.toggle-checkbox {
		right: 4px;
	}

	.toggle-label {
		transition: background-color 0.2s;
	}
</style>
