<template>
	<div class="w-[100%] lg:w-auto mb-8">
		<AccordionGroup
			:accordions="accordions"
			initialOpenAccordionKey="notifications"
			@open="handleAccordionOpen">
			<template v-slot:notifications>
				<div
				>
					<div class="mx-4 mb-4">
						<div class="mt-2">

							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Notifications
								Text Color</div>
							<div class="mt-4">
								<LvColorPicker
									class="mx-2 md:mx-6 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
									:key="'background-launcher-styling-textColor-' + brandingUpdateCounter"
									v-model="computedNotificationTextColor" :clearable="false" :value="computedNotificationTextColor"
									:bottomBar="false" />
							</div>


							<div class="mt-4  text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
								Notification Background Color</div>
							<div class="mt-4">
								<LvColorPicker
									class="mx-2 md:mx-6 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
									:key="'background-launcher-styling-backgroundColor-' + brandingUpdateCounter"
									v-model="computedNotificationBackgroundColor" :clearable="false"
									:value="computedNotificationBackgroundColor" :bottomBar="false" />
							</div>

							<div class="mt-4  text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
								Notification Pill and Border Color</div>
							<div class="mt-4">
								<LvColorPicker
									class="mx-2 md:mx-6 justify-start items-center gap-2 inline-flex cursor-pointer text-sm font-semibold font-['Inter']"
									:key="'background-launcher-styling-backgroundColor-' + brandingUpdateCounter"
									v-model="computedNotificationFillBorderColor" :clearable="false"
									:value="computedNotificationFillBorderColor" :bottomBar="false" />
							</div>
						</div>
					</div>

				</div>
			</template>

		</AccordionGroup>
	</div>
</template>

<script>
import * as Utils from '../../../client-old/utils/Utils';
import AccordionGroup from '../../components/AccordionGroup.ts.vue';
import LearnMoreText from '../../components/LearnMoreText.ts.vue';
import SuperDropdown from '../../components/SuperDropdown.ts.vue';
import ImageUpload from '../../components/ImageUpload.ts.vue';
import LvColorPicker from 'lightvue/color-picker';


const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: [
		'branding',
		'previewType',
		'isLogoUploading',
		'isHeroImageUploading',
		'launcherActive'
	],
	emits: ['fileUploaded', 'switchPreview', 'fileUploading', 'brandingChanged'],
	created() {
		this.debouncedBrandingChanged = this.debounce(this.handleBrandingChanged, 1000);
	},
	data() {
		return {
			accordions: [{
				key: 'notifications',
				title: "Notifications",
				height: '500px'
			}],
			launcherSizes: [],
			launcherRadii: [],
			debouncedBrandingChanged: null,
			brandingUpdateCounter: 0
		}
	},
	watch: {
		'branding.notifications.styling.textColor': function (newValue) {
			this.branding.notifications = this.branding.notifications || {};
			this.branding.notifications.styling = this.branding.notifications.styling || {};
			this.branding.notifications.styling.textColor = this.convertToHex(newValue);
		},
		'branding.notifications.styling.backgroundColor': function (newValue) {
			this.branding.notifications = this.branding.notifications || {};
			this.branding.notifications.styling = this.branding.notifications.styling || {};
			this.branding.notifications.styling.backgroundColor = this.convertToHex(newValue);
		},
		'branding.notifications.styling.fillBorderColor': function (newValue) {
			this.branding.notifications = this.branding.notifications || {};
			this.branding.notifications.styling = this.branding.notifications.styling || {};
			this.branding.notifications.styling.fillBorderColor = this.convertToHex(newValue);
		},
		'branding.launcher.styling.backgroundColor': function (newValue) {
			this.branding.launcher.styling.backgroundColor = this.convertToHex(newValue);
		},

		'branding': {
			handler(newVal) {
				this.debouncedBrandingChanged(newVal);
			},
			deep: true
		}
	},
	components: {
		AccordionGroup,
		SuperDropdown,
		ImageUpload,
		LearnMoreText,
		LvColorPicker
	},
	async mounted() {
	},
	computed: {
		enabledShareTargetsCount: function() {
			return Object.values(this.branding.referrals.shareSettings).reduce((count, setting) => {
				return count + (setting.enabled ? 1 : 0);
			}, 0);
		},
		computedBackgroundColor: {
			get() {
				return this.branding.colors.backgroundColor;
			},
			set(value) {
				this.branding.colors.backgroundColor = value;
			}
		},
		computedButtonBackgroundColor: {
			get() {
				return this.branding.colors.buttonBackgroundColor;
			},
			set(value) {
				this.branding.colors.buttonBackgroundColor = value;
			}
		},
		computedButtonTextColor: {
			get() {
				return this.branding.colors.buttonTextColor;
			},
			set(value) {
				this.branding.colors.buttonTextColor = value;
			}
		},
		computedTextColor: {
			get() {
				return this.branding.colors.textColor;
			},
			set(value) {
				this.branding.colors.textColor = value;
			}
		},
		computedLinkColor: {
			get() {
				return this.branding.colors.linkColor;
			},
			set(value) {
				this.branding.colors.linkColor = value;
			}
		},
		computedAccentColorFrom: {
			get() {
				return this.branding.colors.accentColor.from;
			},
			set(value) {
				this.branding.colors.accentColor.from = value;
			}
		},
		computedAccentColorTo: {
			get() {
				return this.branding.colors.accentColor.to;
			},
			set(value) {
				this.branding.colors.accentColor.to = value;
			}
		},
		computedSecondaryColor: {
			get() {
				return this.branding.colors.secondaryColor;
			},
			set(value) {
				this.branding.colors.secondaryColor = value;
			}
		},
		computedWarningColor: {
			get() {
				return this.branding.colors.warningColor;
			},
			set(value) {
				this.branding.colors.warningColor = value;
			}
		},
		computedNotificationTextColor: {
			get() {
				return this.branding.notifications?.styling?.textColor;
			},
			set(value) {
				this.branding.notifications = this.branding.notifications || {};
				this.branding.notifications.styling = this.branding.notifications.styling || {};
				this.branding.notifications.styling.textColor = value;
			}
		},
		computedNotificationBackgroundColor: {
			get() {
				return this.branding.notifications?.styling?.backgroundColor;
			},
			set(value) {
				this.branding.notifications = this.branding.notifications || {};
				this.branding.notifications.styling = this.branding.notifications.styling || {};
				this.branding.notifications.styling.backgroundColor = value;
			}
		},
		computedNotificationFillBorderColor: {
			get() {
				return this.branding.notifications?.styling?.fillBorderColor;
			},
			set(value) {
				this.branding.notifications = this.branding.notifications || {};
				this.branding.notifications.styling = this.branding.notifications.styling || {};
				this.branding.notifications.styling.fillBorderColor = value;
			}
		},
		computedMemberBenefitsBackgroundColor: {
			get() {
				return this.branding.guest.content.benefitsBackgroundColor || '#C9C3C3';
			},
			set(value) {
				this.branding.guest.content.benefitsBackgroundColor = value;
			}
		},
		accordionPreviewTypeName() {
			if (this.previewType == 'launcher') {
				return 'launcher';
			} else if (this.previewType == 'landing-page-guest') {
				return 'guest';
			} else if (this.previewType == 'landing-page-member') {
				return 'member';
			}
		}
	},
	methods: {
		preventNewLines(event) {
			if (event.keyCode === 13) { // 13 is the key code for Enter
				event.preventDefault();
			}
		},
		handleBrandingChanged(newBranding) {
			this.brandingUpdateCounter++;
			this.$emit('brandingChanged', newBranding);
		},
		debounce(func, delay) {
			let debounceTimer;
			return function () {
				const context = this;
				const args = arguments;
				clearTimeout(debounceTimer);
				debounceTimer = setTimeout(() => func.apply(context, args), delay);
			};
		},
		convertToHex(color) {
			// Check if color is already in hex format with or without '#'
			const hexColorPattern = /^#?([a-fA-F0-9]{3})([a-fA-F0-9]{3})?$/;
			const match = color.match(hexColorPattern);
			if (match) {
				// Reformat if '#' is missing
				return `#${match[1]}${match[2] ? match[2] : ''}`;
			}

			// Assuming the color is in RGB format, e.g., 'rgb(255, 0, 0)'
			if (/rgb/.test(color)) {
				const rgb = color.match(/\d+/g);
				return `#${rgb.map(x => {
					const hex = parseInt(x).toString(16);
					return hex.length === 1 ? '0' + hex : hex;
				}).join('')}`;
			}

			// Return original color if none of the above cases match
			return color;
		},
		handleAccordionOpen(accordionKey) {
			switch (accordionKey) {
				case 'launcher':
					return this.$emit('switchPreview', 'launcher');
				case 'guest':
					return this.$emit('switchPreview', 'landing-page-guest');
				case 'member':
					return this.$emit('switchPreview', 'landing-page-member');
				default:
					break;
			}
		},
		async uploadingHeroImage() {

			console.log("Uploading Hero Image");
			this.$emit('fileUploading', {
				type: 'hero-image'
			});

		},
		async uploadHeroImage(event) {
			this.$nextTick(() => {
				this.$emit('fileUploaded', {
					...event,
					type: 'hero-image'
				});
			});
		},
		async uploadingLogo(event) {
			console.log("Starting logo updating");
			this.$emit('fileUploading', {
				type: 'logo'
			});
		},
		async uploadLogo(event) {
			this.$nextTick(() => {
				this.$emit('fileUploaded', {
					...event,
					type: 'logo'
				});
			});
		},
	}
}
</script>

<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}
.toggle-checkbox {
	opacity: 0;
	width: 0;
	height: 0;
}

.toggle-label {
	display: block;
	overflow: hidden;
	cursor: pointer;
	height: 24px;
	/* Height of the toggle */
	padding: 0;
	line-height: 24px;
	background: #bbb;
	/* Background of the toggle */
	border-radius: 24px;
	transition: background-color 0.2s;
}

.toggle-label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 2px;
	width: 20px;
	/* Width of the toggle handle */
	height: 20px;
	/* Height of the toggle handle */
	border-radius: 20px;
	background: #fff;
	/* Background of the toggle handle */
	transition: 0.2s;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}
.toggle-checkbox:disabled+.toggle-label {
	background-color: #eee;
	cursor: not-allowed;
}


.toggle-checkbox:checked+.toggle-label:after {
	transform: translateX(18px);
}

.toggle-checkbox:checked {
	right: 0;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox {
	right: 4px;
}

.toggle-label {
	transition: background-color 0.2s;
}
</style>
