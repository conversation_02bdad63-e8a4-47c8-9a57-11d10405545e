<template>
	<div class="flex flex-col items-center">
		<div class="w-[100vw] md:w-[50rem] bg-white bg-opacity-75 rounded-2xl border border-violet-200 p-2 md:p-7">
			<div v-if="earn?.shopItemConfig?.length > 0">
				<div class="flex">
					<div class="text-ralsecondary-start text-base font-semibold font-['Inter']">
						Reward Item Details
					</div>

					<Tooltip bg="dark" size="md" position="bottom" class="ml-2 mt-1">
						<div class="text-xs whitespace-nowrap text-white">Set the price a customer has to pay to claim a reward.</div>
					</Tooltip>

					<div class="ml-auto">
						<a
						class="pl-2 pr-4 text-ralprimary-main text-sm justify-start font-semibold items-center gap-1 inline-flex cursor-pointer hover:underline ml-2"
						href="https://docs.raleon.io/docs/setting-up-a-new-campaign"
						target="_blank"
						>
							<svg class="w-4 h-4 fill-current text-ralrpimary-ultralight mr-2 opacity-50" viewBox="0 0 16 16">
							<path d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 12c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1zm1-3H7V4h2v5z" />
							</svg>
							How do I setup a reward?
						</a>
					</div>
				</div>


				<div class="rounded-xl border border-zinc-300 mt-5 p-3 pt-0">
					<div v-for="(conditionType, index) of this.earn.shopItemConfig" :key="index">
						<div class="flex items-center justify-space-between mx-3 mt-3">
							<div class="text-ralgray-dark text-sm font-medium font-['Inter'] ml-3">
								{{ conditionType.label }}
							</div>
							<div
								class="ml-10 w-36 h-10 px-2 py-3 bg-white rounded-lg border flex-col justify-center items-start gap-2.5 inline-flex"
								:class="{
									'border-ralerror-dark': !this.conditionTypeValidations[conditionType.id],
									'border-neutral-700 border-opacity-20': this.conditionTypeValidations[conditionType.id]
								}">
								<div class="items-center gap-2 inline-flex">
									<input
										type="number"
										class="no-focus-outline w-36 text-neutral-700 text-base font-medium font-['Inter'] appearance-none outline-none bg-transparent border-none"
										v-model="conditionType.defaultAmount"
										@input="validate(conditionType)" />
								</div>
							</div>
							<!-- hide for now until we determine better way to show
							<div v-if="earn?.campaign?.loyaltyProgram?.loyaltyCurrencies?.[0]?.conversionToUSD && conditionType.fieldOnDataModel == 'price'"
								class="flex ml-5">
								<div>
									= $<span v-if="conditionType.defaultAmount">{{ (conditionType.defaultAmount / earn.campaign?.loyaltyProgram?.loyaltyCurrencies?.[0]?.conversionToUSD).toFixed(2) }}</span>
								</div>
							</div>
							-->
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>

import * as Utils from '../../../client-old/utils/Utils';
import LightSecondaryButton from '../../components/LightSecondaryButton.ts.vue';
import Tooltip from '../../components/Tooltip.ts.vue'

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: ['campaign', 'earn', 'wteSummaryData'],
	components: {
		LightSecondaryButton,
		Tooltip
	},
	data() {
		return {
			conditionTypeValidations: {},
		};
	},
	computed: {
		setupRewardsDisabled() {
			return !Object.values(this.conditionTypeValidations).every(x => x);
		},
	},
	watch: {
		conditionTypeValidations: {
			deep: true,
			handler(newVal) {
				const allValid = Object.values(newVal).every(x => x);
				this.$emit('isComplete', allValid);
			}
		}
	},
	async mounted() {
		const response = await fetch(`${URL_DOMAIN}/shopitems/uiconfig`, {
			method: 'GET',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			}
		});

		const result = await response.json();
		if (!this.earn.shopItemConfig) {
			this.earn.shopItemConfig = result;
		}
		console.log(result);
		this.initializeValidationStates();
	},
	methods: {
		initializeValidationStates() {
			this.conditionTypeValidations = {};
			this.earn?.shopItemConfig?.forEach(conditionType => {
				this.validate(conditionType);
			});
		},
		validate(conditionType) {
			if(conditionType.fieldOnDataModel == 'maxUserRedemptions') {
				this.conditionTypeValidations[conditionType.id] = conditionType.defaultAmount == -1 || conditionType.defaultAmount > 0;
			}
			else {
				this.conditionTypeValidations[conditionType.id] = conditionType.defaultAmount > 0;
			}
		},
	}
}
</script>
<style scoped>.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}
</style>

