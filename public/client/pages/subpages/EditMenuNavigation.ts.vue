<template>
	<div class="p-2 sm:p-7">
		<div class="space-y-6">
			<!-- Section 1: Introduction -->
			<div class="mb-8">
				<h2 class="text-xl font-semibold text-gray-800 mb-3">Enable Loyalty Sidebar Links</h2>
				<p class="text-gray-600 mb-4">
					Add loyalty rewards links anywhere in your store to open the Loyalty Sidebar. Perfect for custom
					reward landing pages,
					banners, or other loyalty-related call-to-actions.
				</p>
			</div>

			<!-- Section 2: Step-by-Step Guide -->
			<div class="space-y-6">
				<div class="bg-white rounded-lg shadow p-6">
					<h3 class="text-lg font-semibold text-gray-800 mb-4">Step 1: Enable Menu Override</h3>
					<div class="space-y-4">
						<p class="text-gray-600">Open your Shopify theme editor to enable the loyalty menu:</p>
						<PrimaryButton cta="Activate Menu Override" size="normal" :icon="false" @click="openThemeEditor"
							class="mt-2" />
						<div class="ml-4 mt-4">
							<ol class="list-decimal space-y-2 text-gray-600">
								<li>Click "App Embeds" in the left menu</li>
								<li>Find "Menu Override" from Raleon</li>
								<li>Toggle it to "On"</li>
								<li>Click "Save" to apply changes</li>
							</ol>
						</div>
					</div>
				</div>

				<div class="bg-white rounded-lg shadow p-6">
					<h3 class="text-lg font-semibold text-gray-800 mb-4">Step 2: Get Your Loyalty Link</h3>
					<div class="space-y-4">
						<p class="text-gray-600">Your loyalty sidebar can be opened using this link:</p>
						<div class="bg-gray-50 p-3 rounded flex items-center justify-between">
							<code class="text-sm text-gray-700">/pages/loyalty-rewards</code>
							<PrimaryButton cta="Copy Link" size="xs" :icon="false" @click="copyLoyaltyLink" />
						</div>
					</div>
				</div>

				<div class="bg-white rounded-lg shadow p-6">
					<h3 class="text-lg font-semibold text-gray-800 mb-4">Step 3: Implementation Examples</h3>
					<div class="space-y-4">
						<p class="text-gray-600">Add the loyalty link to your store navigation:</p>
						<div class="bg-gray-50 p-4 rounded">
							<pre class="text-sm text-gray-700"><code>
Navigate to Online Store Navigation
Add a new menu item with the link "/pages/loyalty-rewards"
Save changes
							</code></pre>
						</div>
					</div>
				</div>
				                <!-- New FAQ Section -->
				<div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Frequently Asked Questions</h3>
                    <div class="space-y-6">
                        <!-- FAQ Item 1 -->
                        <div class="space-y-2">
                            <h4 class="text-base font-medium text-gray-800">The button isn't opening the Loyalty Launcher</h4>
                            <p class="text-gray-600">
                                There are several things to check:
                                <ul class="list-disc ml-4 mt-2 space-y-1">
                                    <li>Ensure the loyalty snippet is enabled on the site via app embeds</li>
                                    <li>Try enabling fuzzy match in the menu override</li>
                                    <li>Double check that the URL is correct</li>
                                </ul>
                            </p>
                        </div>

                        <!-- FAQ Item 2 -->
                        <div class="space-y-2">
                            <h4 class="text-base font-medium text-gray-800">Can I use this for any button on the page?</h4>
                            <p class="text-gray-600">
                                Yes, you can use this link with any button on your page. The only requirement is that the URL matches what you've set in Menu Override.
                            </p>
                        </div>
                    </div>
                </div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import {defineComponent} from 'vue';
import PrimaryButton from '../../components/PrimaryButton.ts.vue';
import { getCurrentOrg } from '../../services/organization.js';

export default defineComponent({
	name: 'EditMenuNavigation',

	components: {
		PrimaryButton
	},
	async mounted() {
		try {
			const organization = await getCurrentOrg();
			if (organization) {
				this.external_domain = organization.externalDomain;
				this.orgId = organization.id;
			}
		} finally {
			this.isDataLoading = false;
		}
	},

	methods: {
		openThemeEditor() {
			const snippetId = localStorage.getItem('shopify_snippet_id');
			window.open(`https://${this.external_domain}/admin/themes/current/editor?context=apps&activateAppId=${snippetId}/menu-override-embed`, '_blank');
		},

		copyLoyaltyLink() {
			navigator.clipboard.writeText('/pages/loyalty-rewards');
			// You could add a toast notification here to confirm the copy
		},
	}
});
</script>

<style scoped></style>
