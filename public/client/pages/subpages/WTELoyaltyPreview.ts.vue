<template>
	<div class="w-full h-fit max-w-[420px] bg-white bg-opacity-75 rounded-2xl border border-violet-200 p-2 md:pt-7 md:pr-7 md:pl-7 md:pb-2 md:mt-0 md:ml-4 relative overflow-scroll">
		<div v-if="isLoading" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 rounded-2xl">
			<div class="spinner"></div>
		</div>

		<PreviewLoyaltyProgram class="flex justify-center mb-4" />
		<div class="flex justify-center mt-4">
			<div id="external-script-container" class="mb-6"></div>
		</div>
	</div>
</template>
<script>

import * as Utils from '../../../client-old/utils/Utils';
import WTEActionIcon from '../../components/WTEActionIcon.ts.vue';
import WTETypeIcon from '../../components/WTETypeIcon.ts.vue';
import PreviewLoyaltyProgram from '../../components/PreviewLoyalty.ts.vue';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: ['earn', 'isLoading', 'isShopReward', 'shouldNotTransform'],
	components: { WTEActionIcon, WTETypeIcon, PreviewLoyaltyProgram },
	data() {
		return {
			branding: {},
			debouncedEarnUpdater: null,
			loyaltyView: this.isShopReward ? 'single-redeem' : 'single-earn',
		};
	},
	watch: {
		'earn': {
			handler: 'debouncedUpdatePreview',
			deep: true,
		},
	},
	async mounted() {
		this.branding = await this.loadBranding();
		this.loadRaleonLoyaltyScript();
	},
	methods: {
		debouncedUpdatePreview: Utils.debounce(async function() {
			await this.updatePreview();
		}, 200),
		async loadBranding() {
			const response = await fetch(`${URL_DOMAIN}/branding`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
					'ngrok-skip-browser-warning': true,
				}
			});

			if (!response.ok || response.status < 200 || response.status >= 300) {
				throw new Error('Failed to retrieve branding');
			}

			const data = await response.json();

			for (const key in data) {
				if (Object.prototype.hasOwnProperty.call(data, key)) {
					this.branding[key] = data[key];
				}
			}
			return this.branding;
		},
		async updatePreview() {
			const shopItem = this.earn.shopItem || this.earn.staticEffect;
			const shouldUpdateShopItem = this.isShopReward && shopItem;
			const shouldUpdateEarn = !this.isShopReward && this.earn && this.earn.condition && this.earn.condition.type;
			if (shouldUpdateShopItem || shouldUpdateEarn || this.shouldNotTransform) {
				const transformedData = this.shouldNotTransform ? this.earn : await this.getWTE();
				window.postMessage({
					type: 'raleon-view-changed',
					item: JSON.stringify(transformedData),
					view: this.loyaltyView,
				});
			}
		},
		async getWTE() {
			const earn = {
				...this.earn,
				shopItem: this.earn.shopItem || this.earn.staticEffect
			};

			const path = this.isShopReward ? 'transform-shop-item-data' : 'transform-earn-data';
			const response = await fetch(`${URL_DOMAIN}/${path}`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
					'ngrok-skip-browser-warning': true,
				},
				body: JSON.stringify({
					earn,
				}),
			});

			let data = await response.json();
			data = this.setDefaultImages(data);

			if (this.earn.staticEffect) {
				data.price = undefined;
				delete data.price;
			}
			
			return data;
		},
		setDefaultImages(data) {
			if (!this.isShopReward) {
				if (data && data.earnEffects?.length) {
					data.earnEffects.forEach((effect) => {
						if (!effect.imageURL) {
							effect.imageURL = this.getEncodedRewardSvg(effect.imageSlotKey);
						}
					});
				}
				if (data && !data.imageURL) {
					data.imageURL = this.getEncodedActionSvg(this.earn.condition.imageSlotKey);
				}
			} else {
				if (data && data.rewards && data.rewards?.[0] && !data.imageURL) {
					data.imageURL = this.getEncodedRewardSvg(data.rewards[0].imageSlotKey);
				}
			}
			return data;
		},
		loadRaleonLoyaltyScript() {
			const isProd = location.hostname === 'app.raleon.io';
			const scriptSrc = isProd ?
				'https://dqpqjbq51w8fz.cloudfront.net/raleon-loyalty.min.js' :
				'https://d2gcw94pe4tjqh.cloudfront.net/raleon-loyalty.min.js'
			const script = document.createElement('script');
			script.src = scriptSrc;
			// script.src = 'http://localhost:8080/output/raleon-loyalty.min.js'
			script.onload = () => this.initializeRaleonLoyalty();
			document.getElementById('external-script-container').appendChild(script);
		},
		initializeRaleonLoyalty() {
			const loyaltyParams = {
				orgId: localStorage.getItem('userOrgId') || 1,
				enableQuests: true,
				defaultOpenChat: true,
				userLoggedIn: true,
				customerId: '123',
				branding: this.branding,
				parentElementId: 'external-script-container',
				initializedView: {
					type: this.loyaltyView,
					item: this.earn,
					definesHeader: true,
					disableNavigation: true,
				},
			};
			window.setupRaleonLoyalty(loyaltyParams);
		},
		getEncodedActionSvg(type) {
			const svg = this.$options.components.WTEActionIcon.methods.getActionIcon.call(this, type);
			const encodedSvg = btoa(svg);
			return `data:image/svg+xml;base64,${encodedSvg}`;
		},
		getEncodedRewardSvg(type) {
			const svg = this.$options.components.WTETypeIcon.methods.getTypeIcon.call(this, type);
			const encodedSvg = btoa(svg);
			return `data:image/svg+xml;base64,${encodedSvg}`;
		},
	},
};
</script>

<style>
.spinner {
  border: 6px solid #f3f3f3;
  border-top: 6px solid #15803D;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.w-full.sticky {
	position: sticky;
	top: 0;
	height: calc(100vh - 140px);
}

</style>
