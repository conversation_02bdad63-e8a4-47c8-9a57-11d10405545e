<template>
	<div class="w-full">
			<div v-if="this.loading" class="w-full flex items-start justify-center flex-col mt-4 animate-pulse mt-2 ml-4 mr-4">
				<div class="w-full bg-white bg-opacity-75 rounded-2xl border border-violet-200 p-2 md:p-7">
					<div class="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-100"></div>
					<div class="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-100 mt-4"></div>
				</div>
			</div>

			<div v-else class="w-full">
				<div v-if="!this.isShopReward" class="flex flex-col ml-4 mr-4">
					<div class="flex items-start">
							<WayToEarnForm
								:loading="loading"
								:earn="earn"
								:predictionRunning="predictionRunning"
								:isEarnImageUploading="isEarnImageUploading"
								@imageSelected="earnImageSelected"
								@isComplete="earnComplete"
								:wteSummary="wteData" />
					</div>
				</div>

				<div class="flex flex-col ml-4 mr-4 mt-4" v-for="(reward, index) in earn.rewards" :key="index">
					<div class="flex items-start">
						<div class="w-full bg-white bg-opacity-75 rounded-2xl border border-violet-200 p-2 md:p-7">
							<div>
								<RewardForm :loading="loading" :reward="reward" :predictionRunning="predictionRunning" :index="index" :isRewardImageUploading="isRewardImageUploading" :rewardSummary="rewardListData && rewardListData.length > index ? rewardListData[index] : null"
									@imageSelected="rewardImageSelected" @isComplete="rewardComplete" />
							</div>
						</div>
					</div>
				</div>
			</div>
	</div>
</template>

<script>

import * as Utils from '../../../client-old/utils/Utils';
import RewardForm from './CampaignWTERewardView.ts.vue'
import WayToEarnForm from './CampaignWTEEarnView.ts.vue'
import LightSecondaryButton from '../../components/LightSecondaryButton.ts.vue';
import ImageUpload from '../../components/ImageUpload.ts.vue';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: ['campaign', 'earn', 'isShopReward', 'wteData', 'rewardListData', 'predictionRunning', 'loading'],
	emits: ['earn-image-updated'],
	components: {
		WayToEarnForm,
		RewardForm,
		LightSecondaryButton,
		ImageUpload,
	},
	data() {
		return {
			isEarnImageUploading: false,
			isRewardImageUploading: false,
			isShopItemImageUploading: false,
			wteComplete: false,
			rewardsComplete: {},
			allComplete: false,
		}
	},
	watch: {
		allComplete: {
			handler(newVal) {
				this.$emit('isComplete', newVal);
			}
		},
	},
	computed: {

	},
	methods: {
		earnImageSelected(event) {
			console.log('earnImageSelected', event);
			this.$emit('earn-image-updated', event.url);
			this.earn.imageURL = event.url;
		},
		earnComplete(isComplete) {
			this.wteComplete = isComplete;
			const allRewardsComplete =
				Object.values(this.rewardsComplete).every(x => x) &&
				this.earn.rewards?.length === Object.keys(this.rewardsComplete).length;

			this.allComplete = this.wteComplete && allRewardsComplete;
		},
		rewardImageSelected(event, index) {
			console.log('rewardImageSelected', event, index);
			this.$emit('reward-image-updated', event.url, index);
			this.earn.rewards[index].imageURL = event.url;
		},
		rewardComplete(isComplete, index) {
			if(this.isShopReward) {
				this.allComplete = isComplete;
				return;
			}
			this.rewardsComplete[index] = isComplete;

			const allRewardsComplete =
				Object.values(this.rewardsComplete).every(x => x) &&
				this.earn.rewards.length === Object.keys(this.rewardsComplete).length;

			this.allComplete = this.wteComplete && allRewardsComplete;
		},
	}
}
</script>
<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}
</style>

