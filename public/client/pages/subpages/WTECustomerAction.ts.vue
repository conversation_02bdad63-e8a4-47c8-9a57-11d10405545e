<template>
	<div class="flex flex-col items-center">
		<div class="flex text-ralsecondary-start text-2xl font-normal font-['Inter'] mb-1 ml-4 mr-4">
			<div class="w-full md:w-[650px] bg-white bg-opacity-75 rounded-2xl border border-violet-200 p-2 md:p-7">
				<div class="text-ralsecondary-start text-2xl font-normal font-['Inter'] mb-4">
					<span v-if="!this.isPoints">Customer Behavior</span>
					<span v-if="this.isPoints">Branding</span>
				</div>
				<div class="w-full">
					<div v-if="this.isLoading && !this.isPoints" class="w-full flex items-start justify-center flex-col mt-4 animate-pulse mt-2">
						<div class="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-full"></div>
						<div class="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-full mt-4"></div>
					</div>

					<div v-if="!this.isLoading && !this.isPoints" class="flex flex-wrap justify-start gap-2">
						<div
							v-for="(schema, index) in wteSchemas"
							:key="index"
							class="cursor-pointer flex items-center justify-center gap-4 w-[100px] h-[100px] md:w-[140px] md:h-[116px] rounded-md border-2 relative"
							:class="{
								'border-ralprimary-light': this.earnCondition?.type === schema.type,
								'border-ralgray-light': this.earnCondition?.type !== schema.type,
								'shadow-xl': this.earnCondition?.type === schema.type,
								'shadow-sm': this.earnCondition?.type !== schema.type,

							}"
							@click="schema.enabled && setType(schema)"
						>
							<div class="flex flex-col items-center" :class="{ 'disabled-schema': !schema.enabled }">
								<WTEActionIcon :icon-type="schema.imageSlotKey"></WTEActionIcon>
								<div class="text-black text-sm font-semibold font-['Inter'] text-center w-full px-2">
									{{ schema.name }}
								</div>
							</div>
							<div v-if="!schema.enabled" class="absolute top-0 right-0 p-1">
								<Tooltip bg="dark" size="md" position="left" maxWidth="50px">
									<div class="text-xs text-white">
										{{  getTooltipText(schema) }}
									</div>
								</Tooltip>
							</div>
						</div>
					</div>

					<div v-if="this.earnCondition && !this.isPoints">
						<div class="flex mt-10">
							<div class="flex flex-col justify-start">
								<div class="flex items-center justify-ste">
									<div class="text-ralsecondary-start text-sm font-bold font-['Inter']  mr-2">
										Conditions
									</div>
									<Tooltip bg="dark" size="md" position="bottom">
										<div class="text-xs whitespace-nowrap text-white">Conditions let you control details like purchase amount, purchase count, social media handle, etc.</div>
									</Tooltip>
								</div>
								<div class="text-ralsecondary-start text-sm font-normal font-['Inter'] mt-2">
									{{ this.earnCondition?.description }}
								</div>
							</div>
						</div>
						<div
							v-if="this.earnCondition?.uiCustomerActionConditions?.length > 0"
							:hidden="this.earnCondition.type == 'welcome-bonus' || this.earnCondition.type == 'referral-bonus'"
						>
							<div v-for="(conditionType, index) in this.earnCondition.uiCustomerActionConditions"
								:key="conditionType.id"
								class="rounded-xl border border-ralbackground-light-line mt-5 p-3 pt-0">
								<div class="flex flex-col sm:flex-row items-center justify-between mx-3 mt-3">
									<div class="text-ralgray-dark text-sm font-medium font-['Inter'] mb-2 sm:mb-0">
										{{ conditionType.operatorLabel }}&nbsp;{{ conditionType.label }}
										<span class="text-xs text-rose-500">*</span>
									</div>
									<div class="flex-grow"></div>
									<div
										v-if="getInputType(conditionType) === 'product' || getInputType(conditionType) === 'collection' || getInputType(conditionType) === 'targetSegment'"
											class="no-focus-outline w-full sm:w-36 text-neutral-700 text-base font-medium font-['Inter'] appearance-none outline-none bg-transparent border-none placeholder-gray-300"
											:class="{
												'text-ralerror-dark border-red': !this.conditionTypeValidations[conditionType.id],
											}"
										>
										<LvDropdown
											class="border-neutral-700 border-opacity-20"
											v-model="selectedDropdownOption"
            								@update:model-value="(newVal) => onDropdownChange(newVal, conditionType)"
											optionLabel="name"
											optionValue="code"
											placeholder="Select Option"
											emptyFilterMessage="No result found"
											icon-right="light-icon-arrow-down-circle"
											filterPlaceholder="Search"
											:options="dropdownOptions"
											:filter="true"
											:bottom-bar="true"
										/>
									</div>
									<div v-else class="w-full sm:w-36 h-10 px-2 py-3 bg-white rounded-lg border flex justify-center items-center gap-2.5"
										:class="{
											'border-ralerror-dark': !this.conditionTypeValidations[conditionType.id],
											'border-neutral-700 border-opacity-20': this.conditionTypeValidations[conditionType.id],
										}">
										<input :type="getInputType(conditionType)"
											class="no-focus-outline w-full sm:w-36 pl-4 text-neutral-700 text-base font-medium font-['Inter'] appearance-none outline-none bg-transparent border-none placeholder-gray-300"
											:class="{
												'text-ralerror-dark border-red': !this.conditionTypeValidations[conditionType.id],
											}"
											:value="getInputValue(conditionType)"
											@input="updateValue($event, conditionType)"
											:placeholder="conditionType.placeholderText || ''"/>
									</div>
								</div>
							</div>
						</div>
					</div>

					<WayToEarnForm
						:loading="this.isLoading"
						:earn="this.earn"
						:is-giveaway="isGiveaway"
						@imageSelected="(event) => this.$emit('earn-image-selected', event.url)"
						@isComplete="(event) => { this.earnDataValid = event; this.checkComplete(); }"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import * as Utils from '../../../client-old/utils/Utils';
import SuperDropdown from '../../components/SuperDropdown.ts.vue';
import LightSecondaryButton from '../../components/LightSecondaryButton.ts.vue';
import Tooltip from '../../components/Tooltip.ts.vue';
import WTEActionIcon from '../../components/WTEActionIcon.ts.vue';
import WayToEarnForm from './CampaignWTEEarnView.ts.vue';
import LvDropdown  from 'lightvue/dropdown';
const URL_DOMAIN = Utils.URL_DOMAIN;

import { getVipTiers, getFoundationalCampaign } from '../../services/giveaway.js';
import { isFeatureAvailable } from '../../services/features.js';

export default {
	props: ['campaign', 'earn', 'isShopReward', 'isComplete', 'wteData', 'isPoints', 'isGiveaway'],
	emits: ['condition-updated', 'earn-image-selected'],
	components: {
		SuperDropdown,
		LightSecondaryButton,
		Tooltip,
		WTEActionIcon,
		LvDropdown,
		WayToEarnForm,
	},
	data() {
		return {
			wteSchemas: [],
			wteTypeSet: false,
			conditionTypeValidations: {},
			isLoading: true,
			earnCondition: {},
			earnDataValid: false,
			dropdownOptions: [],
			selectedDropdownOption: null,
		};
	},
	async mounted() {
		this.wteSchemas = await this.getCustomerActions();


		if (this.earn.condition?.type) {
			this.earnCondition = this.earn.condition;
		} else {
			this.setType(this.wteSchemas[0]);
		}
		this.wteTypeSet = !!this.getType();
		this.initializeValidationStates();
		console.log(JSON.stringify(this.earn));
		this.isLoading = false;
	},
	computed: {
		setupRewardsDisabled() {
			const earnTypeSet = this.wteTypeSet;
			const conditionTypesValid = Object.values(
				this.conditionTypeValidations,
			).every(x => x);
			return !earnTypeSet || !conditionTypesValid;
		},
		isPoints() {
			return this.$route.path.includes('points');
		},
	},
	watch: {
		conditionTypeValidations: {
			deep: true,
			handler() {
				this.checkComplete();
			},
		},
		earn: {
			handler() {
				this.initializeValidationStates();
				this.earnCondition = this.earn.condition;
				this.fetchDropdownOptions();
			},
			deep: true,
			immediate: true,
		}
	},
	methods: {
		onDropdownChange(newVal, conditionType) {
			if(!conditionType) {
				console.log('conditionType not found');
				return;
			}
			this.selectedDropdownOption = newVal;
			console.log(newVal);
            conditionType.defaultTextValue = `${newVal}`;

			let selectedCondition = this.dropdownOptions.find(x => x.code === newVal);
			conditionType.handle = selectedCondition?.handle;
			if(selectedCondition?.image) {
				this.earn.imageURL = selectedCondition?.image;
			}
			this.validate(conditionType);
        },
		async fetchDropdownOptions() {
			if(!this.earn?.condition) return;
            try {
                if (PURCHASE_ACTION_TYPES.includes(this.earn.condition.type)) {
					const response = await fetch(`${URL_DOMAIN}/shop-products`, {
						method: 'GET',
						credentials: 'omit',
						mode: 'cors',
						headers: {
							'Content-Type': 'application/json',
							Authorization: `Bearer ${localStorage.getItem('token')}`,
						},
					});
					const result = await response.json();
					console.log('products', result);
					this.dropdownOptions = result.map(x => ({ name: x.title, code: `${x.id}`, handle: x.handle, image: x.image}));
				} else if (COLLECTION_ACTION_TYPES.includes(this.earn.condition.type)) {
					const response = await fetch(`${URL_DOMAIN}/shop-collections`, {
						method: 'GET',
						credentials: 'omit',
						mode: 'cors',
						headers: {
							'Content-Type': 'application/json',
							Authorization: `Bearer ${localStorage.getItem('token')}`,
						},
					});
					const result = await response.json();
					console.log('collections', result);
					this.dropdownOptions = result.map(x => ({ name: x.title, code: `${x.id}`, handle: x.handle }));
				} else if (GIVEAWAY_ACTION_TYPES.includes(this.earn.condition.type)) {
					const [ vipTierCampaigns, foundationalCampaignInfo ] = await Promise.all([
						getVipTiers(),
						getFoundationalCampaign()
					]);
					const options = vipTierCampaigns.map(x => {
						return {
							name: x.name,
							code: `${x?.loyaltyCampaignId}`,
							handle: x.name,
						};
					});
					const foundationalCampaign = {
						name: 'Everyone',
						code: `${foundationalCampaignInfo?.id}`,
						handle: 'Everyone'
					};
					options.unshift(foundationalCampaign);
					this.dropdownOptions = options;
					if (this.earn?.loyaltyCampaignId) {
						this.selectedDropdownOption = options.find(x => x.code == `${this.earn.loyaltyCampaignId}`);
					} else {
						this.selectedDropdownOption = foundationalCampaign;
					}
				}
			} catch (error) {
				console.error('Error fetching dropdown options:', error);
				// Handle errors appropriately
			}

			//Might need to change this if we ever want more than one dropdown
			this.selectedDropdownOption = this.earn.condition?.uiCustomerActionConditions?.[0]?.defaultTextValue;
        },
		async getCustomerActions() {
			this.isLoading = true;
			const campaignId = this.$route.params.campaignId;
			const response = await fetch(`${URL_DOMAIN}/wte/customer-actions?campaignId=${campaignId}`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			let result = (await response.json())
				.filter(x => this.isPoints ? x.type === 'dollar-spent' : x.type !== 'dollar-spent')
				.sort((a, b) => {
					if (a.enabled === false && b.enabled === false) return 0;
					if (a.enabled === false) return 1;
					if (b.enabled === false) return -1;

					const aFollow = a.type.startsWith('follow-');
					const bFollow = b.type.startsWith('follow-');

					if (aFollow && bFollow) return 0;
					if (aFollow) return 1;
					if (bFollow) return -1;

					return 0;
				});

			if (this.isGiveaway) {
				//do an includes array here for all the giveaway types
				result = result.filter(x => x.type == 'click-to-redeem' || x.type == 'auto-redeem');
			} else {
				result = result.filter(x => !x.hideFromWteConfig);
			}

			return result;
		},
		getInputType(conditionType) {
			return conditionType.propertyType;
		},
		getInputValue(conditionType) {
			if (EARN_CONDITION_TEXT_TYPES.includes(this.earnCondition.type)) {
				return conditionType.defaultTextValue;
			}
			this.validate(conditionType);
			return conditionType.defaultAmount;
		},
		updateValue(event, conditionType) {
			if(conditionType.propertyType == 'string') {
				console.log('updateValue', event.target.value);
				conditionType.defaultTextValue = event.target.value;
			}
			else {
				console.log('updateValue', event.target.value);
				conditionType.defaultAmount = event.target.value;
			}
			this.validate(conditionType);
			this.$emit('condition-updated', this.earnCondition);
		},
		getType() {
			return this.wteSchemas?.find(
				x =>
					x.type === this.earn?.condition?.type ||
					x.type === this.earn.name,
			);
		},
		setType(schema) {
			this.wteTypeSet = true;
			if (this.earnCondition?.type === schema.type) {
				return;
			}

			this.earnCondition = { ...schema };

			this.initializeValidationStates();
			this.$emit('condition-updated', this.earnCondition);
		},
		initializeValidationStates() {
			this.conditionTypeValidations = {};
			this.earnCondition?.uiCustomerActionConditions?.forEach(
				conditionType => {
					this.validate(conditionType);
				},
			);
		},
		validate(conditionType) {
			this.wteTypeSet = !!this.getType();
			const textValueTypes = ['string', 'collection', 'product', 'targetSegment'];
			if(textValueTypes.includes(conditionType.propertyType)) {
				this.conditionTypeValidations[conditionType.id] =
					conditionType?.defaultTextValue?.length > 0;
			} else {
				this.conditionTypeValidations[conditionType.id] =
					conditionType.defaultAmount >= 0;
			}
		},
		checkComplete() {
			const earnTypeSet = this.wteTypeSet;
			const allValid = Object.values(this.conditionTypeValidations).every(x => x);
			this.$emit('isComplete', earnTypeSet && allValid && this.earnDataValid);
		},
		getTooltipText(schema) {
			const productReviewTypes = ['product-review', 'product-photo-review'];
			const subscriptionTypes = ['first-subscription-purchase', 'milestone-subscription-purchase', 'subscription-purchase'];
			if (productReviewTypes.includes(schema.type)) {
				return 'A product review integration must be configured. Go to the Integrations menu to set up and enable the Product Reviews Way to Earn.';
			} else if (subscriptionTypes.includes(schema.type)) {
				return 'A subscription integration must be configured to enable this WTE. Go to the Integrations menu to set up and enable the Subscription Way to Earn.';
			} else if (schema.type === 'timed-purchase') {
				return 'Time based purchases can only be configured on time-based campaigns. Go to the Campaigns menu to set up a time-based campaign.';
			} else if (['welcome-bonus', 'birthday-bonus'].includes(schema.type) && !isFeatureAvailable(`wte-${schema.type}`)) {
				return 'This behavior requires a higher Raleon Plan.';
			} else if (schema.type.startsWith('follow-') && !isFeatureAvailable('wte-social-follows')) {
				return 'This behavior requires a higher Raleon Plan.';
			} else {
				return 'This WTE is not currently enabled.';
			}
		},
	},
};

const EARN_CONDITION_TEXT_TYPES = ['follow-on-instagram', 'follow-on-tiktok', 'follow-on-facebook', 'follow-facebook-group', 'follow-on-youtube', 'follow-on-custom'];
const SOCIAL_EARN_CONDITION_LABELS = ['instagramHandle', 'tikTokHandle', 'facebookHandle', 'facebookGroup'];
const PURCHASE_ACTION_TYPES = ['specific-product-purchase'];
const COLLECTION_ACTION_TYPES = ['collection-product-purchase'];
const GIVEAWAY_ACTION_TYPES = ['click-to-redeem', 'auto-redeem'];

</script>
<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}
.disabled-schema {
    cursor: default;
    opacity: 0.5;
}
</style>
