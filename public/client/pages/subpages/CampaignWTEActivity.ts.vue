<template>
	<div class="flex flex-col ml-4 mr-4">
		<div class="flex items-start">
			<div class="w-3/4 md:w-[650px] bg-white bg-opacity-75 rounded-2xl border border-violet-200 p-2 md:p-7">
				<div class="text-ralsecondary-start text-2xl font-normal font-['Inter'] mb-4">
					<span>Customer Action</span>
				</div>
				<div class="w-full mx-auto">
					<div v-if="isLoading" role="status"
						class="p-4 space-y-4 bg-gray-300 rounded-full dark:bg-gray-600 w-56 rounded-2xl shadow animate-pulse">
					</div>

					<div v-if="!isLoading" class="flex flex-wrap justify-start gap-2">
						<div
							v-for="(schema, index) in wteSchemas"
							:key="index"
							class="cursor-pointer flex items-center justify-center w-[80px] h-[80px] rounded-md border-2 border-ralprimary-light"
							:class="{'bg-ralprimary-light': this.earn.condition?.type === schema.type, 'bg-ralsecondary-light': this.earn.condition?.type !== schema.type}"
							@click="setType(schema)"
						>
							<div
								v-if="this.earn.condition?.type === schema.type"
								class="text-white text-xs font-bold font-['Inter'] text-center w-full px-2" >
								{{ schema.name }}
							</div>
							<div
								v-else
								class="text-black text-xs font-semibold font-['Inter'] text-center w-full px-2">
								{{ schema.name }}
							</div>
						</div>
					</div>



					<!-- <SuperDropdown :options="wteSchemas" option-label-key="name" option-subtitle-key="description"
						placeholder="Select a way to earn" :model-value="getType()"
						@update:model-value="value => setType(value)" option-left-slot-key="imageSlotKey" v-if="!isLoading">
						<template v-slot:dollar-spent>
							<svg class="mr-4" fill="#15803D" slot="dollar-spent" xmlns="http://www.w3.org/2000/svg"
								height="33" viewBox="0 -960 960 960" width="33">
								<path
									d="M237-120q-23 0-44.5-16T164-175q-25-84-41-145.5t-25.5-108Q88-475 84-511t-4-69q0-92 64-156t156-64h200q27-36 68.5-58t91.5-22q25 0 42.5 17.5T720-820q0 6-1.5 12t-3.5 11q-4 11-7.5 22t-5.5 24l91 91h47q17 0 28.5 11.5T880-620v210q0 13-7.5 23T852-372l-85 28-50 167q-8 26-29 41.5T640-120h-80q-33 0-56.5-23.5T480-200h-80q0 33-23.5 56.5T320-120h-83Zm3-80h80v-80h240v80h80l62-206 98-33v-141h-40L620-720q0-20 2.5-39t7.5-37q-29 8-51 27.5T547-720H300q-58 0-99 41t-41 99q0 41 21 140.5T240-200Zm400-320q17 0 28.5-11.5T680-560q0-17-11.5-28.5T640-600q-17 0-28.5 11.5T600-560q0 17 11.5 28.5T640-520Zm-160-80q17 0 28.5-11.5T520-640q0-17-11.5-28.5T480-680H360q-17 0-28.5 11.5T320-640q0 17 11.5 28.5T360-600h120Zm0 102Z" />
							</svg>
						</template>

						<template v-slot:nth-purchase>
							<svg class="mr-4" fill="#15803D" slot="nth-purchase" xmlns="http://www.w3.org/2000/svg"
								height="33" viewBox="0 -960 960 960" width="33">
								<path
									d="M240-80q-33 0-56.5-23.5T160-160v-480q0-33 23.5-56.5T240-720h80q0-66 47-113t113-47q66 0 113 47t47 113h80q33 0 56.5 23.5T800-640v480q0 33-23.5 56.5T720-80H240Zm0-80h480v-480h-80v80q0 17-11.5 28.5T600-520q-17 0-28.5-11.5T560-560v-80H400v80q0 17-11.5 28.5T360-520q-17 0-28.5-11.5T320-560v-80h-80v480Zm160-560h160q0-33-23.5-56.5T480-800q-33 0-56.5 23.5T400-720ZM240-160v-480 480Z" />
							</svg>
						</template>

						<template v-slot:collection-product-purchase>
							<svg class="mr-4" fill="#15803D" slot="nth-purchase" xmlns="http://www.w3.org/2000/svg"
								height="33" viewBox="0 -960 960 960" width="33">
								<path
									d="M240-80q-33 0-56.5-23.5T160-160v-480q0-33 23.5-56.5T240-720h80q0-66 47-113t113-47q66 0 113 47t47 113h80q33 0 56.5 23.5T800-640v480q0 33-23.5 56.5T720-80H240Zm0-80h480v-480h-80v80q0 17-11.5 28.5T600-520q-17 0-28.5-11.5T560-560v-80H400v80q0 17-11.5 28.5T360-520q-17 0-28.5-11.5T320-560v-80h-80v480Zm160-560h160q0-33-23.5-56.5T480-800q-33 0-56.5 23.5T400-720ZM240-160v-480 480Z" />
							</svg>
						</template>

						<template v-slot:specific-product-purchase>
							<svg class="mr-4" fill="#15803D" slot="nth-purchase" xmlns="http://www.w3.org/2000/svg"
								height="33" viewBox="0 -960 960 960" width="33">
								<path
									d="M240-80q-33 0-56.5-23.5T160-160v-480q0-33 23.5-56.5T240-720h80q0-66 47-113t113-47q66 0 113 47t47 113h80q33 0 56.5 23.5T800-640v480q0 33-23.5 56.5T720-80H240Zm0-80h480v-480h-80v80q0 17-11.5 28.5T600-520q-17 0-28.5-11.5T560-560v-80H400v80q0 17-11.5 28.5T360-520q-17 0-28.5-11.5T320-560v-80h-80v480Zm160-560h160q0-33-23.5-56.5T480-800q-33 0-56.5 23.5T400-720ZM240-160v-480 480Z" />
							</svg>
						</template>

						<template v-slot:timed-purchase>
							<svg class="mr-4" fill="#15803D" slot="timed-purchase" xmlns="http://www.w3.org/2000/svg"
								height="33" viewBox="0 -960 960 960" width="33">
								<path d="M360-840v-80h240v80H360Zm80 440h80v-240h-80v240Zm40 320q-74 0-139.5-28.5T226-186q-49-49-77.5-114.5T120-440q0-74 28.5-139.5T226-694q49-49 114.5-77.5T480-800q62 0 119 20t107 58l56-56 56 56-56 56q38 50 58 107t20 119q0 74-28.5 139.5T734-186q-49 49-114.5 77.5T480-80Zm0-80q116 0 198-82t82-198q0-116-82-198t-198-82q-116 0-198 82t-82 198q0 116 82 198t198 82Zm0-280Z"/>
							</svg>
						</template>

						<template v-slot:ig-follow>
							<svg class="mr-4" fill="#15803D" slot="ig-follow" xmlns="http://www.w3.org/2000/svg" height="33"
								width="33" viewBox="0 0 56.7 56.7">
								<g>
									<path d="M28.2,16.7c-7,0-12.8,5.7-12.8,12.8s5.7,12.8,12.8,12.8S41,36.5,41,29.5S35.2,16.7,28.2,16.7z M28.2,37.7
								c-4.5,0-8.2-3.7-8.2-8.2s3.7-8.2,8.2-8.2s8.2,3.7,8.2,8.2S32.7,37.7,28.2,37.7z" />
									<circle cx="41.5" cy="16.4" r="2.9" />
									<path d="M49,8.9c-2.6-2.7-6.3-4.1-10.5-4.1H17.9c-8.7,0-14.5,5.8-14.5,14.5v20.5c0,4.3,1.4,8,4.2,10.7c2.7,2.6,6.3,3.9,10.4,3.9
								h20.4c4.3,0,7.9-1.4,10.5-3.9c2.7-2.6,4.1-6.3,4.1-10.6V19.3C53,15.1,51.6,11.5,49,8.9z M48.6,39.9c0,3.1-1.1,5.6-2.9,7.3
								s-4.3,2.6-7.3,2.6H18c-3,0-5.5-0.9-7.3-2.6C8.9,45.4,8,42.9,8,39.8V19.3c0-3,0.9-5.5,2.7-7.3c1.7-1.7,4.3-2.6,7.3-2.6h20.6
								c3,0,5.5,0.9,7.3,2.7c1.7,1.8,2.7,4.3,2.7,7.2V39.9L48.6,39.9z" />
								</g>
							</svg>
						</template>
						<template v-slot:tt-follow>
							<svg viewBox="0 0 256 256" class="mr-4" fill="#15803D" width="33" height="33" slot="tt-follow"
								xmlns="http://www.w3.org/2000/svg">
								<rect fill="none" height="256" width="256" />
								<path
									d="M168,106a95.9,95.9,0,0,0,56,18V84a56,56,0,0,1-56-56H128V156a28,28,0,1,1-40-25.3V89.1A68,68,0,1,0,168,156Z"
									fill="none" stroke="#15803D" stroke-linecap="round" stroke-linejoin="round"
									stroke-width="16" />
							</svg>
						</template>

						<template v-slot:fb-follow>
							<svg class="mr-4" height="33" width="33" fill="#15803D" version="1.1" viewBox="0 0 512 512"
								xml:space="preserve" xmlns="http://www.w3.org/2000/svg"
								xmlns:xlink="http://www.w3.org/1999/xlink">
								<path
									d="M288,192v-38.1c0-17.2,3.8-25.9,30.5-25.9H352V64h-55.9c-68.5,0-91.1,31.4-91.1,85.3V192h-45v64h45v192h83V256h56.4l7.6-64  H288z M330.2,240h-41.1H272v15.5V432h-51V255.5V240h-14.9H176v-32h30.1H221v-16.5v-42.2c0-24.5,5.4-41.2,15.5-51.8  C247.7,85.5,267.6,80,296.1,80H336v32h-17.5c-12,0-27.5,1.1-37.1,11.7c-8.1,9-9.4,20.1-9.4,30.1v37.6V208h17.1H334L330.2,240z"
									stroke="#15803D" stroke-width="10" />
							</svg>
						</template>

						<template v-slot:product-review>
							<svg xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"
								class="mr-4" fill="#15803D" slot="product-review">
								<path
									d="m480-461 76 46q11 7 22-.5t8-20.5l-20-87 68-59q10-9 6-21.5T622-617l-89-7-35-82q-5-12-18-12t-18 12l-35 82-89 7q-14 1-18 13.5t6 21.5l68 59-20 87q-3 13 8 20.5t22 .5l76-46ZM240-240l-92 92q-19 19-43.5 8.5T80-177v-623q0-33 23.5-56.5T160-880h640q33 0 56.5 23.5T880-800v480q0 33-23.5 56.5T800-240H240Zm-34-80h594v-480H160v525l46-45Zm-46 0v-480 480Z" />
							</svg>
						</template>
					</SuperDropdown> -->

					<div v-if="earn.condition?.uiCustomerActionConditions?.length > 0">
						<div class="flex mt-10">
							<div class="text-ralsecondary-start text-sm font-bold font-['Inter']">
								Conditions
							</div>

							<div class="w-5 h-5 relative opacity-50 ml-2">
								<svg width="20" height="20" viewBox="0 0 20 20" fill="none"
									xmlns="http://www.w3.org/2000/svg">
									<g opacity="0.5">
										<path
											d="M10 9.16667V13.3333M10 17.5C5.85786 17.5 2.5 14.1421 2.5 10C2.5 5.85786 5.85786 2.5 10 2.5C14.1421 2.5 17.5 5.85786 17.5 10C17.5 14.1421 14.1421 17.5 10 17.5ZM10.0415 6.66667V6.75L9.9585 6.75016V6.66667H10.0415Z"
											stroke="#414141" stroke-width="1.5" stroke-linecap="round"
											stroke-linejoin="round" />
									</g>
								</svg>
							</div>
						</div>
						<div v-if="this.earn && this.earn.condition">
							<div v-for="(conditionType, index) in this.earn.condition.uiCustomerActionConditions"
								:key="conditionType.id"
								class="rounded-xl border border-ralbackground-light-line mt-5 p-3 pt-0">
								<div class="flex flex-col sm:flex-row items-center justify-between mx-3 mt-3">
									<div class="text-ralgray-dark text-sm font-medium font-['Inter'] mb-2 sm:mb-0">
										{{ conditionType.operatorLabel }}
										<span class="text-xs text-rose-500 ml-1">*</span>
									</div>
									<div class="flex-grow"></div>
									<div
										v-if="getInputType() === 'text' || getInputType() === 'number'"
										class="w-full sm:w-36 h-10 px-2 py-3 bg-white rounded-lg border flex justify-center items-center gap-2.5"
										:class="{
											'border-ralerror-dark': !this.conditionTypeValidations[conditionType.id],
											'border-neutral-700 border-opacity-20': this.conditionTypeValidations[conditionType.id],
										}">
										<input
											:type="getInputType()"
											class="no-focus-outline w-full sm:w-36 text-neutral-700 text-base font-medium font-['Inter'] appearance-none outline-none bg-transparent border-none text-center"
											:class="{
												'text-ralerror-dark border-red': !this.conditionTypeValidations[conditionType.id],
											}"
											:value="getInputValue(conditionType)"
											@input="updateValue($event, conditionType)"
										/>

									</div>
									<div
										v-if="getInputType() === 'product' || getInputType() === 'collection'"
										class="w-full sm:w-50 h-10 px-2 py-3 flex justify-center items-center gap-2.5"
										>
										<LvDropdown
											class="border-neutral-700 border-opacity-20"
											v-model="selectedDropdownOption"
            								@update:model-value="(newVal) => onDropdownChange(newVal, conditionType)"
											optionLabel="name"
											optionValue="code"
											placeholder="Select Option"
											emptyFilterMessage="No result found"
											icon-right="light-icon-arrow-down-circle"
											filterPlaceholder="Search"
											:options="dropdownOptions"
											:filter="true"
											:bottom-bar="true"
											/>
										</div>
									<div class="text-ralgray-dark text-sm font-medium font-['Inter'] ml-3">
										{{ conditionType.label }}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- <WTESummary :wteData="this.wteSummaryData.wte" :rewardListData="this.wteSummaryData.rewards"
				class="hidden md:block" /> -->
		</div>
		<!-- <div class="flex justify-end flex-wrap items-center mt-4 mb-4">
			<a
				class="h-8 pl-2 pr-4 py-1.5 rounded-3xl justify-start items-center gap-1 inline-flex cursor-pointer hover:underline ml-2">
			</a>
			<div class="flex-grow"></div>
			<LightSecondaryButton cta="Setup Rewards" @click="$emit('next-tab')" :isDisabled="setupRewardsDisabled"
				class="mr-2 sm:mr-0" style="margin-right: 26%">
			</LightSecondaryButton>
		</div> -->
	</div>
</template>

<script>
import * as Utils from '../../../client-old/utils/Utils';
import SuperDropdown from '../../components/SuperDropdown.ts.vue';
import LightSecondaryButton from '../../components/LightSecondaryButton.ts.vue';
import WTESummary from '../../components/WTESummary.ts.vue'
import LvDropdown  from 'lightvue/dropdown';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: ['campaign', 'earn', 'isShopReward', 'isComplete', 'wteSummaryData'],
	emits: ['tabsDisabled'],
	components: {
		SuperDropdown,
		LightSecondaryButton,
		WTESummary,
		LvDropdown
	},
	data() {
		return {
			wteSchemas: [],
			wteTypeSet: false,
			conditionTypeValidations: {},
			isLoading: true,
			dropdownOptions: [],
			selectedDropdownOption: null,
		};
	},
	computed: {
		setupRewardsDisabled() {
			const earnTypeSet = this.wteTypeSet;
			const conditionTypesValid = Object.values(
				this.conditionTypeValidations,
			).every(x => x);
			return !earnTypeSet || !conditionTypesValid;
		},

		isPoints() {
			return this.$route.path.includes('points');
		},
	},
	watch: {
		conditionTypeValidations: {
			deep: true,
			handler(newVal) {
				const earnTypeSet = this.wteTypeSet;
				const allValid = Object.values(newVal).every(x => x);
				this.$emit('isComplete', earnTypeSet && allValid);
			},
		},
		earn: {
			handler() {
				this.initializeValidationStates();
				this.fetchDropdownOptions();
			},
			deep: true,
			immediate: true,
		}
	},
	async mounted() {
		this.wteSchemas = await this.getCustomerActions();
		this.wteTypeSet = !!this.getType();
		this.initializeValidationStates();
	},
	methods: {
		onDropdownChange(newVal, conditionType) {
			console.log('onDropdownChange', newVal);
			if(!conditionType) {
				console.log('conditionType not found');
				return;
			}
			this.selectedDropdownOption = newVal;
            conditionType.defaultTextValue = `${newVal}`;
			console.log('Updated defaultTextValue', conditionType.defaultTextValue)
			this.validate(conditionType);
        },
		async fetchDropdownOptions() {
            // Replace with your actual API call
			if(!this.earn?.condition) return;
            try {
                if(PURCHASE_ACTION_TYPES.includes(this.earn.condition.type)) {
					const response = await fetch(`${URL_DOMAIN}/shop-products`, {
						method: 'GET',
						credentials: 'omit',
						mode: 'cors',
						headers: {
							'Content-Type': 'application/json',
							Authorization: `Bearer ${localStorage.getItem('token')}`,
						},
					});
					const result = await response.json();
					console.log('products', result);
					this.dropdownOptions = result.map(x => ({ name: x.title, code: `${x.id}` }));
				}
				else if(COLLECTION_ACTION_TYPES.includes(this.earn.condition.type)) {
					const response = await fetch(`${URL_DOMAIN}/shop-collections`, {
						method: 'GET',
						credentials: 'omit',
						mode: 'cors',
						headers: {
							'Content-Type': 'application/json',
							Authorization: `Bearer ${localStorage.getItem('token')}`,
						},
					});
					const result = await response.json();
					console.log('collections', result);
					this.dropdownOptions = result.map(x => ({ name: x.title, code: `${x.id}` }));
				}
			} catch (error) {
				console.error('Error fetching dropdown options:', error);
				// Handle errors appropriately
			}

			//Might need to change this if we ever want more than one dropdown
			console.log('defaultTextValue', this.earn.condition.uiCustomerActionConditions[0].defaultTextValue);
			this.selectedDropdownOption = this.earn.condition.uiCustomerActionConditions[0].defaultTextValue;
			console.log('selectedDropdownOption', this.selectedDropdownOption);
        },
		async getCustomerActions() {
			const campaignId = this.$route.params.campaignId;
			const response = await fetch(`${URL_DOMAIN}/wte/customer-actions?campaignId=${campaignId}`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			const result = (await response.json())
				.filter(x => this.isPoints ? x.type === 'dollar-spent' : x.type !== 'dollar-spent')
				.sort((a, b) => {
					if (a.enabled === false && b.enabled === false) return 0;
					if (a.enabled === false) return 1;
					if (b.enabled === false) return -1;

					const aFollow = a.type.startsWith('follow-');
					const bFollow = b.type.startsWith('follow-');

					if (aFollow && bFollow) return 0;
					if (aFollow) return 1;
					if (bFollow) return -1;

					return 0;
				});

			this.setType(result[0]);
			this.isLoading = false;
			return result;
		},
		getInputType() {
			if (EARN_CONDITION_TEXT_TYPES.includes(this.earn.condition.type)) {
				return 'text';
			}
			if(this.earn.condition.type === 'specific-product-purchase') {
				return 'product';
			}
			if(this.earn.condition.type === 'collection-product-purchase') {
				return 'collection';
			}
			return 'number';
		},
		getInputValue(conditionType) {
			if (EARN_CONDITION_TEXT_TYPES.includes(this.earn.condition.type)) {
				return conditionType.defaultTextValue;
			}
			return conditionType.defaultAmount;
		},
		updateValue(event, conditionType) {
			const newValue = event.target.value;
			if (EARN_CONDITION_TEXT_TYPES.includes(this.earn.condition.type)) {
				conditionType.defaultTextValue = newValue;
			} else {
				conditionType.defaultAmount = newValue;
			}
			this.validate(conditionType);
		},
		getType() {
			return this.wteSchemas?.find(
				x =>
					x.type === this.earn?.condition?.type ||
					x.type === this.earn.name,
			);
		},
		setType(schema) {
			this.wteTypeSet = true;
			if (this.earn.condition?.type === schema.type) {
				return;
			}
			this.earn.condition = {
				...schema,
			};

			this.initializeValidationStates();
			this.earn.rewards = this.earn.rewards || [];
		},
		initializeValidationStates() {
			this.conditionTypeValidations = {};
			this.earn?.condition?.uiCustomerActionConditions?.forEach(
				conditionType => {
					this.validate(conditionType);
				},
			);
		},
		validate(conditionType) {
			console.log('validate', conditionType.variable, conditionType.defaultAmount, conditionType.defaultTextValue);
			this.wteTypeSet = !!this.getType();
			if (conditionType?.variable === 'purchaseTotalIncrease') {
				this.conditionTypeValidations[conditionType.id] =
					conditionType.defaultAmount >= 0;
			} else if (SOCIAL_EARN_CONDITION_LABELS.includes(conditionType.variable)) {
				this.conditionTypeValidations[conditionType.id] =
					conditionType?.defaultTextValue?.length > 0;
			} else if(conditionType?.variable === 'productId') {
				this.conditionTypeValidations[conditionType.id] =
					conditionType?.defaultTextValue?.length > 0;
				console.log('productId', conditionType?.defaultTextValue?.length > 0, conditionType.defaultTextValue);
			}
			else if(conditionType?.variable === 'collectionId') {
				this.conditionTypeValidations[conditionType.id] =
					conditionType?.defaultTextValue?.length > 0;
			}
			else {
				this.conditionTypeValidations[conditionType.id] =
					conditionType.defaultAmount > 0;
			}

			console.log('conditionTypeValidations', this.conditionTypeValidations);
		},
	},
};

const EARN_CONDITION_TEXT_TYPES = ['follow-on-instagram', 'follow-on-tiktok', 'follow-on-facebook', 'follow-facebook-group', 'follow-on-youtube', 'follow-on-custom'];
const PURCHASE_ACTION_TYPES = ['specific-product-purchase'];
const COLLECTION_ACTION_TYPES = ['collection-product-purchase'];
const SOCIAL_EARN_CONDITION_LABELS = ['instagramHandle', 'tikTokHandle', 'facebookHandle', 'facebookGroup'];
</script>
<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}
</style>
