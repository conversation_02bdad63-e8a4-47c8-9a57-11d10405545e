<template>
	<div class="w-full mt-10">
		<div class="flex items-center justify-ste">
			<div class="text-ralsecondary-start text-sm font-bold font-['Inter'] mr-2">
				Branding
			</div>
			<Tooltip bg="dark" size="md" position="bottom">
				<div class="text-xs whitespace-nowrap text-white">
					We use AI to give you ideas on text and imagery here!
					<a
						class="pl-2 pr-4 items-center gap-1 inline-flex cursor-pointer hover:underline ml-2"
						href="https://docs.raleon.io/docs/setting-up-a-new-campaign"
						target="_blank">
						What is branding?
					</a>
				</div>
			</Tooltip>
			<PrimaryButton
				class="text-sm ml-auto"
				cta="Smart Suggest"
				size="xs"
				:disabled="predictionRunning"
				@click="predictBranding"
			/>
		</div>
		<div class="flex items-center justify-ste mt-4">
			<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide mr-2">
				Name
				<span class="text-rose-500">*</span>
				<div class="mb-3 text-ralgray-dark text-xs font-normal font-['Inter']">
					This will be displayed to the customer in the Loyalty Sidebar.
				</div>
			</div>
		</div>
		<div class="mb-8">
			<div v-if="predictionRunning || loading" role="status" class="w-full flex p-3 space-y-4 justify-end bg-gray-300 dark:bg-gray-600  rounded-2xl shadow animate-pulse">
				<img src="../../images/magic.svg" width="20" height="60" />
			</div>

			<div v-if="!predictionRunning && !loading"
				class="w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"
				:class="{
					'border-ralerror-dark': v$.earn.name.$dirty && v$.earn.name.$errors.length,
					'border-gray-400': !v$.earn.name.$errors.length || !v$.earn.name.$dirty,
				}"
			>
				<div class="w-full self-stretch justify-start items-center gap-2 flex">
					<input
						class="no-focus-outline border-none w-full text-gray-600 text-sm font-semibold font-['Inter'] leading-normal appearance-none outline-none"
						:class="{'animate-pulse bg-gray-300 dark:bg-gray-600': predictionRunning}"
						:disabled="predictionRunning"
						maxlength="40"
						v-model="v$.earn.name.$model"
					/>
					<img src="../../images/magic.svg" width="20" height="60" />
				</div>
			</div>
		</div>
		<div class="text-ralgray-dark text-sm font-semibold font-['Inter'] leading-normal tracking-wide">
			Way To {{ isGiveaway ? 'Enter' : 'Earn' }} Description
			<span class="text-rose-500">*</span>
		</div>
		<div class="mb-3 text-ralgray-dark text-xs font-normal font-['Inter']">
			Describe the actions you want customers to take {{ isGiveaway ? '' : ', and the rewards they will get for completing them.' }}
		</div>
		<div v-if="predictionRunning || loading" role="status" class="w-full mb-8 flex p-3 space-y-4 justify-end bg-gray-300 dark:bg-gray-600  rounded-2xl shadow animate-pulse">
			<img src="../../images/magic.svg" width="20" height="60" />
		</div>
		<div
			v-if="!predictionRunning && !loading"
			class="w-full mb-8 h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"
			:class="{
					'border-ralerror-dark': v$.earn.description.$dirty && v$.earn.description.$errors.length,
					'border-gray-400': !v$.earn.description.$errors.length || !v$.earn.description.$dirty,
				}"
			>
			<div class="w-full self-stretch justify-start items-center gap-2 flex">
				<input
					class="no-focus-outline border-none w-full text-gray-600 text-sm font-semibold font-['Inter'] leading-normal appearance-none outline-none"
					:class="{'animate-pulse bg-gray-300 dark:bg-gray-600': predictionRunning}"
					:disabled="predictionRunning"
					maxlength="80"
					v-model="v$.earn.description.$model"
				/>
				<img src="../../images/magic.svg" width="20" height="60" />
			</div>
		</div>
		<div class="text-ralgray-dark text-sm font-semibold font-['Inter'] leading-normal tracking-wide">
			Upload Way To {{ isGiveaway ? 'Enter' : 'Earn' }} Image
			<span class="text-rose-500">*</span>
		</div>
		<div class="mb-1 text-slate-800 text-xs font-normal font-['Inter']">
			This image will be shown to the customer to represent this Way to {{ isGiveaway ? 'Enter' : 'Earn' }}.
		</div>

		<div class="flex flex-wrap mt-2 items-center">
			<div class="image-wrapper">
                <div class="image-container">
					<!-- When prediction is running -->
					<img v-if="predictionRunning || loading"
						class="rounded-3xl border object-center object-cover"
						:class="{
							'animate-pulse bg-gray-300 dark:bg-gray-600': predictionRunning
						}"
						src="../../images/magic.svg" width="20" height="20" />

					<!-- When imageURL is invalid (null or empty) -->
					<img v-else-if="v$.earn.imageURL.$model == null || v$.earn.imageURL.$model == ''"
						class="rounded-3xl border object-center object-cover border-red-500"
					src="https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg" width="20" height="20" />

					<!-- Default case: Display the actual image -->
					<img v-else
						class="rounded-3xl border object-center object-cover"
						:class="{
							'border-ralerror-dark': v$.earn.imageURL.$dirty && v$.earn.imageURL.$errors.length,
							'border-gray-400': !v$.earn.imageURL.$dirty && !v$.earn.imageURL.$errors.length
						}"
						:src="v$.earn.imageURL.$model" />
                </div>
            </div>
			<div class="flex flex-col flex-grow items-start justify-center">
				<div class="w-full ml-4">
					<ImageUpload @imageSelected="(event) => $emit('imageSelected', event)"/>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import * as Utils from '../../../client-old/utils/Utils';
import { useVuelidate } from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import ImageUpload from '../../components/ImageUpload.ts.vue';
import Tooltip from '../../components/Tooltip.ts.vue';
import PrimaryButton from '../../components/PrimaryButton.ts.vue';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		ImageUpload,
		Tooltip,
		PrimaryButton,
	},
	props: ['earn', 'isEarnImageUploading', 'loading', 'isGiveaway'],
	emits: ['isComplete', 'imageSelected'],
	methods: {
		async predictBranding() {
			this.predictionRunning = true;
			const response = await fetch(`${URL_DOMAIN}/earn/predict`, {
				method: 'POST', credentials: 'omit', mode: 'cors',
				headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${localStorage.getItem('token')}`, },
				body: JSON.stringify(this.earn),
			});

			const data = await response.json();
			this.earn.name = data.name;
			this.earn.description = data.description;
			this.earn.imageURL = data.imageURL;

			this.predictionRunning = false;
		}
	},
	watch: {
		'v$.earn': {
			handler(newValue) {
				this.$emit('isComplete', !newValue.$invalid);
			},
			deep: true
		},
	},
	data() {
		return {
			v$: useVuelidate(),
			predictionRunning: false,
		}
	},
	validations() {
		return {
			earn: {
				name: { required },
				description: { required },
				imageURL: { required },
			}
		}
	}
}
</script>

<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

.image-wrapper {
    width: 100%;
    max-width: 150px;
    margin: auto;
}

.image-container {
    position: relative;
    width: 100%;
    padding-top: 100%; /* Maintains a 1:1 aspect ratio */
}

.image-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.upload-button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-top: 10px;
}

@media (min-width: 500px) {
    .upload-button-container {
        margin-left: 12px; /* Add left padding back for larger screens */
        text-align: left; /* Align left on larger screens */
		width: 100%;
		justify-content: flex-start;
    }
}

</style>
