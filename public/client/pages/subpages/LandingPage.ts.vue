<template>
	<div class="p-2 sm:p-7">
		<div class="space-y-6">
			<!-- Introduction -->
			<div class="mb-8">
				<h2 class="text-xl font-semibold text-gray-800 mb-3">Create a Custom Loyalty Landing Page</h2>
				<p class="text-gray-600 mb-4">
					Build a personalized loyalty landing page that integrates seamlessly with your theme using Raleon's app
					and embed features.
				</p>
				<LearnMoreText text="See Full Documentation" url="https://docs.raleon.io/docs/creating-a-loyalty-landing-page"></LearnMoreText>
			</div>

			<!-- Step 1 -->
			<div class="bg-white rounded-lg shadow p-6">
				<h3 class="text-lg font-semibold text-gray-800 mb-4">Step 1: Create a New Landing Page</h3>
				<div class="space-y-4">
					<div class="ml-4">
						<ol class="list-decimal space-y-4 text-gray-600">
							<li>
								<p class="font-medium">Access Theme Customization</p>
								<ul class="list-disc ml-4 mt-2">
									<li>Go to theme settings and click "Customize"</li>
									<li>Click the "Homepage" drop-down at the top</li>
									<li>Select "Pages" then "Create Template"</li>
								</ul>
							</li>
							<li>
								<p class="font-medium">Select Template</p>
								<ul class="list-disc ml-4 mt-2">
									<li>Choose your base template (e.g., "Default Page")</li>
									<li>Click "Create Template"</li>
								</ul>
							</li>
						</ol>
					</div>
					<PrimaryButton cta="Open Theme Editor" size="normal" :icon="false" @click="openThemeEditor"
						class="mt-4" />
				</div>
			</div>

			<!-- Step 2 -->
			<div class="bg-white rounded-lg shadow p-6">
				<h3 class="text-lg font-semibold text-gray-800 mb-4">Step 2: Add the Loyalty Header</h3>
				<div class="mt-6 rounded-lg overflow-hidden mb-6 p-8">
					<img
						:src="landingPageImages.headerExample"
						alt="Loyalty Header Example"
						class="w-full h-auto"
					/>
					<div class="p-4 bg-gray-50">
						<p class="text-sm text-gray-600">Example of a Loyalty Header configuration</p>
					</div>
				</div>
				<div class="space-y-4">
					<div class="ml-4">
						<ol class="list-decimal space-y-4 text-gray-600">
							<li>
								<p class="font-medium">Add Loyalty Header Block</p>
								<ul class="list-disc ml-4 mt-2">
									<li>Select "Loyalty Header" from available blocks</li>
									<li>This adds the Raleon loyalty header to your page top</li>
								</ul>
							</li>
							<li>
								<p class="font-medium">Default Buttons</p>
								<ul class="list-disc ml-4 mt-2">
									<li>"Join Now" (shown to non-members only)</li>
									<li>"Launch Loyalty" (always visible)</li>
								</ul>
							</li>
							<li>
								<p class="font-medium">Customize Header Settings</p>
								<ul class="list-disc ml-4 mt-2">
									<li>Set background color</li>
									<li>Adjust background height</li>
									<li>Modify margins for text spacing</li>
									<li>Upload optional right image</li>
								</ul>
							</li>
						</ol>
					</div>

					<!-- Settings Image Example -->
					<div class="mt-6 rounded-lg overflow-hidden mb-6 p-8">
						<img
							:src="landingPageImages.settingsExample"
							alt="Header Settings Example"
							class="w-full h-auto"
						/>
						<div class="p-4 bg-gray-50">
							<p class="text-sm text-gray-600">Loyalty Header settings configuration</p>
						</div>
					</div>

					<div class="bg-blue-50 p-4 rounded-lg mt-4">
						<h4 class="font-medium text-blue-800 mb-2">Important Settings</h4>
						<ul class="list-disc ml-4 text-blue-700">
							<li>Set right image width to 100%</li>
							<li>Match height to actual image height</li>
						</ul>
					</div>
				</div>
			</div>

			<!-- Step 3 -->
			<div class="bg-white rounded-lg shadow p-6">
				<h3 class="text-lg font-semibold text-gray-800 mb-4">Step 3: Customize Program Information</h3>
				<div class="space-y-4">
					<p class="text-gray-600">
						Raleon's approach focuses on personalized campaigns rather than static rewards. Below the header,
						focus on:
					</p>

					<div class="ml-4">
						<h4 class="font-medium text-gray-800 mb-2">Two Approaches:</h4>
						<ol class="list-decimal space-y-4 ml-4 text-gray-600">
							<li>
								<p class="font-medium">Use Shopify's Content Tools</p>
								<ul class="list-disc ml-4 mt-2">
									<li>Add custom text and images</li>
									<li>Create engaging program descriptions</li>
								</ul>
							</li>
							<li>
								<p class="font-medium">Single Program Image</p>
								<ul class="list-disc ml-4 mt-2">
									<li>Use Image Banner Section</li>
									<li>Place directly under header</li>
									<li>Create cohesive visual experience</li>
								</ul>
							</li>
						</ol>
					</div>
				</div>
			</div>

			<!-- Best Practices -->
			<div class="bg-white rounded-lg shadow p-6">
				<h3 class="text-lg font-semibold text-gray-800 mb-4">Best Practices</h3>
				<div class="space-y-4">
					<ul class="list-disc ml-4 text-gray-600">
						<li>Focus on explaining how your program works</li>
						<li>Generate excitement about membership benefits</li>
						<li>Avoid listing all possible rewards (they may vary by customer)</li>
						<li>Keep design clean and focused on conversion</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import PrimaryButton from '../../components/PrimaryButton.ts.vue';
import { getCurrentOrg } from '../../services/organization.js';
import LearnMoreText from '../../components/LearnMoreText.ts.vue';
import LandingPageImage1 from '../../images/loyalty-landing-page.png';
import LandingPageImage2 from '../../images/create-loyalty-landing-page.png';

export default defineComponent({
	name: 'LoyaltyLandingPage',

	components: {
		PrimaryButton,
		LearnMoreText
	},
	data() {
		return {
			landingPageImages: {
				headerExample: LandingPageImage1,
				settingsExample: LandingPageImage2
			},
			external_domain: '',
			orgId: '',
			isDataLoading: true
		}
	},
	async mounted() {
		try {
			const organization = await getCurrentOrg();
			if (organization) {
				this.external_domain = organization.externalDomain;
				this.orgId = organization.id;
			}
		} finally {
			this.isDataLoading = false;
		}
	},

	methods: {
		openThemeEditor() {
			window.open(`https://${this.external_domain}/admin/themes/current/editor`, '_blank');
		}
	}
});
</script>
