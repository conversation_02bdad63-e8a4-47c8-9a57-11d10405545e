<template>
	<div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 overflow-y-auto">
	  <div class="bg-white rounded-lg p-6 w-11/12 max-w-4xl">
		<h2 v-if="!forceThemeSelection" class="text-2xl font-bold mb-4">Choose a Branding Theme</h2>
		<h2 v-if="forceThemeSelection" class="text-2xl font-bold mb-4">Choose a Starting Theme</h2>
		<div v-if="!forceThemeSelection" class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4" role="alert">
			<p class="font-bold">Warning</p>
			<p>Applying a Theme will override all your existing settings.</p>
		</div>
		<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto">
		  <div v-for="template in templates" :key="template.name" class="p-4 cursor-pointer flex flex-col"
			@click="selectTemplate(template)">
			<div class="relative w-full transition-all duration-500 hover:shadow-lg group">
			  <div class="w-full pb-[70.63%] relative overflow-hidden rounded">
				<img :src="template.previewImage" :alt="template.name"
				  class="absolute top-0 left-0 w-full h-full object-cover transition-all duration-500"
				  :class="[
					selectedTemplate === template
					  ? 'border-2 border-ralpurple-500 ring-2 ring-ralpurple-300'
					  : 'hover:border-2 hover:border-ralpurple-300'
				  ]">
			  </div>
			  <div v-if="selectedTemplate === template" class="absolute" style="top: -10px; right: -10px;">
				<svg width="25" height="25" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
				  <circle cx="16.5" cy="16.5" r="16.5" fill="#5A16C9" />
				  <path d="M13.4487 20.7011L23.784 10.3659C24.0279 10.122 24.3124 10 24.6376 10C24.9628 10 25.2474 10.122 25.4913 10.3659C25.7352 10.6098 25.8571 10.8994 25.8571 11.2347C25.8571 11.5701 25.7352 11.8597 25.4913 12.1036L14.3024 23.323C14.0585 23.5669 13.7739 23.6889 13.4487 23.6889C13.1235 23.6889 12.839 23.5669 12.5951 23.323L7.35122 18.0792C7.10732 17.8353 6.99045 17.5457 7.00061 17.2103C7.01077 16.8749 7.1378 16.5853 7.3817 16.3414C7.6256 16.0975 7.91524 15.9756 8.2506 15.9756C8.58596 15.9756 8.87559 16.0975 9.11949 16.3414L13.4487 20.7011Z" fill="white"/>
				</svg>
			  </div>
			</div>
			<h3 class="font-semibold text-lg mt-2 text-center">{{ template.name }}</h3>
		  </div>
		</div>
		<div class="flex mt-4 justify-end">
		  <CancelButton v-if="!forceThemeSelection" @click="close" cta="Cancel"></CancelButton>
		  <LightSecondaryButton cta="Apply" @click="applyTemplate(); isOpen = false;">
		  </LightSecondaryButton>
		</div>
	  </div>
	</div>
  </template>
<script>
import PrimaryButton from '../../components/PrimaryButton.ts.vue';
import LightSecondaryButton from '../../components/LightSecondaryButton.ts.vue';
import CancelButton from '../../components/CancelButton.ts.vue';
import SelectedSvg from '../../images/Selected.svg';

export default {
	props: {
		isOpen: Boolean,
		currentBranding: Object,
		originalBranding: Object,
		forceThemeSelection: Boolean,
	},
	components: {
		PrimaryButton,
		LightSecondaryButton,
		CancelButton,
	},
	data() {
		return {
			selectedTemplate: null,
			brandingBackup: null,
			noTemplateOption: {
				name: 'No Template',
				branding: this.originalBranding,
			},
			templates: [
				{
					name: 'Minimalist White',
					previewImage: 'https://raleoncdn.s3.us-east-1.amazonaws.com/Theme_MinWhite.png',
					branding: {
						"logoUrl": "https://dqpqjbq51w8fz.cloudfront.net/images/organization=1000649/EnchantIcon.png",
						"launcher": {
							"callToAction": "Enchanted Club",
							"styling": {
								"textColor": "#EDF2F4",
								"backgroundColor": "#3F51B5"
							},
							"launcherPosition": "left",
							"size": "medium",
							"radius": "50%"
						},
						"header": "Enchanted Curators",
						"headerStyle": {
							"title": "Enchanted Curators",
							"textColor": "#1C1C1C",
							"backgroundColor": "#FFFFFF"
						},
						"home": {
							"pointsColor": "#3F51B5",
							"backgroundColor": "#FFFFFF",
							"textColor": "#1C1C1C",
							"linkColor": "#1C1C1C"
						},
						"waysToEarn": {
							"sectionTitle": "Achievements",
							"borderColor": "#777777",
							"hoverEffect": true,
							"style": "floating",
							"backgroundBlur": true,
							"pointPillBackgroundColor": "#FFFFFF",
							"pointPillBorderColor": "#1C1C1C",
							"pointsColor": "#8D99AE",
							"backgroundColor": "#7A7979",
							"textColor": "#1C1C1C",
							"pointPillTextColor": "#1C1C1C",
							"instructionTextColor": "#404040"
						},
						"rewards": {
							"sectionTitle": "Redeem Your Points",
							"textColor": "#121212",
							"buttonBackgroundColor": "#1B1B1B",
							"buttonTextColor": "#FFFFFF",
							"pointPillBackgroundColor": "#444444",
							"backgroundBlur": true,
							"pointPillTextColor": "#FFFFFF"
						},
						"menu": {
							"textColor": "#1C1C1C",
							"underlineColor": "#252A2D",
							"showHistory": true
						},
						"vip": {
							"textColor": "#EDF2F4",
							"progressBackgroundColor": "#49778A",
							"progressFillColor": "#D90429",
							"showProgress": true
						},
						"guestView": {
							"heroTitle": "JOIN NOW",
							"heroDescription": "Exclusive perks await you! Sign up today and earn points with every purchase. Redeem points for special discounts, gifts, and more! \"testing\"",
							"textColor": "#EDF2F4",
							"backgroundColor": "#F0F0F0",
							"heroImageUrl": "https://images.unsplash.com/photo-1463693396721-8ca0cfa2b3b5?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w1NTg1NjV8MHwxfHNlYXJjaHwxfHxtb3VudGFpbiUyMGFkdmVudHVyZXxlbnwwfDB8fHwxNzE3NTk4MzczfDA&ixlib=rb-4.0.3&q=80&w=400",
							"showJoinButton": true,
							"buttonBackgroundColor": "#1B1B1B",
							"buttonTextColor": "#FFFFFF"
						}
					},
				},
				{
					name: 'Calm Mountain',
					previewImage: 'https://raleoncdn.s3.us-east-1.amazonaws.com/Theme_ForestThumb.png',
					branding: {
						"logoUrl": "https://dqpqjbq51w8fz.cloudfront.net/images/organization=1000649/EnchantIcon.png",
						"launcher": {
							"callToAction": "Enchanted Club",
							"styling": {
								"textColor": "#EDF2F4",
								"backgroundColor": "#3F51B5"
							},
							"launcherPosition": "left",
							"size": "medium",
							"radius": "50%"
						},
						"header": "Enchanted Curators",
						"headerStyle": {
							"title": "Enchanted Curators",
							"textColor": "#FFFFFF",
							"backgroundColor": "#577C5A"
						},
						"home": {
							"pointsColor": "#FFFFFF",
							"backgroundColor": "#7B9B7E",
							"textColor": "#FFFFFF",
							"linkColor": "#1C1C1C"
						},
						"waysToEarn": {
							"sectionTitle": "Achievements",
							"borderColor": "#96B099",
							"hoverEffect": true,
							"style": "card",
							"backgroundBlur": true,
							"pointPillBackgroundColor": "#304B63",
							"pointPillBorderColor": "#304B63BF",
							"pointsColor": "#8D99AE",
							"backgroundColor": "#88A58B",
							"textColor": "#FFFFFF",
							"pointPillTextColor": "#FFFFFF",
							"instructionTextColor": "#FFFFFF"
						},
						"rewards": {
							"sectionTitle": "Redeem Your Points",
							"textColor": "#FFFFFF",
							"buttonBackgroundColor": "#CC6328",
							"buttonTextColor": "#FFFFFF",
							"pointPillBackgroundColor": "#CC6328",
							"backgroundBlur": true,
							"pointPillTextColor": "#FFFFFF",
							"pointPillBorderColor": "#FCFCFC61"
						},
						"menu": {
							"textColor": "#FFFFFF",
							"underlineColor": "#FFFFFF",
							"showHistory": true
						},
						"vip": {
							"textColor": "#EDF2F4",
							"progressBackgroundColor": "#49778A",
							"progressFillColor": "#D90429",
							"showProgress": true
						},
						"guestView": {
							"heroTitle": "JOIN NOW",
							"heroDescription": "Exclusive perks await you! Sign up today and earn points with every purchase. Redeem points for special discounts, gifts, and more! \"testing\"",
							"textColor": "#EDF2F4",
							"backgroundColor": "#88A58B",
							"heroImageUrl": "https://images.unsplash.com/photo-1463693396721-8ca0cfa2b3b5?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w1NTg1NjV8MHwxfHNlYXJjaHwxfHxtb3VudGFpbiUyMGFkdmVudHVyZXxlbnwwfDB8fHwxNzE3NTk4MzczfDA&ixlib=rb-4.0.3&q=80&w=400",
							"showJoinButton": true,
							"buttonBackgroundColor": "#CC6328",
							"buttonTextColor": "#FFFFFF"
						}
					},
				},
				{
					name: 'Deep Ocean',
					previewImage: 'https://raleoncdn.s3.us-east-1.amazonaws.com/Theme_DeepOcean.png',
					branding: {
						"logoUrl": "https://dqpqjbq51w8fz.cloudfront.net/images/organization=1000649/EnchantIcon.png",
						"launcher": {
							"callToAction": "Enchanted Club",
							"styling": {
								"textColor": "#EDF2F4",
								"backgroundColor": "#3F51B5"
							},
							"launcherPosition": "left",
							"size": "medium",
							"radius": "50%"
						},
						"header": "Enchanted Curators",
						"headerStyle": {
							"title": "Enchanted Curators",
							"textColor": "#FFFFFF",
							"backgroundColor": "#07111A"
						},
						"home": {
							"pointsColor": "#FFFFFF",
							"backgroundColor": "#0F2130",
							"textColor": "#FFFFFF",
							"linkColor": "#FFC95C"
						},
						"waysToEarn": {
							"sectionTitle": "Achievements",
							"borderColor": "#FFFFFF1A",
							"hoverEffect": true,
							"style": "card",
							"backgroundBlur": true,
							"pointPillBackgroundColor": "#000000",
							"pointPillBorderColor": "#000000",
							"pointsColor": "#8D99AE",
							"backgroundColor": "#273744",
							"textColor": "#FFFFFF",
							"pointPillTextColor": "#FFFFFF",
							"instructionTextColor": "#FFFFFF"
						},
						"rewards": {
							"sectionTitle": "Redeem Your Points",
							"textColor": "#FFFFFF",
							"buttonBackgroundColor": "#FFFFFF",
							"buttonTextColor": "#000000",
							"pointPillBackgroundColor": "#FFFFFF",
							"backgroundBlur": true,
							"pointPillTextColor": "#000000",
							"pointPillBorderColor": "#00000087"
						},
						"menu": {
							"textColor": "#FFFFFF",
							"underlineColor": "#FFFFFF",
							"showHistory": true
						},
						"vip": {
							"textColor": "#EDF2F4",
							"progressBackgroundColor": "#49778A",
							"progressFillColor": "#D90429",
							"showProgress": true
						},
						"guestView": {
							"heroTitle": "JOIN NOW",
							"heroDescription": "Exclusive perks await you! Sign up today and earn points with every purchase. Redeem points for special discounts, gifts, and more! \"testing\"",
							"textColor": "#EDF2F4",
							"backgroundColor": "#273744",
							"heroImageUrl": "https://images.unsplash.com/photo-1463693396721-8ca0cfa2b3b5?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w1NTg1NjV8MHwxfHNlYXJjaHwxfHxtb3VudGFpbiUyMGFkdmVudHVyZXxlbnwwfDB8fHwxNzE3NTk4MzczfDA&ixlib=rb-4.0.3&q=80&w=400",
							"showJoinButton": true,
							"buttonBackgroundColor": "#FFFFFF",
							"buttonTextColor": "#000000"
						}
					},
				},
				{
					name: 'Fruit Punch',
					previewImage: 'https://raleoncdn.s3.us-east-1.amazonaws.com/Theme_CottonCandy.png',
					branding: {
						"logoUrl": "https://dqpqjbq51w8fz.cloudfront.net/images/organization=1000649/EnchantIcon.png",
						"launcher": {
							"callToAction": "Enchanted Club",
							"styling": {
								"textColor": "#EDF2F4",
								"backgroundColor": "#3F51B5"
							},
							"launcherPosition": "left",
							"size": "medium",
							"radius": "50%"
						},
						"header": "Enchanted Curators",
						"headerStyle": {
							"title": "Enchanted Curators",
							"textColor": "#FFFFFF",
							"backgroundColor": "#CE3146"
						},
						"home": {
							"pointsColor": "#FFFFFF",
							"backgroundColor": "#EB445A",
							"textColor": "#FFFFFF",
							"linkColor": "#303030"
						},
						"waysToEarn": {
							"sectionTitle": "Achievements",
							"borderColor": "#FFFFFF1A",
							"hoverEffect": true,
							"style": "card",
							"backgroundBlur": true,
							"pointPillBackgroundColor": "#000000",
							"pointPillBorderColor": "#000000",
							"pointsColor": "#8D99AE",
							"backgroundColor": "#FFFFFF30",
							"textColor": "#FFFFFF",
							"pointPillTextColor": "#FFFFFF",
							"instructionTextColor": "#FFFFFF"
						},
						"rewards": {
							"sectionTitle": "Redeem Your Points",
							"textColor": "#FFFFFF",
							"buttonBackgroundColor": "#FFFFFF",
							"buttonTextColor": "#FFFFFF",
							"pointPillBackgroundColor": "#FFFFFF",
							"backgroundBlur": true,
							"pointPillTextColor": "#000000",
							"pointPillBorderColor": "#FFFFFF"
						},
						"menu": {
							"textColor": "#FFFFFF",
							"underlineColor": "#FFFFFF",
							"showHistory": true
						},
						"vip": {
							"textColor": "#EDF2F4",
							"progressBackgroundColor": "#49778A",
							"progressFillColor": "#D90429",
							"showProgress": true
						},
						"guestView": {
							"heroTitle": "JOIN NOW",
							"heroDescription": "Exclusive perks await you! Sign up today and earn points with every purchase. Redeem points for special discounts, gifts, and more! \"testing\"",
							"textColor": "#EDF2F4",
							"backgroundColor": "#ED566A",
							"heroImageUrl": "https://images.unsplash.com/photo-1463693396721-8ca0cfa2b3b5?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w1NTg1NjV8MHwxfHNlYXJjaHwxfHxtb3VudGFpbiUyMGFkdmVudHVyZXxlbnwwfDB8fHwxNzE3NTk4MzczfDA&ixlib=rb-4.0.3&q=80&w=400",
							"showJoinButton": true,
							"buttonBackgroundColor": "#FFFFFF",
							"buttonTextColor": "#000000"
						}
					},
				},
				{
					name: 'Hazel',
					previewImage: 'https://raleoncdn.s3.us-east-1.amazonaws.com/Theme_HazelThumb.png',
					branding: {
						"logoUrl": "https://dqpqjbq51w8fz.cloudfront.net/images/organization=1000649/EnchantIcon.png",
						"launcher": {
							"callToAction": "Enchanted Club",
							"styling": {
								"textColor": "#EDF2F4",
								"backgroundColor": "#3F51B5"
							},
							"launcherPosition": "left",
							"size": "medium",
							"radius": "50%"
						},
						"header": "Enchanted Curators",
						"headerStyle": {
							"title": "Enchanted Curators",
							"textColor": "#FFFFFF",
							"backgroundColor": "#212121"
						},
						"home": {
							"pointsColor": "#79896E",
							"backgroundColor": "#212121",
							"textColor": "#FFFFFF",
							"linkColor": "#79896E"
						},
						"waysToEarn": {
							"sectionTitle": "Achievements",
							"borderColor": "#383838",
							"hoverEffect": true,
							"style": "card",
							"backgroundBlur": true,
							"pointPillBackgroundColor": "#898989",
							"pointPillBorderColor": "#898989",
							"pointsColor": "#8D99AE",
							"backgroundColor": "#383838",
							"textColor": "#FFFFFF",
							"pointPillTextColor": "#FFFFFF",
							"instructionTextColor": "#AFAFAF"
						},
						"rewards": {
							"sectionTitle": "Redeem Your Points",
							"textColor": "#FFFFFF",
							"buttonBackgroundColor": "#FFFFFF",
							"buttonTextColor": "#1A1A1A",
							"pointPillBackgroundColor": "#FFFFFF",
							"backgroundBlur": true,
							"pointPillTextColor": "#150E0A",
							"pointPillBorderColor": "FFF",
							"hoverTextColor": "150E0A"
						},
						"menu": {
							"textColor": "#FFFFFF",
							"underlineColor": "#79896E",
							"showHistory": true
						},
						"vip": {
							"textColor": "#EDF2F4",
							"progressBackgroundColor": "#49778A",
							"progressFillColor": "#D90429",
							"showProgress": true
						},
						"guestView": {
							"heroTitle": "JOIN NOW",
							"heroDescription": "Exclusive perks await you! Sign up today and earn points with every purchase. Redeem points for special discounts, gifts, and more! \"testing\"",
							"textColor": "#EDF2F4",
							"backgroundColor": "#212121",
							"heroImageUrl": "https://images.unsplash.com/photo-1463693396721-8ca0cfa2b3b5?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w1NTg1NjV8MHwxfHNlYXJjaHwxfHxtb3VudGFpbiUyMGFkdmVudHVyZXxlbnwwfDB8fHwxNzE3NTk4MzczfDA&ixlib=rb-4.0.3&q=80&w=400",
							"showJoinButton": true,
							"buttonBackgroundColor": "#FFFFFF",
							"buttonTextColor": "#1A1A1A"
						}
					},
				},
				{
					name: 'Dark',
					previewImage: 'https://raleoncdn.s3.us-east-1.amazonaws.com/Theme_ClassicBlackThumb.png',
					branding: {
						"logoUrl": "https://dqpqjbq51w8fz.cloudfront.net/images/organization=1000649/EnchantIcon.png",
						"launcher": {
							"callToAction": "Enchanted Club",
							"styling": {
								"textColor": "#EDF2F4",
								"backgroundColor": "#3F51B5"
							},
							"launcherPosition": "left",
							"size": "medium",
							"radius": "50%"
						},
						"header": "Enchanted Curators",
						"headerStyle": {
							"title": "Enchanted Curators",
							"textColor": "#FFFFFF",
							"backgroundColor": "#2D2D2D"
						},
						"home": {
							"pointsColor": "#F55A74",
							"backgroundColor": "#000000",
							"textColor": "#FFFFFF",
							"linkColor": "#F55A74"
						},
						"waysToEarn": {
							"sectionTitle": "Achievements",
							"borderColor": "#272727",
							"hoverEffect": true,
							"style": "card",
							"backgroundBlur": true,
							"pointPillBackgroundColor": "#F5586F",
							"pointPillBorderColor": "#FF7D8F",
							"pointsColor": "#8D99AE",
							"backgroundColor": "#191919",
							"textColor": "#FFFFFF",
							"pointPillTextColor": "#FFFFFF",
							"instructionTextColor": "#FFFFFF"
						},
						"rewards": {
							"sectionTitle": "Redeem Your Points",
							"textColor": "#FFFFFF",
							"buttonBackgroundColor": "#FFFFFF",
							"buttonTextColor": "#000000",
							"pointPillBackgroundColor": "#191919",
							"backgroundBlur": true,
							"pointPillTextColor": "#F56080",
							"pointPillBorderColor": "#2A2A2A",
							"hoverTextColor": "150E0A"
						},
						"menu": {
							"textColor": "#FFFFFF",
							"underlineColor": "#FFFFFF",
							"showHistory": true
						},
						"vip": {
							"textColor": "#EDF2F4",
							"progressBackgroundColor": "#49778A",
							"progressFillColor": "#D90429",
							"showProgress": true
						},
						"guestView": {
							"heroTitle": "JOIN NOW",
							"heroDescription": "Exclusive perks await you! Sign up today and earn points with every purchase. Redeem points for special discounts, gifts, and more! \"testing\"",
							"textColor": "#EDF2F4",
							"backgroundColor": "#2D2D2D",
							"heroImageUrl": "https://images.unsplash.com/photo-1463693396721-8ca0cfa2b3b5?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w1NTg1NjV8MHwxfHNlYXJjaHwxfHxtb3VudGFpbiUyMGFkdmVudHVyZXxlbnwwfDB8fHwxNzE3NTk4MzczfDA&ixlib=rb-4.0.3&q=80&w=400",
							"showJoinButton": true,
							"buttonBackgroundColor": "#FFFFFF",
							"buttonTextColor": "#1A1A1A"
						}
					}
				},
			],
		};
	},
	methods: {
		selectTemplate(template) {
			this.selectedTemplate = template;
			if (this.selectedTemplate.branding === null) {
				this.selectedTemplate.branding = this.originalBranding;
			}
			console.log('Selected template:', template)
		},
		applyTemplate() {
			if (this.selectedTemplate) {
				this.brandingBackup = JSON.parse(JSON.stringify(this.currentBranding));
				this.$emit('update:currentBranding', this.selectedTemplate.branding);
				this.$emit('apply-template', this.selectedTemplate.branding);
				this.close();
			}
		},
		close() {
			this.$emit('close');
			this.selectedTemplate = null;
		},
		revertToPreviousStyling() {
			if (this.brandingBackup) {
				this.$emit('update:currentBranding', this.brandingBackup);
				this.$emit('apply-template', this.brandingBackup);
				this.brandingBackup = null;
			}
		},
	},
};
</script>
