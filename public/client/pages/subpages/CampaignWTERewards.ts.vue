<template>
	<div class="flex flex-col ml-4 mr-4">
		<div class="flex items-start">
			<div class="w-3/4 md:w-[650px] bg-white bg-opacity-75 rounded-2xl border border-violet-200 p-2 md:p-7">
				<div class="flex text-ralsecondary-start text-2xl font-normal font-['Inter'] mb-4">
					<span>Choose a Reward</span>
					<div class="ml-auto">
						<a class="pl-2 pr-4 text-ralprimary-main text-sm justify-start font-semibold items-center gap-1 inline-flex cursor-pointer hover:underline ml-2"
							href="https://docs.raleon.io/docs/setting-up-a-new-campaign" target="_blank">
							<svg class="w-4 h-4 fill-current text-ralrpimary-ultralight mr-2 opacity-50"
								viewBox="0 0 16 16">
								<path
									d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 12c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1zm1-3H7V4h2v5z" />
							</svg>
							How do I setup a reward?
						</a>
					</div>
				</div>
				<div v-if="isLoading" class="flex items-start justify-center flex-col mt-4 shadow animate-pulse mt-2">
					<div class="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-60"></div>
					<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mt-4"></div>
				</div>

				<div v-if="!isLoading" class="flex flex-wrap items-start gap-2">
					<div v-for="(reward, index) in this.rewardOptions" :key="index"
						class="cursor-pointer flex items-center justify-center w-[80px] h-[80px] rounded-md border-2 border-ralprimary-light"
						:class="{'bg-ralprimary-light': reward.type == this.editingReward?.type, 'bg-ralsecondary-light': reward.type !== this.editingReward?.type}"
						@click="switchRewardType(reward.type)">
						<div v-if="reward.type == this.editingReward?.type"
							class="text-white text-xs font-bold font-['Inter'] text-center w-full px-2">
							{{ reward.title }}
						</div>
						<div v-else class="text-black text-xs font-semibold font-['Inter'] text-center w-full px-2">
							{{ reward.title }}
						</div>
					</div>
				</div>

				<div class="mt-7" v-if="rewardType">
					<template v-if="rewardType.type != 'free-shipping'">
						<div class="flex items-center justify-ste">
							<div class="text-ralsecondary-start text-sm font-bold font-['Inter']  mr-2">Reward</div>
							<Tooltip bg="dark" size="md" position="bottom">
								<div class="text-xs whitespace-nowrap text-white">Specify the details of the reward for
									the customer.</div>
							</Tooltip>
						</div>

						<div class="my-3">
							<div class="flex items-center justify-space-between">
								<div
									v-if="rewardType.type == 'percent-off-product' || rewardType.type == 'dollar-off-product'">
									<LvDropdown class="border-neutral-700 border-opacity-20"
										v-model="selectedDropdownOption"
										@update:model-value="(newVal) => onDropdownChange(newVal, conditionType)"
										optionLabel="name" optionValue="code" placeholder="Select Option"
										emptyFilterMessage="No result found" icon-right="light-icon-arrow-down-circle"
										filterPlaceholder="Search" :options="dropdownOptions" :filter="true"
										:bottom-bar="true" />
								</div>

								<div class="text-ralgray-dark text-sm font-medium font-['Inter']">
									{{ rewardType.label }}
									<span class="text-xs text-rose-500 ml-1">*</span>
								</div>
								<div class="flex-grow"></div>
								<div class="flex items-center justify-center">
									<div class="w-52 h-10 px-2 py-3 rounded-lg border flex-col justify-center items-start gap-2.5 inline-flex"
										:class="{
					'border-ralerror-dark': v$.editingReward.amount.$dirty && v$.editingReward.amount.$errors.length,
					'border-ralbackground-dark-line border-opacity-20': !v$.editingReward.amount.$errors.length || !v$.editingReward.amount.$dirty
				}">
										<div class="justify-start items-center gap-2 inline-flex">
											<input type="number"
												class="border-none no-focus-outline w-52 text-ralgray-dark text-base font-medium font-['Inter'] bg-transparent appearance-none outline-none"
												v-model="v$.editingReward.amount.$model" />
										</div>
									</div>

									<div v-if="editingReward.showPointsEquivalency" class="ml-2">= $<span
											v-if="editingReward.amount">{{ editingReward.amount /
					earn.campaign?.loyaltyProgram?.loyaltyCurrencies?.[0]?.conversionToUSD
											}}</span>
									</div>
								</div>
							</div>
						</div>
					</template>

					<div v-if="rewardType.hasRestrictions"
						class="mt-4 mb-4 h-px border border-ralbackground-light-line"></div>

					<div class="my-3" v-if="rewardType.hasRestrictions">
						<div class="flex items-center justify-ste">
							<div class="text-ralsecondary-start text-sm font-bold font-['Inter']  mr-2">Reward
								Restriction</div>
							<Tooltip bg="dark" size="md" position="bottom">
								<div class="text-xs whitespace-nowrap text-white">Reward restrictions let you control
									details like minimum
									order amount, or expiration.</div>
							</Tooltip>
						</div>

						<div v-for="(restriction, index) of editingReward.restrictions" :key="index"
							class="flex items-center justify-end rounded-xl border border-zinc-300 mt-3 p-1 pt-0 flex-wrap">
							<div class="mt-1">
								<select
									class="w-52 h-10 px-2 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex"
									v-model="restriction.type">
									<option v-for="restrictionType of rewardType.uiRewardRestrictions"
										:key="restrictionType.fieldOnDefinition"
										:value="restrictionType.fieldOnDefinition">
										{{ restrictionType.name }}
									</option>
								</select>
								<!-- <SuperDropdown
									:options="rewardType.uiRewardRestrictions"
									option-value-key="fieldOnDefinition"
									option-label-key="name"
									v-model="restriction.type"
									placeholder="Select Type"
								/> -->
							</div>
							<div class="flex-grow"></div>
							<div v-if="restriction.type"
								class="w-52 h-10 px-2 py-3 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex mt-1">
								<div class="justify-start items-center gap-2 inline-flex">
									<input
										class="no-focus-outline text-ralsecondary-start text-base font-medium font-['Inter'] bg-transparent appearance-none outline-none border-none"
										v-model="restriction.amount" />
								</div>
							</div>
							<div v-if="!restriction.required" class="w-6 h-6 relative opacity-40 ml-2 mt-1 cursor-pointer"
								@click="removeRestriction(editingReward, restriction)">
								<svg width="24" height="24" viewBox="0 0 24 24" fill="none"
									xmlns="http://www.w3.org/2000/svg">
									<g>
										<path
											d="M8 12H16M12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21Z"
											stroke="#4D4D4D" stroke-width="1.5" stroke-linecap="round"
											stroke-linejoin="round" />
									</g>
								</svg>
							</div>
						</div>

						<div class="flex items-center justify-end mt-3">
							<div class="h-10 px-3 py-2 bg-opacity-50 border-0 rounded-full bg-ralbackground-light-tertiary hover:bg-ralbackground-light-tertiary hover:bg-opacity-90 transition-all duration-300rounded-full justify-end items-center gap-1 inline-flex cursor-pointer"
								@click="editingReward.restrictions.push({})">
								<div class="w-6 h-6 relative opacity-50">
									<svg width="24" height="24" viewBox="0 0 24 24" fill="none"
										xmlns="http://www.w3.org/2000/svg">
										<path
											d="M8 12H12M12 12H16M12 12V16M12 12V8M12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21Z"
											stroke="#414141" stroke-width="2" stroke-linecap="round"
											stroke-linejoin="round" />
									</svg>
								</div>
								<div class="text-background-dark-base text-base font-medium font-['Inter'] ">Add
									Restriction</div>
							</div>
						</div>
					</div>

					<!-- <div v-if="(earn.rewards?.length < earn.condition?.maxRewardsForThisAction)">
						<div
							class="w-full h-28 rounded-2xl border-4 border-violet-200 border-dashed flex justify-center items-center cursor-pointer hover:border-solid hover:border-ralpurp-dark hover:bg-white hover:shadow-lg transition-all duration-500 ease-in-out opacity-75"
							@click="addReward()">
							<div class="text-ralblack-secondary text-opacity-75 hover:text-opacity-100 text-3xl font-bold font-['Inter'] leading-loose">
								+ Add Reward
							</div>
						</div>
					</div> -->

					<div v-if="rewardType.limitTypes?.length" class="mt-3 h-px border border-ralbackground-dark-line">
					</div>

					<div class="my-3" v-if="rewardType.limitTypes?.length">
						<div class="flex items-center justify-ste">
							<div class="text-ralsecondary-start text-sm font-bold font-['Inter']  mr-2">Reward Limits
							</div>
							<div class="w-5 h-5 relative opacity-50">
								<svg width="20" height="20" viewBox="0 0 20 20" fill="none"
									xmlns="http://www.w3.org/2000/svg">
									<g opacity="0.5">
										<path
											d="M10 9.16667V13.3333M10 17.5C5.85786 17.5 2.5 14.1421 2.5 10C2.5 5.85786 5.85786 2.5 10 2.5C14.1421 2.5 17.5 5.85786 17.5 10C17.5 14.1421 14.1421 17.5 10 17.5ZM10.0415 6.66667V6.75L9.9585 6.75016V6.66667H10.0415Z"
											stroke="#414141" stroke-width="1.5" stroke-linecap="round"
											stroke-linejoin="round" />
									</g>
								</svg>
							</div>
						</div>

						<div class="w-[100%] my-3 h-12 px-2 py-3 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-start items-start gap-2.5 inline-flex cursor-pointer"
							@click="editingReward.limitsEnabled = !editingReward.limitsEnabled">
							<div class="justify-start items-center gap-2 inline-flex">
								<div class="w-6 h-6 border-ralbackground-dark-line border rounded-md"
									:class="editingReward.limitsEnabled ? 'bg-neutral-700' : ''"></div>
								<div class="text-ralsecondary-start text-base font-medium font-['Inter']">Enable Reward
									Limits</div>
							</div>
						</div>

						<div v-for="limitType of rewardType.limitTypes" :key="limitType.label"
							class="flex items-center justify-space-between my-3 p-1">
							<div class="text-ralsecondary-start text-sm font-medium font-['Inter']">{{ limitType.label
								}}</div>
							<div class="flex-grow"></div>
							<div
								class="w-52 h-10 px-2 py-3 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex">
								<div class="justify-start items-center gap-2 inline-flex">
									<input
										class="no-focus-outline w-52 text-ralsecondary-start text-base font-medium font-['Inter'] bg-transparent appearance-none outline-none"
										v-model="limitType.amount" />
								</div>
							</div>
						</div>
					</div>
				</div>


			</div>
		</div>
	</div>
</template>

<script>

import * as Utils from '../../../client-old/utils/Utils';
import SuperDropdown from '../../components/SuperDropdown.ts.vue';
import LightSecondaryButton from '../../components/LightSecondaryButton.ts.vue';
import Tooltip from '../../components/Tooltip.ts.vue'
import CancelButton from '../../components/CancelButton.ts.vue';
import WTESummary from '../../components/WTESummary.ts.vue';
import { useVuelidate } from '@vuelidate/core'
import { helpers } from '@vuelidate/validators'
import * as CurrencyUtils from '../../services/currency.js';
import LvDropdown from 'lightvue/dropdown';

const URL_DOMAIN = Utils.URL_DOMAIN;

const amountValidator = (value, siblings, vm) => {
	if (value == undefined || value == null || value == '' || !helpers.req(value)) return false;

	if (vm.editingReward.type === 'percent-discount') {
		return value > 0 && value <= 100;
	}

	return value >= 0;
}

export default {
	props: ['campaign', 'earn', 'isSingleReward', 'wteSummaryData', 'index'],
	emits: ['tabsDisabled'],
	components: {
		SuperDropdown,
		LightSecondaryButton,
		CancelButton,
		Tooltip,
		WTESummary,
		LvDropdown
	},
	async mounted() {
		const options = await this.getRewardOptions();
		this.rewardOptions = options.sort((a, b) => {
			if (a.enabled === b.enabled) return 0;
			if (a.enabled) return -1;
			return 1;
		});
		await this.switchRewardType(this.rewardOptions[0]?.type);
		console.log(`the reward: ${JSON.stringify(this.editingReward)}`)
		console.log(`the actual reward: ${JSON.stringify(this.earn.rewards)}`)
		if (this.earn.rewards) {
			console.log(`the reward: ${JSON.stringify(this.earn.rewards[this.index])}`)
			this.editingReward = this.earn.rewards[this.index];
		}
	},
	data() {
		return {
			editingReward: this.isSingleReward && this.earn.rewards ? this.earn.rewards[this.index] : null,
			rewardType: null,
			rewardOptions: [],
			v$: useVuelidate(),
			isLoading: true,
			selectedDropdownOption: null,
		}
	},
	computed: {
		rewardType() {
			if (!this.editingReward) {
				return null;
			}

			let rewardOptions = this.rewardOptions;
			return rewardOptions.find(rewardType => rewardType.type === this.editingReward.type);
		},
		previewDisabled() {
			if (this.editingReward && this.editingReward.type == 'free-shipping') {
				this.$emit('isComplete', true);
				return false;
			}
			return this.v$.editingReward.amount.$invalid && (this.editingReward || this.earn?.rewards?.length <= 0)
		},
		wteDoneDisabled() {
			if (this.editingReward && this.editingReward.type == 'free-shipping') {
				this.$emit('isComplete', true);
				return false;
			}
			return this.v$.editingReward.amount.$invalid
		},

	},
	watch: {
		'v$.editingReward.amount.$invalid'(newValue, oldValue) {
			if (!this.editingReward && this.earn?.rewards?.length > 0) {
				this.$emit('isComplete', true);
				return;
			} else if (this.editingReward && this.editingReward.type == 'free-shipping') {
				this.$emit('isComplete', true);
				return;
			}
			this.$emit('isComplete', !newValue);
		},
		'earn.condition.type'(newValue, oldValue) {
			if (newValue === oldValue) {
				return;
			}
			this.getRewardOptions().then((options) => {
				console.log(`mike got options, ${JSON.stringify(options)}`)
				this.rewardOptions = options.sort((a, b) => {
					if (a.enabled === b.enabled) return 0;
					if (a.enabled) return -1;
					return 1;
				});

				this.switchRewardType(this.rewardOptions[this.index].type);
			});
		},
	},
	methods: {
		async fetchDropdownOptions() {
			// Replace with your actual API call
			if (!this.earn?.condition) return;
			try {
				if (this.rewardType.type == 'percent-off-product' || this.rewardType.type == 'dollar-off-product') {
					const response = await fetch(`${URL_DOMAIN}/shop-products`, {
						method: 'GET',
						credentials: 'omit',
						mode: 'cors',
						headers: {
							'Content-Type': 'application/json',
							Authorization: `Bearer ${localStorage.getItem('token')}`,
						},
					});
					const result = await response.json();
					console.log('products', result);
					this.dropdownOptions = result.map(x => ({ name: x.title, code: `${x.id}` }));
				}
			} catch (error) {
				console.error('Error fetching dropdown options:', error);
				// Handle errors appropriately
			}

			this.selectedDropdownOption = 'Select A Product'
			console.log('selectedDropdownOption', this.selectedDropdownOption);
		},
		replaceCurrencyTagsSync(text) {
			return CurrencyUtils.replaceCurrencyTagsSync(text);
		},
		async getRewardOptions() {
			if (this.isSingleReward) {
				const response = await fetch(`${URL_DOMAIN}/shopitems/rewards`, {
					method: 'GET',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
				});

				const result = await response.json();
				this.rewardOptions = result;
				if (!this.earn.rewards || this.earn.rewards.length == 0) {
					this.earn.rewards = [
						{
							restrictions: []
						}
					]
				}
				for (let i = 0; i < this.rewardOptions.length; i++) {
					this.rewardOptions[i].title = await CurrencyUtils.replaceCurrencyTags(this.rewardOptions[i].title);
					this.rewardOptions[i].subtitle = await CurrencyUtils.replaceCurrencyTags(this.rewardOptions[i].subtitle);
					this.rewardOptions[i].label = await CurrencyUtils.replaceCurrencyTags(this.rewardOptions[i].label);
				}
				this.editingReward = this.earn.rewards[0];
				this.isLoading = false;
				return result;
			}
			else {
				console.log("Getting reward options", JSON.stringify(this.earn));
				let payload = {
					actions: [
						this.earn?.condition?.id
					]
				}

				const response = await fetch(`${URL_DOMAIN}/wte/customer-rewards`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				const result = await response.json();
				console.log(result);
				this.rewardOptions = result;
				for (let i = 0; i < this.rewardOptions.length; i++) {
					this.rewardOptions[i].title = await CurrencyUtils.replaceCurrencyTags(this.rewardOptions[i].title);
					this.rewardOptions[i].subtitle = await CurrencyUtils.replaceCurrencyTags(this.rewardOptions[i].subtitle);
					this.rewardOptions[i].label = await CurrencyUtils.replaceCurrencyTags(this.rewardOptions[i].label);
				}
				this.isLoading = false;
				return result;
			}
		},
		addReward() {
			// this.rewardType = null;
			this.editingReward = {
				restrictions: [],
			};
		},
		deleteReward(reward) {
			this.earn.rewards = this.earn.rewards || [];
			const index = this.earn.rewards.indexOf(reward);
			if (index !== -1) {
				this.earn.rewards.splice(index, 1);
			}
			if (this.earn.rewards.length == 0) {
				this.$emit('isComplete', false);
			}
		},
		isExisting(reward) {
			this.earn.rewards = this.earn.rewards || [];

			return this.earn.rewards.indexOf(reward) != -1;
		},
		commitReward(reward) {
			this.earn.rewards = this.earn.rewards || [];
			const match = this.earn.rewards.find(r => r.id == reward.id);

			if (match) {
				return;
			}
			if (this.earn.rewards.indexOf(reward) != -1) {
				return;
			}

			this.earn.rewards.push(reward);

			this.$forceUpdate();
		},
		removeRestriction(reward, restriction) {
			const index = reward.restrictions.indexOf(restriction);
			if (index < 0) {
				return;
			}

			reward.restrictions.splice(index, 1);
		},
		async switchRewardType(rewardTypeValue) {
			console.log("Switching to reward type", rewardTypeValue)
			let rewardOptions = this.rewardOptions || await this.getRewardOptions();
			console.log(rewardOptions)
			const rewardType = rewardOptions.find(rewardType => rewardType.type === rewardTypeValue);
			this.editingReward = {
				...rewardType,
				restrictions: [],
			};
			this.earn.rewards[this.index] = this.editingReward;
			// this.earn.rewards = this.earn.rewards || [];
			// this.commitReward(this.editingReward);
			// this.earn.rewards.push(this.editingReward);

			if (this.isSingleReward) {
				this.earn.rewards[0] = this.editingReward;
			}
		},
	},
	validations() {
		return {
			editingReward: {
				amount: { amountValidator },
			}
		}
	}
}
</script>
<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}
</style>
