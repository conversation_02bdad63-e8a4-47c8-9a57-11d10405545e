<template>
	<div class="w-[100%] lg:w-auto mb-8">
		<AccordionGroup
			:accordions="accordions"
			initialOpenAccordionKey="loggedout"
			@open="handleAccordionOpen">
			<template v-slot:loggedout>
				<div
				>
					<div class="ml-4 mb-8 text-black text-sm font-normal font-['Inter']">Customize the text users see when they're logged out at checkout
					</div>
					<div class="mx-4 mb-4">
						<div class="mt-2">
							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Logged Out Warning Prefix</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">The text that comes before the "Log In" link</div>
							<div
								class="mb-2 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40" v-model="loginPrefix.value" />
								</div>
							</div>

							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Logged Out Warning Link Text</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">The text inside the "Log In" link</div>
							<div
								class="mb-2 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40" v-model="loginText.value" />
								</div>
							</div>

							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Logged Out Warning Suffix</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">The text that comes after the "Log In" link</div>
							<div
								class="mb-2 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40" v-model="loginSuffix.value" />
								</div>
							</div>
						</div>
					</div>

				</div>
			</template>
			<template v-slot:loggedin>
				<div
				>
					<div class="ml-4 mb-8 text-black text-sm font-normal font-['Inter']">Customize the text users see when they're logged in at checkout
					</div>
					<div class="mx-4 mb-4">
						<div class="mt-2">
							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Points Counter Header</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">The text that shows the user how many points they currently have</div>
							<div
								class="mb-2 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40" v-model="pointsHeader.value" />
								</div>
							</div>

							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Insufficient Points Warning</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">The text that shows when a user does not have enough points for any rewards</div>
							<div
								class="mb-2 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40" v-model="notEnoughPoints.value" />
								</div>
							</div>

							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Rewards Selector Label</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">The text at the top of the rewards dropdown, where users can browse available rewards</div>
							<div
								class="mb-2 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40" v-model="rewardsLabel.value" />
								</div>
							</div>

							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Rewards Selector Placeholder</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">The text shown by the dropdown, when no reward is currently selected</div>
							<div
								class="mb-2 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40" v-model="rewardsPlaceholder.value" />
								</div>
							</div>

							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Rewards Selector Template</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Template for how reward listings are displayed in the dropdown</div>
							<div
								class="mb-2 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40" v-model="rewardsTemplate.value" />
								</div>
							</div>

							<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Apply Button Label</div>
							<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">Text displayed in the Apply button, to claim or apply rewards</div>
							<div
								class="mb-2 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40" v-model="applyLabel.value" />
								</div>
							</div>
						</div>
					</div>

				</div>
			</template>

		</AccordionGroup>
	</div>
</template>

<script>
import * as Utils from '../../../client-old/utils/Utils';
import AccordionGroup from '../../components/AccordionGroup.ts.vue';
import LearnMoreText from '../../components/LearnMoreText.ts.vue';
import SuperDropdown from '../../components/SuperDropdown.ts.vue';
import ImageUpload from '../../components/ImageUpload.ts.vue';
import LvColorPicker from 'lightvue/color-picker';


const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: [
		'branding',
		'translations',
		'previewType',
		'isLogoUploading',
		'isHeroImageUploading',
		'launcherActive'
	],
	emits: ['fileUploaded', 'switchPreview', 'fileUploading', 'brandingChanged'],
	created() {
		this.debouncedBrandingChanged = this.debounce(this.handleBrandingChanged, 1000);
	},
	data() {
		return {
			accordions: [{
				key: 'loggedout',
				title: "Logged Out Warning Text",
				height: '500px'
			},{
				key: 'loggedin',
				title: "Logged In Experience",
				height: '1000px'
			}],
			launcherSizes: [],
			launcherRadii: [],
			debouncedBrandingChanged: null,
			brandingUpdateCounter: 0
		}
	},
	watch: {

	},
	components: {
		AccordionGroup,
		SuperDropdown,
		ImageUpload,
		LearnMoreText,
		LvColorPicker
	},
	async mounted() {
	},
	computed: {
		accordionPreviewTypeName() {
			if (this.previewType == 'launcher') {
				return 'launcher';
			} else if (this.previewType == 'landing-page-guest') {
				return 'guest';
			} else if (this.previewType == 'landing-page-member') {
				return 'member';
			}
		},
		loginPrefix() {
			return this.translations?.find?.(x => x.key === 'checkout_ui_login_link_prefix') || { value: '' };
		},
		loginText() {
			return this.translations?.find?.(x => x.key === 'checkout_ui_login_link_text') || {value: '' };
		},
		loginSuffix() {
			return this.translations?.find?.(x => x.key === 'checkout_ui_login_link_suffix') || {value: '' };
		},
		pointsHeader() {
			return this.translations?.find?.(x => x.key === 'checkout_ui_points_header') || {value: '' };
		},
		notEnoughPoints() {
			return this.translations?.find?.(x => x.key === 'checkout_ui_not_enough_points_warning') || {value: '' };
		},
		rewardsLabel() {
			return this.translations?.find?.(x => x.key === 'checkout_ui_rewards_dropdown_label') || {value: '' };
		},
		rewardsTemplate() {
			return this.translations?.find?.(x => x.key === 'checkout_ui_rewards_dropdown_item_text') || {value: '' };
		},
		rewardsPlaceholder() {
			return this.translations?.find?.(x => x.key === 'checkout_ui_rewards_dropdown_placeholder') || {value: '' };
		},
		applyLabel() {
			return this.translations?.find?.(x => x.key === 'checkout_ui_apply_reward_button_text') || {value: '' };

		},


	},
	methods: {
		preventNewLines(event) {
			if (event.keyCode === 13) { // 13 is the key code for Enter
				event.preventDefault();
			}
		},
		handleBrandingChanged(newBranding) {
			this.brandingUpdateCounter++;
			this.$emit('brandingChanged', newBranding);
		},
		debounce(func, delay) {
			let debounceTimer;
			return function () {
				const context = this;
				const args = arguments;
				clearTimeout(debounceTimer);
				debounceTimer = setTimeout(() => func.apply(context, args), delay);
			};
		},
		convertToHex(color) {
			// Check if color is already in hex format with or without '#'
			const hexColorPattern = /^#?([a-fA-F0-9]{3})([a-fA-F0-9]{3})?$/;
			const match = color.match(hexColorPattern);
			if (match) {
				// Reformat if '#' is missing
				return `#${match[1]}${match[2] ? match[2] : ''}`;
			}

			// Assuming the color is in RGB format, e.g., 'rgb(255, 0, 0)'
			if (/rgb/.test(color)) {
				const rgb = color.match(/\d+/g);
				return `#${rgb.map(x => {
					const hex = parseInt(x).toString(16);
					return hex.length === 1 ? '0' + hex : hex;
				}).join('')}`;
			}

			// Return original color if none of the above cases match
			return color;
		},
		handleAccordionOpen(accordionKey) {
			switch (accordionKey) {
				case 'launcher':
					return this.$emit('switchPreview', 'launcher');
				case 'guest':
					return this.$emit('switchPreview', 'landing-page-guest');
				case 'member':
					return this.$emit('switchPreview', 'landing-page-member');
				default:
					break;
			}
		},
		async uploadingHeroImage() {

			console.log("Uploading Hero Image");
			this.$emit('fileUploading', {
				type: 'hero-image'
			});

		},
		async uploadHeroImage(event) {
			this.$nextTick(() => {
				this.$emit('fileUploaded', {
					...event,
					type: 'hero-image'
				});
			});
		},
		async uploadingLogo(event) {
			console.log("Starting logo updating");
			this.$emit('fileUploading', {
				type: 'logo'
			});
		},
		async uploadLogo(event) {
			this.$nextTick(() => {
				this.$emit('fileUploaded', {
					...event,
					type: 'logo'
				});
			});
		},
	}
}
</script>

<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}
.toggle-checkbox {
	opacity: 0;
	width: 0;
	height: 0;
}

.toggle-label {
	display: block;
	overflow: hidden;
	cursor: pointer;
	height: 24px;
	/* Height of the toggle */
	padding: 0;
	line-height: 24px;
	background: #bbb;
	/* Background of the toggle */
	border-radius: 24px;
	transition: background-color 0.2s;
}

.toggle-label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 2px;
	width: 20px;
	/* Width of the toggle handle */
	height: 20px;
	/* Height of the toggle handle */
	border-radius: 20px;
	background: #fff;
	/* Background of the toggle handle */
	transition: 0.2s;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}
.toggle-checkbox:disabled+.toggle-label {
	background-color: #eee;
	cursor: not-allowed;
}


.toggle-checkbox:checked+.toggle-label:after {
	transform: translateX(18px);
}

.toggle-checkbox:checked {
	right: 0;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox {
	right: 4px;
}

.toggle-label {
	transition: background-color 0.2s;
}
</style>
