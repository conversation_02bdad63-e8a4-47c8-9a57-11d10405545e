<template>
	<div class="p-2 sm:p-7">
		<div class="space-y-6">
			<!-- Introduction -->
			<div class="mb-8">
				<h2 class="text-xl font-semibold text-gray-800 mb-3">Setup Promo Box on Product Pages</h2>
				<p class="text-gray-600">
					The Raleon Promo Box helps drive engagement, improve conversion rates, and increase customer lifetime value by displaying loyalty rewards directly on product pages.
				</p>
			</div>

			<!-- Installation Steps -->
			<div class="bg-white rounded-lg shadow p-6">
				<h3 class="text-lg font-semibold text-gray-800 mb-4">Installation Steps</h3>
				<div class="space-y-4">
					<div class="ml-4">
						<ol class="list-decimal space-y-4 text-gray-600">
							<li>
								<p class="font-medium">Access Theme Editor</p>
								<p>Navigate to Online Store and click "Customize" on your active theme</p>
							</li>
							<li>
								<p class="font-medium">Add Promo Box</p>
								<ul class="list-disc ml-4 mt-2">
									<li>Navigate to a product page</li>
									<li>Click "Add Block"</li>
									<li>Select "Apps" tab</li>
									<li>Choose "Raleon Promo Box"</li>
								</ul>
							</li>
							<li>
								<p class="font-medium">Position and Customize</p>
								<ul class="list-disc ml-4 mt-2">
									<li>Drag to desired position</li>
									<li>Adjust sizing and colors</li>
									<li>Click "Save" when done</li>
								</ul>
							</li>
						</ol>
					</div>
					<PrimaryButton
						cta="Open Theme Editor"
						size="normal"
						:icon="false"
						@click="openThemeEditor"
						class="mt-4"
					/>
				</div>
			</div>

			<!-- Display States -->
			<div class="bg-white rounded-lg shadow p-6">
				<h3 class="text-lg font-semibold text-gray-800 mb-4">Promo Box States</h3>
				<div class="space-y-6">
					<div v-for="(state, index) in promoStates" :key="index" class="p-4 border-b last:border-0">
						<h4 class="font-medium text-gray-800 mb-2">{{ state.title }}</h4>
						<p class="text-gray-600">{{ state.description }}</p>
					</div>
				</div>
			</div>

			<!-- Customization -->
			<div class="bg-white rounded-lg shadow p-6">
				<h3 class="text-lg font-semibold text-gray-800 mb-4">Customize Text & Language</h3>
				<div class="space-y-4">
					<ol class="list-decimal ml-4 text-gray-600">
						<li>Click "Manage App" in Promo Box settings</li>
						<li>Go to Settings → Translations</li>
						<li>Search for "Product Page" to find Promo Box text</li>
					</ol>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import PrimaryButton from '../../components/PrimaryButton.ts.vue';
import { getCurrentOrg } from '../../services/organization.js';

export default defineComponent({
	name: 'PromoBox',

	components: {
		PrimaryButton
	},
	async mounted() {
		try {
			const organization = await getCurrentOrg();
			if (organization) {
				this.external_domain = organization.externalDomain;
				this.orgId = organization.id;
			}
		} finally {
			this.isDataLoading = false;
		}
	},

	data() {
		return {
			promoStates: [
				{
					title: 'Guest View',
					description: 'Shows estimated points earnings for the product purchase'
				},
				{
					title: 'Insufficient Points',
					description: 'Displays progress towards next available reward'
				},
				{
					title: 'Points Available',
					description: 'Shows rewards that can be claimed with current points'
				},
				{
					title: 'Reward Available',
					description: 'Displays available rewards from promotions or past earnings'
				},
				{
					title: 'Reward Claimed',
					description: 'Confirms when a reward has been successfully applied'
				}
			]
		};
	},

	methods: {
		openThemeEditor() {
			window.open(`https://${this.external_domain}/admin/themes/current/editor?context=apps&previewPath=%2Fproducts%2F`, '_blank');
		}
	}
});
</script>
