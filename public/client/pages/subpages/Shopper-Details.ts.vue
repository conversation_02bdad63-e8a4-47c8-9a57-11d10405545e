<template>
	<div class="lg:w-3/4 m-10">
		<div class="flex flex-col sm:flex-row items-start justify-between mb-4">
			<div>
				<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Customer Overview</h1>
				<div class="text-sm flex">
					<a v-for="(item, index) in breadcrumbs" :key="item.title" :href="item.href"
						:class="{'text-blue-500': !item.disabled, 'text-slate-400': item.disabled}">
						{{ item.title.toUpperCase() }}
						<span v-if="index < breadcrumbs.length - 1"> / </span>
					</a>
				</div>
			</div>
		</div>
		<div class="w-full">
			<!-- Tabs with visual enhancements -->
			<div class="bg-white shadow-lg rounded-t-sm">
				<ul class="flex border-b-2 border-gray-300">
					<li v-for="item in items" :key="item" @click="tab = item" :class="{
						'bg-white -mb-px border-b-2 border-ralprimary-light py-2 px-4': tab === item,
						'hover:bg-gray-100 py-2 px-4': tab !== item
					}">
						<a href="#" class="text-slate-800 font-medium no-underline font-[Inter]"
							@click="preventDefault">{{ item.toUpperCase() }}</a>
					</li>
				</ul>
			</div>
			<!-- Tab Contents -->
			<div class="bg-white p-6">
				<div v-if="tab === 'details'" class="space-y-4">
					<div class="customer-details">
						<!-- Customer Name and Email -->
						<div>
							<h2 class="text-2xl font-bold text-slate-800">{{ shopper.first_name }} {{ shopper.last_name
								}}</h2>
							<p class="text-sm text-slate-600">{{ shopper.email }}</p>
						</div>
						<!-- Cards for Loyalty Segment, Score, and Points -->
						<div class="flex flex-wrap -mx-2">
							<div class="w-full sm:w-1/3 px-2 mb-4">
								<div class="p-4 bg-white rounded-lg shadow-md">
									<h3 class="text-md leading-tight font-medium text-slate-700">Loyalty Segment</h3>
									<p class="text-lg text-slate-900 font-semibold">{{ shopper.loyalty_segment || 'N/A'
										}}</p>
								</div>
							</div>
							<div class="w-full sm:w-1/3 px-2 mb-4">
								<div class="p-4 bg-white rounded-lg shadow-md">
									<h3 class="text-md leading-tight font-medium text-slate-700">VIP Tier</h3>
									<p class="text-lg text-slate-900 font-semibold">{{ shopper.loyalty_tier || 'N/A' }}
									</p>
								</div>
							</div>
							<div class="w-full sm:w-1/3 px-2 mb-4">
								<div class="p-4 bg-white rounded-lg shadow-md">
									<h3 class="text-md leading-tight font-medium text-slate-700">Loyalty Points</h3>
									<p class="text-lg text-slate-900 font-semibold">{{ shopper.loyalty_points || '0' }}
									</p>
								</div>
							</div>
						</div>
						<!-- Secondary Information -->
						<div class="bg-white p-4 rounded-lg shadow-md">
							<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
								<p class="text-md text-slate-700 font-medium"><strong>Number of Orders:</strong> {{
						shopper.orders_count || 0 }}</p>
								<p class="text-md text-slate-700 font-medium"><strong>Total Spend:</strong> {{
						shopper.total_spent || '$0.00' }}</p>
								<p class="text-md text-slate-700 font-medium"><strong>Created At:</strong> {{
						formatCreatedAt(shopper.created_at) }}</p>
								<p class="text-md text-slate-700 font-medium"><strong>Birthday:</strong> {{
						formatCreatedAt(shopper.birthday) }}</p>
							</div>
						</div>
					</div>
				</div>
				<!-- Placeholder for other tabs 'points log', 'reward log', 'manage' -->
				<!-- Points Log Tab -->
				<div v-if="tab === 'points log'" class="space-y-4">
					<h2 class="text-xl font-bold text-black">Point Log</h2>
					<div class="mt-4 divide-y divide-gray-200">
						<div v-for="log in pointsLog" :key="log.id"
							class="py-4 grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
							<div>
								{{ new Date(log.date).toLocaleDateString('en-US', {
						year: 'numeric', month: 'long', day:
							'numeric', hour: '2-digit', minute: '2-digit'
					}) }}
							</div>
							<div>
								{{ log.info }}
							</div>
							<div
								:class="{'bg-green-100 text-green-800 px-2 py-1 rounded-full text-center': log.amount >= 0, 'bg-red-100 text-red-800 px-2 py-1 rounded-full text-center': log.amount < 0}">
								{{ log.amount.toLocaleString() }} points
							</div>
						</div>
					</div>
				</div>

				<!-- Reward Log Tab -->
				<div v-if="tab === 'reward log'" class="space-y-4">
					<h2 class="text-xl font-bold text-black mb-6">Rewards Log</h2>
					<div class="grid gap-4">
						<div v-for="reward in rewardsLog" :key="reward.id"
							class="bg-white rounded-lg shadow p-4 flex flex-wrap">
							<div class="w-full md:w-1/2">
								<h3 class="text-lg font-bold">{{ reward?.inventoryCoupon?.rewardCoupon?.name }}</h3>
								<div class="flex items-center gap-2 my-2">
									<span class="px-2 py-1 rounded-full"
										:class="{'bg-blue-100 text-blue-800': reward.granted, 'bg-purple-100 text-purple-800': !reward.granted}">
										{{ reward.granted ? 'Earned' : 'Redeemed with Points' }}
									</span>
									<span class="px-2 py-1 rounded-full"
										:class="{'bg-red-100 text-red-800': isExpired(reward?.inventoryCoupon?.expiration), 'bg-green-100 text-green-800': !isExpired(reward?.inventoryCoupon?.expiration)}">
										{{ isExpired(reward?.inventoryCoupon?.expiration) ? 'Expired' : 'Valid' }}
									</span>
									<span v-if="reward?.inventoryCoupon?.used"
										class="px-2 py-1 rounded-full bg-green-100 text-green-800">
										Used
									</span>
									<span v-else class="px-2 py-1 rounded-full bg-gray-100 text-gray-800">
										Not Used
									</span>
								</div>
								<p class="text-sm">
									{{ formatCouponName(reward?.inventoryCoupon?.rewardCoupon?.amountType) }}:
									<span class="px-2 py-1 rounded-full bg-green-100 text-green-800">
										{{ formatValue(reward?.inventoryCoupon?.rewardCoupon) }}
									</span>
								</p>
								<p class="text-sm mt-2">
									Coupon Code
									<span class="px-2 py-1 rounded-full bg-green-100 text-green-800">
										{{ reward?.inventoryCoupon?.externalId || '' }}
									</span>
								</p>
							</div>
							<div class="w-full md:w-1/2 text-sm">
								<p>Received: {{ formatDate(reward.date) }}</p>
								<p>{{ (isExpired(reward?.inventoryCoupon?.expiration) ? 'Expired On: ' : 'Expiration: ') +
						formatDate(reward?.inventoryCoupon?.expiration) }}</p>
							</div>
						</div>
					</div>
				</div>

				<!-- Manage Tab -->
				<div v-if="tab === 'manage'" class="space-y-4">
					<h2 class="text-xl font-bold text-black">Manage Customer</h2>
					<div class="p-4 bg-white rounded-lg shadow">
						<div>
							<h3 class="text-lg font-bold">{{ shopper.first_name }} {{ shopper.last_name }}</h3>
							<p class="text-sm">Loyalty Points: {{ shopper.loyalty_points }}</p>
						</div>
						<div class="mt-4">
							<button class="bg-blue-500 text-white px-4 py-2 rounded mr-2 mt-2"
								@click="showModifyPointsModal = true">Modify Points</button>
							<button class="bg-blue-500 text-white px-4 py-2 rounded mr-2 mt-2"
								@click="showModifyBirthdayModal = true">Modify Birthday</button>
							<!-- <button class="bg-gray-500 text-white px-4 py-2 rounded mr-2 mt-2" disabled>Grant
								Coupon</button>
							<button class="bg-gray-500 text-white px-4 py-2 rounded mr-2 mt-2" disabled>Add Custom
								Properties</button> -->
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Dialog for modifying points -->
	<div v-if="showModifyPointsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex justify-center items-center">
		<div class="bg-white p-4 w-full max-w-lg rounded-lg relative">
			<!-- Spinner -->
			<div v-if="savingPoints" class="absolute inset-0 bg-white bg-opacity-75 flex justify-center items-center">
				<div class="spinner w-8 h-8 border-4 rounded-full animate-spin border-t-4 border-t-ralprimary-light border-gray-200"></div>
			</div>
			<h5 class="text-h5">Modify Points</h5>
			<!-- Tabs within the modal -->
			<div class="flex border-b">
				<button @click="modifyTab = 'add'"
					:class="{'border-b-2 border-blue-500 text-blue-600': modifyTab === 'add', 'text-gray-500 hover:text-gray-700': modifyTab !== 'add'}"
					class="flex-1 py-2">Add Points</button>
				<button @click="modifyTab = 'remove'"
					:class="{'border-b-2 border-blue-500 text-blue-600': modifyTab === 'remove', 'text-gray-500 hover:text-gray-700': modifyTab !== 'remove'}"
					class="flex-1 py-2">Remove Points</button>
			</div>
			<!-- Input fields for Add Points -->
			<div v-if="modifyTab === 'add'" class="my-4">
				<label class="text-green-600">Points to Add</label>
				<input v-model="addPoints" type="number" class="form-input mt-1 block w-full"
					placeholder="Enter points to add" />
				<label class="text-green-600 mt-4">Reason for Addition</label>
				<input v-model="addInfo" type="text" class="form-input mt-1 block w-full" placeholder="Enter reason" />
			</div>
			<!-- Input fields for Remove Points -->
			<div v-if="modifyTab === 'remove'" class="my-4">
				<label class="text-red-600">Points to Remove</label>
				<input v-model="removePoints" type="number" class="form-input mt-1 block w-full"
					placeholder="Enter points to remove" />
				<label class="text-red-600 mt-4">Reason for Removal</label>
				<input v-model="removeInfo" type="text" class="form-input mt-1 block w-full"
					placeholder="Enter reason" />
			</div>
			<div class="flex justify-end space-x-4 mt-4">
				<button @click="showModifyPointsModal = false"
					class="bg-gray-200 text-gray-800 px-4 py-2 rounded">Cancel</button>
				<button @click="submitModification()" class="bg-blue-500 text-white px-4 py-2 rounded">Save</button>
			</div>
		</div>
	</div>


	<!-- Dialog for modifying birthday -->
	<div v-if="showModifyBirthdayModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex justify-center items-center">
		<div class="bg-white p-4 w-full max-w-lg rounded-lg relative">
			<!-- Spinner -->
			<div v-if="savingBirthday" class="absolute inset-0 bg-white bg-opacity-75 flex justify-center items-center">
				<div class="spinner w-8 h-8 border-4 rounded-full animate-spin border-t-4 border-t-ralprimary-light border-gray-200"></div>
			</div>
			<h5 class="text-h5">Modify Birthday</h5>

			<div class="my-4" v-if="shopper">
				<label class="text-green-600">Birthday</label>
				<input v-model="shopper.birthday" type="date" class="form-input mt-1 block w-full"
					placeholder="Enter birthday" />
			</div>
			<div class="flex justify-end space-x-4 mt-4">
				<button @click="showModifyBirthdayModal = false"
					class="bg-gray-200 text-gray-800 px-4 py-2 rounded">Cancel</button>
				<button @click="modifyBirthday()" class="bg-blue-500 text-white px-4 py-2 rounded">Save</button>
			</div>
		</div>
	</div>
</template>

<script>
import * as Utils from '../../../client-old/utils/Utils';
import * as CurrencyUtils from '../../services/currency.js';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: ['id'],
	data() {
		return {
			tab: 'details',
			breadcrumbs: [
				{ title: 'Customers', disabled: false, href: '../loyalty/customers' },
				{ title: `Customer ${this.id}`, disabled: true, href: '' }
			],
			savingPoints: false,
			savingBirthday: false,
			showModifyPointsModal: false,
			showModifyBirthdayModal: false,
			addingPoints: true,
			modificationAmount: '',
			modificationReason: '',
			shopper: {},
			pointsLog: [],
			rewardsLog: [],
			modifyTab: 'add',  // Default tab for the modify points modal
			addPoints: '',
			addInfo: '',
			removePoints: '',
			removeInfo: '',
			items: ['details', 'points log', 'reward log', 'manage'],
			text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
		};
	},
	mounted() {
		console.log(this.id);
		this.fetchShopper();
	},
	methods: {
		submitModification() {
			if (this.modifyTab === 'add') {
				this.modifyPoints(true, this.addPoints, this.addInfo);
			} else {
				this.modifyPoints(false, this.removePoints, this.removeInfo);
			}
		},
		async modifyPoints(isAdding, points, info) {
			this.savingPoints = true;
			const amount = isAdding ? points : -points;
			const modificationReason = info + (isAdding ? ' :Added by Admin' : ' :Removed by Admin');
			try {
				const response = await fetch(`${URL_DOMAIN}/loyalty-currencies/${this.shopper.currency_id}/balance-change`, {
					method: 'POST',
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						info: modificationReason,
						balanceChange: parseInt(amount),
						raleonUserId: this.shopper.id
					})
				});
				const data = await response.json();
				await this.fetchShopper();
			}
			catch (error) {
				console.error(error);
			} finally {
				this.savingPoints = false;
				this.showModifyPointsModal = false;
			}
		},
		async modifyBirthday() {
			this.savingBirthday = true;
			try {
				const response = await fetch(`${URL_DOMAIN}/organizations/shopper-info/${this.id}/birthday-change`, {
					method: 'POST',
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						birthday: new Date(new Date(this.shopper.birthday).toISOString().substring(0, 10) + ' 00:00').toISOString()
					})
				});
				if (!response.ok) {
					throw new Error(`HTTP error! Status: ${response.status}`);
				}
				await this.fetchShopper();
			}
			catch (error) {
				console.error(error);
			} finally {
				this.savingBirthday = false;
				this.showModifyBirthdayModal = false;
			}
		},
		async fetchShopper() {
			try {
				const response = await fetch(`${URL_DOMAIN}/organizations/shopper-info?id=${this.id}`, {
					method: 'GET',
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					}
				});
				const data = await response.json();
				this.shopper = data;
				this.shopper.loyalty_points = Utils.formatNumberWithCommas(this.shopper.loyalty_points);
				this.pointsLog = data.points_log;
				this.rewardsLog = data.rewards_log;
			} catch (error) {
				console.error(error);
			}
		},
		formatCreatedAt(date) {
			return date ? new Date(date).toLocaleDateString('en-US', {
				year: 'numeric',
				month: 'long',
				day: 'numeric',
			}) : 'N/A';
		},
		isExpired(expirationDate) {
			const today = new Date();
			const expiration = new Date(expirationDate);
			return expiration < today;
		},
		formatValue(rewardCoupon) {
			if (!rewardCoupon || !rewardCoupon.amountType) {
				return;
			}
			switch (rewardCoupon.amountType) {
				case 'dollars-off-coupon':
					return CurrencyUtils.replaceCurrencyTagsSync(`<currencyPrefix>${rewardCoupon.amount}<currencyPostfix> off`);
				case 'percent-discount':
					return `${rewardCoupon.amount}% off`;
				default:
					return rewardCoupon.amount; // Default fallback
			}
		},
		formatCouponName(name) {
			switch (name) {
				case 'dollars-off-coupon':
					return 'Dollars Off Coupon';
				case 'percent-discount':
					return 'Percent Discount';
				default:
					return name; // Default fallback
			}
		},
		formatDate(date) {
			if (!date) return 'N/A';
			return new Date(date).toLocaleDateString('en-US', {
				year: 'numeric', month: 'numeric', day: 'numeric'
			});
		},
	}
};
</script>

<style scoped>
/* Additional styles for Tailwind */
.form-input {
	border: 1px solid #d2d6dc;
	padding: 0.5rem;
	border-radius: 0.375rem;
}
</style>
