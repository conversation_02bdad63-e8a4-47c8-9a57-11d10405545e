<template>
	<div class="w-full mt-10">
		<div class="flex items-center justify-ste">
			<div class="text-ralsecondary-start text-sm font-bold font-['Inter'] mr-2">
				Reward Branding
			</div>
			<Tooltip bg="dark" size="md" position="bottom">
				<div class="text-xs whitespace-nowrap text-white">
					We use AI to give you ideas on text and imagery here!
					<a
						class="pl-2 pr-4 items-center gap-1 inline-flex cursor-pointer hover:underline ml-2"
						href="https://docs.raleon.io/docs/setting-up-a-new-campaign"
						target="_blank">
						What is branding?
					</a>
				</div>
			</Tooltip>
		</div>
		<div class="mb-6 text-ralgray-dark text-xs font-normal font-['Inter']">
			We use AI to give you ideas on text and imagery here!
		</div>
		<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide">
			Reward Name
			<span class="text-rose-500">*</span>
		</div>
		<!-- <div class="mb-3 text-black text-sm font-normal italic font-['Inter']">
			{{ reward.title }} with amount {{ reward.amount }}
		</div>  -->
		<div class="mb-3 text-ralgray-dark text-xs font-normal font-['Inter']">
			This will be shown to describe the particular reward to the shopper!
		</div>

		<div
			class="mb-8 mr-3 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"
			:class="{
				'border-ralerror-dark': v$.reward.name.$dirty && v$.reward.name.$errors.length,
				'border-gray-400': !v$.reward.name.$errors.length || !v$.reward.name.$dirty,
			}"
		>
			<div class="w-full mr-3 self-stretch justify-start items-center gap-2 flex">
				<input
					class="no-focus-outline border-none w-full text-gray-600 text-sm font-semibold font-['Inter'] leading-normal appearance-none outline-none"
					:class="{'animate-pulse bg-gray-300 dark:bg-gray-600': predictionRunning}"
					maxlength="40"
					v-model="v$.reward.name.$model"
					:disabled="predictionRunning"
				/>
				<img src="../../images/magic.svg" width="20" height="60" />
			</div>
		</div>
		<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide">
			Reward Description
			<span class="text-rose-500">*</span>
		</div>
		<div class="mb-3 text-ralgray-dark text-xs font-normal font-['Inter'] leading-none">
			Describe the reward to provide the customer more details about what they're receiving.
		</div>
		<div
			class="mb-8 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"
			:class="{
				'border-ralerror-dark': v$.reward.description.$dirty && v$.reward.description.$errors.length,
				'border-gray-400': !v$.reward.description.$errors.length || !v$.reward.description.$dirty,
			}"
		>
			<div class="w-full mr-3 self-stretch justify-start items-center gap-2 flex">
				<input
					class="no-focus-outline border-none w-full text-gray-600 text-sm font-semibold font-['Inter'] leading-normal appearance-none outline-none"
					:class="{'animate-pulse bg-gray-300 dark:bg-gray-600': predictionRunning}"
					maxlength="80"
					:disabled="predictionRunning"
					v-model="v$.reward.description.$model"
				/>
				<img src="../../images/magic.svg" width="20" height="60" />
			</div>
		</div>
		<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide">
			Upload Reward Image
			<span class="text-rose-500">*</span>
		</div>
		<div class="text-ralgray-dark text-xs font-normal font-['Inter'] leading-none">
			This image will be shown to the customer to represent the reward.
		</div>
		<div class="flex flex-wrap mt-3 items-center">
			<div class="reward-image-wrapper">
				<div class="reward-image-container">
					<img v-if="predictionRunning"
						class="rounded-3xl border object-center object-cover"
						:class="{
							'animate-pulse bg-gray-300 dark:bg-gray-600': predictionRunning
						}"
						src="../../images/magic.svg" width="20" height="20" />

					<!-- When imageURL is invalid (null or empty) -->
					<img v-else-if="v$.reward.imageURL.$model == null || v$.reward.imageURL.$model == ''"
						class="rounded-3xl border object-center object-cover border-red-500"
						src="https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg" width="20" height="20" />

					<!-- Default case: Display the actual image -->
					<img v-else
						class="rounded-3xl border object-center object-cover"
						:class="{
							'border-ralerror-dark': v$.reward.imageURL.$dirty && v$.reward.imageURL.$errors.length,
							'border-gray-400': !v$.reward.imageURL.$dirty && !v$.reward.imageURL.$errors.length
						}"
						:src="v$.reward.imageURL.$model" />
				</div>
			</div>
			<div class="flex flex-col flex-grow items-start justify-center">
				<div class="w-full ml-4">
					<ImageUpload @imageSelected="handleRewardUpload($event, index)"/>
				</div>
			</div>
		</div>
	</div>
</template>

<script>

import { useVuelidate } from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import ImageUpload from '../../components/ImageUpload.ts.vue';
import Tooltip from '../../components/Tooltip.ts.vue';

export default {
	props: ['reward', 'isRewardImageUploading', 'index', 'rewardSummary', 'predictionRunning'],
	emits: ['is-complete', 'reward-updated', 'image-selected'],
	components: {
		ImageUpload,
		Tooltip,
	},
	setup() {
		return {
			v$: useVuelidate(),
		}
	},
	methods: {
		handleRewardUpload(event, index) {
			this.$emit('image-selected', event.url, index);
		}
	},
	watch: {
		'v$.reward': {
			handler (newValue) {
				this.$emit('is-complete', !newValue.$invalid, this.index);
				this.$emit('reward-updated', newValue);
			},
			deep: true
		},
	},
	validations() {
		return {
			reward: {
				name: { required },
				description: { required },
				imageURL: { required },
			}
		}
	}
}
</script>
<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

.reward-image-wrapper {
	width: 100%;
	max-width: 150px;
	margin: auto;
}

.reward-image-container {
	position: relative;
	width: 100%;
	padding-top: 100%;
	/* Maintains a 1:1 aspect ratio */
}

.reward-image-container img {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.reward-upload-button-container {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	margin-top: 10px;
}

@media (min-width: 500px) {
	.reward-upload-button-container {
		margin-left: 12px;
		/* Add left padding back for larger screens */
		text-align: left;
		/* Align left on larger screens */
		width: 100%;
		justify-content: flex-start;
	}
}
</style>
