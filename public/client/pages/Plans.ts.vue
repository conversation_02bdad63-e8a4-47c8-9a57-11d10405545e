<template>
	<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>

<!-- Modern Container with Background -->
<div class="bg-gray-50">
	<!-- Header Section -->
	<div class="px-8 py-12 text-center">
		<h1 class="text-5xl font-bold text-gray-900 mb-4">AI Strategy & Retention Platform</h1>
		<p class="text-xl text-gray-600 mb-8">Choose the perfect plan for your business growth</p>
	</div>

	<!-- Agency Brand Message - Show when organization has a parent -->
	<div v-if="isAgencyBrand" class="max-w-4xl mx-auto px-6 py-12">
		<div class="bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-2xl p-12 text-center shadow-lg">
			<div class="mb-8">
				<svg class="mx-auto h-24 w-24 text-blue-500 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
				</svg>
				<h2 class="text-4xl font-bold text-gray-900 mb-4">Billing Managed by Agency</h2>
				<p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
					Your billing and plan management is handled by your agency organization.
					Please contact your agency administrator for any billing-related questions or plan changes.
				</p>
			</div>

			<div class="bg-white rounded-xl p-8 shadow-md border border-blue-100">
				<h3 class="text-lg font-semibold text-gray-900 mb-4">Need Help?</h3>
				<p class="text-gray-600 mb-6">
					If you have questions about your plan or need to make changes, reach out to your agency team or contact our support.
				</p>
				<div class="flex flex-col sm:flex-row gap-4 justify-center">
					<button
						@click="openChat"
						class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200"
					>
						<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.476L3 21l2.476-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
						</svg>
						Contact Support
					</button>
					<button
						@click="$router.push('/ai-strategist/planning')"
						class="inline-flex items-center justify-center px-6 py-3 border border-blue-300 text-base font-medium rounded-lg text-blue-700 bg-white hover:bg-blue-50 transition-colors duration-200"
					>
						<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
						</svg>
						Back to Dashboard
					</button>
				</div>

				<!-- Agency Billing Clarification -->
				<div v-if="hasAccessToParentAgency" class="mt-6 pt-4 border-t border-gray-200 text-center">
					<p class="text-sm text-gray-600">
						Are you the agency?
						<button
							@click="navigateToAgencyBilling()"
							class="text-blue-600 hover:text-blue-800 underline font-medium ml-1"
						>
							Go to your billing
						</button>
					</p>
				</div>
			</div>
		</div>
	</div>

	<!-- Plans Content - Only show if not an agency brand -->
	<div v-if="!isAgencyBrand">
		<!-- Agency Pricing Information - Show for agencies with Agency Platform plan -->
		<div v-if="shouldShowAgencyPlatform && isAgencyPlatformActive && agencyPricingInfo" class="max-w-4xl mx-auto px-6 mb-8">
			<div class="bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-2xl p-8 shadow-lg">
				<div class="mb-6">
					<h2 class="text-2xl font-bold text-gray-900 mb-4">Agency Platform Subscription</h2>
					<p class="text-gray-600 mb-6">Your current subscription details and organization usage</p>
				</div>

				<!-- Current Plan Information -->
				<div class="bg-white rounded-lg p-6 mb-6 border border-blue-100">
					<h3 class="text-lg font-semibold text-gray-900 mb-4">Current Plan</h3>
					<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div class="bg-gray-50 rounded-lg p-4">
							<h4 class="text-sm font-medium text-gray-900 mb-1">Plan</h4>
							<p class="text-xl font-bold text-gray-800">{{ agencyPricingInfo.currentPlan.name }}</p>
						</div>
						<div class="bg-gray-50 rounded-lg p-4">
							<h4 class="text-sm font-medium text-gray-900 mb-1">Current Organizations</h4>
							<p class="text-xl font-bold text-gray-800">{{ agencyPricingInfo.currentBrandCount }}</p>
						</div>
						<div class="bg-gray-50 rounded-lg p-4">
							<h4 class="text-sm font-medium text-gray-900 mb-1">Included in Plan</h4>
							<p class="text-xl font-bold text-gray-800">{{ agencyPricingInfo.brandsIncludedLimit }}</p>
						</div>
					</div>
				</div>

				<!-- Pricing Breakdown -->
				<div class="bg-white rounded-lg p-6 border border-blue-100">
					<h3 class="text-lg font-semibold text-gray-900 mb-4">Pricing Breakdown</h3>
					<div class="space-y-3">
						<div class="flex justify-between items-center">
							<span class="text-gray-600">Base Plan (includes {{ agencyPricingInfo.brandsIncludedLimit }} organizations)</span>
							<span class="font-medium text-gray-900">${{ agencyPricingInfo.currentPlan.price.toFixed(2) }}/month</span>
						</div>
						<div v-if="agencyPricingInfo.currentExtraBrands > 0" class="flex justify-between items-center">
							<span class="text-gray-600">Extra Organizations ({{ agencyPricingInfo.currentExtraBrands }} × ${{ agencyPricingInfo.extraBrandPrice.toFixed(2) }})</span>
							<span class="font-medium text-gray-900">${{ (agencyPricingInfo.currentExtraBrands * agencyPricingInfo.extraBrandPrice).toFixed(2) }}/month</span>
						</div>
						<div class="border-t pt-3 flex justify-between items-center">
							<span class="text-lg font-semibold text-gray-900">Total Monthly Cost</span>
							<span class="text-lg font-bold text-blue-600">${{ agencyPricingInfo.currentPrice.toFixed(2) }}/month</span>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Pending Cancellation/Downgrade Warning Banner - More Prominent -->
		<div v-if="hasPendingCancellations" class="bg-gradient-to-r from-orange-100 to-orange-50 border-2 border-orange-300 rounded-lg p-6 mx-8 mb-8 shadow-lg">
			<div class="flex items-start">
				<div class="flex-shrink-0">
					<svg v-if="hasPendingDowngrades" class="h-8 w-8 text-orange-500" viewBox="0 0 20 20" fill="currentColor">
						<path fill-rule="evenodd" d="M3 10a1 1 0 011-1h5.586l-2.293-2.293a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L9.586 11H4a1 1 0 01-1-1z" clip-rule="evenodd" />
					</svg>
					<svg v-else class="h-8 w-8 text-orange-500" viewBox="0 0 20 20" fill="currentColor">
						<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
					</svg>
				</div>
				<div class="ml-4 flex-1">
					<h3 class="text-lg font-bold text-orange-900 mb-2">
						{{ hasPendingDowngrades ? 'Plan Downgrade Scheduled' : 'Plan Cancellation Scheduled' }}
					</h3>
					<div class="text-base text-orange-800 mb-4">
						<p class="font-medium">{{ pendingCancellationMessage }}</p>
					</div>

					<!-- Action Buttons -->
					<div class="flex flex-wrap gap-3">
						<!-- Cancellation Buttons -->
						<template v-if="hasPendingCancellationsOnly">
							<button
								@click="reactivateCancelledPlan()"
								class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-all duration-300"
							>
								Keep My Plan
							</button>
						</template>

						<!-- Downgrade Buttons -->
						<template v-if="hasPendingDowngrades">
							<button
								@click="cancelDowngrade()"
								class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-all duration-300"
							>
								Keep Current Plan
							</button>
							<button
								@click="cancelAllPlans()"
								class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md font-medium transition-all duration-300"
							>
								Cancel All Plans
							</button>
						</template>
					</div>
				</div>
			</div>
		</div>

		<!-- Processing Payment Warning Banner -->
		<div v-if="hasProcessingPayments" class="bg-gradient-to-r from-yellow-100 to-yellow-50 border-2 border-yellow-300 rounded-lg p-6 mx-8 mb-8 shadow-lg">
			<div class="flex items-start">
				<div class="flex-shrink-0">
					<svg class="h-8 w-8 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
					</svg>
				</div>
				<div class="ml-4 flex-1">
					<h3 class="text-lg font-bold text-yellow-900 mb-2">
						Processing Payment
					</h3>
					<div class="text-base text-yellow-800 mb-4">
						<p class="font-medium">{{ processingPaymentMessage }}</p>
					</div>

					<!-- Action Buttons -->
					<div class="flex flex-wrap gap-3">
						<button
							@click="retryPaymentForProcessingPlan()"
							class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md font-medium transition-all duration-300"
						>
							Retry Payment
						</button>
						<button
							@click="openChat"
							class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium transition-all duration-300"
						>
							Contact Support
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Payment Failed Warning Banner -->
		<div v-if="hasPaymentFailures" class="bg-gradient-to-r from-red-100 to-red-50 border-2 border-red-300 rounded-lg p-6 mx-8 mb-8 shadow-lg">
			<div class="flex items-start">
				<div class="flex-shrink-0">
					<svg class="h-8 w-8 text-red-500" viewBox="0 0 20 20" fill="currentColor">
						<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
					</svg>
				</div>
				<div class="ml-4 flex-1">
					<h3 class="text-lg font-bold text-red-900 mb-2">
						Payment Failed
					</h3>
					<div class="text-base text-red-800 mb-4">
						<p class="font-medium">{{ paymentFailureMessage }}</p>
					</div>

					<!-- Action Buttons -->
					<div class="flex flex-wrap gap-3">
						<button
							@click="retryPaymentForFailedPlan()"
							class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md font-medium transition-all duration-300"
						>
							Retry Payment
						</button>
						<button
							@click="openChat"
							class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium transition-all duration-300"
						>
							Contact Support
						</button>
					</div>
				</div>
			</div>
		</div>



	<!-- Plans Grid -->
	<div class="max-w-8xl mx-auto px-6 mb-8">
		<div class="grid gap-6 items-stretch" :class="shouldShowAgencyPlatform ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'">
			<!-- Legacy Growth Plan - Only show if customer is paying for it -->
			<PlanCard
				v-if="shouldShowGrowthLegacy"
				:plan-name="'Growth (Legacy)'"
				:price="growthLegacyPricing?.price || 99"
				:period="'month'"
				:description="'Legacy Growth plan with premium features.'"
				:is-active="isGrowthActive"
				:is-pending-cancellation="isPlanPendingCancellation('Growth')"
				:is-pending-downgrade="isPlanPendingDowngrade('Growth')"
				:is-pending-activation="isPlanPendingActivation('Growth')"
				:pending-cancellation-date="getPendingCancellationDate('Growth')"
				:pending-activation-date="getPendingActivationDate('Growth')"
				:downgrade-target="getDowngradeTargetPlan()"
				:is-in-trial="isInTrial"
				:has-revenue-pricing="true"
				:revenue-options="getRevenueOptionsForPlan('Growth')"
				:selected-revenue="selectedRevenue"
				:is-pending="isGrowthPending"
				:is-past-due="isGrowthPastDue"
				@unsubscribe-plan="unsubscribeGrowthLegacy"
				@revenue-change="updateSelectedRevenue"
			/>

			<!-- Legacy Pro Plan - Only show if customer is paying for it -->
			<PlanCard
				v-if="shouldShowProLegacy"
				:plan-name="'Pro (Legacy)'"
				:price="proLegacyPricing?.price || 199"
				:period="'month'"
				:description="'Legacy Pro plan with advanced features.'"
				:is-active="isProActive"
				:is-pending-cancellation="isPlanPendingCancellation('Pro')"
				:is-pending-downgrade="isPlanPendingDowngrade('Pro')"
				:is-pending-activation="isPlanPendingActivation('Pro')"
				:pending-cancellation-date="getPendingCancellationDate('Pro')"
				:pending-activation-date="getPendingActivationDate('Pro')"
				:downgrade-target="getDowngradeTargetPlan()"
				:is-in-trial="isInTrial"
				:has-revenue-pricing="true"
				:revenue-options="getRevenueOptionsForPlan('Pro')"
				:selected-revenue="selectedRevenue"
				:is-pending="isProPending"
				:is-past-due="isProPastDue"
				@unsubscribe-plan="unsubscribeProLegacy"
				@revenue-change="updateSelectedRevenue"
			/>

			<!-- Starter Plan - Only for non-agency orgs -->
			<PlanCard
				v-if="shouldShowStrategistPlans"
				:plan-name="'Starter'"
				:price="strategistLitePricing?.price || 20"
				:period="'month'"
				:description="'Perfect for smaller brands or freelancers wanting to use AI to get work done faster.'"
				:features="strategistLiteFeatures"
				:is-active="isStrategistLiteActive"
				:is-pending-cancellation="isPlanPendingCancellation('Strategist Lite')"
				:is-pending-downgrade="isPlanPendingDowngrade('Strategist Lite')"
				:is-pending-activation="isPlanPendingActivation('Strategist Lite')"
				:pending-cancellation-date="getPendingCancellationDate('Strategist Lite')"
				:pending-activation-date="getPendingActivationDate('Strategist Lite')"
				:downgrade-target="getDowngradeTargetPlan()"
				:is-in-trial="isInTrial"
				:has-revenue-pricing="false"
				:is-pending="isStrategistLitePending"
				:is-past-due="isStrategistLitePastDue"
				@choose-plan="chooseStrategistLite"
				@unsubscribe-plan="unsubscribeStrategistLite"
			/>

			<!-- Advanced Plan - Only for non-agency orgs -->
			<PlanCard
				v-if="shouldShowStrategistPlans"
				:plan-name="'Advanced'"
				:price="strategistPricing?.price || 99"
				:period="'month'"
				:description="'Advanced AI agent for growing brands that want more revenue and efficiency.'"
				:features="strategistFeatures"
				:includes-text="getIncludesText('Strategist')"
				:is-active="isStrategistActive"
				:is-pending-cancellation="isPlanPendingCancellation('Strategist')"
				:is-pending-downgrade="isPlanPendingDowngrade('Strategist')"
				:is-pending-activation="isPlanPendingActivation('Strategist')"
				:pending-cancellation-date="getPendingCancellationDate('Strategist')"
				:pending-activation-date="getPendingActivationDate('Strategist')"
				:downgrade-target="getDowngradeTargetPlan()"
				:is-in-trial="isInTrial"
				:has-revenue-pricing="getStrategistPlan()?.hasRevenueBasedPricing || false"
				:revenue-options="getRevenueOptionsForPlan('Strategist')"
				:selected-revenue="selectedRevenue"
				:is-pending="isStrategistPending"
				:is-past-due="isStrategistPastDue"
				@choose-plan="chooseStrategist"
				@unsubscribe-plan="unsubscribeStrategist"
				@revenue-change="updateSelectedRevenue"
			/>

			<!-- Max Plan - Only for non-agency orgs -->
			<PlanCard
				v-if="shouldShowStrategistPlans"
				:plan-name="'Pro'"
				:price="strategistMaxPricing?.price || 199"
				:period="'month'"
				:description="'AI Segmentation tunes to your brand and the most powerful retention agent available.'"
				:features="strategistMaxFeatures"
				:includes-text="getIncludesText('Strategist Max')"
				:is-featured="true"
				:is-active="isStrategistMaxActive"
				:is-pending-cancellation="isPlanPendingCancellation('Strategist Max')"
				:is-pending-downgrade="isPlanPendingDowngrade('Strategist Max')"
				:is-pending-activation="isPlanPendingActivation('Strategist Max')"
				:pending-cancellation-date="getPendingCancellationDate('Strategist Max')"
				:pending-activation-date="getPendingActivationDate('Strategist Max')"
				:downgrade-target="getDowngradeTargetPlan()"
				:is-in-trial="isInTrial"
				:has-revenue-pricing="getStrategistMaxPlan()?.hasRevenueBasedPricing || false"
				:revenue-options="getRevenueOptionsForPlan('Strategist Max')"
				:selected-revenue="selectedRevenue"
				:is-pending="isStrategistMaxPending"
				:is-past-due="isStrategistMaxPastDue"
				@choose-plan="chooseStrategistMax"
				@unsubscribe-plan="unsubscribeStrategistMax"
				@revenue-change="updateSelectedRevenue"
			/>

			<!-- Agency Platform Plan - Only for agency orgs -->
			<PlanCard
				v-if="shouldShowAgencyPlatform"
				:plan-name="'Agency Platform'"
				:price="agencyPlatformPricing?.price || 499"
				:period="'month'"
				:description="'Get AI Segmentation, Strategist Agent, and exclusive features for your organization.'"
				:features="agencyPlatformFeatures"
				:is-active="isAgencyPlatformActive"
				:is-pending-cancellation="isPlanPendingCancellation('Agency Platform')"
				:is-pending-downgrade="isPlanPendingDowngrade('Agency Platform')"
				:is-pending-activation="isPlanPendingActivation('Agency Platform')"
				:pending-cancellation-date="getPendingCancellationDate('Agency Platform')"
				:pending-activation-date="getPendingActivationDate('Agency Platform')"
				:downgrade-target="getDowngradeTargetPlan()"
				:is-in-trial="isInTrial"
				:has-revenue-pricing="false"
				:is-pending="isAgencyPlatformPending"
				:is-past-due="isAgencyPlatformPastDue"
				@choose-plan="chooseAgencyPlatform"
				@unsubscribe-plan="unsubscribeAgencyPlatform"
			/>



			<!-- Enterprise Plan - Always shown -->
			<PlanCard
				:plan-name="'Enterprise'"
				:price="enterprisePricing?.price || 'Custom'"
				:period="'month'"
				:description="'Everything in Agency Platform and custom features & development.'"
				:features="enterpriseFeatures"
				:is-contact-sales="true"
				:is-active="isEnterpriseActive"
				:has-revenue-pricing="false"
				@contact-sales="openChat"
			/>
		</div>
	</div>

	<!-- Confirmation Modal -->
	<ModalBlank id="plan-confirmation-modal" :modalOpen="confirmationModalOpen" @close-modal="confirmationModalOpen = false">
		<div class="p-5 flex space-x-4">
			<div class="w-10 h-10 rounded-full flex items-center justify-center shrink-0" :class="confirmationModalType === 'cancel' ? 'bg-red-100' : 'bg-indigo-100'">
				<svg v-if="confirmationModalType === 'cancel'" class="w-5 h-5 shrink-0 fill-current text-red-500" viewBox="0 0 16 16">
					<path d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm7 8c0 3.9-3.1 7-7 7s-7-3.1-7-7 3.1-7 7-7 7 3.1 7 7zM6 5h4v6H6z" />
				</svg>
				<svg v-else class="w-5 h-5 shrink-0 fill-current text-indigo-500" viewBox="0 0 16 16">
					<path d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm1 12H7V7h2v5zM8 6c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1z" />
				</svg>
			</div>
			<div class="w-full">
				<div class="mb-2">
					<div class="text-lg font-semibold text-ralblack-primary">{{ confirmationModalTitle }}</div>
				</div>
				<div class="text-sm mb-6">
					<div class="space-y-2">
						<p class="text-ralblack-secondary">{{ confirmationModalMessage }}</p>
					</div>
				</div>
				<div class="flex flex-wrap justify-end space-x-2">
					<button
						class="btn-sm border border-slate-200 hover:border-slate-300 text-slate-600 rounded-full px-4 py-2 transition-all duration-300"
						@click="confirmationModalOpen = false"
					>
						Cancel
					</button>
					<button
						class="btn-sm text-white rounded-full px-4 py-2 transition-all duration-300"
						:class="confirmationModalType === 'cancel' ? 'bg-red-500 hover:bg-red-600' : 'bg-gradient-button-primary hover:opacity-90'"
						@click="confirmAction"
					>
						{{ confirmationModalType === 'cancel' ? 'Yes, Cancel Plan' : 'Yes, Switch Plan' }}
					</button>
				</div>
			</div>
		</div>
	</ModalBlank>









</div>

<!-- loyalty features -->
<a name="features"></a>
<!-- AI-Powered Lifetime Value Optimization -->
<!-- <div id="features-ai" class="px-2 sm:pl-7 sm:pr-7 mt-8">
    <h2 class="text-4xl font-bold text-left mb-4">AI-Powered Lifetime Value Optimization</h2>
    <p class="text-base text-left mb-6">Features designed to maximize customer lifetime value using AI.</p>
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Features</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ">Free<br>Less than $250K GMV</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider bg-green-100">Growth</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Pro</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Enterprise</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">AI Segmentation</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 "></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">AI Customer Insights</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 "></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">AI Planner - Builds plans and campaigns for you</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 "></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Custom Segmentation Signals</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 "></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 "></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div> -->

<!-- <div id="features-loyalty" class="px-2 sm:pl-7 mt-8">
    <h2 class="text-4xl font-bold text-left mb-4">Loyalty Platform Features</h2>
    <p class="text-base text-left mb-6">Core features to build and manage your loyalty program.</p>
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loyalty</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ">Free<br>Less than $250K GMV</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider bg-green-100">Growth</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Pro</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Enterprise</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Points Program</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">VIP Program</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Basic Ways to Earn</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Advanced ways to earn & rewards</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Referrals</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">On-site Loyalty Sidebar</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">On-site Loyalty Notifications</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
					<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">5+ Onsite Embeds (like on Product Page)</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Built-In Email Automation (no integrations required)</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Integrations (such Klaviyo, Judge.me, or Skio)</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                       <p class="text-base text-center font-semibold">2</p>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Loyalty Campaigns</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Loyalty Flow Automations</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Checkout Extension (Shopify+ only)</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Performance Dashboards</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Custom Landing Page</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Multilanguage & Currency</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div> -->

<!-- Gift With Purchase Features -->
<!-- <div id="features-gift-with-purchase" class="px-2 sm:pl-7 mt-8">
    <h2 class="text-4xl font-bold text-left mb-4">Gift With Purchase</h2>
    <p class="text-base text-left mb-6">Features and capabilities related to offering gifts with purchase.</p>
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gift With Purchase</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ">Free<br>Less than $250K GMV</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider bg-green-100">Growth</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Pro</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Enterprise</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Auto-Add Gift to Cart</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 "></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Ignore Discount Stacking (Shopify+ only)</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 "></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Free Gift through Discount (follows stacking)</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 "></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Free Gifts work with 3PL</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 "></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Backup Inventory Item for Free Gift</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 "></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Analytics (Conversions, Abandons, and more)</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 "></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div> -->

<!-- Support Features -->
<!-- <div id="features-support" class="px-2 sm:pl-7 mt-8">
    <h2 class="text-4xl font-bold text-left mb-4">Support</h2>
    <p class="text-base text-left mb-6">Support features and services offered with each plan.</p>
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Support</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ">Free<br>Less than $250K GMV</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider bg-green-100">Growth</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Pro</th>
                    <th class="px-6 text-center py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Enterprise</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Email & Chat Support</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">1:1 Onboarding with Raleon Specialist</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Dedicated Slack Channel</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Dedicated Lifetime Value Specialist</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Monthly Insights and Recommended Campaigns Meeting</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 ">
                        <svg aria-hidden="true" color="#007A5A" viewBox="0 0 20 20" class="w-6 mx-auto"><path fill="currentColor" fill-rule="evenodd" d="M17.557 3.295a1.25 1.25 0 0 1 .148 1.762l-9.72 11.5A1.25 1.25 0 0 1 6 16.46l-3.78-5.476a1.25 1.25 0 1 1 2.058-1.42l2.851 4.13 8.665-10.25a1.25 1.25 0 0 1 1.762-.149" clip-rule="evenodd"></path></svg>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div> -->

<div id="talktuahus" class="px-2 sm:pl-7 mt-8">
	<div class="flex items-center flex-col mt-8 mb-10">
		<p class="text-3xl text-center mb-4">Need help deciding?</p>
		<PrimaryButton cta="Talk to us" @click="openChat"></PrimaryButton>
	</div>

	</div> <!-- End of conditional plans content -->
</div> <!-- End of main container -->

<ModalBlank id="info-modal" :modalOpen="loadingModalOpen" @close-modal="loadingModalOpen = false">
		<div class="p-5 flex flex-col space-x-4 justify-center">
			<div class="flex justify-center">
				<RaleonLoader />
			</div>
			<div>

				<div class="flex justify-center mb-2 mt-10">
					<div class="text-lg font-semibold text-ralblack-primary flex items-center">
						<svg width="24" height="26" viewBox="0 0 24 26" fill="none" xmlns="http://www.w3.org/2000/svg"
							class="mr-2" v-if="aiTextIndex == 0">
							<path
								d="M8.14838 4.24398L8.31532 4.32745C8.99 4.67522 9.53949 5.22471 9.88726 5.89939L9.97073 6.06632C10.3046 6.7271 11.2505 6.7271 11.5914 6.06632L11.6748 5.89939C12.0226 5.22471 12.5721 4.67522 13.2468 4.32745L13.4137 4.24398C14.0745 3.91011 14.0745 2.96416 13.4137 2.62334L13.2468 2.53988C12.5721 2.1921 12.0226 1.64262 11.6748 0.967932L11.5914 0.801C11.2575 0.140226 10.3115 0.140226 9.97073 0.801L9.88726 0.967932C9.53949 1.64262 8.99 2.1921 8.31532 2.53988L8.14838 2.62334C7.48761 2.95721 7.48761 3.90316 8.14838 4.24398Z"
								fill="#E86AD6" />
							<path
								d="M9.46298 10.7613C10.1446 10.4135 10.1446 9.4467 9.46298 9.09893L8.44747 8.57726C7.54325 8.11124 6.80597 7.37396 6.33995 6.46974L5.81829 5.45424C5.47051 4.7726 4.5037 4.7726 4.15592 5.45424L3.63426 6.46974C3.16824 7.37396 2.43095 8.11124 1.52673 8.57726L0.51123 9.09893C-0.17041 9.4467 -0.17041 10.4135 0.51123 10.7613L1.52673 11.283C2.43095 11.749 3.16824 12.4863 3.63426 13.3905L4.15592 14.406C4.5037 15.0876 5.47051 15.0876 5.81829 14.406L6.33995 13.3905C6.80597 12.4863 7.54325 11.749 8.44747 11.283L9.46298 10.7613Z"
								fill="#E86AD6" />
							<path
								d="M22.5115 14.9207L21.4821 14.3921C20.2371 13.7591 19.2424 12.7645 18.6095 11.5194L18.0809 10.49C17.6148 9.5719 16.6828 9.00155 15.6534 9.00155C14.624 9.00155 13.6919 9.5719 13.2259 10.49L12.6973 11.5194C12.0643 12.7645 11.0697 13.7591 9.82466 14.3921L8.79525 14.9207C7.87712 15.3867 7.30677 16.3188 7.30677 17.3482C7.30677 18.3776 7.87712 19.3096 8.79525 19.7756L9.82466 20.3043C11.0697 20.9372 12.0643 21.9319 12.6973 23.1769L13.2259 24.2063C13.6919 25.1244 14.624 25.6948 15.6534 25.6948C16.6828 25.6948 17.6148 25.1244 18.0809 24.2063L18.6095 23.1769C19.2424 21.9319 20.2371 20.9372 21.4821 20.3043L22.5115 19.7756C23.4296 19.3096 24 18.3776 24 17.3482C24 16.3188 23.4296 15.3867 22.5115 14.9207ZM21.5656 17.9185L20.5362 18.4471C18.8947 19.2818 17.587 20.5894 16.7524 22.2309L16.2237 23.2604C16.0638 23.5734 15.7716 23.6081 15.6534 23.6081C15.5351 23.6081 15.243 23.5734 15.083 23.2604L14.5544 22.2309C13.7197 20.5894 12.4121 19.2818 10.7706 18.4471L9.7412 17.9185C9.4282 17.7585 9.39342 17.4664 9.39342 17.3482C9.39342 17.2299 9.4282 16.9378 9.7412 16.7778L10.7706 16.2492C12.4121 15.4145 13.7197 14.1069 14.5544 12.4654L15.083 11.436C15.243 11.123 15.5351 11.0882 15.6534 11.0882C15.7716 11.0882 16.0638 11.123 16.2237 11.436L16.7524 12.4654C17.587 14.1069 18.8947 15.4145 20.5362 16.2492L21.5656 16.7778C21.8786 16.9378 21.9133 17.2299 21.9133 17.3482C21.9133 17.4664 21.8786 17.7585 21.5656 17.9185Z"
								fill="#E86AD6" />
						</svg>
						Creating your subscription
					</div>
				</div>
			</div>
		</div>
	</ModalBlank>


</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import CardContainer from '../components/CardContainer.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue'
import ToggleItem from '../components/ToggleItem.ts.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
import StatusMessage from '../components/StatusMessage.ts.vue';
import { useRoute } from 'vue-router';
import ModalBlank from '../components/ModalBlank.vue';
import RaleonLoader from '../components/RaleonLoader.ts.vue';
import { Crisp } from 'crisp-sdk-web';
import { freeTrialInfo } from '../services/features.js';
import * as OrganizationSettings from '../services/organization-settings.js';
import { REVENUE_RANGES } from '../../client-old/utils/Utils';
import PlanCard from '../components/PlanCard.vue';
import * as OrgServices from '../services/organization.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		CardContainer,
		LightSecondaryButton,
		PrimaryButton,
		StatusMessage,
		ToggleItem,
		ModalBlank,
		RaleonLoader,
		PlanCard
	},
        async mounted() {
                customerIOTrackEvent('Pricing Viewed');
                const route = useRoute();

                // Load organization data
                this.currentOrg = await OrgServices.getCurrentOrg();

                // Load user organizations to check agency access
                await this.fetchUserOrgs();

                await this.getAvailablePlans();
                this.freeTrialData = await freeTrialInfo();
                this.isInTrial = !this.freeTrialData.hasPaidPlan;

                // Load agency pricing information if applicable
                await this.fetchAgencyPricingInfo();

                // Load stored revenue settings - try annualrevenue first, then fall back to revenue
                let storedRevenue = await OrganizationSettings.getOrganizationSetting('annualrevenue');
                if (!storedRevenue) {
                	storedRevenue = await OrganizationSettings.getOrganizationSetting('revenue');
                }

                if (storedRevenue !== null && storedRevenue !== undefined) {
                        const val = parseInt(storedRevenue);
                        if (!isNaN(val)) {
                                this.selectedRevenue = val;
                        }
                }

		console.log(this.freeTrialData);

		if (route.query.code && route.query.name) {
			this.exchangeCode(route.query.code, route.query.name);
		}
	},
	data() {
		return {
			status: {},
			freeTrialData: {},
			isInTrial: false,
			isConnected: false,
			isActive: true,
			activeState: true,
			exchangingCode: false,
			planName: 'Free',
			planCost: '0',
			planSelection: 'free',
			selectedPlanId: null,
			subscribed: false,
			planDetails: [],
			selectedPlan: {name: 'Free', cost: '0'},
			loyaltyPlan: true,
			gwpPlan: false,
			bundlePlan: false,
			selectedRevenue: 0,
			loadingModalOpen: false,
			visiblePlanIds: [13, 14, 15, 16, 17, 11], // Updated to show new plan IDs
			confirmationModalOpen: false,
			confirmationModalTitle: '',
			confirmationModalMessage: '',
                        confirmationModalType: 'switch', // 'switch' or 'cancel'
                        pendingAction: null, // Function to execute after confirmation
                        revenues: REVENUE_RANGES,
			currentOrg: null,
			agencyPricingInfo: null,
			userOrgs: [], // List of organizations user has access to
			// Feature definitions for each plan
			strategistLiteFeatures: [
				{ text: 'AI Strategist Agent', tooltip: 'Your personal Marketing Strategist Agent that will complete tasks for you, fully understanding your brand.' },
				{ text: 'Up to 20 user messages per day', tooltip: 'Send up to 20 messages daily to your AI Strategist Agent. Refreshes daily.' },
				{ text: 'Automated Campaign Planning', tooltip: 'Your Strategist automatically performs brand-informed strategic reasoning to come up with a plan of campaigns to run, when, and who to target.' },
				{ text: 'Branded copywriting', tooltip: 'AI Agent generated copy that matches your brand voice and messaging style' },
				{ text: 'Email generation', tooltip: 'Emails laid out, designed, and coded for you. With 1-Click send to ESP.' },
				{ text: 'Live Chat & Email support', tooltip: 'Get help from our support team via live chat during business hours or email anytime' }
			],
			strategistFeatures: [
				{ text: 'Up to 30 user messages per day', tooltip: 'Increased daily message limit for more extensive AI consultations and content creation' },
				{ text: 'Up to 2 integrations (like Shopify, Klaviyo)', tooltip: 'Connect with your favorite tools to sync data and automate workflows across platforms' },
				{ text: 'Automated AI data analysis', tooltip: 'AI automatically analyzes your marketing performance and provides actionable insights' },
				{ text: 'Advanced agent learning', tooltip: 'Your AI strategist learns from your feedback and improves recommendations over time' },
				{ text: 'Agent brainstorm mode', tooltip: 'Collaborative ideation sessions where AI generates multiple creative solutions for your challenges' }
			],
			strategistMaxFeatures: [
				{ text: 'Up to 50 user messages per day', tooltip: 'Maximum daily messaging capacity for extensive AI-powered marketing operations' },
				{ text: 'AI Segmentation', tooltip: 'Automatically segment your audience based on behavior, preferences, and engagement patterns' },
				{ text: 'Automated AI Segment Management', tooltip: 'AI continuously optimizes your customer segments and updates targeting strategies' },
				{ text: 'Unlimited integrations', tooltip: 'Connect with all your marketing tools and platforms without any limits' },
				{ text: 'Customize Strategist Agent', tooltip: 'Tailor your AI assistant\'s personality, expertise focus, and communication style' },
				{ text: 'Retention analytics', tooltip: 'Deep insights into customer lifetime value, churn prediction, and retention strategies' },
				{ text: 'Dedicated slack channel', tooltip: 'Private Slack channel for direct communication with our team and priority support' }
			],
			agencyPlatformFeatures: [
				{ text: '🏢 Multi-brand Management', tooltip: 'Manage multiple brands and clients from a single dashboard.' },
				{ text: '100 messages/day per brand', tooltip: 'High-volume messaging capacity for agency-scale operations.' },
				{ text: 'Dedicated Slack + CSM', tooltip: 'Private Slack channel and dedicated customer success manager for agencies.' }
			],
			enterpriseFeatures: [
				{ text: 'Custom AI Signals', tooltip: 'Build custom triggers and alerts based on your unique business metrics and customer behaviors' },
				{ text: 'Custom Strategist Agent setup', tooltip: 'Fully customized AI assistant trained on your specific industry, brand guidelines, and business model' },
				{ text: 'Custom Email Generation Setup', tooltip: 'Personalized email templates and automation flows designed specifically for your brand' },
				{ text: 'Customer success manager with quarterly meetings', tooltip: 'Dedicated CSM for strategic guidance, feature training, and quarterly business reviews' }
			]
                }
        },
        computed: {
                pricingRevenue() {
                        return this.selectedRevenue >= 15000000 ? 10000000 : this.selectedRevenue;
                },
                selectedPlan() {
			let plan = this.planDetails.find(plan => plan.status === "ACTIVE" || plan.status === "PENDING");

			if (!plan) {
				plan = this.planDetails.find(plan => plan.priceOverride);
			}

			if (!plan) {
				plan = this.planDetails.find(plan => plan.id === this.selectedPlanId);
			}
			return plan;
		},
		visiblePlans() {
			return this.planDetails.filter(plan => this.visiblePlanIds.includes(plan.id));
		},
		selectedPlanForReal() {
			return this.planDetails.find(plan => plan.id === this.selectedPlanId);
		},
		hasSelectedPrice() {
			return this.selectedPrice || this.selectedPrice === 0;
		},
		selectedPriceMin() {
			const plan = this.selectedPlanForReal;
			if (!plan.hasRevenueBasedPricing) {
				return undefined;
			}
			return plan.planRevenuePricings[0].price;
		},
		selectedPriceMax() {
			const plan = this.selectedPlanForReal;
			if (!plan.hasRevenueBasedPricing) {
				return undefined;
			}
			return plan.planRevenuePricings[plan.planRevenuePricings.length - 1].price;
		},
		hasSelectedPriceRange() {
			return (this.selectedPriceMin || this.selectedPriceMin === 0) && (this.selectedPriceMax || this.selectedPriceMax === 0);
		},
		growthPricing() {
			const plan = this.planDetails.find(plan => plan.name === 'Growth');
			if (!plan) return;
			const pricing = plan.planRevenuePricings;

			if (plan.priceOverride) {
				return { price: plan.priceOverride };
			}

                        return pricing.find(price => price.revenueUsdMin == this.pricingRevenue);
                },
		proPricing() {
			const plan = this.planDetails.find(plan => plan.name === 'Pro');
			if (!plan) return;
			const pricing = plan.planRevenuePricings;

			if (plan.priceOverride) {
				return { price: plan.priceOverride };
			}

                        return pricing.find(price => price.revenueUsdMin == this.pricingRevenue);
                },
		enterprisePricing() {
			const plan = this.planDetails.find(plan => plan.name === 'Enterprise');
			if (!plan) return;
			const pricing = plan.planRevenuePricings;

			if (plan.priceOverride) {
				return { price: plan.priceOverride };
			}

                        return pricing.find(price => price.revenueUsdMin == this.pricingRevenue);
                },
		isFreePlanActive() {
			return this.selectedPlan?.name === 'Free';
		},
		isGrowthPlanActive() {
			return this.selectedPlan?.name === 'Growth';
		},
		isProPlanActive() {
			return this.selectedPlan?.name === 'Pro';
		},
		isEnterprisePlanActive() {
			return this.selectedPlan?.name === 'Enterprise' || this.selectedPlanId === 7;
		},

		isFreePlanSubscribed() {
			return this.selectedPlan?.name === 'Free' && this.selectedPlan?.subscriptionId;
		},
		isGrowthPlanSubscribed() {
			return this.selectedPlan?.name === 'Growth' && this.selectedPlan?.subscriptionId;
		},
		isProPlanSubscribed() {
			return this.selectedPlan?.name === 'Pro' && this.selectedPlan?.subscriptionId;
		},
		isEnterprisePlanSubscribed() {
			return this.selectedPlan?.name === 'Enterprise' && this.selectedPlan?.subscriptionId;
		},

		isStrategistLiteActive() {
			return this.selectedPlan?.name === 'Strategist Lite';
		},
		isStrategistActive() {
			return this.selectedPlan?.name === 'Strategist';
		},
		isStrategistMaxActive() {
			return this.selectedPlan?.name === 'Strategist Max';
		},
		isAgencyPlatformActive() {
			return this.selectedPlan?.name === 'Agency Platform';
		},
		isExtraAgencyBrandActive() {
			return this.selectedPlan?.name === 'Extra Agency Brand';
		},
		isEnterpriseActive() {
			return this.selectedPlan?.name === 'Enterprise';
		},

		// Check if plans are in PENDING status
		isStrategistLitePending() {
			return this.planDetails.some(plan => plan.name === 'Strategist Lite' && plan.status === 'PENDING');
		},
		isStrategistPending() {
			return this.planDetails.some(plan => plan.name === 'Strategist' && plan.status === 'PENDING');
		},
		isStrategistMaxPending() {
			return this.planDetails.some(plan => plan.name === 'Strategist Max' && plan.status === 'PENDING');
		},
		isAgencyPlatformPending() {
			return this.planDetails.some(plan => plan.name === 'Agency Platform' && plan.status === 'PENDING');
		},
		isGrowthPending() {
			return this.planDetails.some(plan => plan.name === 'Growth' && plan.status === 'PENDING');
		},
		isProPending() {
			return this.planDetails.some(plan => plan.name === 'Pro' && plan.status === 'PENDING');
		},

		// Check if plans are in PAST_DUE status
		isStrategistLitePastDue() {
			return this.planDetails.some(plan => plan.name === 'Strategist Lite' && plan.status === 'PAST_DUE');
		},
		isStrategistPastDue() {
			return this.planDetails.some(plan => plan.name === 'Strategist' && plan.status === 'PAST_DUE');
		},
		isStrategistMaxPastDue() {
			return this.planDetails.some(plan => plan.name === 'Strategist Max' && plan.status === 'PAST_DUE');
		},
		isAgencyPlatformPastDue() {
			return this.planDetails.some(plan => plan.name === 'Agency Platform' && plan.status === 'PAST_DUE');
		},
		isGrowthPastDue() {
			return this.planDetails.some(plan => plan.name === 'Growth' && plan.status === 'PAST_DUE');
		},
		isProPastDue() {
			return this.planDetails.some(plan => plan.name === 'Pro' && plan.status === 'PAST_DUE');
		},

		// Legacy plan visibility - only show if customer is paying for them
		shouldShowGrowthLegacy() {
			const growthPlan = this.planDetails.find(plan => plan.name === 'Growth');
			return growthPlan && (growthPlan.status === 'ACTIVE' || growthPlan.status === 'PENDING') && growthPlan.subscriptionId;
		},
		shouldShowProLegacy() {
			const proPlan = this.planDetails.find(plan => plan.name === 'Pro');
			return proPlan && (proPlan.status === 'ACTIVE' || proPlan.status === 'PENDING') && proPlan.subscriptionId;
		},

		// Legacy plan active states
		isGrowthActive() {
			return this.selectedPlan?.name === 'Growth';
		},
		isProActive() {
			return this.selectedPlan?.name === 'Pro';
		},

		// Check if organization is an agency brand (has a parent organization)
		isAgencyBrand() {
			return this.currentOrg?.parentOrgId != null;
		},

		// Check if user has access to the parent agency organization
		hasAccessToParentAgency() {
			if (!this.isAgencyBrand || !this.currentOrg?.parentOrgId) {
				return false;
			}
			// Check if the parent agency org is in the user's accessible organizations
			return this.userOrgs.some(org => org.id === this.currentOrg.parentOrgId);
		},

		// Plan visibility based on organization type
		shouldShowStrategistPlans() {
			return this.currentOrg?.orgType !== 'agency';
		},
		shouldShowAgencyPlatform() {
			return this.currentOrg?.orgType === 'agency';
		},

		// Check if user has any ACTIVE plans with subscriptionIds (paid subscriptions)
		hasActivePaidSubscription() {
			return this.planDetails.some(plan =>
				plan.status === 'ACTIVE' && plan.subscriptionId
			);
		},

		// Helper method to get "includes" text for plan hierarchy
		getIncludesText() {
			return (planName) => {
				const planHierarchy = ['Strategist Lite', 'Strategist', 'Strategist Max', 'Enterprise'];
				const currentIndex = planHierarchy.indexOf(planName);

				if (currentIndex <= 0) return null; // First plan or not found

				const previousPlan = planHierarchy[currentIndex - 1];
				// Convert plan names to match Plans2 style (lowercase for "starter")
				const displayName = previousPlan.toLowerCase();
				return `Includes ${displayName}, plus:`;
			};
		},
		strategistLitePricing() {
			const plan = this.planDetails.find(plan => plan.name === 'Strategist Lite');
			if (!plan) return;
			const pricing = plan.planRevenuePricings;

			if (plan.priceOverride) {
				return { price: plan.priceOverride };
			}

			// If no revenue-based pricing, return default price
			if (!pricing || pricing.length === 0) {
				return { price: 20 };
			}

                        return pricing.find(price => price.revenueUsdMin == this.pricingRevenue);
                },
		strategistPricing() {
			const plan = this.planDetails.find(plan => plan.name === 'Strategist');
			if (!plan) return;
			const pricing = plan.planRevenuePricings;

			if (plan.priceOverride) {
				return { price: plan.priceOverride };
			}

                        return pricing.find(price => price.revenueUsdMin == this.pricingRevenue);
                },
		strategistMaxPricing() {
			const plan = this.planDetails.find(plan => plan.name === 'Strategist Max');
			if (!plan) return;
			const pricing = plan.planRevenuePricings;

			if (plan.priceOverride) {
				return { price: plan.priceOverride };
			}

                        return pricing.find(price => price.revenueUsdMin == this.pricingRevenue);
                },
		agencyPlatformPricing() {
			const plan = this.planDetails.find(plan => plan.name === 'Agency Platform');
			if (!plan) return;
			const pricing = plan.planRevenuePricings;

			if (plan.priceOverride) {
				return { price: plan.priceOverride };
			}

			// If no revenue-based pricing, return default price
			if (!pricing || pricing.length === 0) {
				return { price: 499 };
			}

                        return pricing.find(price => price.revenueUsdMin == this.pricingRevenue);
                },
		enterprisePricing() {
			const plan = this.planDetails.find(plan => plan.name === 'Enterprise');
			if (!plan) return;

			if (plan.priceOverride) {
				return { price: plan.priceOverride };
			}

			// Return null to indicate "Custom" pricing should be shown
			return null;
		},
		// Legacy plan pricing
		growthLegacyPricing() {
			const plan = this.planDetails.find(plan => plan.name === 'Growth');
			if (!plan) return;
			const pricing = plan.planRevenuePricings;

			if (plan.priceOverride) {
				return { price: plan.priceOverride };
			}

			return pricing?.find(price => price.revenueUsdMin == this.pricingRevenue);
		},
		proLegacyPricing() {
			const plan = this.planDetails.find(plan => plan.name === 'Pro');
			if (!plan) return;
			const pricing = plan.planRevenuePricings;

			if (plan.priceOverride) {
				return { price: plan.priceOverride };
			}

			return pricing?.find(price => price.revenueUsdMin == this.pricingRevenue);
		},
		// Check if any plans have pending cancellations or downgrades
		hasPendingCancellations() {
			return this.hasPendingCancellationsOnly || this.hasPendingDowngrades;
		},
		// Check if any plans have payment failures
		hasPaymentFailures() {
			return this.planDetails.some(plan => plan.status === 'PAST_DUE');
		},
		// Check if any plans have processing payments
		hasProcessingPayments() {
			return this.planDetails.some(plan => plan.status === 'PENDING');
		},
		// Check if there are pending cancellations (scheduledEnd without corresponding scheduledStart)
		hasPendingCancellationsOnly() {
			const plansWithScheduledEnd = this.planDetails.filter(plan =>
				plan.status === 'ACTIVE' && plan.scheduledEnd
			);
			const plansWithScheduledStart = this.planDetails.filter(plan =>
				plan.scheduledStart
			);

			// If there are plans ending but no plans starting, it's a cancellation
			return plansWithScheduledEnd.length > 0 && plansWithScheduledStart.length === 0;
		},
		// Check if there are pending downgrades (scheduledEnd with corresponding scheduledStart)
		hasPendingDowngrades() {
			const plansWithScheduledEnd = this.planDetails.filter(plan =>
				plan.status === 'ACTIVE' && plan.scheduledEnd
			);
			const plansWithScheduledStart = this.planDetails.filter(plan =>
				plan.scheduledStart
			);

			// If there are plans ending AND plans starting, it's a downgrade
			return plansWithScheduledEnd.length > 0 && plansWithScheduledStart.length > 0;
		},
		// Generate message for pending cancellations or downgrades
		pendingCancellationMessage() {
			if (this.hasPendingDowngrades) {
				const currentPlan = this.planDetails.find(plan =>
					plan.status === 'ACTIVE' && plan.scheduledEnd
				);
				const newPlan = this.planDetails.find(plan =>
					plan.scheduledStart
				);

				if (currentPlan && newPlan) {
					const date = this.formatScheduledEndDate(currentPlan.scheduledEnd);
					const currentDisplayName = this.getDisplayPlanName(currentPlan.name);
					const newDisplayName = this.getDisplayPlanName(newPlan.name);
					return `Your plan will be downgraded from ${currentDisplayName} to ${newDisplayName} on ${date}. You'll continue to have access to all ${currentDisplayName} features until then.`;
				}
			}

			if (this.hasPendingCancellationsOnly) {
				const pendingPlans = this.planDetails.filter(plan =>
					plan.status === 'ACTIVE' && plan.scheduledEnd
				);

				if (pendingPlans.length === 1) {
					const plan = pendingPlans[0];
					const date = this.formatScheduledEndDate(plan.scheduledEnd);
					const displayName = this.getDisplayPlanName(plan.name);
					return `Your ${displayName} plan will be cancelled on ${date}. You'll continue to have access to all features until then.`;
				} else {
					const dates = pendingPlans.map(plan => this.formatScheduledEndDate(plan.scheduledEnd));
					const uniqueDates = [...new Set(dates)];
					if (uniqueDates.length === 1) {
						return `Your plans will be cancelled on ${uniqueDates[0]}. You'll continue to have access to all features until then.`;
					} else {
						return `You have multiple plans scheduled for cancellation. You'll continue to have access to all features until their respective end dates.`;
					}
				}
			}

			return '';
		},
		// Generate message for payment failures
		paymentFailureMessage() {
			const failedPlans = this.planDetails.filter(plan => plan.status === 'PAST_DUE');

			if (failedPlans.length === 1) {
				const plan = failedPlans[0];
				const displayName = this.getDisplayPlanName(plan.name);
				return `Your ${displayName} plan payment has failed. Please update your payment method to continue accessing all features.`;
			} else if (failedPlans.length > 1) {
				return `Multiple plan payments have failed. Please update your payment method to continue accessing all features.`;
			}

			return '';
		},
		// Generate message for processing payments
		processingPaymentMessage() {
			const processingPlans = this.planDetails.filter(plan => plan.status === 'PENDING');

			if (processingPlans.length === 1) {
				const plan = processingPlans[0];
				const displayName = this.getDisplayPlanName(plan.name);
				return `Your ${displayName} plan payment is being processed. If this takes longer than expected, you can retry the payment.`;
			} else if (processingPlans.length > 1) {
				return `Multiple plan payments are being processed. If this takes longer than expected, you can retry the payments.`;
			}

			return '';
		},

	},
        watch: {
        selectedPlan(newValue, oldValue) {
                        if (!newValue) {
                                return;
                        }
                        this.selectedPlanId = newValue.id;
                        customerIOTrackEvent('Pricing Plan Selection: ' + newValue.name);
        },
        async selectedRevenue(newValue) {
                        // Save to both revenue settings for compatibility
                        await OrganizationSettings.updateOrganizationSetting('revenue', String(newValue));
                        await OrganizationSettings.updateOrganizationSetting('annualrevenue', String(newValue));
        },
        },
	methods: {
		// Helper methods to get plan data
		getStrategistPlan() {
			return this.planDetails.find(plan => plan.name === 'Strategist');
		},
		getStrategistMaxPlan() {
			return this.planDetails.find(plan => plan.name === 'Strategist Max');
		},

		// Helper method to convert database plan names to display names
		getDisplayPlanName(planName) {
			const displayNameMap = {
				'Strategist Lite': 'Starter',
				'Strategist': 'Advanced',
				'Strategist Max': 'Pro'
			};
			return displayNameMap[planName] || planName;
		},

		// Helper method to get revenue options for a plan
		getRevenueOptionsForPlan(planName) {
			const plan = this.planDetails.find(p => p.name === planName);
			if (!plan || !plan.hasRevenueBasedPricing || !plan.planRevenuePricings) {
				return [];
			}

			// Convert plan revenue pricings to dropdown options
			return plan.planRevenuePricings.map(pricing => {
				const min = pricing.revenueUsdMin;
				const max = pricing.revenueUsdMax;

				// Format the label based on min/max values
				let label;
				if (max === null || max === undefined) {
					if (min >= 15000000) {
						label = '$15M+';
					} else {
						label = `$${this.formatRevenue(min)}+`;
					}
				} else {
					label = `$${this.formatRevenue(min)} - $${this.formatRevenue(max)}`;
				}

				return {
					value: min, // Use the minimum value as the key
					label: label,
					price: pricing.price
				};
			}).sort((a, b) => a.value - b.value);
		},

		// Helper method to format revenue values
		formatRevenue(value) {
			if (value >= 1000000) {
				return `${value / 1000000}M`;
			} else if (value >= 1000) {
				return `${value / 1000}K`;
			}
			return value.toString();
		},

		// Method to update selected revenue
		updateSelectedRevenue(newRevenue) {
			this.selectedRevenue = parseInt(newRevenue);
		},

		// Helper methods to check if plans are pending cancellation or downgrade
		isPlanPendingCancellation(planName) {
			const plan = this.planDetails.find(p => p.name === planName);
			// Only consider it a cancellation if there's no corresponding plan starting
			return plan && plan.status === 'ACTIVE' && plan.scheduledEnd && !this.hasPendingDowngrades;
		},
		isPlanPendingDowngrade(planName) {
			const plan = this.planDetails.find(p => p.name === planName);
			// Current plan in a downgrade scenario
			return plan && plan.status === 'ACTIVE' && plan.scheduledEnd && this.hasPendingDowngrades;
		},
		isPlanPendingActivation(planName) {
			const plan = this.planDetails.find(p => p.name === planName);
			// Target plan in a downgrade scenario
			return plan && plan.scheduledStart;
		},
		getPendingCancellationDate(planName) {
			const plan = this.planDetails.find(p => p.name === planName);
			if (plan && plan.scheduledEnd) {
				return this.formatScheduledEndDate(plan.scheduledEnd);
			}
			return null;
		},
		getPendingActivationDate(planName) {
			const plan = this.planDetails.find(p => p.name === planName);
			if (plan && plan.scheduledStart) {
				return this.formatScheduledEndDate(plan.scheduledStart);
			}
			return null;
		},
		getDowngradeTargetPlan() {
			const targetPlan = this.planDetails.find(p => p.scheduledStart);
			return targetPlan ? this.getDisplayPlanName(targetPlan.name) : '';
		},
		formatScheduledEndDate(scheduledEnd) {
			try {
				return new Date(scheduledEnd).toLocaleDateString();
			} catch (error) {
				return 'the end of your billing period';
			}
		},
		// Banner action methods
		async reactivateCancelledPlan() {
			// Find the plan that's being cancelled
			const cancelledPlan = this.planDetails.find(plan =>
				plan.status === 'ACTIVE' && plan.scheduledEnd
			);

			if (cancelledPlan) {
				const displayName = this.getDisplayPlanName(cancelledPlan.name);
				// Show confirmation modal first, only proceed if user confirms
				this.showConfirmationModal(
					'Keep Your Plan?',
					`Are you sure you want to keep your ${displayName} plan? This will cancel the scheduled cancellation and continue your subscription.`,
					'keep',
					async () => {
						try {
							// Use the existing plan selection logic - it will handle reactivating cancelled plans
							await this.choosePlan(cancelledPlan);
							this.status = { type: 'success', message: 'Plan reactivated successfully! Your subscription will continue.' };
						} catch (error) {
							console.error('Error reactivating plan:', error);
							this.status = { type: 'error', message: 'Failed to reactivate plan. Please try again.' };
						}
					}
				);
			}
		},
		async cancelDowngrade() {
			// Find the current active plan that's being downgraded
			const currentPlan = this.planDetails.find(plan =>
				plan.status === 'ACTIVE' && plan.scheduledEnd
			);

			if (currentPlan) {
				const displayName = this.getDisplayPlanName(currentPlan.name);
				// Show confirmation modal first, only proceed if user confirms
				this.showConfirmationModal(
					'Keep Current Plan?',
					`Are you sure you want to keep your ${displayName} plan? This will cancel the scheduled downgrade.`,
					'keep',
					async () => {
						try {
							await this.choosePlan(currentPlan);
							this.status = { type: 'success', message: 'Downgrade cancelled. Keeping your current plan!' };
						} catch (error) {
							console.error('Error cancelling downgrade:', error);
							this.status = { type: 'error', message: 'Failed to cancel downgrade. Please try again.' };
						}
					}
				);
			}
		},

		async cancelAllPlans() {
			try {
				// Find the current active plan to get the right cancellation method
				const currentPlan = this.planDetails.find(plan =>
					plan.status === 'ACTIVE'
				);

				if (currentPlan) {
					// Call the appropriate cancellation method based on plan name
					switch (currentPlan.name) {
						case 'Strategist Lite':
							await this.unsubscribeStrategistLite();
							break;
						case 'Strategist':
							await this.unsubscribeStrategist();
							break;
						case 'Strategist Max':
							await this.unsubscribeStrategistMax();
							break;
						case 'Agency Platform':
							await this.unsubscribeAgencyPlatform();
							break;
						case 'Extra Agency Brand':
							await this.unsubscribeExtraAgencyBrand();
							break;
						default:
							throw new Error('Unknown plan type');
					}
					this.status = { type: 'success', message: 'All plans cancelled successfully.' };
				}
			} catch (error) {
				console.error('Error cancelling all plans:', error);
				this.status = { type: 'error', message: 'Failed to cancel plans. Please try again.' };
			}
		},
		async retryPaymentForFailedPlan() {
			// Find the first failed plan
			const failedPlan = this.planDetails.find(plan => plan.status === 'PAST_DUE');

			if (failedPlan) {
				const displayName = this.getDisplayPlanName(failedPlan.name);
				// Show confirmation modal first, only proceed if user confirms
				this.showConfirmationModal(
					'Retry Payment?',
					`Are you sure you want to retry payment for your ${displayName} plan? This will redirect you to update your payment method.`,
					'switch',
					async () => {
						try {
							// Use the existing plan selection logic to retry payment
							await this.choosePlan(failedPlan);
							this.status = { type: 'success', message: 'Redirecting to payment update...' };
						} catch (error) {
							console.error('Error retrying payment:', error);
							this.status = { type: 'error', message: 'Failed to retry payment. Please try again.' };
						}
					}
				);
			}
		},
		async retryPaymentForProcessingPlan() {
			// Find the first processing plan
			const processingPlan = this.planDetails.find(plan => plan.status === 'PENDING');

			if (processingPlan) {
				const displayName = this.getDisplayPlanName(processingPlan.name);
				// Show confirmation modal first, only proceed if user confirms
				this.showConfirmationModal(
					'Retry Payment?',
					`Are you sure you want to retry payment for your ${displayName} plan? This will redirect you to update your payment method.`,
					'switch',
					async () => {
						try {
							// Use the existing plan selection logic to retry payment
							await this.choosePlan(processingPlan);
							this.status = { type: 'success', message: 'Redirecting to payment update...' };
						} catch (error) {
							console.error('Error retrying payment:', error);
							this.status = { type: 'error', message: 'Failed to retry payment. Please try again.' };
						}
					}
				);
			}
		},
		async mounted() {
			customerIOTrackEvent('Pricing Viewed');
		},
		openChat() {
			Crisp.chat.open();
			customerIOTrackEvent('Pricing Plan Talk to us');
		},
		setStatus(data) {
			this.status.type = data.type;
			this.status.message = data.message;
		},
		// Confirmation modal methods
		// Confirmation modal method to pass pending action
		showConfirmationModal(title, message, type, action) {
			this.confirmationModalTitle = title;
			this.confirmationModalMessage = message;
			this.confirmationModalType = type;
			this.pendingAction = action;
			this.confirmationModalOpen = true;
		},
		async getAvailablePlans() {
			const response = await fetch(`${URL_DOMAIN}/plans`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				}
			});
			const jsonresponse = await response.json();
			this.planDetails = jsonresponse;
			this.selectedPlan = this.planDetails.find(plan => plan.status === "ACTIVE");

			if (!this.selectedPlan) {
				this.selectedPlan = this.planDetails.find(plan => plan.id === this.selectedPlanId);
			}

			const featureStates = JSON.parse(localStorage.getItem('featureStates' || '{}'));
			const planId = featureStates?.features?.[0]?.planId;
		},
		async choosePlan(selectedPlan) {
			//window.location.href = selectedIntegration.oAuthURL;
			selectedPlan.active = !selectedPlan.active;
			customerIOTrackEvent('free_trial_ended', {
				reason: 'plan-changed',
			});

			this.loadingModalOpen = true;

			const response = await fetch(`${URL_DOMAIN}/create-subscription`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					planId: selectedPlan.id,
                                        revenue: this.pricingRevenue + 1,
                                }),

			});
			const jsonresponse = await response.json();
			console.log("Response from exchange:", jsonresponse);
			if (jsonresponse.confirmationUrl) {
				if (selectedPlan.id !== 1) {
					customerIOTrackEvent('payment_plan_started', {
						planID: selectedPlan.id,
						planName: selectedPlan.name,
					});

					await new Promise(resolve => setTimeout(resolve, 3000));
				}
			}
			window.location.href = jsonresponse.confirmationUrl;
		},
		async chooseFreePlan() {
			console.log(this.planDetails);
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Free');

			// Skip confirmation modal if no active paid subscriptions (going to Stripe checkout)
			if (!this.hasActivePaidSubscription) {
				return this.choosePlan(selectedPlan);
			}

			// Show confirmation modal only if user has active paid subscription
			if (this.selectedPlan && this.selectedPlan.name !== 'Free') {
				this.showConfirmationModal(
					'Switch to Free Plan?',
					'Are you sure you want to switch to the Free plan? Any active subscription will be cancelled.',
					'switch',
					() => this.choosePlan(selectedPlan)
				);
			} else {
				return this.choosePlan(selectedPlan);
			}
		},
		async chooseGrowthPlan() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Growth');

			// Skip confirmation modal if no active paid subscriptions (going to Stripe checkout)
			if (!this.hasActivePaidSubscription) {
				return this.choosePlan(selectedPlan);
			}

			// Show confirmation modal only if user has active paid subscription
			if (this.selectedPlan && this.selectedPlan.name !== 'Growth') {
				this.showConfirmationModal(
					'Switch to Growth Plan?',
					'Are you sure you want to switch to the Growth plan? This will update your billing.',
					'switch',
					() => this.choosePlan(selectedPlan)
				);
			} else {
				return this.choosePlan(selectedPlan);
			}
		},
		async chooseProPlan() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Pro');

			// Skip confirmation modal if no active paid subscriptions (going to Stripe checkout)
			if (!this.hasActivePaidSubscription) {
				return this.choosePlan(selectedPlan);
			}

			// Show confirmation modal only if user has active paid subscription
			if (this.selectedPlan && this.selectedPlan.name !== 'Pro') {
				this.showConfirmationModal(
					'Switch to Pro Plan?',
					'Are you sure you want to switch to the Pro plan? This will update your billing.',
					'switch',
					() => this.choosePlan(selectedPlan)
				);
			} else {
				return this.choosePlan(selectedPlan);
			}
		},
		async chooseEnterprisePlan() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Enterprise');

			// Skip confirmation modal if no active paid subscriptions (going to Stripe checkout)
			if (!this.hasActivePaidSubscription) {
				return this.choosePlan(selectedPlan);
			}

			// Show confirmation modal only if user has active paid subscription
			if (this.selectedPlan && this.selectedPlan.name !== 'Enterprise') {
				this.showConfirmationModal(
					'Switch to Enterprise Plan?',
					'Are you sure you want to switch to the Enterprise plan? This will update your billing.',
					'switch',
					() => this.choosePlan(selectedPlan)
				);
			} else {
				return this.choosePlan(selectedPlan);
			}
		},
		async unsubscribeFreePlan() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Free');

			this.showConfirmationModal(
				'Cancel Free Plan?',
				'Are you sure you want to cancel your Free plan? This will disable all features.',
				'cancel',
				() => this.unsubscribe(selectedPlan)
			);
		},
		async unsubscribeGrowthPlan() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Growth');

			this.showConfirmationModal(
				'Cancel Growth Plan?',
				'Are you sure you want to cancel your Growth plan? You will lose access to all premium features.',
				'cancel',
				() => this.unsubscribe(selectedPlan)
			);
		},
		async unsubscribeProPlan() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Pro');

			this.showConfirmationModal(
				'Cancel Pro Plan?',
				'Are you sure you want to cancel your Pro plan? You will lose access to all premium features.',
				'cancel',
				() => this.unsubscribe(selectedPlan)
			);
		},
		async unsubscribeEnterprisePlan() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Enterprise');

			this.showConfirmationModal(
				'Cancel Enterprise Plan?',
				'Are you sure you want to cancel your Enterprise plan? You will lose access to all enterprise features.',
				'cancel',
				() => this.unsubscribe(selectedPlan)
			);
		},
		async unsubscribe(plan) {
			const response = await fetch(`${URL_DOMAIN}/unsubscribe`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					planId: plan.id
				})
			});
			const jsonresponse = await response.json();
			console.log("Response from unsubscribe:", jsonresponse);
			if (jsonresponse.status == 'CANCELLED') {
				await this.getAvailablePlans();
				this.setStatus({
					type: 'success',
					message: "Successfully Unsubscribed.",
				});

				if (!plan.subscriptionId) {
					customerIOTrackEvent('free_trial_ended', {
						reason: 'cancelled'
					});
				}
			}
			else if (jsonresponse.status == 'SCHEDULED_CANCELLATION') {
				await this.getAvailablePlans();
				let scheduledEndMessage = 'the end of your billing period';

				if (jsonresponse.scheduledEnd) {
					try {
						const scheduledEndDate = new Date(jsonresponse.scheduledEnd);
						if (!isNaN(scheduledEndDate.getTime())) {
							scheduledEndMessage = scheduledEndDate.toLocaleDateString();
						}
					} catch (error) {
						console.warn('Error parsing scheduled end date:', error);
					}
				}

				this.setStatus({
					type: 'success',
					message: `Your subscription has been scheduled for cancellation at ${scheduledEndMessage}. You will continue to have access to all features until then.`,
				});

				if (!plan.subscriptionId) {
					customerIOTrackEvent('free_trial_ended', {
						reason: 'scheduled_cancellation'
					});
				}
			}
			else {
				this.setStatus({
					type: 'fail',
					message: "Failed to Unsubscribe. Please try again, or reach out to Raleon support for assistance.",
				});
			}
		},
		confirmAction() {
			this.confirmationModalOpen = false;

			if (this.pendingAction) {
				this.pendingAction();
			}
		},
		async chooseStrategistLite() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Strategist Lite');

			// Skip confirmation modal if no active paid subscriptions (going to Stripe checkout)
			if (!this.hasActivePaidSubscription) {
				return this.choosePlan(selectedPlan);
			}

			this.showConfirmationModal(
				'Switch to Starter Plan?',
				'Are you sure you want to switch to the Starter plan? This will update your billing.',
				'switch',
				() => this.choosePlan(selectedPlan)
			);
		},
		async chooseStrategist() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Strategist');

			// Skip confirmation modal if no active paid subscriptions (going to Stripe checkout)
			if (!this.hasActivePaidSubscription) {
				return this.choosePlan(selectedPlan);
			}

			this.showConfirmationModal(
				'Switch to Advanced Plan?',
				'Are you sure you want to switch to the Advanced plan? This will update your billing.',
				'switch',
				() => this.choosePlan(selectedPlan)
			);
		},
		async chooseStrategistMax() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Strategist Max');

			// Skip confirmation modal if no active paid subscriptions (going to Stripe checkout)
			if (!this.hasActivePaidSubscription) {
				return this.choosePlan(selectedPlan);
			}

			this.showConfirmationModal(
				'Switch to Max Plan?',
				'Are you sure you want to switch to the Max plan? This will update your billing.',
				'switch',
				() => this.choosePlan(selectedPlan)
			);
		},

		async chooseAgencyPlatform() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Agency Platform');

			// Skip confirmation modal if no active paid subscriptions (going to Stripe checkout)
			if (!this.hasActivePaidSubscription) {
				return this.choosePlan(selectedPlan);
			}

			this.showConfirmationModal(
				'Switch to Agency Platform?',
				'Are you sure you want to switch to the Agency Platform? This will update your billing.',
				'switch',
				() => this.choosePlan(selectedPlan)
			);
		},
		async chooseExtraAgencyBrand() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Extra Agency Brand');

			// Skip confirmation modal if no active paid subscriptions (going to Stripe checkout)
			if (!this.hasActivePaidSubscription) {
				return this.choosePlan(selectedPlan);
			}

			this.showConfirmationModal(
				'Add Extra Agency Brand?',
				'Are you sure you want to add an additional brand seat? This will update your billing.',
				'switch',
				() => this.choosePlan(selectedPlan)
			);
		},


		async unsubscribeAgencyPlatform() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Agency Platform');
			this.showConfirmationModal(
				'Cancel Agency Platform Plan?',
				'Are you sure you want to cancel your Agency Platform subscription? You will lose access to all agency features.',
				'cancel',
				() => this.unsubscribe(selectedPlan)
			);
		},

		async unsubscribeExtraAgencyBrand() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Extra Agency Brand');
			this.showConfirmationModal(
				'Cancel Extra Agency Brand?',
				'Are you sure you want to remove this additional brand seat? This will update your billing.',
				'cancel',
				() => this.unsubscribe(selectedPlan)
			);
		},
		async unsubscribeStrategistLite() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Strategist Lite');
			this.showConfirmationModal(
				'Cancel Starter Plan?',
				'Are you sure you want to cancel your Starter plan? You will lose access to all basic AI features.',
				'cancel',
				() => this.unsubscribe(selectedPlan)
			);
		},
		async unsubscribeStrategist() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Strategist');
			this.showConfirmationModal(
				'Cancel Advanced Plan?',
				'Are you sure you want to cancel your Advanced plan? You will lose access to all premium AI features.',
				'cancel',
				() => this.unsubscribe(selectedPlan)
			);
		},
		async unsubscribeStrategistMax() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Strategist Max');
			this.showConfirmationModal(
				'Cancel Max Plan?',
				'Are you sure you want to cancel your Max plan? You will lose access to all unlimited AI features.',
				'cancel',
				() => this.unsubscribe(selectedPlan)
			);
		},
		async unsubscribeGrowthLegacy() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Growth');
			this.showConfirmationModal(
				'Cancel Growth Plan?',
				'Are you sure you want to cancel your Growth plan? You will lose access to all premium features.',
				'cancel',
				() => this.unsubscribe(selectedPlan)
			);
		},
		async unsubscribeProLegacy() {
			const selectedPlan = this.planDetails.find(plan => plan.name === 'Pro');
			this.showConfirmationModal(
				'Cancel Pro Plan?',
				'Are you sure you want to cancel your Pro plan? You will lose access to all premium features.',
				'cancel',
				() => this.unsubscribe(selectedPlan)
			);
		},
		async fetchAgencyPricingInfo() {
			if (!this.shouldShowAgencyPlatform || !this.isAgencyPlatformActive) {
				return;
			}

			try {
				const response = await fetch(`${URL_DOMAIN}/onboard/agency/pricing-preview`, {
					method: 'GET',
					headers: {
						Authorization: `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json'
					}
				});

				if (response.ok) {
					const pricingData = await response.json();
					// Calculate current extra brands for display
					const currentExtraBrands = Math.max(0, pricingData.currentBrandCount - pricingData.brandsIncludedLimit);

					this.agencyPricingInfo = {
						...pricingData,
						currentExtraBrands
					};
				} else {
					console.error('Failed to fetch agency pricing information');
				}
			} catch (error) {
				console.error('Error fetching agency pricing information:', error);
			}
		},

		async fetchUserOrgs() {
			try {
				const response = await fetch(`${URL_DOMAIN}/user/orgs`, {
					method: 'GET',
					headers: {
						Authorization: `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json'
					}
				});

				if (response.ok) {
					this.userOrgs = await response.json();
				} else {
					console.error('Failed to fetch user organizations');
				}
			} catch (error) {
				console.error('Error fetching user organizations:', error);
			}
		},

		async navigateToAgencyBilling() {
			if (!this.hasAccessToParentAgency || !this.currentOrg?.parentOrgId) {
				return;
			}

			try {
				const token = localStorage.getItem('token');

				// Switch to parent agency organization
				const loginRequest = await fetch(`${URL_DOMAIN}/users/login/${this.currentOrg.parentOrgId}`, {
					method: 'POST',
					credentials: 'omit',
					headers: {
						Authorization: `Bearer ${token}`,
						'Content-Type': 'application/json',
					}
				});

				if (!loginRequest.ok) {
					console.error(`Failed to switch to agency organization: ${loginRequest.status} ${loginRequest.statusText}`);
					return;
				}

				const loginResult = await loginRequest.json();
				if (loginResult.token) {
					localStorage.setItem('token', loginResult.token);
					localStorage.setItem('userOrgId', this.currentOrg.parentOrgId.toString());

					// Navigate to agency billing page
					window.location.assign('/loyalty/settings/plans');
				} else {
					console.error('No token received after switching to agency organization.');
				}
			} catch (error) {
				console.error('Error switching to agency organization:', error);
			}
		},
	},
}
</script>

<style>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}

/* Add this to hide the scrollbar but keep functionality */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}
</style>
