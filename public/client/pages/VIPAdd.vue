<template>
	<SuperModal>
		<SuperModalHeader :go-home-on-logo-click="true" :collapse-logo="true" :showHeaderBackground="true"
			:show-close-button="true" :back-button-href="backButtonHref">
			Add VIP Tier
			<div class="flex-grow"></div>

			<div class="flex flex-row lg:items-start lg:justify-end items-center justify-between w-full md:w-1/2 lg:w-1/3">
				<div class="flex flex-row items-center pr-2 sm:pr-5 justify-center flex-grow h-20">
					<PreviewLoyaltyProgram />
				</div>
				<div class="flex-shrink-0">
					<PrimaryButton cta="Save" @click="enable" size="small" class="mr-2 sm:mr-7"></PrimaryButton>
				</div>
			</div>
		</SuperModalHeader>
	<div class="overflow-auto">
		<div class="w-full flex flex-col items-center justify-center">
			<div class="w-1/2 bg-white bg-opacity-75 rounded-2xl border border-violet-200 p-2 sm:p-7 mt-10">
				<div class="text-zinc-900 text-xl font-normal font-['Open Sans'] leading-loose flex-grow truncate">Tier Information</div>
				<div class="mt-3 text-black text-sm font-normal font-['Inter']">
					Adjust which side of your store the launcher appears on for computers, tablets, and mobile.
				</div>

				<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal mb-2">
					Tier Level
				</div>
				<SuperDropdown class="mt-3" :options="[
						{value: 'level1', label: 'Level 1'},
						{value: 'level2', label: 'Level 2'},
					]" option-value-key="value" option-label-key="label" v-model="tierLevel" />

				<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal mb-2">
					Tier Name
				</div>

				<input type="text"
					v-model="tierName"
					class="w-96 bg-white rounded-lg border border-gray-400 no-focus-outline text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
					maxlength="40" />

				<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal mb-2">
					Benefits Description
				</div>
				<input type="text"
					v-model="tierDescription"
					class="w-96 bg-white rounded-lg border border-gray-400 no-focus-outline text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
					maxlength="40" />
				<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
					Points to Enter Tier
				</div>
				<div class="mt-3 text-black text-sm font-normal font-['Inter']">
					Points earned since January 1, 1986
				</div>
				<input type="text"
					:disabled="shouldDisableMinPoints"
					v-model="minPoints"
					class="w-96 bg-white rounded-lg border no-focus-outline text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none placeholder:text-gray-400 placeholder:font-normal placeholder:italic"
					:class="{'border-gray-400 text-gray-600': !shouldDisableMinPoints, 'border-gray-300 text-gray-300': shouldDisableMinPoints}"
					maxlength="40" placeholder="Number of points" />

				<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
					Max Points for Tier
				</div>
				<div class="mt-3 text-black text-sm font-normal font-['Inter']">
					Points earned since January 1, 1986
				</div>
				<input type="text"
					v-model="maxPoints"
					class="w-96 bg-white rounded-lg border border-gray-400 no-focus-outline text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none placeholder:text-gray-400 placeholder:font-normal placeholder:italic"
					maxlength="40" placeholder="Number of points" />

				<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
					Tier Image
				</div>

			</div>
			<div class="w-1/2 bg-white bg-opacity-75 rounded-2xl border border-violet-200 p-2 sm:p-7 mt-4">
				<div class="text-zinc-900 text-xl font-normal font-['Open Sans'] leading-loose flex-grow truncate">Tier Retention</div>
				<div class="mt-3 text-black text-sm font-normal font-['Inter']">
					The amount of of points or spend required to retain a certain tier level during the term of the VIP program.
				</div>

				<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal mb-2">
					Retention Amount
				</div>

				<input type="text"
					class="w-96 bg-white rounded-lg border border-gray-400 no-focus-outline text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
					maxlength="40" />

				<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal mb-2">
					Retention Re-Entry Amount
				</div>
				<div class="mt-3 text-black text-sm font-normal font-['Inter']">
					The amount of points or spend to re-enter a tier a customer has acheived in the past. By default, this is the same as the tier entry conditions.
				</div>
				<input type="text"
					class="w-96 bg-white rounded-lg border border-gray-400 no-focus-outline text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
					maxlength="40" />
				<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
					Conditions to Achieve Tier
				</div>
				<div class="mt-3 text-black text-sm font-normal font-['Inter']">
					Points earned since January 1, 1986
				</div>
				<input type="text"
					class="w-96 bg-white rounded-lg border border-gray-400 no-focus-outline text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none placeholder:text-gray-400 placeholder:font-normal placeholder:italic"
					maxlength="40" placeholder="Number of points" />
			</div>
			<div class="mt-4 justify-right items-end mb-4">
				<PrimaryButton cta="Save" @click="enable" size="small" class="mr-2 sm:mr-7"></PrimaryButton>
			</div>
		</div>
	</div>

	</SuperModal>
</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import SuperModal from '../components/SuperModal.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import SuperModalHeader from '../components/SuperModalHeader.ts.vue';
import TabSwitcher from '../components/TabSwitcher.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue'
import CampaignWTEActivity from './subpages/CampaignWTEActivity.ts.vue';
import CampaignWTERewards from './subpages/CampaignWTERewards.ts.vue';
import CampaignWTELivePreview from './subpages/CampaignWTELivePreview.ts.vue';
import CampaignShopItemAdd from './subpages/CampaignShopItemAdd.ts.vue';
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import Tooltip from '../components/Tooltip.ts.vue';
import PreviewLoyaltyProgram from '../components/PreviewLoyalty.ts.vue';
import { customerIOTrackEvent } from '../services/customerio.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: ['vipID'],
	components: {
		SuperModal,
		SuperModalHeader,
		TabSwitcher,
		StatusMessage,
		ToggleItem,
		PrimaryButton,
		Tooltip,
		PreviewLoyaltyProgram
	},
	computed: {

	},
	async mounted() {
		customerIOTrackEvent('VIP Early');
	},
	data() {
		return {
			tierLevel: '1',
			tierName: '',
			tierDescription: '',
			minPoints: localStorage.getItem('raleon_vip_last_tier_max') || 0,
			maxPoints: 0,
		}
	},
	computed: {
		shouldDisableMinPoints() {
			return localStorage.getItem('raleon_vip_last_tier_max');
		}
	},
	methods: {
		async enable() {
			const response = await fetch(`${URL_DOMAIN}/tier`, {
				method: 'POST',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					tierName: this.tierName,
					tierDescription: this.tierDescription,
					minPoints: Number(this.minPoints || 0),
				}),

			});


			if (response.ok) {
				localStorage.setItem('raleon_vip_last_tier_max', this.maxPoints);
			}
		}
	}
}
</script>
<style scoped>
.super-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 10000;

	background: linear-gradient(144deg, #FAF8F5 20.89%, #E2E8F8 53.59%, #BCCAFD 88.28%);
}
</style>

