<template>
	<div class="p-2 sm:p-7">
		<div class="flex justify-between">
			<div>
				<h1 class="text-3xl sm:text-6xl font-sans font-medium opacity-70 text-ralblack-primary">Grow With Collaborations</h1>
				<div class="ml-1 mt-3 flex mb-10">
					<span class="text-neutral-800 text-opacity-80 text-sm font-medium font-['Inter'] mr-2">
					Increase your revenue through Raleon's loyalty network by collaborating with other amazing brands.
					</span>
					<LearnMoreText text="Learn more" url="https://docs.raleon.io/docs/the-loyalty-panel"></LearnMoreText>
				<span class="text-neutral-500 text-sm font-medium font-['Inter']">&nbsp;</span>
				</div>
			</div>
			<div class="items-center flex">

			</div>
		</div>

				<!-- Metrics Cards -->
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
				<div class="bg-white p-4 rounded-lg shadow-md">
					<p class="text-gray-600 flex items-center mb-2">
					Growth Conversions
					<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 text-gray-400" viewBox="0 0 24 24" fill="currentColor">
						<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM11 17h2v-2h-2v2zm0-4h2V7h-2v6z"/>
					</svg>
					</p>
					<p class="text-3xl font-bold">800</p>
					<div class="flex justify-between mt-14">
						<p class="text-gray-600">Conversion Rate</p>
						<p class="text-gray-600">92%</p>
					</div>
				</div>
				<div class="bg-white p-4 rounded-lg shadow-md">
					<p class="text-gray-600 flex items-center mb-2">
					Offers Claimed
					<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 text-gray-400" viewBox="0 0 24 24" fill="currentColor">
						<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM11 17h2v-2h-2v2zm0-4h2V7h-2v6z"/>
					</svg>
					</p>
					<p class="text-3xl font-bold">865</p>
					<div class="flex justify-between mt-14">
						<p class="text-gray-600">Offer Reach</p>
						<p class="text-gray-600">3,300</p>
					</div>
				</div>
				<div class="bg-white p-4 rounded-lg shadow-md">
					<p class="text-gray-600 flex items-center mb-2">
					Total Spend
					<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 text-gray-400" viewBox="0 0 24 24" fill="currentColor">
						<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM11 17h2v-2h-2v2zm0-4h2V7h-2v6z"/>
					</svg>
					</p>
					<p class="text-3xl font-bold">$11,200</p>
				</div>
				<div class="bg-white p-4 rounded-lg shadow-md">
					<p class="text-gray-600 flex justify-between mb-2">
					Wallet Balance
					<LightSecondaryButton cta="Add Funds"></LightSecondaryButton>
					</p>
					<p class="text-3xl font-bold">$540</p>
					<div class="flex justify-between mt-11">
						<p class="text-gray-600">Pending Funds</p>
						<p class="text-gray-600">$0.00</p>
					</div>
				</div>
				</div>
				<button class="text-gray-600 border border-gray-300 px-3 py-1 rounded-lg">Explain metrics</button>

		<div class="border-b border-gray-300 mt-6 mb-6">
			<div class="flex space-x-8">

				<div class="whitespace-nowrap py-4 px-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
				@click.stop="this.overview = true; this.insights = false; this.history = false;"
				:class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': this.overview == true}"
				>
					Overview
				</div>

				<div class="whitespace-nowrap py-4 px-1 border-b-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
				@click.stop="this.overview = false; this.insights = true; this.history = false;"
				:class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': this.insights == true}"
				>
					Insights
				</div>

				<div class="whitespace-nowrap py-4 px-1 border-b-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
				@click.stop="this.overview = false; this.insights = false; this.history = true;"
				:class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': this.history == true}"
				>
					Collab History
				</div>
			</div>
		</div>

		<div class="" v-if="this.partners">

			<div
			class="bg-white rounded-2xl border shadow border-ralprimary-light border-opacity-50 hover:border-opacity-90 hover:shadow-lg mt-10 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
				<div class="p-4">
					<div class="text-slate-800 text-xl font-semibold font-['Open Sans'] leading-normal tracking-wide">
						Offer Details
					</div>
					<div class="mt-3 text-slate-800 text-xs font-normal font-['Open Sans'] leading-none">
						Provide information about the offer you want to invite partners to participate in.
					</div>

					<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Cost per Acqusition</div>
					<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">What you're willing to pay when a collabarators customer makes a purchase with your brand.</div>
					<div
						class="mb-2 w-40 h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
						<div class="w-full self-stretch justify-start items-center gap-2 flex">
							$
							<input type="text"
								class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
								maxlength="40" />
						</div>
					</div>

					<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Target Merchants</div>
					<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">What merchants do you want to send your collaboration request to?</div>
					<div class="flex flex-col mt-4">
						<div class="border border-blue-500 bg-blue-50 rounded-lg p-4 cursor-pointer hover:bg-gray-50">
							<div class="flex items-center">
								<input type="radio" id="advantage-plus" name="campaign" class="mr-4" checked>
								<label for="advantage-plus" class="flex items-center cursor-pointer">
								<img src="../images/magic.svg" width="32" height="32" alt="shopping bag icon" class="mr-4">
								<div>
									<h3 class="text-md font-semibold">Smart Merchant Matching</h3>
									<p class="text-gray-500 text-sm">Let Raleon's matching algorithm find merchants that are complimentary and non-competitive with yours.</p>
								</div>
								</label>
							</div>
						</div>
						<div class="border-b border-gray-300 my-4"></div>
						<div class="border  rounded-lg p-4 cursor-pointer">
							<div class="flex items-center">
								<input type="radio" id="manual-setup" name="campaign" class="mr-4">
								<label for="manual-setup" class="flex items-center cursor-pointer">
								<svg xmlns="http://www.w3.org/2000/svg" height="42px" viewBox="0 -960 960 960" width="42px" fill="#5f6368" class="mr-4"><path d="M433-80q-27 0-46.5-18T363-142l-9-66q-13-5-24.5-12T307-235l-62 26q-25 11-50 2t-39-32l-47-82q-14-23-8-49t27-43l53-40q-1-7-1-13.5v-27q0-6.5 1-13.5l-53-40q-21-17-27-43t8-49l47-82q14-23 39-32t50 2l62 26q11-8 23-15t24-12l9-66q4-26 23.5-44t46.5-18h94q27 0 46.5 18t23.5 44l9 66q13 5 24.5 12t22.5 15l62-26q25-11 50-2t39 32l47 82q14 23 8 49t-27 43l-53 40q1 7 1 13.5v27q0 6.5-2 13.5l53 40q21 17 27 43t-8 49l-48 82q-14 23-39 32t-50-2l-60-26q-11 8-23 15t-24 12l-9 66q-4 26-23.5 44T527-80h-94Zm7-80h79l14-106q31-8 57.5-23.5T639-327l99 41 39-68-86-65q5-14 7-29.5t2-31.5q0-16-2-31.5t-7-29.5l86-65-39-68-99 42q-22-23-48.5-38.5T533-694l-13-106h-79l-14 106q-31 8-57.5 23.5T321-633l-99-41-39 68 86 64q-5 15-7 30t-2 32q0 16 2 31t7 30l-86 65 39 68 99-42q22 23 48.5 38.5T427-266l13 106Zm42-180q58 0 99-41t41-99q0-58-41-99t-99-41q-59 0-99.5 41T342-480q0 58 40.5 99t99.5 41Zm-2-140Z"/></svg>
								<div>
									<h3 class="text-md font-semibold">Manual offer targeting</h3>
									<p class="text-gray-500 text-sm">Manually find and request other merchants to collaborate with your offer.</p>
								</div>
								</label>
							</div>
						</div>
					</div>
				<!-- //offer details -->

				<!-- design -->
				<div class="text-slate-800 text-xl font-semibold font-['Open Sans'] leading-normal tracking-wide mt-6">
						Offer Design
					</div>
					<div class="mt-3 text-slate-800 text-xs font-normal font-['Open Sans'] leading-none">
						Provide information about the offer you want to invite partners to participate in.
					</div>

					<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Promotion</div>
						<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">What is the deal the collaborating shopper will get?</div>

						<div class="flex">
							<div class="self-stretch justify-start items-center gap-2 flex">
								<div class="w-full self-stretch justify-start items-center gap-2 flex mt-2">
								<select
									class="w-52 h-12 px-2 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex justify-center">
									<option>Dollar Off</option>
									<option>Percent Off</option>
									<option>Free Shipping</option>
									<option>Free Product</option>
								</select>
								</div>
							</div>
							<div
							class="mt-2 ml-4 w-40 h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
								<div class="w-full self-stretch justify-start items-center gap-2 flex">
									$
									<input type="text"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40" />
								</div>
							</div>
						</div>

						<!--
						<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Points for Offer</div>
						<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">How many points it will cost the collab shopper. This is also the number of points you are "buying".</div>
							<div
							class="mb-2 w-40 h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input type="text"
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									maxlength="40" v-model="pointsCost" />pts
							</div>
						</div>

						<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Cost for Points</div>
						<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">What it will cost you to buy the points for a collab shopper to "claim" your offer.</div>
						<div class="font-medium">${{costCalc}}</div>

						-->
						<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Offer Call to Action</div>
						<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">What a shopper will see listed.</div>
						<div
							class="mb-2 w-1/2 h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input type="text"
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									maxlength="40" />
							</div>
						</div>


						<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Offer Description</div>
						<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">The description the shopper will see.</div>
						<div
							class="mb-2 w-1/2 h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input type="text"
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									maxlength="40" />
							</div>
						</div>

					<div class="flex flex-wrap justify-end space-x-2 mt-4">
					<CancelButton @click="overview = true; partners = false;" cta="Cancel"></CancelButton>

					<PrimaryButton cta="Save" size="xs" @click="smartModalOpen = false"></PrimaryButton>
					</div>
				</div>
			</div>
		</div>

		<div class="" v-if="this.overview">
			<div class="bg-white p-6 rounded-lg shadow-md">
			<h2 class="text-lg font-bold mb-2">Find Potential Collab Partners</h2>
			<p class="text-gray-600 mb-4">Find brands that could be a great partner, and send them a collaboration offer to consider!</p>
			<PrimaryButton size="xs" cta="Invite Brands to Collaborate" @click="this.partners=true; this.overview=false;"></PrimaryButton>
			</div>

				<!-- Content -->
				<div class="mt-6">
					<h2 class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary mb-4">Live Collaborations (2)</h2>

					<div class="bg-white rounded-2xl border shadow border-ralprimary-light border-opacity-50 hover:border-opacity-90 hover:shadow-lg mt-4 flex-grow transition-all duration-300 ease-in-out overflow-hidden">

						<div class="container rounded-2xl">
							<table class="min-w-full bg-white border border-gray-200">
							<thead>
								<tr class="w-full bg-gray-100 border-b border-gray-200">
								<th class="px-6 py-3 text-left text-sm font-medium text-gray-700">Partner</th>
								<th class="px-6 py-3 text-left text-sm font-medium text-gray-700">Reach</th>
								<th class="px-6 py-3 text-left text-sm font-medium text-gray-700">Claims</th>
								<th class="px-6 py-3 text-left text-sm font-medium text-gray-700">Conversions</th>
								<th class="px-6 py-3 text-left text-sm font-medium text-gray-700">Trend</th>
								<th class="px-6 py-3 text-left text-sm font-medium text-gray-700">Cost</th>
								<th></th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="variant in variants" :key="variant.id" class="bg-white border-t border-b border-ralbackground-light-line hover:bg-indigo-50 hover:cursor-pointer h-20"
								@mouseenter="onMouseEnter(variant.id)" @mouseleave="hoveredRowIndex = null"
								>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ variant.name }}</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ formatNumber(variant.reach) }}</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ formatNumber(variant.claims) }}</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ formatNumber(variant.conversions) }}</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
									<img src="../images/graph1.png" class="rounded-xl" v-if="variant.id == 1">
									<img src="../images/graph2.png" class="rounded-xl" v-if="variant.id == 2">
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${{ variant.cost }}</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 w-60 text-right font-medium flex justify-end items-center">
									<div class="mt-2">
										<LightSecondaryButton cta="Edit" v-show="hoveredRowIndex === variant.id"
											@click="onEditClick(row, index)"
											/>
										<!-- <svg class="ml-4" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M240-400q-33 0-56.5-23.5T160-480q0-33 23.5-56.5T240-560q33 0 56.5 23.5T320-480q0 33-23.5 56.5T240-400Zm240 0q-33 0-56.5-23.5T400-480q0-33 23.5-56.5T480-560q33 0 56.5 23.5T560-480q0 33-23.5 56.5T480-400Zm240 0q-33 0-56.5-23.5T640-480q0-33 23.5-56.5T720-560q33 0 56.5 23.5T800-480q0 33-23.5 56.5T720-400Z"/></svg> -->
									</div>
								</td>
								</tr>
							</tbody>
							<tfoot>
								<tr class="bg-gray-100 border-t border-gray-200">
								<td colspan="1" class="px-6 py-4 text-right text-sm font-medium text-gray-700">Total</td>
								<td class="px-6 py-4 text-sm font-medium text-gray-700">{{ formatNumber(totalReach) }}</td>
								<td class="px-6 py-4 text-sm font-medium text-gray-700">{{ formatNumber(totalClaims) }}</td>
								<td class="px-6 py-4 text-sm font-medium text-gray-700">{{ formatNumber(totalConversions) }}</td>
								<td></td>
								<td class="px-6 py-4 text-sm font-medium text-gray-700">${{ formatNumber(totalCost) }}</td>
								<td></td>
								</tr>
							</tfoot>
							</table>
						</div>

						</div>
				</div>
		</div>

		<div class="" v-if="this.insights">
			<div class="bg-white max-w-sm rounded-2xl border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-2 px-4 py-4 hover:shadow-lg transition-all duratio-300  flex flex-col justify-between">
				<div>
				<div class="flex justify-between items-start mb-4">
					<img src="https://via.placeholder.com/50" alt="simple.ai" class="w-10 h-10">
					<button class="text-gray-400 hover:text-gray-600">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
					</svg>
					</button>
				</div>
				<h3 class="text-xl font-bold">Mosi Tea</h3>
				<p class="text-gray-500">The best on-the-go tea infuser and premium tea on the planet.</p>

				<div class="mt-4">
					<p class="font-semibold">Summary</p>
					<p class="text-gray-600">Started on <span class="text-sm">5/01/2025</span></p>
					<p class="text-gray-600"><span class="font-bold">125</span> claims to date</p>
					<p class="text-gray-600"><span class="font-bold">$1,852</span> in revenue earned</p>
				</div>
				<div class="mt-4">
					<p class="font-semibold">Collab Offer Summary</p>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="M560-440q-50 0-85-35t-35-85q0-50 35-85t85-35q50 0 85 35t35 85q0 50-35 85t-85 35ZM280-320q-33 0-56.5-23.5T200-400v-320q0-33 23.5-56.5T280-800h560q33 0 56.5 23.5T920-720v320q0 33-23.5 56.5T840-320H280Zm80-80h400q0-33 23.5-56.5T840-480v-160q-33 0-56.5-23.5T760-720H360q0 33-23.5 56.5T280-640v160q33 0 56.5 23.5T360-400Zm440 240H120q-33 0-56.5-23.5T40-240v-440h80v440h680v80ZM280-400v-320 320Z"/></svg>
						<p class="text-gray-600">
						CPA <span class="font-bold text-green-600">$19</span> per customer</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="m520-260 140-140q11-11 17.5-26t6.5-32q0-34-24-58t-58-24q-19 0-37.5 11T520-492q-30-28-47-38t-35-10q-34 0-58 24t-24 58q0 17 6.5 32t17.5 26l140 140Zm336-130L570-104q-12 12-27 18t-30 6q-15 0-30-6t-27-18L103-457q-11-11-17-25.5T80-513v-287q0-33 23.5-56.5T160-880h287q16 0 31 6.5t26 17.5l352 353q12 12 17.5 27t5.5 30q0 15-5.5 29.5T856-390ZM513-160l286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640Zm220 160Z"/></svg>
						<p class="text-gray-600">
						Earn <span class="font-bold text-green-600">$3</span> for 400 points</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m354-287 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143Zm126 18L314-169q-11 7-23 6t-21-8q-9-7-14-17.5t-2-23.5l44-189-147-127q-10-9-12.5-20.5T140-571q4-11 12-18t22-9l194-17 75-178q5-12 15.5-18t21.5-6q11 0 21.5 6t15.5 18l75 178 194 17q14 2 22 9t12 18q4 11 1.5 22.5T809-528L662-401l44 189q3 13-2 23.5T690-171q-9 7-21 8t-23-6L480-269Zm0-201Z"/></svg>
						<p class="text-gray-600">
						$10 off first order with Mosi Tea.</p>
					</div>
				</div>
				</div>
				<div class="flex justify-end mt-4">
				<LightSecondaryButton cta="Cancel"></LightSecondaryButton>
				</div>
			</div>
		</div>
		<div class="" v-if="this.history">
			<div class="flex items-center space-x-4 mb-4">
			<input type="text" placeholder="Search offers" class="w-1/3 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none">
			<select class="w-1/3 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none">
				<option>Select Categories</option>
			</select>
			<select class="w-1/3 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none">
				<option>Select Countries</option>
			</select>
			<button class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none w-40">Clear filters</button>
			</div>
			<div class="flex justify-end mb-4">

			</div>
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
			<!-- Card 1 -->
			<div class=" bg-white rounded-2xl border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-2 px-4 py-4 hover:shadow-lg transition-all duratio-300  flex flex-col justify-between">
				<div>
				<div class="flex justify-between items-start mb-4">
					<img src="https://via.placeholder.com/50" alt="simple.ai" class="w-10 h-10">
					<button class="text-gray-400 hover:text-gray-600">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
					</svg>
					</button>
				</div>
				<h3 class="text-xl font-bold">Mosi Tea</h3>
				<p class="text-gray-500">The best on-the-go tea infuser and premium tea on the planet.</p>
				<div class="mt-4">
					<p class="font-semibold">Audience</p>
					<p class="text-gray-600">Tea lovers that are on the go or like to be outdoors.</p>
				</div>
				<div class="mt-4">
					<p class="font-semibold">Collab Offer Summary</p>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="M560-440q-50 0-85-35t-35-85q0-50 35-85t85-35q50 0 85 35t35 85q0 50-35 85t-85 35ZM280-320q-33 0-56.5-23.5T200-400v-320q0-33 23.5-56.5T280-800h560q33 0 56.5 23.5T920-720v320q0 33-23.5 56.5T840-320H280Zm80-80h400q0-33 23.5-56.5T840-480v-160q-33 0-56.5-23.5T760-720H360q0 33-23.5 56.5T280-640v160q33 0 56.5 23.5T360-400Zm440 240H120q-33 0-56.5-23.5T40-240v-440h80v440h680v80ZM280-400v-320 320Z"/></svg>
						<p class="text-gray-600">
						CPA <span class="font-bold text-green-600">$19</span> per customer</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="m520-260 140-140q11-11 17.5-26t6.5-32q0-34-24-58t-58-24q-19 0-37.5 11T520-492q-30-28-47-38t-35-10q-34 0-58 24t-24 58q0 17 6.5 32t17.5 26l140 140Zm336-130L570-104q-12 12-27 18t-30 6q-15 0-30-6t-27-18L103-457q-11-11-17-25.5T80-513v-287q0-33 23.5-56.5T160-880h287q16 0 31 6.5t26 17.5l352 353q12 12 17.5 27t5.5 30q0 15-5.5 29.5T856-390ZM513-160l286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640Zm220 160Z"/></svg>
						<p class="text-gray-600">
						Earn <span class="font-bold text-green-600">$3</span> for 400 points</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m354-287 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143Zm126 18L314-169q-11 7-23 6t-21-8q-9-7-14-17.5t-2-23.5l44-189-147-127q-10-9-12.5-20.5T140-571q4-11 12-18t22-9l194-17 75-178q5-12 15.5-18t21.5-6q11 0 21.5 6t15.5 18l75 178 194 17q14 2 22 9t12 18q4 11 1.5 22.5T809-528L662-401l44 189q3 13-2 23.5T690-171q-9 7-21 8t-23-6L480-269Zm0-201Z"/></svg>
						<p class="text-gray-600">
						$10 off first order with Mosi Tea.</p>
					</div>
				</div>
				</div>
				<div class="flex justify-end mt-4">
				<PrimaryButton cta="Accept" size="xs"></PrimaryButton>
				</div>
			</div>
			<!-- Card 2 -->
			<div class=" bg-white rounded-2xl border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-2 px-4 py-4 hover:shadow-lg transition-all duratio-300  flex flex-col justify-between">
				<div>
				<div class="flex justify-between items-start mb-4">
					<img src="https://via.placeholder.com/50" alt="simple.ai" class="w-10 h-10">
					<button class="text-gray-400 hover:text-gray-600">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
					</svg>
					</button>
				</div>
				<h3 class="text-xl font-bold">Protein Gummies</h3>
				<p class="text-gray-500">Gummies that meet you your after-workout protein needs.</p>
				<div class="mt-4">
					<p class="font-semibold">Audience</p>
					<p class="text-gray-600">Athletes, outdoorsmen, gym rats.</p>
				</div>
				<div class="mt-4">
					<p class="font-semibold">Collab Offer Summary</p>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="M560-440q-50 0-85-35t-35-85q0-50 35-85t85-35q50 0 85 35t35 85q0 50-35 85t-85 35ZM280-320q-33 0-56.5-23.5T200-400v-320q0-33 23.5-56.5T280-800h560q33 0 56.5 23.5T920-720v320q0 33-23.5 56.5T840-320H280Zm80-80h400q0-33 23.5-56.5T840-480v-160q-33 0-56.5-23.5T760-720H360q0 33-23.5 56.5T280-640v160q33 0 56.5 23.5T360-400Zm440 240H120q-33 0-56.5-23.5T40-240v-440h80v440h680v80ZM280-400v-320 320Z"/></svg>
						<p class="text-gray-600">
						CPA <span class="font-bold text-green-600">$22</span> per customer</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="m520-260 140-140q11-11 17.5-26t6.5-32q0-34-24-58t-58-24q-19 0-37.5 11T520-492q-30-28-47-38t-35-10q-34 0-58 24t-24 58q0 17 6.5 32t17.5 26l140 140Zm336-130L570-104q-12 12-27 18t-30 6q-15 0-30-6t-27-18L103-457q-11-11-17-25.5T80-513v-287q0-33 23.5-56.5T160-880h287q16 0 31 6.5t26 17.5l352 353q12 12 17.5 27t5.5 30q0 15-5.5 29.5T856-390ZM513-160l286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640Zm220 160Z"/></svg>
						<p class="text-gray-600">
						Earn <span class="font-bold text-green-600">$5</span> for 600 points</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m354-287 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143Zm126 18L314-169q-11 7-23 6t-21-8q-9-7-14-17.5t-2-23.5l44-189-147-127q-10-9-12.5-20.5T140-571q4-11 12-18t22-9l194-17 75-178q5-12 15.5-18t21.5-6q11 0 21.5 6t15.5 18l75 178 194 17q14 2 22 9t12 18q4 11 1.5 22.5T809-528L662-401l44 189q3 13-2 23.5T690-171q-9 7-21 8t-23-6L480-269Zm0-201Z"/></svg>
						<p class="text-gray-600">
						$10 off first order and a free gummy hat.</p>
					</div>
				</div>
				</div>
				<div class="flex justify-end mt-4">
				<PrimaryButton cta="Accept" size="xs"></PrimaryButton>
				</div>
			</div>
			<!-- Card 3 -->
			<div class=" bg-white rounded-2xl border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-2 px-4 py-4 hover:shadow-lg transition-all duratio-300  flex flex-col justify-between">
				<div>
				<div class="flex justify-between items-start mb-4">
					<img src="https://via.placeholder.com/50" alt="simple.ai" class="w-10 h-10">
					<button class="text-gray-400 hover:text-gray-600">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
					</svg>
					</button>
				</div>
				<h3 class="text-xl font-bold">Haven</h3>
				<p class="text-gray-500">The best gym bags.</p>
				<div class="mt-4">
					<p class="font-semibold">Audience</p>
					<p class="text-gray-600">Athletes, gym rats, MMA fighters.</p>
				</div>
				<div class="mt-4">
					<p class="font-semibold">Collab Offer Summary</p>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="M560-440q-50 0-85-35t-35-85q0-50 35-85t85-35q50 0 85 35t35 85q0 50-35 85t-85 35ZM280-320q-33 0-56.5-23.5T200-400v-320q0-33 23.5-56.5T280-800h560q33 0 56.5 23.5T920-720v320q0 33-23.5 56.5T840-320H280Zm80-80h400q0-33 23.5-56.5T840-480v-160q-33 0-56.5-23.5T760-720H360q0 33-23.5 56.5T280-640v160q33 0 56.5 23.5T360-400Zm440 240H120q-33 0-56.5-23.5T40-240v-440h80v440h680v80ZM280-400v-320 320Z"/></svg>
						<p class="text-gray-600">
						CPA <span class="font-bold text-green-600">$30</span> per customer</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="m520-260 140-140q11-11 17.5-26t6.5-32q0-34-24-58t-58-24q-19 0-37.5 11T520-492q-30-28-47-38t-35-10q-34 0-58 24t-24 58q0 17 6.5 32t17.5 26l140 140Zm336-130L570-104q-12 12-27 18t-30 6q-15 0-30-6t-27-18L103-457q-11-11-17-25.5T80-513v-287q0-33 23.5-56.5T160-880h287q16 0 31 6.5t26 17.5l352 353q12 12 17.5 27t5.5 30q0 15-5.5 29.5T856-390ZM513-160l286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640Zm220 160Z"/></svg>
						<p class="text-gray-600">
						Earn <span class="font-bold text-green-600">$10</span> for 1,000 points</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m354-287 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143Zm126 18L314-169q-11 7-23 6t-21-8q-9-7-14-17.5t-2-23.5l44-189-147-127q-10-9-12.5-20.5T140-571q4-11 12-18t22-9l194-17 75-178q5-12 15.5-18t21.5-6q11 0 21.5 6t15.5 18l75 178 194 17q14 2 22 9t12 18q4 11 1.5 22.5T809-528L662-401l44 189q3 13-2 23.5T690-171q-9 7-21 8t-23-6L480-269Zm0-201Z"/></svg>
						<p class="text-gray-600">
						Personalize your gym bag.</p>
					</div>
				</div>
				</div>
				<div class="flex justify-end mt-4">
				<PrimaryButton cta="Accept" size="xs"></PrimaryButton>
				</div>
			</div>
			<!-- end card -->
			</div>
		</div>


	</div>

</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import CardContainer from '../components/CardContainer.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import CancelButton from '../components/CancelButton.ts.vue';
import { useRoute } from 'vue-router';
import { Crisp } from 'crisp-sdk-web';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		CardContainer,
		LightSecondaryButton,
		StatusMessage,
		ToggleItem,
		PrimaryButton,
		LearnMoreText,
		CancelButton
	},
	async mounted() {

	},
	data() {
		return {
			insights: false,
			history: false,
			overview: true,
			partners: false,
			pointsCost: 0,
			variants: [
						{
							id: 1,
							name: 'Blu Atlas',
							claims: 50,
							reach: 1000,
							conversions: 200,
							cost: 320,
							trendData: [10, 20, 15, 30, 25]
						},
						{
							id: 2,
							name: 'Mad Viking',
							claims: 50,
							reach: 1200,
							conversions: 250,
							cost: 400,
							trendData: [5, 10, 5, 20, 15]
						}
						],
		}
	},
	computed: {
		totalReach() {
			return this.variants.reduce((total, variant) => total + variant.reach, 0);
		},
		totalConversions() {
			return this.variants.reduce((total, variant) => total + variant.conversions, 0);
		},
		totalClaims() {
			return this.variants.reduce((total, variant) => total + variant.claims, 0);
		},
		totalCost() {
			return this.variants.reduce((total, variant) => total + variant.cost, 0);
		},
		costCalc() {
			return this.pointsCost * 0.02;
		}
	},
	methods: {
		openDocs(integration) {
			window.open(integration.docURL, '_blank');
		},
		openChat() {
			Crisp.chat.open();
		},
		onMouseEnter(index) {
			console.log("Testing: "+ index);
			this.hoveredRowIndex = index;
		},
		formatNumber(number) {
      		return number.toLocaleString();
		},
	},
}
</script>

<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}
</style>
