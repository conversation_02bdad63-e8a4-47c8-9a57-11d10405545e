<template>
	<div class="flex flex-col items-center justify-center mt-20 gap-6">
		<h1 class="text-2xl font-bold">We're sad to see you go!</h1>
		<button
			@click="unsubscribe"
			class="bg-ralpurple-400 rounded-md text-white px-4 py-2 mt-2 relative flex justify-center items-center w-32 h-10"
			:disabled="unsubscribed"
			:class="{ 'opacity-60': unsubscribed, 'cursor-not-allowed': unsubscribed }"
		>
			<span v-if="!isUnsubscribing">{{ buttonText }}</span>
			<span v-else class="spinner"></span>
		</button>
	</div>
</template>

<script>
	import * as Utils from '../../client-old/utils/Utils';
	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		name: 'Unsubscribe',
		props: ['customerId'],
		data() {
			return {
				unsubscribed: false,
				isUnsubscribing: false,
				buttonText: "Unsubscribe",
			}
		},
		methods: {
			async unsubscribe() {
				this.isUnsubscribing = true;
				this.buttonText = "";
				try {
					await fetch(`${URL_DOMAIN}/customers/${this.customerId}/unsubscribe`, {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
						},
					});
				} catch (e) {
					console.log(`Error unsubscribing: ${e}`);
				}
				this.buttonText = "Unsubscribed";
				this.isUnsubscribing = false;
				this.unsubscribed = true;
			}
		}
	}
</script>

<style scoped>
	button {
		width: 128px;
		height: 40px;
		position: relative;
	}

	.spinner {
		border: 4px solid rgba(255, 255, 255, 0.3);
		border-top: 4px solid white;
		border-radius: 50%;
		width: 20px;
		height: 20px;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
</style>
