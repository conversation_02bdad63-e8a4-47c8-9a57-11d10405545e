<template>
	<ProgramActive></ProgramActive>
	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="!isFeatureAvailable && !isTrialEnded">
		<img src="../images/VIPTeirPromo.png" width="800">
		<a class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]" href="/loyalty/settings/plans">Upgrade to Loyalty Plan</a>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			Loyalty programs powered by Raleon help brands increase repeat purchase rate and customer lifetime value.
		</p>
		<PrimaryButton
					cta="Upgrade Loyalty Plan"
					size="xs"
					@click="() => this.$router.push('/loyalty/settings/plans')"
					/>
	</div>

	<div class="bg-gradient-to-tr from-[#4F3EAC] to-[#5E48F8] p-4 w-full justify-center shadow-lg flex items-center" v-if="!isFeatureAvailable && isTrialEnded">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 14H11V9.8L12.6 11.4L14 10L10 6L6 10L7.4 11.4L9 9.8V14ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z" fill="#FFA3DF"/>
        </svg>
        <div class="text-white ml-2">Your trial has expired and your program has been deactivated.</div>
        <div class="px-2 py-2 text-white text-sm border-[#FFF] border rounded-full ml-4 hover:text-black hover:bg-[#FFF] hover:cursor-pointer transitional-bg delay-200" @click="() => this.$router.push('/loyalty/settings/plans')">
                    Choose a Plan
        </div>
    </div>

	<div v-if="isFeatureAvailable || isTrialEnded" class="m-3 sm:m-10 sm:m-7">
		<div v-if="true">

			<div class="w-full lg:w-[93%] flex flex-col sm:flex-row items-center justify-between">
				<h1 class="text-3xl sm:text-6xl font-sans font-medium opacity-70 text-ralblack-primary">VIP Management
				</h1>
				<PreviewLoyaltyProgram class="ml-auto align-center" />

			</div>
			<div class="w-full lg:w-[93%] flex flex-col sm:flex-row items-start justify-between lg:mb-12">
				<!--<div class="flex-shrink-0 mt-2">
					Create and manage special rewards and offers for customers in VIP tiers.
				</div>-->
				<div class="text-ralblack-primary text-sm font-[Inter] mt-4 mb-4">
					<span>Create and manage special rewards and offers for customers in VIP tiers.</span>
				</div>
			</div>
			<div v-if="tiers.length" class="flex justify-between">
				<div class="text-zinc-500 text-2xl sm:text-5xl font-medium font-['Inter'] leading-10">Settings</div>
			</div>

			<div v-if="tiers.length"
				class="bg-white rounded-2xl border shadow border-ralprimary-light pl-4 pr-4 pb-4 border-opacity-50 hover:border-opacity-90 hover:shadow-lg mt-10 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
				<label class="flex text-ralsecondary-start text-2xl font-normal font-['Inter'] mt-4">Enable VIP
					Program</label>
				<div
					class="relative inline-block w-10 mr-2 mt-3 align-middle select-none transition duration-200 ease-in">
					<input type="checkbox" id="vipActive"
						:class="{'toggle-checkbox': true, 'absolute': true, 'block': true, 'w-6': true, 'h-6': true, 'rounded-full': true, 'bg-white': true, 'border-4': true, 'appearance-none': true, 'cursor-pointer': !isLoading, 'cursor-not-allowed': isLoading, 'bg-gray-200': isLoading}"
						v-model="vipActive"
						:disabled="isLoading"
						@change="setVipActive($event.target.checked)" />
					<label for="vipActive"
						class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
				</div>
				<div class="mt-4">
					When enabled, you can preview the VIP program on your site. Customers cannot see it until it is Live.
				</div>

				<div class="text-slate-800 text-base font-semibold font-['Inter'] leading-normal mt-4">
					Tier Term
				</div>

				<div class="w-full self-stretch justify-start items-center gap-2 flex mt-2">
					<select
						class="w-52 h-12 px-2 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex justify-center opacity-65 disabled"
						v-model="termSelection"
						disabled>
						<option
							v-for="item in termOptions"
							:value="item.value"
						>
							{{ item.label }}
						</option>
					</select>
					<span class="text-sm italic">Additional term options coming soon!</span>
				</div>


				<div class="mt-4" v-if="vipActive">
					<label class="flex text-ralsecondary-start text-2xl font-normal font-['Inter'] mt-4">VIP Live</label>
					<div
						class="relative inline-block w-10 mr-2 mt-3 align-middle select-none transition duration-200 ease-in">
						<input type="checkbox" id="vipLive"
							:class="{'toggle-checkbox': true, 'absolute': true, 'block': true, 'w-6': true, 'h-6': true, 'rounded-full': true, 'bg-white': true, 'border-4': true, 'appearance-none': true, 'cursor-pointer': !isLoading, 'cursor-not-allowed': isLoading, 'bg-gray-200': isLoading}"
							v-model="vipLive"
							:disabled="isLoading"
							@change="setVIPFeatureSetting(this.vipActive, $event.target.checked)" />
						<label for="vipLive" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
					</div>
					<div class="mt-4">
						When live, VIP tiers will be visible to customers.
					</div>
				</div>
			</div>

			<div v-if="false" role="status"
				class="w-full p-4 space-y-4 border bg-white border-ralprimary-light opacity-75 border-opacity-50 rounded-2xl shadow animate-pulse mt-2">
				<!-- <div class="flex items-center justify-between">
					<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
					<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
					<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
					<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
				</div> -->
				<div v-if="true"
					class="bg-white rounded-2xl border shadow border-ralprimary-light pl-4 pr-4 pb-4 border-opacity-50 hover:border-opacity-90 hover:shadow-lg mt-10 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
					<label class="flex text-ralsecondary-start text-2xl font-normal font-['Inter'] mt-4">Enable VIP
						Program</label>
					<div
						class="relative inline-block w-10 mr-2 mt-3 align-middle select-none transition duration-200 ease-in">
						<input type="checkbox" id="vipActive"
							class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
							v-model="vipActive" @change="setVipActive" />
						<label for="vipActive"
							class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
					</div>
					<!-- <div class="mt-4 text-ralgray-dark text-sm font-normal font-['Inter'] leading-none">
						When you're finished here, go to Referral Branding to customize the look and feel of the referral program.
						<LightSecondaryButton
							cta="Referral Branding"
							@click="navigateToBranding"
							class="mx-2">
						</LightSecondaryButton>
					</div> -->
				</div>
			</div>


			<div class="mt-20 mb-8">
				<div class="flex">
					<div>
						<div class="text-zinc-500 text-2xl sm:text-5xl font-medium font-['Inter'] leading-[65px]">Tiers
						</div>
						Manage your VIP Tiers.
					</div>
					<div class="flex-grow"></div>
					<PrimaryButton cta="add" icon="true" @click="tierModalOpen = true"></PrimaryButton>
				</div>
			</div>

			<div class="tier-accordion-group">
				<AccordionGroup :accordions="accordions">
					<template v-for="(tier, index) in tiers" :key="index" v-slot:[`${tier.id}`] class="p-8">
						<div class="mb-16" :ref="`accordion-${tier.id}`">
							<div class="flex items-center">
								<div class="text-xl font-semibold text-ralblack-primary">Entry Conditions</div>
								<div class="flex-grow"></div>
								<div class="flex-shrink-0">
									<PrimaryButton cta="Edit" size="xs" icon="false" @click="editTier(tier)">
									</PrimaryButton>
									<LightSecondaryButton v-if="index != 0" class="ml-4" cta="Delete" size="xs" icon="false" @click="deleteTier(tier)" />
								</div>
							</div>
							<div v-for="(condition, index2) in tier.entryConditions" :key="index2">
								<div class="mt-4">
									<div class="text-sm font-semibold text-ralblack-primary">Condition</div>
									<div>
										<span class="text-sm font-normal text-ralblack-primary">{{ condition.variable
			=== 'points-ttm' ? 'Points (Trailing 12 Months)' : condition.variable
											}}</span>&nbsp;
										<span class="text-sm font-normal text-ralblack-primary">{{ condition.operator
											}}</span>&nbsp;
										<span class="text-sm font-normal text-ralblack-primary">{{ condition.amount
											}}</span>
									</div>
								</div>
							</div>
						</div>

						<!-- <div class="flex justify-between items-center">
						<h2 class="text-xl font-bold">{{ tier.name }}</h2>
						</div>

						<div class="flex-shrink-0">
							<PrimaryButton cta="Delete" size="normal" icon="true" @click="deleteTier(tier)"></PrimaryButton>
						</div> -->

						<div v-if="tier.summary">
							<div v-if="tier.summary.perks">
								<div class="mt-10">
									<div class="flex">
										<div>
											<div
												class="text-zinc-500 text-lg sm:text-2xl font-medium font-['Inter'] leading-[65px]">
												VIP Perks
												<!--
											<Tooltip bg="dark" size="md" position="bottom">
												<div class="text-xs whitespace-nowrap text-white">Shoppers can earn rewards directly through a Way to Earn, or they can redeem points for a reward that's available in the Points Shop.</div>
											</Tooltip>
											-->
											</div>
											Members belonging to this tier will always receive these perks
										</div>
										<div class="flex-grow"></div>
										<div class="flex-shrink-0">
											<PrimaryButton cta="add" size="small" icon="true" @click="addPerk(tier)">
											</PrimaryButton>
										</div>

									</div>
								</div>
								<div class="my-10">
									<div v-if="isSummaryLoading == true" role="status"
										class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-full rounded-2xl shadow animate-pulse mb-6 sm:mb-0">
									</div>
									<div class="flex flex-col items-center justify-center h-22 mb-2"
										v-if="tier.summary.perks.length <= 0">
										<svg width="540" height="30" viewBox="0 0 501 30" fill="none"
											xmlns="http://www.w3.org/2000/svg">
											<g opacity="0.7">
												<g filter="url(#filter0_d_605_5769)">
													<rect x="6" y="4" width="501" height="17.0606" rx="8.53032"
														fill="white" fill-opacity="0.75" shape-rendering="crispEdges" />
													<rect x="6.5" y="4.5" width="500" height="16.0606" rx="8.03032"
														stroke="#D6D1FD" shape-rendering="crispEdges" />
												</g>
												<rect x="387" y="10" width="110" height="5" rx="2.5"
													fill="url(#paint0_linear_605_5769)" />
												<rect x="237" y="10" width="110" height="5" rx="2.5"
													fill="url(#paint1_linear_605_5769)" />
												<rect x="47" y="10" width="110" height="5" rx="2.5"
													fill="url(#paint2_linear_605_5769)" />
											</g>
											<defs>
												<filter id="filter0_d_605_5769" x="0" y="0" width="513" height="29.0605"
													filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
													<feFlood flood-opacity="0" result="BackgroundImageFix" />
													<feColorMatrix in="SourceAlpha" type="matrix"
														values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
														result="hardAlpha" />
													<feOffset dy="2" />
													<feGaussianBlur stdDeviation="3" />
													<feComposite in2="hardAlpha" operator="out" />
													<feColorMatrix type="matrix"
														values="0 0 0 0 0.0509804 0 0 0 0 0.0392157 0 0 0 0 0.172549 0 0 0 0.08 0" />
													<feBlend mode="normal" in2="BackgroundImageFix"
														result="effect1_dropShadow_605_5769" />
													<feBlend mode="normal" in="SourceGraphic"
														in2="effect1_dropShadow_605_5769" result="shape" />
												</filter>
												<linearGradient id="paint0_linear_605_5769" x1="430" y1="-0.555557"
													x2="433.874" y2="26.1776" gradientUnits="userSpaceOnUse">
													<stop stop-color="#D9D9D9" />
													<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
												</linearGradient>
												<linearGradient id="paint1_linear_605_5769" x1="280" y1="-0.555557"
													x2="283.874" y2="26.1776" gradientUnits="userSpaceOnUse">
													<stop stop-color="#D9D9D9" />
													<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
												</linearGradient>
												<linearGradient id="paint2_linear_605_5769" x1="90" y1="-0.555557"
													x2="93.8744" y2="26.1776" gradientUnits="userSpaceOnUse">
													<stop stop-color="#D9D9D9" />
													<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
												</linearGradient>
											</defs>
										</svg>

										<p class="text-xl text-ralblack-primary mt-2">You haven't added any perks yet.
										</p>
									</div>
									<div class="mt-10" v-else-if="tier.summary.perks.length > 0">
										<div
											class="overflow-x-auto bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
											<WTERewardTable header1="Perk Type" header2="Status"
												:tableData="tier.summary.perks"
												@editClicked="evt => handlePerkSummaryEdit(evt, tier)"
												@toggleChanged="evt => handlePerkSummaryToggle(evt, tier)"
												@priorityChanged="evt => updatePriorityPerk(evt, tier)"/>
										</div>
									</div>
								</div>
							</div>


							<div v-if="tier.summary.wtes">
								<div class="mt-20">
									<div class="flex">
										<div>
											<div
												class="text-zinc-500 text-lg sm:text-2xl font-medium font-['Inter'] leading-[65px]">
												VIP Ways To Earn
												<!--
											<Tooltip bg="dark" size="md" position="bottom">
												<div class="text-xs whitespace-nowrap text-white">Shoppers can earn rewards directly through a Way to Earn, or they can redeem points for a reward that's available in the Points Shop.</div>
											</Tooltip>
											-->
											</div>
											Members belonging to this tier have access to these exclusive ways to earn
										</div>
										<div class="flex-grow"></div>
										<div class="flex-shrink-0">
											<PrimaryButton cta="add" size="small" icon="true"
												@click="addWayToEarn(tier)"></PrimaryButton>
										</div>

									</div>
								</div>
								<div class="my-10">
									<div v-if="isSummaryLoading == true" role="status"
										class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-full rounded-2xl shadow animate-pulse mb-6 sm:mb-0">
									</div>
									<div class="flex flex-col items-center justify-center h-22 mb-2"
										v-if="tier.summary.wtes.length <= 0">
										<svg width="540" height="30" viewBox="0 0 501 30" fill="none"
											xmlns="http://www.w3.org/2000/svg">
											<g opacity="0.7">
												<g filter="url(#filter0_d_605_5769)">
													<rect x="6" y="4" width="501" height="17.0606" rx="8.53032"
														fill="white" fill-opacity="0.75" shape-rendering="crispEdges" />
													<rect x="6.5" y="4.5" width="500" height="16.0606" rx="8.03032"
														stroke="#D6D1FD" shape-rendering="crispEdges" />
												</g>
												<rect x="387" y="10" width="110" height="5" rx="2.5"
													fill="url(#paint0_linear_605_5769)" />
												<rect x="237" y="10" width="110" height="5" rx="2.5"
													fill="url(#paint1_linear_605_5769)" />
												<rect x="47" y="10" width="110" height="5" rx="2.5"
													fill="url(#paint2_linear_605_5769)" />
											</g>
											<defs>
												<filter id="filter0_d_605_5769" x="0" y="0" width="513" height="29.0605"
													filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
													<feFlood flood-opacity="0" result="BackgroundImageFix" />
													<feColorMatrix in="SourceAlpha" type="matrix"
														values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
														result="hardAlpha" />
													<feOffset dy="2" />
													<feGaussianBlur stdDeviation="3" />
													<feComposite in2="hardAlpha" operator="out" />
													<feColorMatrix type="matrix"
														values="0 0 0 0 0.0509804 0 0 0 0 0.0392157 0 0 0 0 0.172549 0 0 0 0.08 0" />
													<feBlend mode="normal" in2="BackgroundImageFix"
														result="effect1_dropShadow_605_5769" />
													<feBlend mode="normal" in="SourceGraphic"
														in2="effect1_dropShadow_605_5769" result="shape" />
												</filter>
												<linearGradient id="paint0_linear_605_5769" x1="430" y1="-0.555557"
													x2="433.874" y2="26.1776" gradientUnits="userSpaceOnUse">
													<stop stop-color="#D9D9D9" />
													<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
												</linearGradient>
												<linearGradient id="paint1_linear_605_5769" x1="280" y1="-0.555557"
													x2="283.874" y2="26.1776" gradientUnits="userSpaceOnUse">
													<stop stop-color="#D9D9D9" />
													<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
												</linearGradient>
												<linearGradient id="paint2_linear_605_5769" x1="90" y1="-0.555557"
													x2="93.8744" y2="26.1776" gradientUnits="userSpaceOnUse">
													<stop stop-color="#D9D9D9" />
													<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
												</linearGradient>
											</defs>
										</svg>

										<p class="text-xl text-ralblack-primary mt-2">You haven't added any ways to earn
											yet.</p>
									</div>
									<div class="mt-10" v-else-if="tier.summary.wtes.length > 0">
										<div
											class="overflow-x-auto bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
											<WTERewardTable header1="Reward Type" header2="Status"
												:tableData="tier.summary.wtes"
												@editClicked="evt => handleWTESummaryEdit(evt, tier)"
												@toggleChanged="evt => handleWTESummaryToggle(evt, tier)"
												@priorityChanged="evt => updatePriorityEarn(evt, tier)"/>
										</div>
									</div>
								</div>
							</div>


							<div v-if="tier.summary.rewards">
								<div class="mt-20">
									<div class="flex">
										<div>
											<div
												class="text-zinc-500 text-lg sm:text-2xl font-medium font-['Inter'] leading-[65px]">
												VIP Rewards
												<!--
											<Tooltip bg="dark" size="md" position="bottom">
												<div class="text-xs whitespace-nowrap text-white">Shoppers can earn rewards directly through a Way to Earn, or they can redeem points for a reward that's available in the Points Shop.</div>
											</Tooltip>
											-->
											</div>
											Members belonging to this tier will have access to spend points to receive
											these exclusive rewards
										</div>
										<div class="flex-grow"></div>
										<div class="flex-shrink-0">
											<PrimaryButton cta="add" size="small" icon="true" @click="addReward(tier)">
											</PrimaryButton>
										</div>

									</div>
								</div>
								<div class="flex flex-col items-center justify-center h-22 mb-2"
									v-if="tier.summary.rewards.length <= 0">
									<svg width="540" height="30" viewBox="0 0 501 30" fill="none"
										xmlns="http://www.w3.org/2000/svg">
										<g opacity="0.7">
											<g filter="url(#filter0_d_605_5769)">
												<rect x="6" y="4" width="501" height="17.0606" rx="8.53032" fill="white"
													fill-opacity="0.75" shape-rendering="crispEdges" />
												<rect x="6.5" y="4.5" width="500" height="16.0606" rx="8.03032"
													stroke="#D6D1FD" shape-rendering="crispEdges" />
											</g>
											<rect x="387" y="10" width="110" height="5" rx="2.5"
												fill="url(#paint0_linear_605_5769)" />
											<rect x="237" y="10" width="110" height="5" rx="2.5"
												fill="url(#paint1_linear_605_5769)" />
											<rect x="47" y="10" width="110" height="5" rx="2.5"
												fill="url(#paint2_linear_605_5769)" />
										</g>
										<defs>
											<filter id="filter0_d_605_5769" x="0" y="0" width="513" height="29.0605"
												filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
												<feFlood flood-opacity="0" result="BackgroundImageFix" />
												<feColorMatrix in="SourceAlpha" type="matrix"
													values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
													result="hardAlpha" />
												<feOffset dy="2" />
												<feGaussianBlur stdDeviation="3" />
												<feComposite in2="hardAlpha" operator="out" />
												<feColorMatrix type="matrix"
													values="0 0 0 0 0.0509804 0 0 0 0 0.0392157 0 0 0 0 0.172549 0 0 0 0.08 0" />
												<feBlend mode="normal" in2="BackgroundImageFix"
													result="effect1_dropShadow_605_5769" />
												<feBlend mode="normal" in="SourceGraphic"
													in2="effect1_dropShadow_605_5769" result="shape" />
											</filter>
											<linearGradient id="paint0_linear_605_5769" x1="430" y1="-0.555557"
												x2="433.874" y2="26.1776" gradientUnits="userSpaceOnUse">
												<stop stop-color="#D9D9D9" />
												<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
											</linearGradient>
											<linearGradient id="paint1_linear_605_5769" x1="280" y1="-0.555557"
												x2="283.874" y2="26.1776" gradientUnits="userSpaceOnUse">
												<stop stop-color="#D9D9D9" />
												<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
											</linearGradient>
											<linearGradient id="paint2_linear_605_5769" x1="90" y1="-0.555557"
												x2="93.8744" y2="26.1776" gradientUnits="userSpaceOnUse">
												<stop stop-color="#D9D9D9" />
												<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
											</linearGradient>
										</defs>
									</svg>

									<p class="text-xl text-ralblack-primary mt-2">You haven't added any redeemable
										rewards yet.</p>
								</div>
								<div class="my-10">
									<div v-if="isSummaryLoading == true" role="status"
										class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-full rounded-2xl shadow animate-pulse mb-6 sm:mb-0">
									</div>
									<div class="mt-10" v-else-if="tier.summary.rewards.length > 0">
										<div
											class="overflow-x-auto bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
											<WTERewardTable header1="Reward Type" header2="Status"
												:tableData="tier.summary.rewards"
												@editClicked="evt => handleShopItemSummaryEdit(evt, tier)"
												@toggleChanged="evt => handleShopItemSummaryToggle(evt, tier)"
												@priorityChanged="evt => updatePriorityReward(evt, tier)" />
										</div>
									</div>
								</div>
							</div>
						</div>
					</template>
				</AccordionGroup>
			</div>


			<div class="flex flex-col items-center justify-center h-22 mb-2"
				v-if="tiers.length <= 0 && isLoading == false">
				<svg width="540" height="30" viewBox="0 0 501 30" fill="none" xmlns="http://www.w3.org/2000/svg">
					<g opacity="0.7">
						<g filter="url(#filter0_d_605_5769)">
							<rect x="6" y="4" width="501" height="17.0606" rx="8.53032" fill="white" fill-opacity="0.75"
								shape-rendering="crispEdges" />
							<rect x="6.5" y="4.5" width="500" height="16.0606" rx="8.03032" stroke="#D6D1FD"
								shape-rendering="crispEdges" />
						</g>
						<rect x="387" y="10" width="110" height="5" rx="2.5" fill="url(#paint0_linear_605_5769)" />
						<rect x="237" y="10" width="110" height="5" rx="2.5" fill="url(#paint1_linear_605_5769)" />
						<rect x="47" y="10" width="110" height="5" rx="2.5" fill="url(#paint2_linear_605_5769)" />
					</g>
					<defs>
						<filter id="filter0_d_605_5769" x="0" y="0" width="513" height="29.0605"
							filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
							<feFlood flood-opacity="0" result="BackgroundImageFix" />
							<feColorMatrix in="SourceAlpha" type="matrix"
								values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
							<feOffset dy="2" />
							<feGaussianBlur stdDeviation="3" />
							<feComposite in2="hardAlpha" operator="out" />
							<feColorMatrix type="matrix"
								values="0 0 0 0 0.0509804 0 0 0 0 0.0392157 0 0 0 0 0.172549 0 0 0 0.08 0" />
							<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_605_5769" />
							<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_605_5769"
								result="shape" />
						</filter>
						<linearGradient id="paint0_linear_605_5769" x1="430" y1="-0.555557" x2="433.874" y2="26.1776"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#D9D9D9" />
							<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
						</linearGradient>
						<linearGradient id="paint1_linear_605_5769" x1="280" y1="-0.555557" x2="283.874" y2="26.1776"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#D9D9D9" />
							<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
						</linearGradient>
						<linearGradient id="paint2_linear_605_5769" x1="90" y1="-0.555557" x2="93.8744" y2="26.1776"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#D9D9D9" />
							<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
						</linearGradient>
					</defs>
				</svg>

				<p class="text-xl text-ralblack-primary mt-2">You haven't added any tiers yet.</p>
			</div>


			<!-- <RaleonTable class="mt-3" :column-headers="this.columnHeaders" :row-data="this.rowData" :showActiveOrNot="false" v-if="this.rowData.length > 0" :always-show-delete="true" @delete-row-clicked="deleteTier" /> -->
		</div> <!-- end hide -->

	</div>

	<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>

	<ModalBlank id="tier-modal" :modalOpen="tierModalOpen" @close-modal="tierModalOpen = false">
		<div class="p-5 flex space-x-4">
			<div>
				<div class="mb-2">
					<div class="text-lg font-semibold text-ralblack-primary">{{ isEditing ? 'Edit VIP Tier' : 'Create VIP Tier' }}</div>
				</div>
				<div class="text-sm mb-10">
					<div class="space-y-2">

						<div
							class="text-zinc-900 text-xl font-normal font-['Open Sans'] leading-loose flex-grow truncate">
							Tier Information</div>
						<div class="mt-3 text-black text-sm font-normal font-['Inter']">
							<!-- Adjust which side of your store the launcher appears on for computers, tablets, and mobile. -->
						</div>

						<!-- <div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal mb-2">
							Tier Level
						</div>
						<SuperDropdown class="mt-3" :options="[
								{value: 'level1', label: 'Level 1'},
								{value: 'level2', label: 'Level 2'},
							]" option-value-key="value" option-label-key="label" v-model="tierLevel" /> -->

						<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal mb-2">
							Tier Name
						</div>

						<input type="text" v-model="tierName"
							class="w-96 bg-white rounded-lg border border-gray-400 no-focus-outline text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
							maxlength="40" />

						<!-- <div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal mb-2">
							Benefits Description
						</div>
						<input type="text"
							v-model="tierDescription"
							class="w-96 bg-white rounded-lg border border-gray-400 no-focus-outline text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
							maxlength="40" /> -->


						<div class="pt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal mb-2">
							Tier Image (optional)
						</div>

						<div class="flex flex-wrap pb-4">
							<div class="tier-image-wrapper">
								<div class="tier-image-container">
									<img v-if="!this.tierImageUrl"
										class="rounded-3xl border object-center object-cover border-red-500"
										src="https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg"
										width="40" height="40" />
									<img v-else :src="this.tierImageUrl"
										class="rounded-3xl border object-center object-cover" width="40" height="40" />
								</div>
							</div>
							<div class="flex flex-col flex-grow items-start justify-center">
								<div class="w-full ml-4">
									<ImageUpload @imageSelected="(event) => {this.tierImageUrl = event.url;}" />
								</div>
							</div>
						</div>

						<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
							Points to Enter Tier
						</div>
						<div class="mt-3 text-black text-sm font-normal font-['Inter']">
							Points earned during the most recent 12 months
						</div>
						<input type="text" :disabled="shouldDisableMinPoints" v-model="minPoints"
							class="w-96 bg-white rounded-lg border no-focus-outline text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none placeholder:text-gray-400 placeholder:font-normal placeholder:italic"
							:class="{'border-gray-400 text-gray-600': !shouldDisableMinPoints, 'border-gray-300 text-gray-300': shouldDisableMinPoints}"
							maxlength="40" placeholder="Number of points" />

						<!-- <div class="pt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
							Max Points for Tier
						</div>
						<div class="mt-3 text-black text-sm font-normal font-['Inter']">
							Points earned during the most recent 12 months
						</div>
						<input type="text"
							v-model="maxPoints"
							:disabled="true"
							class="w-96 bg-gray-100 cursor-not-allowed rounded-lg border border-gray-400 no-focus-outline text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none placeholder:text-gray-400 placeholder:font-normal placeholder:italic border-gray-300 text-gray-400"
							maxlength="40" placeholder="Number of points" /> -->

						<div class="mt-2 italic text-gray-600 text-sm font-normal font-['Inter']">
							The maximum is controlled automatically by the next highest tier, and will be updated as you
							add
							higher tiers.
						</div>

						<!-- <div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
							Tier Image
						</div> -->

					</div>
				</div>
				<div class="flex flex-wrap justify-end space-x-2">
					<CancelButton @click="resetDirtySettings(); tierModalOpen = false" cta="Cancel"></CancelButton>

					<LightSecondaryButtonTs @click="saveTier();" cta="Save"></LightSecondaryButtonTs>
<!--
					<button
						class="btn-sm bg-ralwarning-dark hover:bg-opacity-75 text-black rounded-full transitiona-all duration-300"
						@click="saveTier();">Save</button> -->
				</div>
			</div>
		</div>
	</ModalBlank>

</template>

<script>
import NumberChart from '../components/NumberChart.ts.vue';
import RaleonTable from '../components/RaleonTable.ts.vue';
import ModalBlank from '../components/ModalBlank.vue';
import CardContainer from '../components/CardContainer.ts.vue';
import SuperDropdown from '../components/SuperDropdown.ts.vue';
import * as Utils from '../../client-old/utils/Utils';
import EditableHeader from '../components/EditableHeader.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import ProgramActive from '../components/ProgramActive.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue';
import CancelButton from '../components/CancelButton.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import Tooltip from '../components/Tooltip.ts.vue';
import GeneratedText from '../components/GeneratedText.ts.vue';
import RaleonLoader from '../components/RaleonLoader.ts.vue'
import TextFadeEffect from '../components/TextFadeEffect.ts.vue'
import { getMetric } from '../services/metrics.js';
import PreviewLoyaltyProgram from '../components/PreviewLoyalty.ts.vue';
import * as OrganizationSettings from '../services/organization-settings.js'
import WTERewardTable from '../components/WTERewardTable.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
import AccordionGroup from '../components/AccordionGroup.ts.vue';
import ImageUpload from '../components/ImageUpload.ts.vue';
import LightSecondaryButtonTs from '../components/LightSecondaryButton.ts.vue';
import { isFeatureAvailable } from '../services/features.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	data() {
		return {
			tierLevel: '1',
			tierName: '',
			isLoading: true,
			isEditing: false,
			editingTierId: null,
			tierImageUrl: '',
			tierDescription: ' ',
			status: { message: '', type: 'nope' },
			minPoints: localStorage.getItem('raleon_vip_last_tier_max') || 0,
			tierModalOpen: false,
			tiers: [],
			termOptions: [{label: 'Trailing 12 Months', value: 'ttm'}],
			termSelection: 'ttm',
			columnHeaders: [{
				name: 'Name',
				Tooltip: 'Name',
				value: 'name'
			}],
			rowData: [],
			vipLive: false,
		}
	},
	components: {
		PrimaryButton,
		LightSecondaryButton,
		CancelButton,
		ToggleItem,
		StatusMessage,
		CardContainer,
		Tooltip,
		OrganizationSettings,
		RaleonTable,
		AccordionGroup,
		WTERewardTable,
		ModalBlank,
		ImageUpload,
		LightSecondaryButtonTs,
		PreviewLoyaltyProgram,
	},
	async mounted() {
		customerIOTrackEvent('VIP Viewed');

		await this.getTiers();
		await this.getVIPFeatureSetting();
	},
	computed: {
		isFeatureAvailable() {
			return isFeatureAvailable('vip');
		},
		isTrialEnded() {
			const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));

			return freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0
		},
		accordions() {
			return this.tiers.map((tier, i, tiers) => {
				const isLastTier = i === tiers.length - 1;
				let nextTierPoints = isLastTier ? 'Unlimited Points' : tiers[i + 1].entryConditions[0]?.amount + ' Points';
				if(tiers.length == 1) {
					nextTierPoints = 'Next Tier Points';
				}
				return {
					key: tier.id.toString(),
					title: tier.name,
					subtitle: `${Utils.formatNumberWithCommas(tier.entryConditions?.[0]?.amount)} Points - ${Utils.formatNumberWithCommas(nextTierPoints)}`,
					height: 'auto'
				}
			});
		},
		isProd() {
			return location.hostname === 'app.raleon.io';
		},
		shouldDisableMinPoints() {
			if (this.tiers.length > 0 && this.editingTierId === this.tiers[0].id) {
				this.minPoints = 0;
				return true;
			}
			if(this.tiers.length === 0) {
				this.minPoints = 0;
				return true;
			}
			return false;
		},
		maxMinPoints() {
			let maxMin = null;
			this.tiers?.forEach?.(tier => {
				maxMin = Math.max(maxMin || 0, tier.entryConditions[0]?.amount);
			});

			return maxMin;
		},
		vipActive() {
			return this.tiers?.some?.(x => x.summary?.active);
		},
		maxPoints() {
			// find the tiers that the minPoints fits below
			const tier = this.tiers.find(tier => tier.entryConditions[0]?.amount >= this.minPoints);
			const max = tier?.entryConditions[0]?.amount;

			if (!max) {
				return 'No Maximum';
			}

			return max;
		}
	},
	methods: {
		checkIfFirstTier() {
			if (!this.editingTierId && this.tiers.length === 0) {
				// This is the first tier being added
				this.minPoints = 0;
				return true;
			}
			return false;
		},
		resetDirtySettings() {
			this.tierName = '';
			this.tierDescription = ' ';
			this.isEditing = false;
			this.editingTierId = null;
			this.tierImageUrl = '';
			this.minPoints = this.getNextTierGuess() || 0;
			this.maxPoints = '';

		},
		getNextTierGuess() {
			const mins = this.tiers?.map?.(tier => {
				return tier.entryConditions[0]?.amount
			});

			if (mins.indexOf(0) == -1) {
				mins.splice(0, 0, 0);
			}

			const ranges = mins.map((min, i) => {
				const next = mins[i + 1];
				return next - min;
			}).slice(0, -1);

			const averageRange = ranges.reduce((a, b) => a + b, 0) / ranges.length;

			const result = this.maxMinPoints + averageRange;

			return isNaN(result) ? 0 : result;
		},
		async setVipActive(active) {
			this.isLoading = true;
			// const response = await fetch(`${URL_DOMAIN}/vip/enable/${active}`, {
			// 	method: 'POST',
			// 	withCreditentials: true,
			// 	credentials: 'omit',
			// 	headers: {
			// 		'Authorization': `Bearer ${localStorage.getItem('token')}`,
			// 		'Access-Control-Allow-Origin': '*',
			// 		'Content-Type': 'application/json'
			// 	},
			// });


			// if (response.ok) {
			// 	this.tiers.forEach(tier => {
			// 		if (tier.summary.campaign) {
			// 			tier.summary.campaign.active = active;
			// 		}
			// 	});

			// 	this.getTiers().catch();
			// }
			if (active) {
				customerIOTrackEvent('VIP Program Enabled');
			} else {
				customerIOTrackEvent('VIP Program Disabled');
			}
			await this.setVIPFeatureSetting(active, this.vipLive);
		},
		async setVIPFeatureSetting(active, live) {
			const response = await fetch(`${URL_DOMAIN}/vip/setEnabledAndLive?enabled=${active}&live=${live}`, {
				method: 'POST',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});


			if (response.ok) {
				this.tiers.forEach(tier => {
					if (tier.summary.campaign) {
						tier.summary.campaign.active = active;
					}
				});

				this.getTiers().catch();
			}
		},
		editTier(tier) {
			this.tierName = tier.name;
			this.tierDescription = tier.description;
			this.tierImageUrl = tier.imageURL;
			this.minPoints = tier.entryConditions[0]?.amount;
			this.isEditing = true;
			this.editingTierId = tier.id;
			this.tierModalOpen = true;
		},
		async saveTier() {
			this.tierModalOpen = false;

			const url = this.isEditing ? `${URL_DOMAIN}/tier/${this.editingTierId}` : `${URL_DOMAIN}/tier`;

			const response = await fetch(url, {
				method: this.isEditing ? 'PATCH' : 'POST',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					id: this.editingTierId,
					tierName: this.tierName,
					tierImageUrl: this.tierImageUrl || '',
					minPoints: Number(this.minPoints || 0),
					enabled: this.vipActive
				}),

			});


			if (response.ok) {
				localStorage.setItem('raleon_vip_last_tier_max', this.maxPoints);
				this.tierModalOpen = false;
				this.resetDirtySettings();
				this.getTiers();
			}
		},
		async getTiers() {
			this.isLoading = true;
			const response = await fetch(`${URL_DOMAIN}/tiers`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				},
			});
			let jsonresponse = await response.json();

			if(jsonresponse.length == 0) {
				await this.createDefaultTiers();
				return this.getTiers();
			}
			else {
				this.tiers = jsonresponse.sort((a, b) => {
					const aCondition = a.entryConditions[0];
					const bCondition = b.entryConditions[0];

					return aCondition?.amount - bCondition?.amount;
				});
				this.minPoints = this.getNextTierGuess();
				this.rowData = this.tiers;
			}

			this.isLoading = false;

			this.getTierSummaries().catch();

			// if (!this.tiers.length) {
			// 	localStorage.removeItem('raleon_vip_last_tier_max');
			// }
		},
		async createDefaultTiers() {
			// Logic to create default tiers
			const defaultTiers = [
				{ name: 'Bronze', amount: 0, summary: { perks: [] } },
				{ name: 'Silver', amount: 5000, summary: { perks: [] } },
				{ name: 'Gold', amount: 10000, summary: { perks: [] } }
			];

			const url = `${URL_DOMAIN}/tier`;
			for(var i = 0; i < defaultTiers.length; i++) {
				await fetch(url, {
					method: 'POST',
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						tierName: defaultTiers[i].name,
						tierDescription: ' ',
						tierImageUrl: '',
						minPoints: defaultTiers[i].amount,
						enabled: false
					}),

				});
			}
		},
		deleteTier(tier) {
			// Call to backend to delete the tier
			fetch(`${URL_DOMAIN}/tier/${tier.id}`, {
				method: 'DELETE',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json'
				},
			}).then(response => {
				if (response.ok) {
					this.tiers = this.tiers.filter(t => t.id !== tier.id);
					this.rowData = this.tiers;
				}
			}).catch(error => console.error('Error:', error));
		},
		async getTierSummaries() {
			return Promise.all(this.tiers.map(tier => this.getTierSummary(tier)));
		},
		async getTierSummary(tier) {
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${tier.loyaltyCampaignId}/summary`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				},
			});
			let jsonresponse = await response.json();
			tier.summary = jsonresponse?.summary;
		},
		async deleteTier(tier) {
			await fetch(`${URL_DOMAIN}/tier/${tier.id}`, {
				method: 'DELETE',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				},
			});

			await this.getTiers();
		},
		async getVIPFeatureSetting() {
			const data = await fetch(`${URL_DOMAIN}/feature-setting/vip`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				},
			});
			const setting = await data.json();
			this.vipLive = setting.live || false;
		},
		addWayToEarn(tier) {
			this.$router.push(`/campaign/${tier.loyaltyCampaignId}/new-ways-to-earn/add?vip`);
		},
		addReward(tier) {
			this.$router.push(`/campaign/${tier.loyaltyCampaignId}/new-shop-reward/add?vip`);
		},
		addPerk(tier) {
			this.$router.push(`/campaign/${tier.loyaltyCampaignId}/new-perk-reward/add?vip`);
		},
		handleWTESummaryEdit(clickData, tier) {
			this.$router.push(`/campaign/${tier.loyaltyCampaignId}/new-ways-to-earn/edit/${clickData.id}?vip`);
		},
		async handleWTESummaryToggle(clickData, tier) {
			let whereClause = {
				id: clickData.id
			}
			let payload = {
				active: clickData.toggle
			}
			const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${tier.loyaltyCampaignId}/loyalty-earns?${queryString}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (response.ok) {
				//Show success message
				await this.getTiers();
				if (clickData.toggle)
					customerIOTrackEvent('VIP WTE Is Live');
				else
					customerIOTrackEvent('VIP WTE Is Hidden');

				this.status.type = 'success';
				this.status.message = clickData.toggle ? 'If your VIP program is enabled, WTE is now visible to your VIP members.' : 'WTE is now hidden from your VIP members.';
			}
			else {
				//Show error message
				this.status.type = 'fail';
				this.status.message = 'Failed to update. Please try again.';
			}
		},
		handleShopItemSummaryEdit(clickData, tier) {
			this.$router.push(`/campaign/${tier.loyaltyCampaignId}/new-shop-reward/edit/${clickData.id}?vip`);
		},
		async handleShopItemSummaryToggle(clickData, tier) {
			console.log(clickData);
			//this.$router.push(`/campaign/${this.campaignId}/shop-reward`);
			let whereClause = {
				id: clickData.id
			}
			let payload = {
				active: clickData.toggle
			}
			const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${tier.loyaltyCampaignId}/loyalty-redemption-shop-items?${queryString}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (response.ok) {
				//Show success message
				await this.getTiers();
				if (clickData.toggle)
					customerIOTrackEvent('VIP Reward Is Live');
				else
					customerIOTrackEvent('VIP Reward Is Hidden');

				this.status.type = 'success';
				this.status.message = clickData.toggle ? 'If your VIP program is enabled, Reward is now visible to your VIP members.' : 'Reward is now hidden from your VIP members.';
			}
			else {
				//Show error message
				this.status.type = 'fail';
				this.status.message = 'Failed to update. Please try again.';
			}

		},
		handlePerkSummaryEdit(clickData, tier) {
			this.$router.push(`/campaign/${tier.loyaltyCampaignId}/new-perk-reward/edit/${clickData.id}?vip`);
		},
		async handlePerkSummaryToggle(clickData, tier) {
			console.log(clickData);
			//this.$router.push(`/campaign/${this.campaignId}/perk-reward`);
			let whereClause = {
				id: clickData.id
			}
			let payload = {
				active: clickData.toggle
			}
			const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${tier.loyaltyCampaignId}/static-effects?${queryString}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (response.ok) {
				//Show success message
				await this.getTiers();
				if (clickData.toggle)
					customerIOTrackEvent('VIP Perk Is Live');
				else
					customerIOTrackEvent('VIP Perk Is Hidden');

				this.status.type = 'success';
				this.status.message = clickData.toggle ? 'If your VIP system is enabled, Perk is now visible to your VIP members.' : 'Perk is now hidden from your VIP members.';
			}
			else {
				//Show error message
				this.status.type = 'fail';
				this.status.message = 'Failed to update. Please try again.';
			}

		},
		async updatePriorityPerk(clickData, tier) {
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${tier.loyaltyCampaignId}/bulk-update-perks`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(clickData),
			});

			if (response.ok) {
				this.status.type = 'success';
				this.status.message = 'Sort order saved.';
			}
			else {
				this.status.type = 'fail';
				this.status.message = 'Failed to update sort order. Please try again.';
			}
		},
		async updatePriorityEarn(clickData, tier) {
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${tier.loyaltyCampaignId}/bulk-update-loyalty-earns`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(clickData),
			});

			if (response.ok) {
				this.status.type = 'success';
				this.status.message = 'Sort order saved.';
			}
			else {
				this.status.type = 'fail';
				this.status.message = 'Failed to update sort order. Please try again.';
			}
		},
		async updatePriorityReward(clickData, tier) {
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${tier.loyaltyCampaignId}/bulk-update-loyalty-redemption-shop-items`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(clickData),
			});

			if (response.ok) {
				this.status.type = 'success';
				this.status.message = 'Sort order saved.';
			}
			else {
				this.status.type = 'fail';
				this.status.message = 'Failed to update sort order. Please try again.';
			}
		},
	},
}

function detimeify(date) {
	if (!date || !date.includes('T')) {
		return date;
	}

	return date.split('T')[0];
}

function entimeify(date) {
	if (date.includes('T')) {
		return date;
	}

	return `${date}T00:00:00.000Z`;
}

function ordinal_suffix_of(i) {
	let j = i % 10,
		k = i % 100;
	if (j === 1 && k !== 11) {
		return i + "st";
	}
	if (j === 2 && k !== 12) {
		return i + "nd";
	}
	if (j === 3 && k !== 13) {
		return i + "rd";
	}
	return i + "th";
}
</script>

<style>
.tier-accordion-group .accordion-closed {
	height: 3.5rem !important;
}

.tier-accordion-group .accordion .accordion-title {
	font-size: 1.5rem;
	font-weight: 700;
}

.tier-accordion-group .accordion .accordion-subtitle {
	/* font-size: 1.5rem; */
	font-weight: 700;
}

.tier-accordion-group .accordion .accordion-body {
	padding: 1rem;
}
</style>

<style scoped>
.rotate-90 {
	transform: rotate(90deg);
	transition: transform 0.3s;
}

.test-container {
	margin: 0 -1em;
}

.test {
	width: 50%;
	padding-right: 0.4em;
}

@media all and (max-width: 768px) {
	.test-container {
		width: 100%;
		margin: 0;
		margin-left: -0.5em;
	}

	.test {
		width: 100%;
		padding: 0;
		/* padding-left: 0.4em; */
	}
}

.campaign-name {
	font-family: 'Inter', sans-serif;
	font-style: normal;
	font-weight: 500;
	font-size: 40px;
	line-height: 65px;
	/* identical to box height, or 162% */
	letter-spacing: -0.04em;
	text-transform: uppercase;

	color: #5E48F8;
	/* -webkit-background-clip: text; */
	/* -webkit-text-fill-color: transparent; */
	/* background-clip: text; */
	/* text-fill-color: transparent; */
}

.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

.accordion-animation-enter-active,
.accordion-animation-leave-active {
	transition: opacity 0.5s, transform 0.5s;
	overflow: hidden;
}

.accordion-animation-enter,
.accordion-animation-leave-to {
	opacity: 0;
	transform: scaleY(0);
}

.accordion-animation-enter-to,
.accordion-animation-leave {
	opacity: 1;
	transform: scaleY(1);
}

.accordion-body {
	transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out;
}

.fade-enter-active,
.fade-leave-active {
	transition-property: opacity, transform;
	transition-duration: 500ms;
	transition-timing-function: ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
	opacity: 0;
	transform: translateX(10px);
}

.fade-enter-to,
.fade-leave-from {
	opacity: 1;
	transform: translateX(0);
}
</style>


<style scoped>
.toggle-checkbox {
	opacity: 0;
	width: 0;
	height: 0;
}

.toggle-label {
	display: block;
	overflow: hidden;
	cursor: pointer;
	height: 24px;
	/* Height of the toggle */
	padding: 0;
	line-height: 24px;
	background: #bbb;
	/* Background of the toggle */
	border-radius: 24px;
	transition: background-color 0.2s;
}

.toggle-label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 2px;
	width: 20px;
	/* Width of the toggle handle */
	height: 20px;
	/* Height of the toggle handle */
	border-radius: 20px;
	background: #fff;
	/* Background of the toggle handle */
	transition: 0.2s;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox:checked+.toggle-label:after {
	transform: translateX(18px);
}

.toggle-checkbox:checked {
	right: 0;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox {
	right: 4px;
}

.toggle-label {
	transition: background-color 0.2s;
}
</style>
