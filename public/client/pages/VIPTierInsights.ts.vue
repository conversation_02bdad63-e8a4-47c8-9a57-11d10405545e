<template>
	<div class="p-2 sm:p-7 mr-24">
		<div class="text-neutral-800 text-opacity-70 sm:text-4xl md:text-6xl lg:text-7xl font-normal font-['Open Sans']">VIP Tier Insights</div>
		<div class="inline-flex mt-4 items-center cursor-pointer" @click="this.$router.push('/loyalty/analytics');">
			<svg width="24" height="24" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg"
				class="hover:text-ralprimary-dark transition-color duration-300">
				<path
					d="M15.3333 12L10.3333 17M10.3333 17L15.3333 22M10.3333 17H23.6667M32 17C32 8.71573 25.2843 2 17 2C8.71573 2 2 8.71573 2 17C2 25.2843 8.71573 32 17 32C25.2843 32 32 25.2843 32 17Z"
					stroke="#202020" stroke-opacity="0.8" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
			</svg>
			<span class="ml-3 text-lg transition-color duration-300">Analytics</span>
		</div>
		<div class="my-3">
			<LearnMoreText text="Read about making data-driven loyalty decisions" url="https://docs.raleon.io/docs/program-return-on-investment" />
			<span class="text-slate-800 text-sm font-medium font-['Inter']"> in our Loyalty Academy.</span>
		</div>
		<div class="mt-10">
			<RevenuePotentialText />
		</div>
		<div v-if="!hasData" class="flex flex-col items-center justify-center h-22 mb-2 mt-10">
				<svg xmlns="http://www.w3.org/2000/svg" height="52" viewBox="0 -960 960 960" width="52" fill="#5A16C9"><path d="M280-280h80v-280h-80v280Zm160 0h80v-400h-80v400Zm160 0h80v-160h-80v160ZM200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm0-560v560-560Z"/></svg>
				<p class="text-xl text-ralblack-primary mt-2">Your VIP tier insights will show up within 24 hours.</p>
		</div>
		<div v-if="hasData">
			<div class="my-4 flex overflow-x-hidden">
				<div class="w-[calc(50%-0.5rem)] p-2 bg-white rounded-2xl mr-4">
					<PieChart
						@loading="handleLoading('vip_tier_metrics', $event)"
						:metricName="'vip_tier_metrics'"
						:keyFieldArray="['customer_count']"
						:type="'multirowcategory'"
						:chartTitle="'Customers in each Tier'"
						:keyLabel="''"
						:valueLabel="'Customer Tier'"
						:groupBy="''"
						:calculation="''"
					/>
				</div>
				<div class="w-[calc(50%-0.5rem)] p-2 bg-white rounded-2xl">
					<PieChart
						@loading="handleLoading('vip_tier_metrics', $event)"
						:metricName="'vip_tier_metrics'"
						:keyFieldArray="['revenue']"
						:type="'multirowcategory'"
						:chartTitle="'Customer total spend in each Tier'"
						:keyLabel="''"
						:valueLabel="'Customer Tier'"
						:groupBy="''"
						:calculation="''"
					/>
				</div>
			</div>
			<div class="my-4 overflow-x-hidden bg-white rounded-2xl">
				<BarChart
					@loading="handleLoading('vip_tier_metrics', $event)"
					:metricName="'vip_tier_metrics'"
					:keyFieldArray="['aov']"
					:type="'multirowcategory'"
					:chartTitle="'Average Order Value in each Tier'"
					:keyLabel="'AOV'"
					:groupBy="''"
					:calculation="''"
					:valueLabel="'Customer Tier'"
				/>
			</div>
			<div class="my-4 overflow-x-hidden bg-white rounded-2xl">
				<BarChart
					@loading="handleLoading('vip_tier_metrics', $event)"
					:metricName="'vip_tier_metrics'"
					:keyFieldArray="['avg_ltv']"
					:type="'multirowcategory'"
					:chartTitle="'Customer LTV in each Tier'"
					:keyLabel="'LTV'"
					:groupBy="''"
					:calculation="''"
					:valueLabel="'Customer Tier'"
				/>
			</div>
			<div class="my-4 overflow-x-hidden bg-white rounded-2xl">
				<BarChart
					@loading="handleLoading('vip_tier_top_coupons', $event)"
					:metricName="'vip_tier_top_coupons'"
					:keyFieldArray="['used_count']"
					:customLabelsArray="['Used']"
					:type="'top10'"
					:chartTitle="'Top VIP Coupons Used'"
					:keyLabel="'VIP Coupons Used'"
					:startDate="latest"
					:calculation="''"
					:valueLabel="'Customer Segment'"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';
import { dashboards } from './AnalyticsDashboard.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import BarChart from '../components/charts/BarChart.ts.vue';
import LineChart from '../components/charts/LineChart.ts.vue';
import PieChart from '../components/charts/PieChart.ts.vue';
import RevenuePotentialText from '../components/charts/RevenuePotentialText.ts.vue';
import { getMetric } from '../services/metrics.js';
import RaleonTable from '../../client-old/pages/component/RaleonTable.vue';
import * as CurrencyUtils from '../services/currency.js';

export default {
	components: {
		LearnMoreText,
		BarChart,
		LineChart,
		PieChart,
		RevenuePotentialText,
		RaleonTable
	},
	data() {
		return {
			dashboards,
			hasData: true,
			tableHasData: false,
			rowData: []
		}
	},
	computed: {
	},
	async mounted() {

	},
	methods: {
		handleLoading(chart, isLoading) {
			//this.hasData = isLoading;
		}
	}
}
</script>

