<template>
	<div class="flex flex-col p-6 bg-white shadow-sm sm:flex-row items-center justify-between">
		<div>
			<div class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary">
				Email Calendar
			</div>
			<p class="mt-1 text-gray-500">Below are the emails prepared by your AI Marketing Team.</p>
		</div>


	</div>

	<div v-if="!isLoading && projects.length === 0" class="w-full flex items-center justify-center p-4">
	   <div class="max-w-xl w-full p-8 flex flex-col items-center text-center space-y-6 bg-white rounded-lg shadow-lg">
      <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
        <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#400F92"><path d="M284-506q14-28 29-54t33-52l-56-11-84 84 78 33Zm482-275q-70 2-149.5 41T472-636q-42 42-75 90t-49 90l114 113q42-16 90-49t90-75q65-65 104-144t41-149q0-4-1.5-8t-4.5-7q-3-3-7-4.5t-8-1.5ZM546-541q-23-23-23-56.5t23-56.5q23-23 57-23t57 23q23 23 23 56.5T660-541q-23 23-57 23t-57-23Zm-34 262 33 79 84-84-11-56q-26 18-52 32.5T512-279Zm351-534q8 110-36 214.5T688-399l20 99q4 20-2 39t-20 33L560-102q-15 15-36 11.5T495-114l-61-143-171-171-143-61q-20-8-24-29t11-36l126-126q14-14 33.5-20t39.5-2l99 20q95-95 199.5-139T819-857q8 1 16 4.5t14 9.5q6 6 9.5 14t4.5 16ZM157-321q35-35 85.5-35.5T328-322q35 35 34.5 85.5T327-151q-48 48-113.5 57T82-76q9-66 18-131.5T157-321Zm57 56q-17 17-23.5 41T180-175q25-4 49-10t41-23q12-12 13-29t-11-29q-12-12-29-11.5T214-265Z"/></svg>
      </div>

      <div class="space-y-3">
        <h2 class="text-2xl font-semibold text-gray-900">
          Your AI Team is Waiting.
        </h2>
        <p class="text-gray-600 max-w-md">
          There aren't any emails here yet. Get started by creating a new campaign with Raleon's AI Strategst
        </p>
      </div>

      <button
        class="bg-ralprimary-dark hover:bg-ralprimary-dark/80 text-white px-6 py-2 rounded-lg flex items-center gap-2"
		@click.stop="this.$router.push('/chat');"
      >
	  <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="m240-240-92 92q-19 19-43.5 8.5T80-177v-623q0-33 23.5-56.5T160-880h640q33 0 56.5 23.5T880-800v480q0 33-23.5 56.5T800-240H240Zm-34-80h594v-480H160v525l46-45Zm-46 0v-480 480Z"/></svg>
        Create Emails with Your AI Strategist
      </button>

      <p class="text-sm text-gray-500">
        Your AI Marketing Strategist will help you plan, create, and execute marketing campaigns tailored to your brand.
      </p>
    </div>
  </div>

    <!-- Content -->
    <div class="p-6" v-if="isLoading">
      <!-- Skeleton Loading -->
      <div class="space-y-4 relative mt-4">
        <div v-for="i in 3" :key="i" class="w-full">
          <div class="bg-white rounded-lg p-6 border border-purple-100 shadow-sm mb-4">
            <!-- Header -->
            <div class="flex gap-4">
              <div class="h-12 w-12 bg-purple-50 rounded-full animate-pulse"></div>
              <div class="flex-1">
                <div class="h-5 bg-purple-100 rounded-full w-3/4 animate-pulse"></div>
                <div class="h-4 bg-purple-50 rounded-full w-1/2 animate-pulse mt-2"></div>
              </div>
            </div>

            <!-- Skeleton stats -->
            <div class="mt-6 grid grid-cols-2 gap-4">
              <div class="space-y-2">
                <div class="h-3 bg-purple-50 rounded-full w-3/4 animate-pulse"></div>
                <div class="h-4 bg-purple-100 rounded-full w-1/2 animate-pulse"></div>
              </div>
              <div class="space-y-2">
                <div class="h-3 bg-purple-50 rounded-full w-3/4 animate-pulse"></div>
                <div class="h-4 bg-purple-100 rounded-full w-1/2 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="p-6" v-if="!isLoading && projects.length > 0">
      <div class="border-b border-gray-300 mb-6">
        <div class="flex space-x-8">
          <div class="whitespace-nowrap py-4 px-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer flex items-center"
          @click.stop="planning = true; activePlanning = false; archive = false"
          :class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': planning == true}"
          >
            Pending Emails
            <div class="flex items-center justify-center min-w-5 h-5 px-1.5 ml-2 text-xs text-center font-semibold rounded-full bg-[#32316A] text-white">
              {{ notStartedCount }}
            </div>
          </div>

          <div class="whitespace-nowrap py-4 px-1 border-b-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer flex items-center"
          @click.stop="planning = false; activePlanning = false; archive = true;"
          :class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': archive == true}"
          >
            Sent Emails
            <div class="flex items-center justify-center min-w-5 h-5 px-1.5 ml-2 text-xs text-center font-semibold rounded-full bg-[#32316A] text-white">
              {{ doneCount }}
            </div>
          </div>
        </div>
      </div>

      <!-- Plan Filter Notification -->
      <div v-if="activePlanId" class="mb-4 p-3 bg-purple-50 rounded-lg flex items-center justify-between">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-purple-600 mr-2" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
          </svg>
          <span class="text-purple-800">
            Showing emails for plan: <strong>{{ activePlanName }}</strong>
          </span>
        </div>
        <button
          @click="clearPlanFilter"
          class="px-2 py-1 text-sm text-purple-700 hover:text-purple-900 hover:bg-purple-100 rounded transition-colors"
        >
          Show All Emails
        </button>
      </div>

      <!-- Calendar View -->
      <EmailCalendarView
        :current-month="currentMonth"
        :current-year="currentYear"
        :calendar-days="calendarDays"
        :days-of-week="daysOfWeek"
        @previous-month="previousMonth"
        @next-month="nextMonth"
        @go-to-current="goToCurrentMonth"
        @navigate="navigateToProject"
        @show-day-detail="showDayDetail"
      />
    </div>

  <!-- Day Detail Modal Component -->
  <EmailDayDetailModal
    :day="selectedDay"
    @close="selectedDay = null"
    @navigate="navigateToProject"
  />
</template>

<script>

  import * as Utils from '../../client-old/utils/Utils';
  import PrimaryButton from '../components/PrimaryButton.ts.vue';
  import ToggleItem from '../components/ToggleItem.ts.vue';
  import EmailCalendarView from '../components/EmailCalendarView.ts.vue';
  import EmailDayDetailModal from '../components/EmailDayDetailModal.ts.vue';
  import { customerIOTrackEvent } from '../services/customerio.js';
  const URL_DOMAIN = Utils.URL_DOMAIN;

  export default {
  name: 'SignalLibrary',
  components: {
    PrimaryButton,
    ToggleItem,
    EmailCalendarView,
    EmailDayDetailModal
  },

  data() {
    return {
      tasks: [],
      projects: [],
      planning: true,
      activePlanning: false,
      archive: false,
      isLoading: true,
      activePlanId: null,
      activePlanName: '',
      currentView: 'calendar',
      currentMonth: new Date().getMonth(),
      currentYear: new Date().getFullYear(),
      daysOfWeek: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
      selectedDay: null
    }
  },

  computed: {
    // Map status values to display text
    getDisplayStatus() {
      return (status) => {
        const statusMap = {
          'Ready': 'Not Started',
          'In Progress': 'In Progress',
          'In Review': 'In Review',
          'Not Started': 'Not Started',
          'Complete': 'Complete',
          'Archive': 'Archive',
          'Processing': 'Processing'
        };
        return statusMap[status] || status;
      };
    },

    filteredProjects() {
      let filtered = [];

      // First filter out projects with archived plans
      const nonArchivedProjects = this.projects.filter(project => !project.planArchived);

      // Then apply plan filter if active
      if (this.activePlanId) {
        filtered = nonArchivedProjects.filter(project => String(project.plannerPlanVersionId) === String(this.activePlanId));
      } else {
        filtered = nonArchivedProjects;
      }

      // Exclude archived campaigns
      filtered = filtered.filter(project => project.status !== 'Archive');

      // Then apply status filter
      if (this.archive) {
        return filtered.filter(project => project.status === 'Complete');
      } else {
        return filtered.filter(project => project.status !== 'Complete');
      }
    },
    notStartedCount() {
      if (this.activePlanId) {
        return this.projects.filter(project =>
          project.status !== 'Complete' &&
          project.status !== 'Archive' &&
          String(project.plannerPlanVersionId) === String(this.activePlanId)
        ).length;
      } else {
        return this.projects.filter(project => project.status !== 'Complete' && project.status !== 'Archive').length;
      }
    },
    doneCount() {
      if (this.activePlanId) {
        return this.projects.filter(project =>
          project.status === 'Complete' &&
          String(project.plannerPlanVersionId) === String(this.activePlanId)
        ).length;
      } else {
        return this.projects.filter(project => project.status === 'Complete').length;
      }
    },
    // Calendar related computed properties
    calendarTitle() {
      return new Date(this.currentYear, this.currentMonth).toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric'
      });
    },

    calendarDays() {
      const days = [];
      const today = new Date();
      // Create a consistent format for today's date in UTC to compare against
      const todayDateStr = today.toISOString().split('T')[0];

      // Get first day of the current month view
      const firstDayOfMonth = new Date(this.currentYear, this.currentMonth, 1);
      const dayOfWeek = firstDayOfMonth.getDay();

      // Get the date for the first calendar cell (may be in previous month)
      const startDate = new Date(firstDayOfMonth);
      startDate.setDate(startDate.getDate() - dayOfWeek);

      // Get projects grouped by date for efficient lookup
      const projectsByDate = this.getProjectsByDate();

      // Generate 42 days (6 weeks) to ensure we have enough rows
      for (let i = 0; i < 42; i++) {
        const currentDate = new Date(startDate);
        currentDate.setDate(startDate.getDate() + i);

        // Format the date in YYYY-MM-DD format, preserving the date part
        const dateStr = currentDate.toISOString().split('T')[0];

        days.push({
          date: dateStr,
          isCurrentMonth: currentDate.getMonth() === this.currentMonth,
          isToday: dateStr === todayDateStr,
          emails: projectsByDate[dateStr] || [],
          totalEmails: (projectsByDate[dateStr] || []).length
        });
      }

      // Only return the days we need (if the last week has no days in the current month, exclude it)
      const lastDaysCurrentMonth = days.slice(35).some(day => day.isCurrentMonth);
      return lastDaysCurrentMonth ? days : days.slice(0, 35);
    },

    // Check if there are any emails
    hasAnyEmails() {
      return this.filteredProjects && this.filteredProjects.length > 0;
    },


  },

  methods: {
    // New calendar methods
    getProjectsByDate() {
      const projectsByDate = {};

      this.filteredProjects.forEach(project => {
        if (!project.scheduledFor) return;

        // Create a date object from the UTC time string
        const utcDate = new Date(project.scheduledFor);

        // Create a date string that preserves the UTC date
        // This ensures we're using the date as stored, not as converted to local time
        const dateStr = utcDate.toISOString().split('T')[0];

        if (!projectsByDate[dateStr]) {
          projectsByDate[dateStr] = [];
        }

        projectsByDate[dateStr].push(project);
      });

      return projectsByDate;
    },

    previousMonth() {
      if (this.currentMonth === 0) {
        this.currentMonth = 11;
        this.currentYear--;
      } else {
        this.currentMonth--;
      }
    },

    nextMonth() {
      if (this.currentMonth === 11) {
        this.currentMonth = 0;
        this.currentYear++;
      } else {
        this.currentMonth++;
      }
    },

    goToCurrentMonth() {
      this.currentMonth = new Date().getMonth();
      this.currentYear = new Date().getFullYear();
    },

    showDayDetail(day) {
      // Set the selected day to show the modal
      this.selectedDay = day;
    },

    // Used by both views - returns hex color values matching TopBar status colors
    getStatusColor(status) {
      if (status === 'Processing') return '#9CA3AF'; // Gray for processing
      const colors = {
        // Legacy status mappings to match TopBar color scheme
        'Ready': '#6366F1', // Indigo - maps to Campaign Ready
        'Not Started': '#6366F1', // Indigo - maps to Campaign Ready
        'In Progress': '#A78BFA', // Light purple - maps to In Copywriting
        'In Review': '#34D399', // Green - maps to In Review
        'Complete': '#059669', // Green - maps to Done/Complete
        'Archive': '#6B7280', // Slate - maps to Archive
        // New status values (matching TopBar statusOptions)
        'Campaign Ready': '#6366F1', // Indigo
        'Ready for Copywriting': '#8B5CF6', // Purple
        'In Copywriting': '#A78BFA', // Light purple
        'Ready for Design': '#EC4899', // Pink
        'In Design': '#F472B6', // Light pink
        'Quality Check': '#F59E0B', // Amber
        'Ready for Review': '#10B981', // Emerald
        'Approved': '#3B82F6' // Blue
      };
      return colors[status] || '#9CA3AF'; // Default gray if not found
    },

    formatScheduledTime(dateString) {
      // Create date object from the UTC string
      const date = new Date(dateString);

      // Instead of letting the browser automatically convert to local time,
      // we'll explicitly format it using the UTC date components
      const options = {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
        timeZone: 'UTC'  // Important: Treat the date as UTC
      };

      return new Intl.DateTimeFormat('en-US', options).format(date);
    },

    getDayAbbreviation(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        timeZone: 'UTC'
      }).substring(0, 2);
    },

    formatDayHeader(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        timeZone: 'UTC'
      });
    },

    getWeekRange(date) {
      const day = date.getDay();
      const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
      const monday = new Date(date);
      monday.setDate(diff);

      const sunday = new Date(monday);
      sunday.setDate(monday.getDate() + 6);

      return {
        start: monday,
        end: sunday
      };
    },

    formatWeekHeader(startDate, endDate) {
      const startMonth = startDate.toLocaleDateString('en-US', { month: 'short' });
      const endMonth = endDate.toLocaleDateString('en-US', { month: 'short' });
      const startDay = startDate.getDate();
      const endDay = endDate.getDate();

      if (startMonth === endMonth) {
        return `${startMonth} ${startDay} - ${endDay}`;
      } else {
        return `${startMonth} ${startDay} - ${endMonth} ${endDay}`;
      }
    },

    navigateToProject(planId) {
  		// Pass the plan ID as a query parameter if it exists for the project
      const project = this.projects.find(p => p.id === planId);
      const plannerPlanVersionId = project?.plannerPlanVersionId;

      if (plannerPlanVersionId) {
        this.$router.push({
          path: '/ai-strategist/tasks/' + planId,
          //query: { planId: plannerPlanVersionId }
        });
      } else {
        this.$router.push('/ai-strategist/tasks/' + planId);
      }
    },

    getIconComponent(taskType) {
      // Map task types to icons
      const typeToIcon = {
        'Email': 'mail',
        'Message': 'message-square',
        'Campaign': 'campaign',
        'Loyalty': 'loyalty',
        'Reward': 'gift',
        'Analytics': 'bar-chart'
      }
      return typeToIcon[taskType] || 'settings'
    },

    async fetchTasks() {
      const response = await fetch(`${URL_DOMAIN}/planner/tasks`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      return data;
    },

    async loadTasksAndProjects() {
      this.tasks = await this.fetchTasks();
      this.projects = this.tasks
        .filter(task => !task.isTemplate && task.status !== 'Archive')
        .map(task => ({
        id: task.id,
        title: task.name,
        type: task.taskType,
        status: task.status,
        description: task.description,
        scheduledFor: task.plannerCampaign.scheduledDate,
        plannerCampaignId: task.plannerCampaignId,
        plannerPlanVersionId: task.plannerPlanVersionId,
        planName: task.plan?.name || 'View Plan',
        planArchived: task.plan?.archived || false
      }));

      // If we have an activePlanId but no activePlanName yet, find it
      if (this.activePlanId && !this.activePlanName) {
        const planProject = this.projects.find(p => String(p.plannerPlanVersionId) === String(this.activePlanId));
        if (planProject) {
          this.activePlanName = planProject.planName;
        }
      }
    },

    clearPlanFilter() {
      this.activePlanId = null;
      this.activePlanName = '';
      // Update the URL without refreshing the page
      this.$router.replace({
        path: this.$route.path,
        query: { ...this.$route.query, plan: undefined }
      });
    },

    applyPlanFilter(planId, planName) {
      this.activePlanId = planId;
      this.activePlanName = planName;
      // Update the URL without refreshing the page
      this.$router.replace({
        path: this.$route.path,
        query: { ...this.$route.query, plan: planId }
      });

      // Jump to the first month with campaigns for this plan
      this.jumpToFirstMonthWithCampaigns();
    },

    jumpToFirstMonthWithCampaigns() {
      // Only perform this when a plan filter is active
      if (!this.activePlanId) return;

      // Find the earliest campaign date for the filtered plan
      const filteredProjects = this.filteredProjects.filter(project => project.scheduledFor);

      if (filteredProjects.length === 0) return;

      // Sort by scheduled date (ascending)
      const sortedProjects = [...filteredProjects].sort((a, b) => {
        return new Date(a.scheduledFor) - new Date(b.scheduledFor);
      });

      // Get the earliest campaign date
      const earliestDate = new Date(sortedProjects[0].scheduledFor);

      // Set the calendar to that month and year
      this.currentMonth = earliestDate.getMonth();
      this.currentYear = earliestDate.getFullYear();
    },


  },

  async mounted() {
    this.isLoading = true;
    customerIOTrackEvent('Viewed Email Calendar');

    // Check if plan ID is in the route query
    if (this.$route.query.plan) {
      this.activePlanId = this.$route.query.plan;
    }



    try {
      await this.loadTasksAndProjects();

      // If a plan filter is active, jump to the first month with campaigns
      if (this.activePlanId) {
        this.jumpToFirstMonthWithCampaigns();
      }



    } catch (error) {
      console.error('Error in mounted:', error);
    } finally {
      this.isLoading = false;
    }
  },


}
</script>

<style>
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
.animate-shimmer {
  animation: shimmer 2.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes thinking {
  0%, 100% { opacity: 0.3; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1); }
}
.animate-thinking-1 {
  animation: thinking 1.5s infinite;
}
.animate-thinking-2 {
  animation: thinking 1.5s infinite;
  animation-delay: 0.2s;
}
.animate-thinking-3 {
  animation: thinking 1.5s infinite;
  animation-delay: 0.4s;
}

/* Bounce animation for robot icon */
@keyframes bounce-subtle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}
.animate-bounce-subtle {
  animation: bounce-subtle 1.5s ease-in-out infinite;
}

/* Fade transition */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}
</style>
