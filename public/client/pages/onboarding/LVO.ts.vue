<template>
  <div
    class="fixed inset-0 bg-cover bg-center"
  >
  <img
      src="../../images/onboarding-app-background.png"
      alt="App Screenshot"
      class="w-full h-full object-cover"
    />
    <!-- Dark Overlay and Blur -->
    <div class="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"></div>


	<div class="fixed inset-0 z-50 overflow-y-auto">
		<div class="flex min-h-screen items-center justify-center p-6">
			<div class="flex flex-col items-center justify-center p-6">

				<transition name="fade" mode="out-in">
					<div :key="onboardingStep">
						<div v-if="onboardingStep == 0" class="w-full max-w-3xl bg-white rounded-lg p-12 shadow-lg">
						<div  class="max-w-md mx-auto space-y-12">
							<div class="flex justify-center">
								<svg width="147" height="24" viewBox="0 0 147 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" clip-rule="evenodd" d="M25.2297 15.7711L12.7 0L0.170328 15.7711L2.49907 18.1962L0 21.2107V24H5.35415L12.7 14.8335L20.0459 24H25.4V21.2107L22.9009 18.1962L25.2297 15.7711ZM12.7 5.89126L3.75309 16.6835L2.76044 15.6498L12.7 3.13888L22.6396 15.6498L21.6469 16.6835L12.7 5.89126ZM1.95385 21.9131L12.7 8.95052L23.4462 21.9131V22.0495H20.9849L12.7 11.7112L4.41508 22.0495H1.95385V21.9131Z" fill="url(#paint0_linear_736_89064)"/>
									<path d="M18.1643 24L12.7002 17.0577L7.23607 24H9.72088L12.7002 20.2147L15.6795 24H18.1643Z" fill="url(#paint1_linear_736_89064)"/>
									<path d="M41.4004 17.8333V6.16667H46.2081C47.2431 6.16667 48.1278 6.33333 48.8623 6.66667C49.608 7 50.1811 7.47778 50.5818 8.1C50.9824 8.72222 51.1827 9.46111 51.1827 10.3167C51.1827 11.1722 50.9824 11.9111 50.5818 12.5333C50.1811 13.1444 49.608 13.6167 48.8623 13.95C48.1278 14.2722 47.2431 14.4333 46.2081 14.4333H42.6023L43.5705 13.45V17.8333H41.4004ZM49.046 17.8333L46.0912 13.6H48.4116L51.3831 17.8333H49.046ZM43.5705 13.6833L42.6023 12.65H46.1079C47.065 12.65 47.7828 12.4444 48.2614 12.0333C48.7511 11.6222 48.9959 11.05 48.9959 10.3167C48.9959 9.57222 48.7511 9 48.2614 8.6C47.7828 8.2 47.065 8 46.1079 8H42.6023L43.5705 6.93333V13.6833Z" fill="#202020"/>
									<path d="M58.9693 17.8333L64.2277 6.16667H66.3645L71.6396 17.8333H69.3693L64.8454 7.31667H65.7134L61.2062 17.8333H58.9693ZM61.3898 15.1333L61.9741 13.4333H68.2842L68.8685 15.1333H61.3898Z" fill="#202020"/>
									<path d="M80.1369 17.8333V6.16667H82.3071V16H88.4169V17.8333H80.1369Z" fill="#202020"/>
									<path d="M99.1446 11.0167H104.954V12.8H99.1446V11.0167ZM99.3116 16.0167H105.905V17.8333H97.1414V6.16667H105.672V7.98333H99.3116V16.0167Z" fill="#202020"/>
									<path d="M120.811 18C119.899 18 119.059 17.85 118.291 17.55C117.523 17.25 116.855 16.8333 116.287 16.3C115.72 15.7556 115.28 15.1222 114.969 14.4C114.657 13.6667 114.501 12.8667 114.501 12C114.501 11.1333 114.657 10.3389 114.969 9.61667C115.28 8.88333 115.72 8.25 116.287 7.71667C116.855 7.17222 117.523 6.75 118.291 6.45C119.059 6.15 119.893 6 120.795 6C121.707 6 122.542 6.15 123.299 6.45C124.067 6.75 124.734 7.17222 125.302 7.71667C125.869 8.25 126.309 8.88333 126.621 9.61667C126.932 10.3389 127.088 11.1333 127.088 12C127.088 12.8667 126.932 13.6667 126.621 14.4C126.309 15.1333 125.869 15.7667 125.302 16.3C124.734 16.8333 124.067 17.25 123.299 17.55C122.542 17.85 121.713 18 120.811 18ZM120.795 16.1C121.385 16.1 121.93 16 122.431 15.8C122.931 15.6 123.365 15.3167 123.733 14.95C124.1 14.5722 124.384 14.1389 124.584 13.65C124.796 13.15 124.901 12.6 124.901 12C124.901 11.4 124.796 10.8556 124.584 10.3667C124.384 9.86667 124.1 9.43333 123.733 9.06667C123.365 8.68889 122.931 8.4 122.431 8.2C121.93 8 121.385 7.9 120.795 7.9C120.205 7.9 119.66 8 119.159 8.2C118.669 8.4 118.235 8.68889 117.857 9.06667C117.489 9.43333 117.2 9.86667 116.989 10.3667C116.788 10.8556 116.688 11.4 116.688 12C116.688 12.5889 116.788 13.1333 116.989 13.6333C117.2 14.1333 117.489 14.5722 117.857 14.95C118.224 15.3167 118.658 15.6 119.159 15.8C119.66 16 120.205 16.1 120.795 16.1Z" fill="#202020"/>
									<path d="M136.4 17.8333V6.16667H138.186L145.514 15.15H144.629V6.16667H146.783V17.8333H144.997L137.668 8.85H138.553V17.8333H136.4Z" fill="#202020"/>
									<defs>
									<linearGradient id="paint0_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
									<stop stop-color="#6536E2"/>
									<stop offset="1" stop-color="#1E90DB"/>
									</linearGradient>
									<linearGradient id="paint1_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
									<stop stop-color="#6536E2"/>
									<stop offset="1" stop-color="#1E90DB"/>
									</linearGradient>
									</defs>
								</svg>
							</div>
							<!-- Header -->
							<div class="text-center space-y-4 mb-16">
								<span class="inline-flex items-center rounded-full bg-indigo-50 px-3 py-1 text-sm font-medium text-indigo-600">
								Step 1 of 4
								</span>
								<h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
								Welcome to your LTV growth journey.
								</h1>
								<p class="text-lg text-gray-600 max-w-2xl mx-auto">
								Let's get to know you better.
								</p>
							</div>

							<div class="space-y-10">

							<!-- Goals Selection -->
							<div class="space-y-4">
								<label class="block text-lg font-medium text-gray-900">What brings you here?</label>
								<div class="grid grid-cols-1 gap-3">
								<!-- AI Strategist -->
								<button
									@click="selectedGoal = 'strategist'"
									:class="[
									'flex items-center p-4 rounded-lg border-2 transition-all duration-200',
									selectedGoal === 'strategist' ? 'border-indigo-600 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'
									]"
								>
									<svg v-if="selectedGoal != 'strategist'" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24" :class="['w-5 h-5 mr-3', selectedGoal === 'strategist' ? 'text-indigo-600' : 'text-gray-600']">
									<path d="M0 0h24v24H0z" fill="none"/>
									<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
									</svg>

									<svg v-if="selectedGoal === 'strategist'" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24" :class="['w-5 h-5 mr-3', selectedGoal === 'strategist' ? 'text-indigo-600' : 'text-gray-600']">
									<path d="M0 0h24v24H0z" fill="none"/>
									<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" fill="#4f46e5"/>
									</svg>
									<span :class="['flex-grow text-left font-medium', selectedGoal === 'strategist' ? 'text-indigo-600' : 'text-gray-900']">
									Work with AI Marketing Strategist
									</span>
									<svg v-if="selectedGoal === 'strategist'" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24" class="w-5 h-5 text-indigo-600">
									<path d="M0 0h24v24H0z" fill="none"/>
									<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="#4f46e5"/>
									</svg>
								</button>

								<!-- AI Segmentation -->
								<button
									@click="selectedGoal = 'segmentation'"
									:class="[
									'flex items-center p-4 rounded-lg border-2 transition-all duration-200',
									selectedGoal === 'segmentation' ? 'border-indigo-600 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'
									]"
								>
									<svg v-if="selectedGoal === 'segmentation'" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24" :class="['w-5 h-5 mr-3', selectedGoal === 'segmentation' ? 'text-indigo-600' : 'text-gray-600']">
									<path d="M0 0h24v24H0z" fill="none"/>
									<path d="M5 9.2h3V19H5zM10.6 5h2.8v14h-2.8zm5.6 8H19v6h-2.8z" fill="#4f46e5"/>
									</svg>

									<svg v-if="selectedGoal != 'segmentation'" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24" :class="['w-5 h-5 mr-3', selectedGoal === 'segmentation' ? 'text-indigo-600' : 'text-gray-600']">
									<path d="M0 0h24v24H0z" fill="none"/>
									<path d="M5 9.2h3V19H5zM10.6 5h2.8v14h-2.8zm5.6 8H19v6h-2.8z"/>
									</svg>
									<span :class="['flex-grow text-left font-medium', selectedGoal === 'segmentation' ? 'text-indigo-600' : 'text-gray-900']">
									Create AI Segments
									</span>
									<svg v-if="selectedGoal === 'segmentation'" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24" class="w-5 h-5 text-indigo-600">
									<path d="M0 0h24v24H0z" fill="none"/>
									<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="#4f46e5"/>
									</svg>
								</button>


								</div>
							</div>

							</div>

							<!-- Continue Button -->
							<button
								:disabled="!selectedType || !selectedGoal || !isEmailValid"
								class="w-full bg-indigo-600 text-white text-lg font-medium py-4 px-6 rounded-lg disabled:opacity-40 disabled:cursor-not-allowed hover:bg-indigo-700 transition-all duration-200"
								@click="goNext()"
								>
								Continue
							</button>
						</div>
						</div>

						<!-- LVO Death Star -->
						<div v-else-if="onboardingStep == 1" class="relative w-full max-w-6xl">
							<!-- Main Content Card -->
							<div class="relative bg-white backdrop-blur-xl rounded-2xl border border-white shadow-2xl shadow-indigo-500/5 p-12 space-y-12">
								<div class="flex justify-center">
								<svg width="147" height="24" viewBox="0 0 147 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" clip-rule="evenodd" d="M25.2297 15.7711L12.7 0L0.170328 15.7711L2.49907 18.1962L0 21.2107V24H5.35415L12.7 14.8335L20.0459 24H25.4V21.2107L22.9009 18.1962L25.2297 15.7711ZM12.7 5.89126L3.75309 16.6835L2.76044 15.6498L12.7 3.13888L22.6396 15.6498L21.6469 16.6835L12.7 5.89126ZM1.95385 21.9131L12.7 8.95052L23.4462 21.9131V22.0495H20.9849L12.7 11.7112L4.41508 22.0495H1.95385V21.9131Z" fill="url(#paint0_linear_736_89064)"/>
									<path d="M18.1643 24L12.7002 17.0577L7.23607 24H9.72088L12.7002 20.2147L15.6795 24H18.1643Z" fill="url(#paint1_linear_736_89064)"/>
									<path d="M41.4004 17.8333V6.16667H46.2081C47.2431 6.16667 48.1278 6.33333 48.8623 6.66667C49.608 7 50.1811 7.47778 50.5818 8.1C50.9824 8.72222 51.1827 9.46111 51.1827 10.3167C51.1827 11.1722 50.9824 11.9111 50.5818 12.5333C50.1811 13.1444 49.608 13.6167 48.8623 13.95C48.1278 14.2722 47.2431 14.4333 46.2081 14.4333H42.6023L43.5705 13.45V17.8333H41.4004ZM49.046 17.8333L46.0912 13.6H48.4116L51.3831 17.8333H49.046ZM43.5705 13.6833L42.6023 12.65H46.1079C47.065 12.65 47.7828 12.4444 48.2614 12.0333C48.7511 11.6222 48.9959 11.05 48.9959 10.3167C48.9959 9.57222 48.7511 9 48.2614 8.6C47.7828 8.2 47.065 8 46.1079 8H42.6023L43.5705 6.93333V13.6833Z" fill="#202020"/>
									<path d="M58.9693 17.8333L64.2277 6.16667H66.3645L71.6396 17.8333H69.3693L64.8454 7.31667H65.7134L61.2062 17.8333H58.9693ZM61.3898 15.1333L61.9741 13.4333H68.2842L68.8685 15.1333H61.3898Z" fill="#202020"/>
									<path d="M80.1369 17.8333V6.16667H82.3071V16H88.4169V17.8333H80.1369Z" fill="#202020"/>
									<path d="M99.1446 11.0167H104.954V12.8H99.1446V11.0167ZM99.3116 16.0167H105.905V17.8333H97.1414V6.16667H105.672V7.98333H99.3116V16.0167Z" fill="#202020"/>
									<path d="M120.811 18C119.899 18 119.059 17.85 118.291 17.55C117.523 17.25 116.855 16.8333 116.287 16.3C115.72 15.7556 115.28 15.1222 114.969 14.4C114.657 13.6667 114.501 12.8667 114.501 12C114.501 11.1333 114.657 10.3389 114.969 9.61667C115.28 8.88333 115.72 8.25 116.287 7.71667C116.855 7.17222 117.523 6.75 118.291 6.45C119.059 6.15 119.893 6 120.795 6C121.707 6 122.542 6.15 123.299 6.45C124.067 6.75 124.734 7.17222 125.302 7.71667C125.869 8.25 126.309 8.88333 126.621 9.61667C126.932 10.3389 127.088 11.1333 127.088 12C127.088 12.8667 126.932 13.6667 126.621 14.4C126.309 15.1333 125.869 15.7667 125.302 16.3C124.734 16.8333 124.067 17.25 123.299 17.55C122.542 17.85 121.713 18 120.811 18ZM120.795 16.1C121.385 16.1 121.93 16 122.431 15.8C122.931 15.6 123.365 15.3167 123.733 14.95C124.1 14.5722 124.384 14.1389 124.584 13.65C124.796 13.15 124.901 12.6 124.901 12C124.901 11.4 124.796 10.8556 124.584 10.3667C124.384 9.86667 124.1 9.43333 123.733 9.06667C123.365 8.68889 122.931 8.4 122.431 8.2C121.93 8 121.385 7.9 120.795 7.9C120.205 7.9 119.66 8 119.159 8.2C118.669 8.4 118.235 8.68889 117.857 9.06667C117.489 9.43333 117.2 9.86667 116.989 10.3667C116.788 10.8556 116.688 11.4 116.688 12C116.688 12.5889 116.788 13.1333 116.989 13.6333C117.2 14.1333 117.489 14.5722 117.857 14.95C118.224 15.3167 118.658 15.6 119.159 15.8C119.66 16 120.205 16.1 120.795 16.1Z" fill="#202020"/>
									<path d="M136.4 17.8333V6.16667H138.186L145.514 15.15H144.629V6.16667H146.783V17.8333H144.997L137.668 8.85H138.553V17.8333H136.4Z" fill="#202020"/>
									<defs>
									<linearGradient id="paint0_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
									<stop stop-color="#6536E2"/>
									<stop offset="1" stop-color="#1E90DB"/>
									</linearGradient>
									<linearGradient id="paint1_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
									<stop stop-color="#6536E2"/>
									<stop offset="1" stop-color="#1E90DB"/>
									</linearGradient>
									</defs>
								</svg>
								</div>
							<!-- Header -->
							<div class="text-center space-y-4 mb-16">
								<span v-if="flowType === 'default'" class="inline-flex items-center rounded-full bg-indigo-50 px-3 py-1 text-sm font-medium text-indigo-600">
								Step 2 of 4
								</span>
								<h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
								Lifetime Value Optimization Platform
								</h1>
								<p class="text-lg text-gray-600 max-w-2xl mx-auto">
								Our AI-powered platform gives you the tools, and AI team, to improve retention & grow LTV.
								</p>
							</div>

							<!-- Features Grid -->
							<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
								<!-- Feature 1: AI Marketing Strategist -->
								<div class="relative group">
									<!-- Optional gradient blur effect -->
									<div class="absolute -inset-0.5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl blur opacity-20 group-hover:opacity-30 transition duration-200" />

									<!-- Main content container -->
									<div class="relative bg-white rounded-2xl border border-white/40 shadow-lg overflow-hidden">
										<!-- Image Section -->
										<div class="w-full aspect-[2/1] overflow-hidden relative">
										<img
											src="../../images/AIStrat-Snippet.png"
											alt="AI Marketing Strategist"
											class="w-full h-full object-cover border-b"
										/>
										<!-- Soft Bottom Border -->
										<div class="absolute inset-x-0 bottom-0 h-8 bg-gradient-to-t from-white/50 to-transparent"></div>
										</div>

										<!-- Content Section -->
										<div class="p-6 space-y-4">
										<div class="flex items-center gap-3">
											<h3 class="text-xl font-semibold text-gray-900">AI Marketing Strategist</h3>
										</div>
										<p class="text-gray-600">
											Plan campaigns, segments, and emails with AI-powered guidance for optimal engagement
										</p>
										</div>
									</div>
								</div>

								<!-- Feature 2: AI Segmentation -->
								<div class="relative group">
									<!-- Optional gradient blur effect -->
									<div class="absolute -inset-0.5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl blur opacity-20 group-hover:opacity-30 transition duration-200" />

									<!-- Main content container -->
									<div class="relative bg-white rounded-2xl border border-white/40 shadow-lg overflow-hidden">
										<!-- Image Section -->
										<div class="w-full aspect-[2/1] overflow-hidden relative">
										<img
											src="../../images/AISegment-Snippet.png"
											alt="AI Segmentation"
											class="w-full h-full object-cover border-b"
										/>
										<!-- Soft Bottom Border -->
										<div class="absolute inset-x-0 bottom-0 h-8 bg-gradient-to-t from-white/50 to-transparent"></div>
										</div>

										<!-- Content Section -->
										<div class="p-6 space-y-4">
										<div class="flex items-center gap-3">
											<h3 class="text-xl font-semibold text-gray-900">AI Segmentation</h3>
										</div>
										<p class="text-gray-600">
											Create high-performing segments using AI signals that outperform traditional rule-based targeting
										</p>
										</div>
									</div>
								</div>
								<!-- Feature 3: Personalized Loyalty & Gift with Purchase -->
								<div class="relative group">
									<!-- Optional gradient blur effect -->
									<div class="absolute -inset-0.5 bg-gradient-to-r from-pink-500 to-indigo-500 rounded-2xl blur opacity-20 group-hover:opacity-30 transition duration-200" />

									<!-- Main content container -->
									<div class="relative bg-white rounded-2xl border border-white/40 shadow-lg overflow-hidden">
										<!-- Image Section -->
										<div class="w-full aspect-[2/1] overflow-hidden relative">
										<img
											src="../../images/Loyalty-Snippet.png"
											alt="Personalized Loyalty & Gift with Purchase"
											class="w-full h-full object-cover border-b"
										/>
										<!-- Soft Bottom Border -->
										<div class="absolute inset-x-0 bottom-0 h-8 bg-gradient-to-t from-white/50 to-transparent"></div>
										</div>

										<!-- Content Section -->
										<div class="p-6 space-y-4">
										<div class="flex items-center gap-3">
											<h3 class="text-xl font-semibold text-gray-900">Automated Loyalty & Gift with Purchase</h3>
										</div>
										<p class="text-gray-600">
											Drive retention with perfectly timed rewards and personalized incentives
										</p>
										</div>
									</div>
								</div>
							</div>

							<!-- Continue Button -->
							<div class="mt-12 flex justify-center">
								<div class="flex flex-col items-center space-y-7">
									<!-- Continue Button -->
									<button class="group relative" @click="goNext()">
									<div class="absolute -inset-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl blur opacity-30 group-hover:opacity-50 transition duration-200" />
									<div class="relative bg-indigo-600 text-white text-lg font-medium py-4 px-8 rounded-xl hover:bg-indigo-700 transition-all duration-200">
										Continue
									</div>
									</button>

									<!-- Back Button -->
									<button class="text-gray-600 hover:text-gray-900 font-medium flex items-center gap-2 group transition-colors duration-200"
									@click="goBack()">
									<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368">
										<path d="m313-440 224 224-57 56-320-320 320-320 57 56-224 224h487v80H313Z"/>
									</svg>
									Back
									</button>
								</div>
							</div>
							</div>
						</div>

						<!-- Enable in Shopify -->
						<div v-else-if="onboardingStep == 2" class="relative w-full max-w-3xl">
							<!-- Main Content Card -->
							<div class="relative bg-white backdrop-blur-xl rounded-2xl border border-white shadow-2xl shadow-indigo-500/5 overflow-hidden">
								<!-- Top Section with Logo and Text -->
								<div class="space-y-12 mt-12">
									<div class="flex justify-center">
										<svg width="147" height="24" viewBox="0 0 147 24" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path fill-rule="evenodd" clip-rule="evenodd" d="M25.2297 15.7711L12.7 0L0.170328 15.7711L2.49907 18.1962L0 21.2107V24H5.35415L12.7 14.8335L20.0459 24H25.4V21.2107L22.9009 18.1962L25.2297 15.7711ZM12.7 5.89126L3.75309 16.6835L2.76044 15.6498L12.7 3.13888L22.6396 15.6498L21.6469 16.6835L12.7 5.89126ZM1.95385 21.9131L12.7 8.95052L23.4462 21.9131V22.0495H20.9849L12.7 11.7112L4.41508 22.0495H1.95385V21.9131Z" fill="url(#paint0_linear_736_89064)"/>
											<path d="M18.1643 24L12.7002 17.0577L7.23607 24H9.72088L12.7002 20.2147L15.6795 24H18.1643Z" fill="url(#paint1_linear_736_89064)"/>
											<path d="M41.4004 17.8333V6.16667H46.2081C47.2431 6.16667 48.1278 6.33333 48.8623 6.66667C49.608 7 50.1811 7.47778 50.5818 8.1C50.9824 8.72222 51.1827 9.46111 51.1827 10.3167C51.1827 11.1722 50.9824 11.9111 50.5818 12.5333C50.1811 13.1444 49.608 13.6167 48.8623 13.95C48.1278 14.2722 47.2431 14.4333 46.2081 14.4333H42.6023L43.5705 13.45V17.8333H41.4004ZM49.046 17.8333L46.0912 13.6H48.4116L51.3831 17.8333H49.046ZM43.5705 13.6833L42.6023 12.65H46.1079C47.065 12.65 47.7828 12.4444 48.2614 12.0333C48.7511 11.6222 48.9959 11.05 48.9959 10.3167C48.9959 9.57222 48.7511 9 48.2614 8.6C47.7828 8.2 47.065 8 46.1079 8H42.6023L43.5705 6.93333V13.6833Z" fill="#202020"/>
											<path d="M58.9693 17.8333L64.2277 6.16667H66.3645L71.6396 17.8333H69.3693L64.8454 7.31667H65.7134L61.2062 17.8333H58.9693ZM61.3898 15.1333L61.9741 13.4333H68.2842L68.8685 15.1333H61.3898Z" fill="#202020"/>
											<path d="M80.1369 17.8333V6.16667H82.3071V16H88.4169V17.8333H80.1369Z" fill="#202020"/>
											<path d="M99.1446 11.0167H104.954V12.8H99.1446V11.0167ZM99.3116 16.0167H105.905V17.8333H97.1414V6.16667H105.672V7.98333H99.3116V16.0167Z" fill="#202020"/>
											<path d="M120.811 18C119.899 18 119.059 17.85 118.291 17.55C117.523 17.25 116.855 16.8333 116.287 16.3C115.72 15.7556 115.28 15.1222 114.969 14.4C114.657 13.6667 114.501 12.8667 114.501 12C114.501 11.1333 114.657 10.3389 114.969 9.61667C115.28 8.88333 115.72 8.25 116.287 7.71667C116.855 7.17222 117.523 6.75 118.291 6.45C119.059 6.15 119.893 6 120.795 6C121.707 6 122.542 6.15 123.299 6.45C124.067 6.75 124.734 7.17222 125.302 7.71667C125.869 8.25 126.309 8.88333 126.621 9.61667C126.932 10.3389 127.088 11.1333 127.088 12C127.088 12.8667 126.932 13.6667 126.621 14.4C126.309 15.1333 125.869 15.7667 125.302 16.3C124.734 16.8333 124.067 17.25 123.299 17.55C122.542 17.85 121.713 18 120.811 18ZM120.795 16.1C121.385 16.1 121.93 16 122.431 15.8C122.931 15.6 123.365 15.3167 123.733 14.95C124.1 14.5722 124.384 14.1389 124.584 13.65C124.796 13.15 124.901 12.6 124.901 12C124.901 11.4 124.796 10.8556 124.584 10.3667C124.384 9.86667 124.1 9.43333 123.733 9.06667C123.365 8.68889 122.931 8.4 122.431 8.2C121.93 8 121.385 7.9 120.795 7.9C120.205 7.9 119.66 8 119.159 8.2C118.669 8.4 118.235 8.68889 117.857 9.06667C117.489 9.43333 117.2 9.86667 116.989 10.3667C116.788 10.8556 116.688 11.4 116.688 12C116.688 12.5889 116.788 13.1333 116.989 13.6333C117.2 14.1333 117.489 14.5722 117.857 14.95C118.224 15.3167 118.658 15.6 119.159 15.8C119.66 16 120.205 16.1 120.795 16.1Z" fill="#202020"/>
											<path d="M136.4 17.8333V6.16667H138.186L145.514 15.15H144.629V6.16667H146.783V17.8333H144.997L137.668 8.85H138.553V17.8333H136.4Z" fill="#202020"/>
											<defs>
											<linearGradient id="paint0_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
											<stop stop-color="#6536E2"/>
											<stop offset="1" stop-color="#1E90DB"/>
											</linearGradient>
											<linearGradient id="paint1_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
											<stop stop-color="#6536E2"/>
											<stop offset="1" stop-color="#1E90DB"/>
											</linearGradient>
											</defs>
										</svg>
									</div>
									<div class="text-center space-y-4 mb-12">
										<span v-if="flowType === 'default'" class="inline-flex items-center rounded-full bg-indigo-50 px-3 py-1 text-sm font-medium text-indigo-600">
										Step 3 of 4
										</span>
										<h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
										Enable Raleon in Your Shopify Store
										</h1>
									</div>
								</div>

								<!-- Animation Section with Gradient Background -->
								<div class="relative mt-8">
								<div class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-purple-500/10" />
								<div class="relative">
									<!-- Full-width Image -->
									<img
									src="../../images/EnableRaleon2.gif"
									alt="Shopify Store"
									class="w-full h-64 object-cover object-top"
									/>
									<!-- Text Content -->
									<div class="pt-8 pb-8 px-8 text-center">
									<p class="text-gray-600 mb-8">
										Enabling Raleon in your Shopify store makes sure we can get the data we need to optimize segments, campaigns, and more.
									</p>

									<!-- Connect Button -->
									<div class="flex flex-col items-center space-y-7">
										<!-- Continue Button -->
										<button class="group relative" @click="enableInShopify()">
											<div class="absolute -inset-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl blur opacity-30 group-hover:opacity-50 transition duration-200" />
											<div class="relative bg-indigo-600 text-white text-lg font-medium py-4 px-8 rounded-xl hover:bg-indigo-700 transition-all duration-200">
											Continue
											</div>
										</button>

										<!-- Skip This Step Button -->
										<button class="text-gray-500 hover:text-indigo-600 font-medium transition-colors duration-200 underline underline-offset-2"
											@click="skipStep()">
											Skip this step
										</button>

										<!-- Back Button -->
										<button class="text-gray-600 hover:text-gray-900 font-medium flex items-center gap-2 group transition-colors duration-200"
											@click="goBack()">
											<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368">
											<path d="m313-440 224 224-57 56-320-320 320-320 57 56-224 224h487v80H313Z"/>
											</svg>
											Back
										</button>
										</div>
									</div>
								</div>
								</div>
							</div>
						</div>

						<!-- Educate -->
						<div v-else-if="onboardingStep == 3" class="relative w-full max-w-3xl">
							<!-- Main Content Card -->
							<div class="relative bg-white backdrop-blur-xl rounded-2xl border border-white shadow-2xl shadow-indigo-500/5 overflow-hidden">
								<!-- Top Section with Logo and Text -->
								<div class="space-y-12 mt-12">
									<div class="flex justify-center">
										<svg width="147" height="24" viewBox="0 0 147 24" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path fill-rule="evenodd" clip-rule="evenodd" d="M25.2297 15.7711L12.7 0L0.170328 15.7711L2.49907 18.1962L0 21.2107V24H5.35415L12.7 14.8335L20.0459 24H25.4V21.2107L22.9009 18.1962L25.2297 15.7711ZM12.7 5.89126L3.75309 16.6835L2.76044 15.6498L12.7 3.13888L22.6396 15.6498L21.6469 16.6835L12.7 5.89126ZM1.95385 21.9131L12.7 8.95052L23.4462 21.9131V22.0495H20.9849L12.7 11.7112L4.41508 22.0495H1.95385V21.9131Z" fill="url(#paint0_linear_736_89064)"/>
											<path d="M18.1643 24L12.7002 17.0577L7.23607 24H9.72088L12.7002 20.2147L15.6795 24H18.1643Z" fill="url(#paint1_linear_736_89064)"/>
											<path d="M41.4004 17.8333V6.16667H46.2081C47.2431 6.16667 48.1278 6.33333 48.8623 6.66667C49.608 7 50.1811 7.47778 50.5818 8.1C50.9824 8.72222 51.1827 9.46111 51.1827 10.3167C51.1827 11.1722 50.9824 11.9111 50.5818 12.5333C50.1811 13.1444 49.608 13.6167 48.8623 13.95C48.1278 14.2722 47.2431 14.4333 46.2081 14.4333H42.6023L43.5705 13.45V17.8333H41.4004ZM49.046 17.8333L46.0912 13.6H48.4116L51.3831 17.8333H49.046ZM43.5705 13.6833L42.6023 12.65H46.1079C47.065 12.65 47.7828 12.4444 48.2614 12.0333C48.7511 11.6222 48.9959 11.05 48.9959 10.3167C48.9959 9.57222 48.7511 9 48.2614 8.6C47.7828 8.2 47.065 8 46.1079 8H42.6023L43.5705 6.93333V13.6833Z" fill="#202020"/>
											<path d="M58.9693 17.8333L64.2277 6.16667H66.3645L71.6396 17.8333H69.3693L64.8454 7.31667H65.7134L61.2062 17.8333H58.9693ZM61.3898 15.1333L61.9741 13.4333H68.2842L68.8685 15.1333H61.3898Z" fill="#202020"/>
											<path d="M80.1369 17.8333V6.16667H82.3071V16H88.4169V17.8333H80.1369Z" fill="#202020"/>
											<path d="M99.1446 11.0167H104.954V12.8H99.1446V11.0167ZM99.3116 16.0167H105.905V17.8333H97.1414V6.16667H105.672V7.98333H99.3116V16.0167Z" fill="#202020"/>
											<path d="M120.811 18C119.899 18 119.059 17.85 118.291 17.55C117.523 17.25 116.855 16.8333 116.287 16.3C115.72 15.7556 115.28 15.1222 114.969 14.4C114.657 13.6667 114.501 12.8667 114.501 12C114.501 11.1333 114.657 10.3389 114.969 9.61667C115.28 8.88333 115.72 8.25 116.287 7.71667C116.855 7.17222 117.523 6.75 118.291 6.45C119.059 6.15 119.893 6 120.795 6C121.707 6 122.542 6.15 123.299 6.45C124.067 6.75 124.734 7.17222 125.302 7.71667C125.869 8.25 126.309 8.88333 126.621 9.61667C126.932 10.3389 127.088 11.1333 127.088 12C127.088 12.8667 126.932 13.6667 126.621 14.4C126.309 15.1333 125.869 15.7667 125.302 16.3C124.734 16.8333 124.067 17.25 123.299 17.55C122.542 17.85 121.713 18 120.811 18ZM120.795 16.1C121.385 16.1 121.93 16 122.431 15.8C122.931 15.6 123.365 15.3167 123.733 14.95C124.1 14.5722 124.384 14.1389 124.584 13.65C124.796 13.15 124.901 12.6 124.901 12C124.901 11.4 124.796 10.8556 124.584 10.3667C124.384 9.86667 124.1 9.43333 123.733 9.06667C123.365 8.68889 122.931 8.4 122.431 8.2C121.93 8 121.385 7.9 120.795 7.9C120.205 7.9 119.66 8 119.159 8.2C118.669 8.4 118.235 8.68889 117.857 9.06667C117.489 9.43333 117.2 9.86667 116.989 10.3667C116.788 10.8556 116.688 11.4 116.688 12C116.688 12.5889 116.788 13.1333 116.989 13.6333C117.2 14.1333 117.489 14.5722 117.857 14.95C118.224 15.3167 118.658 15.6 119.159 15.8C119.66 16 120.205 16.1 120.795 16.1Z" fill="#202020"/>
											<path d="M136.4 17.8333V6.16667H138.186L145.514 15.15H144.629V6.16667H146.783V17.8333H144.997L137.668 8.85H138.553V17.8333H136.4Z" fill="#202020"/>
											<defs>
											<linearGradient id="paint0_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
											<stop stop-color="#6536E2"/>
											<stop offset="1" stop-color="#1E90DB"/>
											</linearGradient>
											<linearGradient id="paint1_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
											<stop stop-color="#6536E2"/>
											<stop offset="1" stop-color="#1E90DB"/>
											</linearGradient>
											</defs>
										</svg>
									</div>
									<div class="text-center space-y-4 mb-12 px-12">
										<span v-if="flowType === 'default'" class="inline-flex items-center rounded-full bg-indigo-50 px-3 py-1 text-sm font-medium text-indigo-600">
										Step 4 of 4
										</span>
										<h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
										{{ eduStepDetails.title }}
										</h1>
										<p class="text-lg text-gray-600 max-w-2xl mx-auto">
										{{ eduStepDetails.description }}
										</p>
									</div>
								</div>

								<!-- Animation Section with Gradient Background -->
								<div class="relative mt-8">
									<div v-if="selectedOption === 'form'" class="flex justify-center mb-8">
										<div class="flex items-center space-x-2">
											<div v-for="(_, index) in questions" :key="index" class="flex items-center">
											<div
												:class="[
												'w-2 h-2 rounded-full transition-colors duration-300',
												index === currentStep ? 'bg-violet-600 w-6' :
												index < currentStep ? 'bg-violet-200' : 'bg-gray-200'
												]"
											/>
											<div v-if="index < questions.length - 1" class="w-8 h-0.5 bg-gray-100" />
											</div>
										</div>
										</div>

										<div class="flex-1 px-6">
										<!-- Content -->
										<div class="max-w-2xl mx-auto">
											<div v-if="!selectedOption" class="grid grid-cols-2 gap-4">
											<!-- Quick Call Option -->
											<button
												@click="selectedOption = 'call'"
												class="p-6 rounded-xl border border-gray-200 hover:border-violet-600 transition-all bg-white"
											>
												<div class="flex flex-col items-center text-center space-y-3">
												<div class="w-12 h-12 rounded-full bg-violet-50 flex items-center justify-center">
													<div class="w-6 h-6 bg-violet-600 rounded-full" />
												</div>
												<h3 class="text-lg font-semibold">Quick Call</h3>
												<p class="text-sm text-gray-600">15-minute chat with your AI strategist</p>
												</div>
											</button>

											<!-- Quick Questions Option -->
											<button
												@click="selectedOption = 'form'"
												class="p-6 rounded-xl border border-gray-200 hover:border-violet-600 transition-all bg-white"
											>
												<div class="flex flex-col items-center text-center space-y-3">
												<div class="w-12 h-12 rounded-full bg-violet-50 flex items-center justify-center">
													<div class="w-6 h-6 bg-violet-600 rounded-full" />
												</div>
												<h3 class="text-lg font-semibold">Quick Questions</h3>
												<p class="text-sm text-gray-600">Share details through guided prompts</p>
												</div>
											</button>
											</div>

											<!-- Form or Call Content -->
											<div v-else :class="['transition-opacity duration-300', isTransitioning ? 'opacity-0' : 'opacity-100']">
											<div v-if="selectedOption === 'form'">
                                                                              <label class="block text-lg font-medium text-gray-900">{{questions[currentStep].question}}</label>
                                                                              <select v-if="questions[currentStep].id === 'revenue'" v-model="answers[questions[currentStep].id]" class="w-full p-4 mt-4 rounded-xl border border-gray-200 focus:border-violet-600 focus:ring-0 transition-all bg-white">
                                                                                <option v-for="rev in revenues" :key="rev.value" :value="rev.value">{{ rev.label }}</option>
                                                                              </select>
                                                                              <textarea
                                                                              v-else
                                                                              v-model="answers[questions[currentStep].id]"
                                                                              :placeholder="questions[currentStep].placeholder"
                                                                              rows="6"
                                                                              class="w-full p-4 mt-4 rounded-xl border border-gray-200 focus:border-violet-600 focus:ring-0 transition-all resize-none bg-white"
                                                                              />
											</div>

											<div v-else class="text-center space-y-6 py-8">
												<div class="w-16 h-16 rounded-full bg-violet-50 flex items-center justify-center mx-auto">
												<div class="w-8 h-8 bg-violet-600 rounded-full" />
												</div>
												<div class="space-y-2">
												<h2 class="text-xl font-semibold text-gray-900">
													We'll reach out shortly
												</h2>
												<p class="text-gray-600">
													Your AI strategist will contact you to schedule a quick chat
												</p>
												</div>
											</div>
											</div>
										</div>
										</div>

										<div v-if="selectedOption == 'form'" class="flex flex-col items-center space-y-7 py-4 mt-4">
											<!-- Continue Button -->
											<button class="group relative" @click="handleNext" :disabled="isLoading">
											<!-- Blurred Gradient Background -->
											<div class="absolute -inset-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl blur opacity-30 group-hover:opacity-50 transition duration-200" />

											<!-- Button Content (Positioned Above the Blurred Background) -->
											<div class="relative flex items-center bg-indigo-600 text-white text-lg font-medium py-4 px-8 rounded-xl hover:bg-indigo-700 transition-all duration-200">
												<!-- Spinner (Visible During Loading) -->
												<span v-if="isLoading" class="animate-spin mr-2">
												<div class="w-5 h-5 border-2 border-white border-t-transparent rounded-full" />
												</span>
												<!-- Button Text -->
												<span>{{ currentStep === questions.length - 1 ? 'Continue' : 'Next Question' }}</span>
											</div>
											</button>

											<!-- Back Button -->
											<button class="text-gray-600 hover:text-gray-900 font-medium flex items-center gap-2 group transition-colors duration-200"
											@click="handleBack">
											<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368">
												<path d="m313-440 224 224-57 56-320-320 320-320 57 56-224 224h487v80H313Z"/>
											</svg>
											Back
											</button>
										</div>
								</div>
							</div>
						</div>

						<!-- Loyalty -->
						<div v-else-if="onboardingStep == 5" class="relative w-full max-w-3xl">
							<!-- Main Content Card -->
							<div class="relative bg-white backdrop-blur-xl rounded-2xl border border-white shadow-2xl shadow-indigo-500/5 overflow-hidden">
								<!-- Top Section with Logo and Text -->
								<div class="space-y-12 mt-12">
									<div class="flex justify-center">
										<svg width="147" height="24" viewBox="0 0 147 24" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path fill-rule="evenodd" clip-rule="evenodd" d="M25.2297 15.7711L12.7 0L0.170328 15.7711L2.49907 18.1962L0 21.2107V24H5.35415L12.7 14.8335L20.0459 24H25.4V21.2107L22.9009 18.1962L25.2297 15.7711ZM12.7 5.89126L3.75309 16.6835L2.76044 15.6498L12.7 3.13888L22.6396 15.6498L21.6469 16.6835L12.7 5.89126ZM1.95385 21.9131L12.7 8.95052L23.4462 21.9131V22.0495H20.9849L12.7 11.7112L4.41508 22.0495H1.95385V21.9131Z" fill="url(#paint0_linear_736_89064)"/>
											<path d="M18.1643 24L12.7002 17.0577L7.23607 24H9.72088L12.7002 20.2147L15.6795 24H18.1643Z" fill="url(#paint1_linear_736_89064)"/>
											<path d="M41.4004 17.8333V6.16667H46.2081C47.2431 6.16667 48.1278 6.33333 48.8623 6.66667C49.608 7 50.1811 7.47778 50.5818 8.1C50.9824 8.72222 51.1827 9.46111 51.1827 10.3167C51.1827 11.1722 50.9824 11.9111 50.5818 12.5333C50.1811 13.1444 49.608 13.6167 48.8623 13.95C48.1278 14.2722 47.2431 14.4333 46.2081 14.4333H42.6023L43.5705 13.45V17.8333H41.4004ZM49.046 17.8333L46.0912 13.6H48.4116L51.3831 17.8333H49.046ZM43.5705 13.6833L42.6023 12.65H46.1079C47.065 12.65 47.7828 12.4444 48.2614 12.0333C48.7511 11.6222 48.9959 11.05 48.9959 10.3167C48.9959 9.57222 48.7511 9 48.2614 8.6C47.7828 8.2 47.065 8 46.1079 8H42.6023L43.5705 6.93333V13.6833Z" fill="#202020"/>
											<path d="M58.9693 17.8333L64.2277 6.16667H66.3645L71.6396 17.8333H69.3693L64.8454 7.31667H65.7134L61.2062 17.8333H58.9693ZM61.3898 15.1333L61.9741 13.4333H68.2842L68.8685 15.1333H61.3898Z" fill="#202020"/>
											<path d="M80.1369 17.8333V6.16667H82.3071V16H88.4169V17.8333H80.1369Z" fill="#202020"/>
											<path d="M99.1446 11.0167H104.954V12.8H99.1446V11.0167ZM99.3116 16.0167H105.905V17.8333H97.1414V6.16667H105.672V7.98333H99.3116V16.0167Z" fill="#202020"/>
											<path d="M120.811 18C119.899 18 119.059 17.85 118.291 17.55C117.523 17.25 116.855 16.8333 116.287 16.3C115.72 15.7556 115.28 15.1222 114.969 14.4C114.657 13.6667 114.501 12.8667 114.501 12C114.501 11.1333 114.657 10.3389 114.969 9.61667C115.28 8.88333 115.72 8.25 116.287 7.71667C116.855 7.17222 117.523 6.75 118.291 6.45C119.059 6.15 119.893 6 120.795 6C121.707 6 122.542 6.15 123.299 6.45C124.067 6.75 124.734 7.17222 125.302 7.71667C125.869 8.25 126.309 8.88333 126.621 9.61667C126.932 10.3389 127.088 11.1333 127.088 12C127.088 12.8667 126.932 13.6667 126.621 14.4C126.309 15.1333 125.869 15.7667 125.302 16.3C124.734 16.8333 124.067 17.25 123.299 17.55C122.542 17.85 121.713 18 120.811 18ZM120.795 16.1C121.385 16.1 121.93 16 122.431 15.8C122.931 15.6 123.365 15.3167 123.733 14.95C124.1 14.5722 124.384 14.1389 124.584 13.65C124.796 13.15 124.901 12.6 124.901 12C124.901 11.4 124.796 10.8556 124.584 10.3667C124.384 9.86667 124.1 9.43333 123.733 9.06667C123.365 8.68889 122.931 8.4 122.431 8.2C121.93 8 121.385 7.9 120.795 7.9C120.205 7.9 119.66 8 119.159 8.2C118.669 8.4 118.235 8.68889 117.857 9.06667C117.489 9.43333 117.2 9.86667 116.989 10.3667C116.788 10.8556 116.688 11.4 116.688 12C116.688 12.5889 116.788 13.1333 116.989 13.6333C117.2 14.1333 117.489 14.5722 117.857 14.95C118.224 15.3167 118.658 15.6 119.159 15.8C119.66 16 120.205 16.1 120.795 16.1Z" fill="#202020"/>
											<path d="M136.4 17.8333V6.16667H138.186L145.514 15.15H144.629V6.16667H146.783V17.8333H144.997L137.668 8.85H138.553V17.8333H136.4Z" fill="#202020"/>
											<defs>
											<linearGradient id="paint0_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
											<stop stop-color="#6536E2"/>
											<stop offset="1" stop-color="#1E90DB"/>
											</linearGradient>
											<linearGradient id="paint1_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
											<stop stop-color="#6536E2"/>
											<stop offset="1" stop-color="#1E90DB"/>
											</linearGradient>
											</defs>
										</svg>
									</div>
									<div class="text-center space-y-4 mb-12">
										<span v-if="flowType === 'default'" class="inline-flex items-center rounded-full bg-indigo-50 px-3 py-1 text-sm font-medium text-indigo-600">
										Step 3 of 4
										</span>
										<h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
										Automated Loyalty is Great
										</h1>
										<p class="text-lg text-gray-600 max-w-2xl mx-auto">
										Connect your Shopify store to start optimizing customer lifetime value with AI-powered segmentation
										</p>
									</div>
								</div>

								<!-- Animation Section with Gradient Background -->
								<div class="relative mt-8">
								<div class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-purple-500/10" />
								<div class="relative">
									<!-- Full-width Image -->
									<img
									src="https://framerusercontent.com/images/kQN5QgY7wK4rt3ZPf1FcmKXXp4.jpeg"
									alt="Shopify Store"
									class="w-full h-64 object-cover"
									/>
									<!-- Text Content -->
									<div class="pt-16 pb-8 px-8 text-center">
									<h3 class="text-2xl font-semibold text-gray-900 mb-4">
										Ready to Connect Your Store?
									</h3>
									<p class="text-gray-600 mb-8">
										We'll automatically sync your customer data and start analyzing patterns to boost retention
									</p>

									<!-- Connect Button -->
									<div class="flex flex-col items-center space-y-7">
											<!-- Continue Button -->
											<button class="group relative" @click="goNext()">
											<div class="absolute -inset-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl blur opacity-30 group-hover:opacity-50 transition duration-200" />
											<div class="relative bg-indigo-600 text-white text-lg font-medium py-4 px-8 rounded-xl hover:bg-indigo-700 transition-all duration-200">
												Continue
											</div>
											</button>

											<!-- Back Button -->
											<button class="text-gray-600 hover:text-gray-900 font-medium flex items-center gap-2 group transition-colors duration-200"
											@click="goBack()">
											<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368">
												<path d="m313-440 224 224-57 56-320-320 320-320 57 56-224 224h487v80H313Z"/>
											</svg>
											Back
											</button>
										</div>
									</div>
								</div>
								</div>
							</div>
						</div>

						<!-- GWP -->
						<div v-else-if="onboardingStep == 6" class="relative w-full max-w-3xl">
							<!-- Main Content Card -->
							<div class="relative bg-white backdrop-blur-xl rounded-2xl border border-white shadow-2xl shadow-indigo-500/5 overflow-hidden">
								<!-- Top Section with Logo and Text -->
								<div class="space-y-12 mt-12">
									<div class="flex justify-center">
										<svg width="147" height="24" viewBox="0 0 147 24" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path fill-rule="evenodd" clip-rule="evenodd" d="M25.2297 15.7711L12.7 0L0.170328 15.7711L2.49907 18.1962L0 21.2107V24H5.35415L12.7 14.8335L20.0459 24H25.4V21.2107L22.9009 18.1962L25.2297 15.7711ZM12.7 5.89126L3.75309 16.6835L2.76044 15.6498L12.7 3.13888L22.6396 15.6498L21.6469 16.6835L12.7 5.89126ZM1.95385 21.9131L12.7 8.95052L23.4462 21.9131V22.0495H20.9849L12.7 11.7112L4.41508 22.0495H1.95385V21.9131Z" fill="url(#paint0_linear_736_89064)"/>
											<path d="M18.1643 24L12.7002 17.0577L7.23607 24H9.72088L12.7002 20.2147L15.6795 24H18.1643Z" fill="url(#paint1_linear_736_89064)"/>
											<path d="M41.4004 17.8333V6.16667H46.2081C47.2431 6.16667 48.1278 6.33333 48.8623 6.66667C49.608 7 50.1811 7.47778 50.5818 8.1C50.9824 8.72222 51.1827 9.46111 51.1827 10.3167C51.1827 11.1722 50.9824 11.9111 50.5818 12.5333C50.1811 13.1444 49.608 13.6167 48.8623 13.95C48.1278 14.2722 47.2431 14.4333 46.2081 14.4333H42.6023L43.5705 13.45V17.8333H41.4004ZM49.046 17.8333L46.0912 13.6H48.4116L51.3831 17.8333H49.046ZM43.5705 13.6833L42.6023 12.65H46.1079C47.065 12.65 47.7828 12.4444 48.2614 12.0333C48.7511 11.6222 48.9959 11.05 48.9959 10.3167C48.9959 9.57222 48.7511 9 48.2614 8.6C47.7828 8.2 47.065 8 46.1079 8H42.6023L43.5705 6.93333V13.6833Z" fill="#202020"/>
											<path d="M58.9693 17.8333L64.2277 6.16667H66.3645L71.6396 17.8333H69.3693L64.8454 7.31667H65.7134L61.2062 17.8333H58.9693ZM61.3898 15.1333L61.9741 13.4333H68.2842L68.8685 15.1333H61.3898Z" fill="#202020"/>
											<path d="M80.1369 17.8333V6.16667H82.3071V16H88.4169V17.8333H80.1369Z" fill="#202020"/>
											<path d="M99.1446 11.0167H104.954V12.8H99.1446V11.0167ZM99.3116 16.0167H105.905V17.8333H97.1414V6.16667H105.672V7.98333H99.3116V16.0167Z" fill="#202020"/>
											<path d="M120.811 18C119.899 18 119.059 17.85 118.291 17.55C117.523 17.25 116.855 16.8333 116.287 16.3C115.72 15.7556 115.28 15.1222 114.969 14.4C114.657 13.6667 114.501 12.8667 114.501 12C114.501 11.1333 114.657 10.3389 114.969 9.61667C115.28 8.88333 115.72 8.25 116.287 7.71667C116.855 7.17222 117.523 6.75 118.291 6.45C119.059 6.15 119.893 6 120.795 6C121.707 6 122.542 6.15 123.299 6.45C124.067 6.75 124.734 7.17222 125.302 7.71667C125.869 8.25 126.309 8.88333 126.621 9.61667C126.932 10.3389 127.088 11.1333 127.088 12C127.088 12.8667 126.932 13.6667 126.621 14.4C126.309 15.1333 125.869 15.7667 125.302 16.3C124.734 16.8333 124.067 17.25 123.299 17.55C122.542 17.85 121.713 18 120.811 18ZM120.795 16.1C121.385 16.1 121.93 16 122.431 15.8C122.931 15.6 123.365 15.3167 123.733 14.95C124.1 14.5722 124.384 14.1389 124.584 13.65C124.796 13.15 124.901 12.6 124.901 12C124.901 11.4 124.796 10.8556 124.584 10.3667C124.384 9.86667 124.1 9.43333 123.733 9.06667C123.365 8.68889 122.931 8.4 122.431 8.2C121.93 8 121.385 7.9 120.795 7.9C120.205 7.9 119.66 8 119.159 8.2C118.669 8.4 118.235 8.68889 117.857 9.06667C117.489 9.43333 117.2 9.86667 116.989 10.3667C116.788 10.8556 116.688 11.4 116.688 12C116.688 12.5889 116.788 13.1333 116.989 13.6333C117.2 14.1333 117.489 14.5722 117.857 14.95C118.224 15.3167 118.658 15.6 119.159 15.8C119.66 16 120.205 16.1 120.795 16.1Z" fill="#202020"/>
											<path d="M136.4 17.8333V6.16667H138.186L145.514 15.15H144.629V6.16667H146.783V17.8333H144.997L137.668 8.85H138.553V17.8333H136.4Z" fill="#202020"/>
											<defs>
											<linearGradient id="paint0_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
											<stop stop-color="#6536E2"/>
											<stop offset="1" stop-color="#1E90DB"/>
											</linearGradient>
											<linearGradient id="paint1_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
											<stop stop-color="#6536E2"/>
											<stop offset="1" stop-color="#1E90DB"/>
											</linearGradient>
											</defs>
										</svg>
									</div>
									<div class="text-center space-y-4 mb-12">
										<span v-if="flowType === 'default'" class="inline-flex items-center rounded-full bg-indigo-50 px-3 py-1 text-sm font-medium text-indigo-600">
										Step 3 of 4
										</span>
										<h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
										GWP is great
										</h1>
										<p class="text-lg text-gray-600 max-w-2xl mx-auto">
										Connect your Shopify store to start optimizing customer lifetime value with AI-powered segmentation
										</p>
									</div>
								</div>

								<!-- Animation Section with Gradient Background -->
								<div class="relative mt-8">
								<div class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-purple-500/10" />
								<div class="relative">
									<!-- Full-width Image -->
									<img
									src="https://framerusercontent.com/images/kQN5QgY7wK4rt3ZPf1FcmKXXp4.jpeg"
									alt="Shopify Store"
									class="w-full h-64 object-cover"
									/>
									<!-- Text Content -->
									<div class="pt-16 pb-8 px-8 text-center">
									<h3 class="text-2xl font-semibold text-gray-900 mb-4">
										Ready to Connect Your Store?
									</h3>
									<p class="text-gray-600 mb-8">
										We'll automatically sync your customer data and start analyzing patterns to boost retention
									</p>

									<!-- Connect Button -->
									<div class="flex flex-col items-center space-y-7">
											<!-- Continue Button -->
											<button class="group relative" @click="goNext()">
											<div class="absolute -inset-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl blur opacity-30 group-hover:opacity-50 transition duration-200" />
											<div class="relative bg-indigo-600 text-white text-lg font-medium py-4 px-8 rounded-xl hover:bg-indigo-700 transition-all duration-200">
												Continue
											</div>
											</button>

											<!-- Back Button -->
											<button class="text-gray-600 hover:text-gray-900 font-medium flex items-center gap-2 group transition-colors duration-200"
											@click="goBack()">
											<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368">
												<path d="m313-440 224 224-57 56-320-320 320-320 57 56-224 224h487v80H313Z"/>
											</svg>
											Back
											</button>
										</div>
									</div>
								</div>
								</div>
							</div>
						</div>

						<!-- Final -->
						<div v-else-if="onboardingStep == 4" class="relative w-full max-w-3xl">
							<!-- Main Content Card -->
							<div class="relative bg-white backdrop-blur-xl rounded-2xl border border-white shadow-2xl shadow-indigo-500/5 overflow-hidden">
								<!-- Top Section with Logo and Text -->
								<div class="space-y-12 mt-12">
									<div class="flex justify-center">
										<svg width="147" height="24" viewBox="0 0 147 24" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path fill-rule="evenodd" clip-rule="evenodd" d="M25.2297 15.7711L12.7 0L0.170328 15.7711L2.49907 18.1962L0 21.2107V24H5.35415L12.7 14.8335L20.0459 24H25.4V21.2107L22.9009 18.1962L25.2297 15.7711ZM12.7 5.89126L3.75309 16.6835L2.76044 15.6498L12.7 3.13888L22.6396 15.6498L21.6469 16.6835L12.7 5.89126ZM1.95385 21.9131L12.7 8.95052L23.4462 21.9131V22.0495H20.9849L12.7 11.7112L4.41508 22.0495H1.95385V21.9131Z" fill="url(#paint0_linear_736_89064)"/>
											<path d="M18.1643 24L12.7002 17.0577L7.23607 24H9.72088L12.7002 20.2147L15.6795 24H18.1643Z" fill="url(#paint1_linear_736_89064)"/>
											<path d="M41.4004 17.8333V6.16667H46.2081C47.2431 6.16667 48.1278 6.33333 48.8623 6.66667C49.608 7 50.1811 7.47778 50.5818 8.1C50.9824 8.72222 51.1827 9.46111 51.1827 10.3167C51.1827 11.1722 50.9824 11.9111 50.5818 12.5333C50.1811 13.1444 49.608 13.6167 48.8623 13.95C48.1278 14.2722 47.2431 14.4333 46.2081 14.4333H42.6023L43.5705 13.45V17.8333H41.4004ZM49.046 17.8333L46.0912 13.6H48.4116L51.3831 17.8333H49.046ZM43.5705 13.6833L42.6023 12.65H46.1079C47.065 12.65 47.7828 12.4444 48.2614 12.0333C48.7511 11.6222 48.9959 11.05 48.9959 10.3167C48.9959 9.57222 48.7511 9 48.2614 8.6C47.7828 8.2 47.065 8 46.1079 8H42.6023L43.5705 6.93333V13.6833Z" fill="#202020"/>
											<path d="M58.9693 17.8333L64.2277 6.16667H66.3645L71.6396 17.8333H69.3693L64.8454 7.31667H65.7134L61.2062 17.8333H58.9693ZM61.3898 15.1333L61.9741 13.4333H68.2842L68.8685 15.1333H61.3898Z" fill="#202020"/>
											<path d="M80.1369 17.8333V6.16667H82.3071V16H88.4169V17.8333H80.1369Z" fill="#202020"/>
											<path d="M99.1446 11.0167H104.954V12.8H99.1446V11.0167ZM99.3116 16.0167H105.905V17.8333H97.1414V6.16667H105.672V7.98333H99.3116V16.0167Z" fill="#202020"/>
											<path d="M120.811 18C119.899 18 119.059 17.85 118.291 17.55C117.523 17.25 116.855 16.8333 116.287 16.3C115.72 15.7556 115.28 15.1222 114.969 14.4C114.657 13.6667 114.501 12.8667 114.501 12C114.501 11.1333 114.657 10.3389 114.969 9.61667C115.28 8.88333 115.72 8.25 116.287 7.71667C116.855 7.17222 117.523 6.75 118.291 6.45C119.059 6.15 119.893 6 120.795 6C121.707 6 122.542 6.15 123.299 6.45C124.067 6.75 124.734 7.17222 125.302 7.71667C125.869 8.25 126.309 8.88333 126.621 9.61667C126.932 10.3389 127.088 11.1333 127.088 12C127.088 12.8667 126.932 13.6667 126.621 14.4C126.309 15.1333 125.869 15.7667 125.302 16.3C124.734 16.8333 124.067 17.25 123.299 17.55C122.542 17.85 121.713 18 120.811 18ZM120.795 16.1C121.385 16.1 121.93 16 122.431 15.8C122.931 15.6 123.365 15.3167 123.733 14.95C124.1 14.5722 124.384 14.1389 124.584 13.65C124.796 13.15 124.901 12.6 124.901 12C124.901 11.4 124.796 10.8556 124.584 10.3667C124.384 9.86667 124.1 9.43333 123.733 9.06667C123.365 8.68889 122.931 8.4 122.431 8.2C121.93 8 121.385 7.9 120.795 7.9C120.205 7.9 119.66 8 119.159 8.2C118.669 8.4 118.235 8.68889 117.857 9.06667C117.489 9.43333 117.2 9.86667 116.989 10.3667C116.788 10.8556 116.688 11.4 116.688 12C116.688 12.5889 116.788 13.1333 116.989 13.6333C117.2 14.1333 117.489 14.5722 117.857 14.95C118.224 15.3167 118.658 15.6 119.159 15.8C119.66 16 120.205 16.1 120.795 16.1Z" fill="#202020"/>
											<path d="M136.4 17.8333V6.16667H138.186L145.514 15.15H144.629V6.16667H146.783V17.8333H144.997L137.668 8.85H138.553V17.8333H136.4Z" fill="#202020"/>
											<defs>
											<linearGradient id="paint0_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
											<stop stop-color="#6536E2"/>
											<stop offset="1" stop-color="#1E90DB"/>
											</linearGradient>
											<linearGradient id="paint1_linear_736_89064" x1="-0.250431" y1="15" x2="26.9778" y2="13.7507" gradientUnits="userSpaceOnUse">
											<stop stop-color="#6536E2"/>
											<stop offset="1" stop-color="#1E90DB"/>
											</linearGradient>
											</defs>
										</svg>
									</div>
									<div class="text-center space-y-4 mb-12 px-6">
											<div class="inline-flex items-center rounded-full bg-indigo-50 px-3 py-1 text-sm font-medium text-indigo-600">
												<svg xmlns="http://www.w3.org/2000/svg" class="mr-2" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="M203-480h117q11 0 21 5.5t15 16.5l44 88 124-248q11-23 36-23t36 23l69 138h92q-15-102-93-171t-184-69q-106 0-184 69t-93 171Zm277 320q106 0 184-69t93-171H640q-11 0-21-5.5T604-422l-44-88-124 248q-11 23-36 23t-36-23l-69-138h-92q15 102 93 171t184 69Zm0 80q-74 0-139.5-28.5T226-186q-49-49-77.5-114.5T120-440h80q0 116 82 198t198 82q116 0 198-82t82-198h80q0 74-28.5 139.5T734-186q-49 49-114.5 77.5T480-80ZM120-440q0-74 28.5-139.5T226-694q49-49 114.5-77.5T480-800q62 0 119 20t107 58l28-28q11-11 28-11t28 11q11 11 11 28t-11 28l-28 28q38 50 58 107t20 119h-80q0-116-82-198t-198-82q-116 0-198 82t-82 198h-80Zm280-400q-17 0-28.5-11.5T360-880q0-17 11.5-28.5T400-920h160q17 0 28.5 11.5T600-880q0 17-11.5 28.5T560-840H400Zm80 680q-116 0-198-82t-82-198q0-116 82-198t198-82q116 0 198 82t82 198q0 116-82 198t-198 82Zm0-280Z" fill="#4f46e5"/></svg>
												14 day trial unlocked
											</div>
										<h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
										{{finalStepDetails.title}}
										</h1>
										<p class="text-lg text-gray-600 max-w-2xl mx-auto">
											{{finalStepDetails.description}}
										</p>
									</div>
								</div>

								<!-- Animation Section with Gradient Background -->
								<div class="relative mt-8">
								<div class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-purple-500/10" />
								<div class="relative">
									<!-- Blurred Background Around the Image -->
									<div class="p-2 rounded-lg backdrop-blur-lg bg-white/20 border border-indigo-200/20">
										<img v-if="selectedGoal == 'segmentation'"
										src="../../images/AISegment-BigSnippet.gif"
										alt="Shopify Store"
										class="w-full h-64 object-cover rounded-md"
										/>

										<img v-if="selectedGoal == 'strategist'"
										src="../../images/AIStraetgist-BigSnippet.gif"
										alt="Shopify Store"
										class="w-full h-64 object-cover rounded-md"
										/>

									</div>

									<!-- Content Section -->
									<div class="pt-16 pb-8 px-8 text-center">
										<!-- Connect Button -->
										<div class="flex flex-col items-center space-y-7">
										<!-- Continue Button -->
										<button class="group relative" @click="goNext()">
											<div class="absolute -inset-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl blur opacity-30 group-hover:opacity-50 transition duration-200" />
											<div class="relative bg-indigo-600 text-white text-lg font-medium py-4 px-8 rounded-xl hover:bg-indigo-700 transition-all duration-200">
											{{finalStepDetails.ctatext}}
											</div>
										</button>

										<!-- Back Button -->
										<button
											class="text-gray-600 hover:text-gray-900 font-medium flex items-center gap-2 group transition-colors duration-200"
											@click="goBack()"
										>
											<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368">
											<path d="m313-440 224 224-57 56-320-320 320-320 57 56-224 224h487v80H313Z"/>
											</svg>
											Back
										</button>
										</div>
									</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</transition>
			</div>
		</div>
	</div>
</div>
  </template>

  <script>
  import { getCurrentOrg } from '../../services/organization';
  import { customerIOTrackEvent } from '../../services/customerio.js';
import * as Utils from '../../utils/utils';
import { REVENUE_RANGES } from '../../utils/utils';
import * as OrganizationSettings from '../../services/organization-settings.js';
import { Crisp } from 'crisp-sdk-web';
  const URL_DOMAIN = Utils.URL_DOMAIN;
  export default {
	data() {
	  return {
		appScreenshot: '../../images/CampaignEmptyState.png',
		onboardingStep: 0,
		external_domain: '',
		store_name: '',
		selectedType: '',
		selectedGoal: '',
		email: '',
		selectedOption: 'form',
		currentStep: 0,
		answers: {},
		isTransitioning: false,
                isLoading: false,
                flowType: 'default', // Add flowType to track which flow we're in
                revenues: REVENUE_RANGES,
                questions: [
        {
          id: 'idealCustomer',
          question: 'Who is your ideal customer?',
          subtext: 'Understanding your target audience helps us create more effective segments',
          placeholder: 'Tell us about their motivations and challenges...'
        },
        {
          id: 'marketingSuccess',
          question: 'What marketing strategies or promotional tactics have been most successful for your business in the past?',
          subtext: 'Help us understand what resonates with your audience',
          placeholder: 'For example: Bundling works better than pure discounts...'
        },
        {
          id: 'customerBehavior',
          question: 'Is there anything unique about your brand, products, or customer relationships that you think is important for our AI to understand?',
          subtext: 'Share any unique patterns or behaviors you\'ve observed',
          placeholder: 'For example: We have two influencers that are key to our brand...'
        },
        {
          id: 'revenue',
          question: "What was your brand's revenue in the last 12 months?",
          subtext: 'Choose the range that best matches your revenue',
          placeholder: ''
        },
        ],
		  steps: {
			0: { name: 'Get Details', next: 1 },
			1: { name: 'LVO', next: 3 },
			2: { name: 'Enable in Shopify', next: 3 }, // Next step depends on selectedGoal
			3: { name: 'Step 3', next: 4 }, // Only for strategist, complete, segmentation
			4: { name: 'Final Step', next: null }, // Final step
			},
	  };
	},
	computed: {
		isEmailValid() {
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // Basic email regex
			return this.email && emailRegex.test(this.email);
		},
		finalStepDetails() {
			const selectedGoal = this.selectedGoal;

			const data = {
			complete: {
				title: 'Time to Grow Customer Lifetime Value',
				description: 'Your Lifetime Value Optimization platform is set up and learning about your customers. You can begin working with your AI Marketing Strategist.',
				ctatext: 'Plan With AI Strategist'
			},
			strategist: {
				title: 'Your AI Marketing Strategist is Ready',
				description: 'While your strategist is still learning about your customers and brand, you can begin planning with it now.',
				ctatext: 'Plan With AI Strategist'
			},
			segmentation: {
				title: 'Your AI Segmentation is Ready to Launch',
				description: 'Enter your AI Segmentation Dashboard where you will find your first AI-powered segments being generated. Full AI analysis may take up to 12 hours.',
				ctatext: 'View AI Segmentation Dashboard'
			},
			};

			return data[selectedGoal] || data.complete;
		},
		eduStepDetails() {
			const selectedGoal = this.selectedGoal;

			const data = {
			complete: {
				title: 'Educate Your AI Team',
				description: 'You are the boss. Your AI team is already learning from the data, but they need your point of view.',
				ctatext: 'Plan With AI Strategist'
			},
			strategist: {
				title: 'Educate Your AI Marketing Strategist',
				description: 'You are the boss. Your AI team is already learning from the data, but they need your point of view.',
				ctatext: 'Plan With AI Strategist'
			},
			segmentation: {
				title: "Educate Raleon With Your Knowledge",
				description: "Your answers give Raleon's AI the knowledge it needs to generate great recommendations and results.",
				ctatext: 'View AI Segmentation Dashboard'
			},
			};

			return data[selectedGoal] || data.complete;
		},
		async nextStep() {
			const currentStep = this.onboardingStep;
			const selectedGoal = this.selectedGoal;

			// Handle different flows
			if (this.flowType === 'shopify') {
				// Check if this is an agency organization by fetching org details
				try {
					const token = localStorage.getItem('token');
					const orgId = localStorage.getItem('userOrgId');

					if (token && orgId) {
						const response = await fetch(`${URL_DOMAIN}/organizations/${orgId}`, {
							method: 'GET',
							headers: {
								Authorization: `Bearer ${token}`,
								'Content-Type': 'application/json',
							},
						});

						if (response.ok) {
							const orgData = await response.json();
							if (orgData.orgType === 'agency') {
								this.$router.push('/agency');
							} else {
								this.$router.push('/chat');
							}
						} else {
							// Fallback to URL parameter check if API fails
							if (this.$route.query.orgType === 'agency') {
								this.$router.push('/agency');
							} else {
								this.$router.push('/chat');
							}
						}
					} else {
						// Fallback to URL parameter check if no token/orgId
						if (this.$route.query.orgType === 'agency') {
							this.$router.push('/agency');
						} else {
							this.$router.push('/chat');
						}
					}
				} catch (error) {
					console.error('Error fetching organization details for redirect:', error);
					// Fallback to URL parameter check if fetch fails
					if (this.$route.query.orgType === 'agency') {
						this.$router.push('/agency');
					} else {
						this.$router.push('/chat');
					}
				}
			}

			if (this.flowType === 'chat') {
				if (currentStep === 1) return 3; // Skip step 2
				if (currentStep === 3) return 4;
			}

			// Default flow logic
			if(currentStep === 4) {
				let route;

				// Check if this is an agency organization by fetching org details
				try {
					const token = localStorage.getItem('token');
					const orgId = localStorage.getItem('userOrgId');

					if (token && orgId) {
						const response = await fetch(`${URL_DOMAIN}/organizations/${orgId}`, {
							method: 'GET',
							headers: {
								Authorization: `Bearer ${token}`,
								'Content-Type': 'application/json',
							},
						});

						if (response.ok) {
							const orgData = await response.json();
							if (orgData.orgType === 'agency') {
								route = '/agency';
							} else {
								// For brand organizations, use goal-based routing
								switch (this.selectedGoal) {
									case 'strategist':
										route = '/chat';
										break;
									case 'complete':
										route = '/chat';
										break;
									case 'segmentation':
										route = '/ai-segments/overview';
										break;
									default:
										route = '/chat';
										break;
								}
							}
						} else {
							// Fallback to URL parameter check if API fails
							if (this.$route.query.orgType === 'agency') {
								route = '/agency';
							} else {
								route = '/chat';
							}
						}
					} else {
						// Fallback to URL parameter check if no token/orgId
						if (this.$route.query.orgType === 'agency') {
							route = '/agency';
						} else {
							route = '/chat';
						}
					}
				} catch (error) {
					console.error('Error fetching organization details for redirect:', error);
					// Fallback to URL parameter check if fetch fails
					if (this.$route.query.orgType === 'agency') {
						route = '/agency';
					} else {
						route = '/chat';
					}
				}

				this.$router.push(route);
			}

			return this.steps[currentStep].next;
		},
	},
	watch: {
		onboardingStep() {
			this.scrollToTop();
		},
		async selectedType() {
			await OrganizationSettings.updateOrganizationSetting('UserType', this.selectedType);
			customerIOTrackEvent('user_type', { type: this.selectedType });
		},
		async selectedGoal() {
			await OrganizationSettings.updateOrganizationSetting('UserGoal', this.selectedGoal);
		},
	},
	async mounted() {
		// Get URL parameters
		const urlParams = new URLSearchParams(window.location.search);
		const source = urlParams.get('source');

		// Set flow type and initial step based on source parameter
		if (source === 'shopify') {
			this.flowType = 'shopify';
			this.onboardingStep = 2; // Go directly to step 3
		} else if (source === 'chat') {
			this.flowType = 'chat';
			this.onboardingStep = 1; // Start at step 2
		} else {
			this.flowType = 'default';
			this.onboardingStep = 0; // Start at beginning
		}

		// Disable background scrolling
		document.body.classList.add('overflow-hidden');

		await this.getOrgDetails();

		customerIOTrackEvent('onboarding-started', {
			external_domain: this.external_domain,
			store_name: this.store_name,
		});
		// Only call onboard/begin if it hasn't been called before
		if (!localStorage.getItem('onboard_begin_called' + localStorage.getItem('userOrgId'))) {
			localStorage.setItem('onboard_begin_called' + localStorage.getItem('userOrgId'), 'true');

			let onboardStartURL = `/onboard/begin`;
			const res = await fetch(`${URL_DOMAIN}${onboardStartURL}`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					externalDomain: this.external_domain,
					orgType: this.$route.query.orgType
				}),
			});

			// Unset the flag after successful call
			if (!res.ok) {
				localStorage.removeItem('onboard_begin_called' + localStorage.getItem('userOrgId'));
			} else {
				await this.getOrgDetails();
			}
		}
	},
	methods: {
		previousStep() {
			const currentStep = this.onboardingStep;

			// Handle special cases for Steps 5 and 6
			if (currentStep === 5 || currentStep === 6) {
			return 2; // Go back to Step 2
			}

			// For all other steps, go back to the previous step
			if (currentStep > 0) {
			return currentStep - 1;
			}

			return 0; // Stay at Step 0 if already at the beginning
		},
		async getOrgDetails() {
			const org = await getCurrentOrg();
			console.log(org);
			if (org) {
				this.store_name = org?.externalDomain?.split('.myshopify.com')[0];
				this.external_domain = org?.externalDomain;
				if (!this.external_domain) {
					console.log(`No external domain ::: found for org ${org.id}`);
				}
			}
		},
		enableInShopify() {
			console.log('Enable in Shopify');
			customerIOTrackEvent('Clicked Enabled In Shopify');
				const snippetId = localStorage.getItem('shopify_snippet_id');
				window.open(`https://${this.external_domain}/admin/themes/current/editor?context=apps&activateAppId=${snippetId}/app-embed`, '_blank');
				Crisp.session.pushEvent('onboarding-enable-raleon', {
					external_domain: this.external_domain,
					store_name: this.store_name,
				});
				console.log('Enable Raleon', this.external_domain, this.store_name);
				if (this.nextStep !== null) {
					this.onboardingStep = this.nextStep;
				}
		},
		skipStep() {
			this.$router.push("/chat");
		},
		async goNext() {
			console.log('Go Next');
			console.log(this.onboardingStep);
			if(this.onboardingStep == 0) {
				await OrganizationSettings.updateOrganizationSetting('UserEmail', this.email);
				customerIOTrackEvent('preferred_email', { email: this.email });
			}
			if (this.nextStep !== null) {
				this.onboardingStep = this.nextStep;
			}
			customerIOTrackEvent(`Onboarding: Step ${this.onboardingStep}`);
		},
		goBack() {
			this.onboardingStep = this.previousStep();
		},
		beforeUnmount() {
			// Re-enable background scrolling
			document.body.classList.remove('overflow-hidden');
		},
		scrollToTop() {
			window.scrollTo({ top: 0, behavior: 'smooth' });
		},
		async handleNext() {
			console.log('Handle Next', this.questions.length);
			console.log(this.questions[this.currentStep]);
			console.log(this.answers);
			const [key, value] = Object.entries(this.answers)[Object.keys(this.answers).length - 1];
			console.log("Calling updateOrganizationSetting", key, value);
			await OrganizationSettings.updateOrganizationSetting(key, value);
			if (this.currentStep < this.questions.length - 1) {
				this.isLoading = true; // Start loading animation
				this.isTransitioning = true;

				// Simulate a delay (e.g., API call or processing)
				await new Promise((resolve) => setTimeout(resolve, 1000));

				this.currentStep++;
				this.isTransitioning = false;
				this.isLoading = false; // Stop loading animation
			} else {
				this.onboardingStep = 4;
			}
		},
		handleBack() {
			if (this.currentStep > 0) {
				this.isTransitioning = true;
				setTimeout(() => {
				this.currentStep--;
				this.isTransitioning = false;
				}, 300);
			} else if(this.currentStep === 0) {
				this.onboardingStep = 2;
			}
		}
	}
  };
  </script>

  <style scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

  .animate-spin {
	animation: spin 1s linear infinite;
	}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
  </style>
