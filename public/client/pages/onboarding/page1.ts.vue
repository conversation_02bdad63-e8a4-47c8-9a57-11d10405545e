<template>
	<div class="flex flex-col sm:flex-row h-screen w-screen z-100 fixed overflow-y-auto">

		<div class="w-full flex-grow sm:w-7/12 bg-white flex flex-col p-12 sm:h-screen">
			<svg width="147" height="24" viewBox="0 0 147 24" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd" d="M25.1863 15.7711L12.6781 0L0.170035 15.7711L2.49477 18.1962L0 21.2107V24H5.34493L12.6781 14.8335L20.0114 24H25.3563V21.2107L22.8615 18.1962L25.1863 15.7711ZM12.6781 5.89126L3.74663 16.6835L2.75569 15.6498L12.6781 3.13888L22.6006 15.6498L21.6097 16.6835L12.6781 5.89126ZM1.95048 21.9131L12.6781 8.95052L23.4058 21.9131V22.0495H20.9488L12.6781 11.7112L4.40748 22.0495H1.95048V21.9131Z" fill="#5A16C9"/>
				<path d="M18.133 24L12.6783 17.0577L7.22362 24H9.70415L12.6783 20.2147L15.6525 24H18.133Z" fill="#5A16C9"/>
				<path d="M41.3564 17.8333V6.16667H46.1642C47.1991 6.16667 48.0839 6.33333 48.8184 6.66667C49.564 7 50.1372 7.47778 50.5378 8.1C50.9385 8.72222 51.1388 9.46111 51.1388 10.3167C51.1388 11.1722 50.9385 11.9111 50.5378 12.5333C50.1372 13.1444 49.564 13.6167 48.8184 13.95C48.0839 14.2722 47.1991 14.4333 46.1642 14.4333H42.5584L43.5266 13.45V17.8333H41.3564ZM49.002 17.8333L46.0473 13.6H48.3677L51.3391 17.8333H49.002ZM43.5266 13.6833L42.5584 12.65H46.064C47.0211 12.65 47.7389 12.4444 48.2174 12.0333C48.7071 11.6222 48.952 11.05 48.952 10.3167C48.952 9.57222 48.7071 9 48.2174 8.6C47.7389 8.2 47.0211 8 46.064 8H42.5584L43.5266 6.93333V13.6833Z" fill="#202020"/>
				<path d="M58.9254 17.8333L64.1838 6.16667H66.3205L71.5957 17.8333H69.3254L64.8014 7.31667H65.6695L61.1623 17.8333H58.9254ZM61.3459 15.1333L61.9302 13.4333H68.2403L68.8245 15.1333H61.3459Z" fill="#202020"/>
				<path d="M80.093 17.8333V6.16667H82.2631V16H88.3729V17.8333H80.093Z" fill="#202020"/>
				<path d="M99.1007 11.0167H104.91V12.8H99.1007V11.0167ZM99.2676 16.0167H105.862V17.8333H97.0975V6.16667H105.628V7.98333H99.2676V16.0167Z" fill="#202020"/>
				<path d="M120.767 18C119.855 18 119.015 17.85 118.247 17.55C117.479 17.25 116.811 16.8333 116.244 16.3C115.676 15.7556 115.236 15.1222 114.925 14.4C114.613 13.6667 114.457 12.8667 114.457 12C114.457 11.1333 114.613 10.3389 114.925 9.61667C115.236 8.88333 115.676 8.25 116.244 7.71667C116.811 7.17222 117.479 6.75 118.247 6.45C119.015 6.15 119.849 6 120.751 6C121.663 6 122.498 6.15 123.255 6.45C124.023 6.75 124.69 7.17222 125.258 7.71667C125.826 8.25 126.265 8.88333 126.577 9.61667C126.888 10.3389 127.044 11.1333 127.044 12C127.044 12.8667 126.888 13.6667 126.577 14.4C126.265 15.1333 125.826 15.7667 125.258 16.3C124.69 16.8333 124.023 17.25 123.255 17.55C122.498 17.85 121.669 18 120.767 18ZM120.751 16.1C121.341 16.1 121.886 16 122.387 15.8C122.887 15.6 123.322 15.3167 123.689 14.95C124.056 14.5722 124.34 14.1389 124.54 13.65C124.752 13.15 124.857 12.6 124.857 12C124.857 11.4 124.752 10.8556 124.54 10.3667C124.34 9.86667 124.056 9.43333 123.689 9.06667C123.322 8.68889 122.887 8.4 122.387 8.2C121.886 8 121.341 7.9 120.751 7.9C120.161 7.9 119.616 8 119.115 8.2C118.625 8.4 118.191 8.68889 117.813 9.06667C117.445 9.43333 117.156 9.86667 116.945 10.3667C116.744 10.8556 116.644 11.4 116.644 12C116.644 12.5889 116.744 13.1333 116.945 13.6333C117.156 14.1333 117.445 14.5722 117.813 14.95C118.18 15.3167 118.614 15.6 119.115 15.8C119.616 16 120.161 16.1 120.751 16.1Z" fill="#202020"/>
				<path d="M136.356 17.8333V6.16667H138.142L145.47 15.15H144.586V6.16667H146.739V17.8333H144.953L137.624 8.85H138.509V17.8333H136.356Z" fill="#202020"/>
			</svg>

			<transition name="scene-fade" mode="out-in">
				<div class="scene-content" :key="currentStep">
					<div v-if="currentStep == 0" >
						<h1 class="text-6xl font-semibold mb-4 font-[Inter] text-ralblack-primary mt-28">Welcome to Raleon</h1>
						<p class="text-lg mb-8 font-[Inter] text-ralblack-primary w-10/12">In one sentence, what are you hoping to accomplish with Raleon?</p>
						<textarea
						v-model="feedback"
						class="no-focus-outline w-full mb-8 rounded-lg border border-ralblack-secondary border-opacity-50 p-2 text-gray-600 text-base font-semibold font-['Inter'] leading-normal resize-none focus:outline-none placeholder:font-semibold placeholder:italic placeholder:text-ralgray-main" placeholder="Eg: I want to increase repeat purchase rate and loyal customers."></textarea>

						<LightSecondaryButton cta="Get Started" @click="handleButtonClick" class="self-start mb-10 sm:mb-0"></LightSecondaryButton>
					</div>
					<div v-if="currentStep == 1" class="mb-10 sm:mb-0">
						<h1 class="text-5xl sm:text-6xl font-semibold mb-4 font-[Inter] text-ralblack-primary mt-28">This is your Loyalty Launcher</h1>

						<img src="../../images/LoyaltyLauncherOnboarding.png"  alt="Description" class="ml-12 sm:hidden" style="width: auto; max-width: 100%; height: 398px;"/>

						<p class="text-lg mb-8 font-[Inter] text-ralblack-primary w-10/12">It’s what your customers use to open the Loyalty Sidebar, which lets them see all the ways to earn and rewards they have.</p>

						<LightSecondaryButton cta="Next" @click="handleButtonClick" class="self-start"></LightSecondaryButton>
					</div>
					<div v-if="currentStep == 2">
						<h1 class="text-5xl sm:text-6xl font-semibold mb-4 font-[Inter] text-ralblack-primary mt-28">Meet The Loyalty Sidebar</h1>

						<img src="../../images/LoyaltySidebarOnboarding.png"  alt="Description" class="ml-12 sm:hidden" style="width: auto; max-width: 100%;"/>

						<div class="flex items-center">
							<div class="w-10/12">
							<p class="text-lg font-[Inter] text-ralblack-primary whitespace-normal mb-8">
								Your customers will love it. Our AI
								<svg width="24" height="26" viewBox="0 0 24 26" fill="none" xmlns="http://www.w3.org/2000/svg" class="inline-block align-middle mx-1">
								<path
									d="M8.14838 4.24398L8.31532 4.32745C8.99 4.67522 9.53949 5.22471 9.88726 5.89939L9.97073 6.06632C10.3046 6.7271 11.2505 6.7271 11.5914 6.06632L11.6748 5.89939C12.0226 5.22471 12.5721 4.67522 13.2468 4.32745L13.4137 4.24398C14.0745 3.91011 14.0745 2.96416 13.4137 2.62334L13.2468 2.53988C12.5721 2.1921 12.0226 1.64262 11.6748 0.967932L11.5914 0.801C11.2575 0.140226 10.3115 0.140226 9.97073 0.801L9.88726 0.967932C9.53949 1.64262 8.99 2.1921 8.31532 2.53988L8.14838 2.62334C7.48761 2.95721 7.48761 3.90316 8.14838 4.24398Z"
									fill="#E86AD6" />
								<path
									d="M9.46298 10.7613C10.1446 10.4135 10.1446 9.4467 9.46298 9.09893L8.44747 8.57726C7.54325 8.11124 6.80597 7.37396 6.33995 6.46974L5.81829 5.45424C5.47051 4.7726 4.5037 4.7726 4.15592 5.45424L3.63426 6.46974C3.16824 7.37396 2.43095 8.11124 1.52673 8.57726L0.51123 9.09893C-0.17041 9.4467 -0.17041 10.4135 0.51123 10.7613L1.52673 11.283C2.43095 11.749 3.16824 12.4863 3.63426 13.3905L4.15592 14.406C4.5037 15.0876 5.47051 15.0876 5.81829 14.406L6.33995 13.3905C6.80597 12.4863 7.54325 11.749 8.44747 11.283L9.46298 10.7613Z"
									fill="#E86AD6" />
								<path
									d="M22.5115 14.9207L21.4821 14.3921C20.2371 13.7591 19.2424 12.7645 18.6095 11.5194L18.0809 10.49C17.6148 9.5719 16.6828 9.00155 15.6534 9.00155C14.624 9.00155 13.6919 9.5719 13.2259 10.49L12.6973 11.5194C12.0643 12.7645 11.0697 13.7591 9.82466 14.3921L8.79525 14.9207C7.87712 15.3867 7.30677 16.3188 7.30677 17.3482C7.30677 18.3776 7.87712 19.3096 8.79525 19.7756L9.82466 20.3043C11.0697 20.9372 12.0643 21.9319 12.6973 23.1769L13.2259 24.2063C13.6919 25.1244 14.624 25.6948 15.6534 25.6948C16.6828 25.6948 17.6148 25.1244 18.0809 24.2063L18.6095 23.1769C19.2424 21.9319 20.2371 20.9372 21.4821 20.3043L22.5115 19.7756C23.4296 19.3096 24 18.3776 24 17.3482C24 16.3188 23.4296 15.3867 22.5115 14.9207ZM21.5656 17.9185L20.5362 18.4471C18.8947 19.2818 17.587 20.5894 16.7524 22.2309L16.2237 23.2604C16.0638 23.5734 15.7716 23.6081 15.6534 23.6081C15.5351 23.6081 15.243 23.5734 15.083 23.2604L14.5544 22.2309C13.7197 20.5894 12.4121 19.2818 10.7706 18.4471L9.7412 17.9185C9.4282 17.7585 9.39342 17.4664 9.39342 17.3482C9.39342 17.2299 9.4282 16.9378 9.7412 16.7778L10.7706 16.2492C12.4121 15.4145 13.7197 14.1069 14.5544 12.4654L15.083 11.436C15.243 11.123 15.5351 11.0882 15.6534 11.0882C15.7716 11.0882 16.0638 11.123 16.2237 11.436L16.7524 12.4654C17.587 14.1069 18.8947 15.4145 20.5362 16.2492L21.5656 16.7778C21.8786 16.9378 21.9133 17.2299 21.9133 17.3482C21.9133 17.4664 21.8786 17.7585 21.5656 17.9185Z"
									fill="#E86AD6" />
							</svg>
								is in the process of branding it for you.
							</p>
							</div>
						</div>


						<LightSecondaryButton cta="Next" @click="handleButtonClick" class="self-start"></LightSecondaryButton>
					</div>
					<div v-if="currentStep == 3">
						<h1 class="text-6xl font-semibold mb-4 font-[Inter] text-ralblack-primary mt-28">Enable Raleon in Your Store</h1>

						<img src="../../images/EnableRaleon.gif" class="rounded-lg w-[90%] sm:w-[80%] sm:hidden mb-6">

						<p class="text-lg mb-8 font-[Inter] text-ralblack-primary w-10/12">Enabling Raleon in your store does not make Raleon live, but gets everything ready.</p>

						<LightSecondaryButton cta="Enable in Shopify" @click="handleButtonClick" class="self-start"></LightSecondaryButton>
					</div>
					<div v-if="currentStep == 4">
						<h1 class="text-6xl font-semibold mb-4 font-[Inter] text-ralblack-primary mt-28">Raleon is Enabled in Your Store</h1>

						<img src="../../images/EnableRaleon.gif" class="rounded-lg w-[90%] sm:w-[80%] sm:hidden mb-6">

						<p class="text-lg mb-8 font-[Inter] text-ralblack-primary w-10/12">With Raleon enabled, we only have one more Shopify store prep step.</p>

						<LightSecondaryButton cta="Next Step" @click="handleButtonClick" class="self-start"></LightSecondaryButton>
					</div>
					<div v-if="currentStep == 5">
						<h1 class="text-6xl font-semibold mb-4 font-[Inter] text-ralblack-primary mt-28">Enable Customer Accounts</h1>

						<img src="../../images/CustomerAccountEnable.gif" class="rounded-lg w-[80%] ml-4 sm:hidden mb-6">

						<p class="text-lg mb-8 font-[Inter] text-ralblack-primary w-10/12">We're preparing your Shopify store with Customer Accounts. Customer Accounts in your store is what allows you to have loyalty members and collect information on them.</p>

						<LightSecondaryButton cta="Enable Customer Accounts" @click="handleButtonClick" class="self-start"></LightSecondaryButton>
					</div>
					<div v-if="currentStep == 6">
						<h1 class="text-6xl font-semibold mb-4 font-[Inter] text-ralblack-primary mt-28">Customer Accounts Enabled in Your Store</h1>

						<img src="../../images/CustomerAccountEnable.gif" class="rounded-lg w-[80%] ml-4 sm:hidden mb-6">

						<p class="text-lg mb-8 font-[Inter] text-ralblack-primary w-10/12">Enabling Customer Accounts in your store is what allows you to have loyalty members and collect information on them.</p>

						<LightSecondaryButton cta="Go to Raleon" @click="handleButtonClick" class="self-start"></LightSecondaryButton>
					</div>
				</div>
			</transition>
		</div>

		<transition name="scene-fade" mode="out-in">
			<div class="scene-content w-6/12 bg-blue-100 relative flex justify-center" :key="imageStep">
				<div v-if="imageStep == 0" class="hidden sm:block ">
					<img src="../../images/OnboardingQuote.png" class="rounded-lg w-[100%] mt-[12rem]">
				</div>
				<div v-show="imageStep == 1" class="hidden sm:block">
					<img src="../../images/LoyaltyLauncherOnboarding.png"  alt="Description" class="absolute right-0 top-1/2 transform -translate-y-1/2 h-auto max-w-none" style="width: auto; max-width: 90%;"/>

					<svg width="203" height="246" viewBox="0 0 203 246" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute z-20 left-[0%] top-[68%] transform -translate-x-1/2 -translate-y-1/2">
					<path d="M181.489 206.16C180.794 207.391 180.774 207.949 180.404 208.366C149.256 230.422 113.886 239.666 74.0634 232.615C64.0039 230.858 54.5036 226.685 45.9579 220.631C27.5307 207.801 19.8242 186.434 26.3287 166.598C29.6558 156.239 34.779 146.974 44.24 140.574C49.5776 141.953 54.8502 143.494 59.9676 144.407C68.3213 145.86 76.1188 144.639 82.6342 139.513C87.5596 135.825 89.5084 130.948 87.7462 127.227C85.4334 122.344 80.5307 120.385 75.659 120.89C68.8867 121.577 62.2048 123.055 55.8081 124.835C51.682 126.014 47.7564 128.216 43.9607 130.093C25.2793 119.424 23.8254 98.6702 40.3696 81.1491C43.431 81.6182 46.6675 82.1572 49.7938 82.4638C61.4235 83.3401 71.7063 80.4732 79.2808 72.188C82.1757 69.0085 84.3249 65.1541 81.9471 60.4334C80.12 56.8754 75.2173 54.9164 69.2753 55.5587C59.8822 56.7072 51.9209 60.8788 44.465 65.8179C41.9148 67.6268 39.3646 69.4357 36.9894 71.3146C7.64702 55.8198 -3.86612 21.6204 18.3942 1.48148C16.053 0.734541 14.192 -0.197629 12.8816 0.0328899C11.3311 0.356019 9.91618 1.86448 8.98135 3.18771C0.6583 15.887 -2.72253 29.4299 3.91099 44.3349C8.78234 55.5192 16.444 64.8021 26.2209 72.8563C27.6019 73.9737 28.9829 75.0911 30.2989 76.371C30.474 76.441 30.5192 76.8361 30.7846 77.6962C30.0249 79.0894 29.3103 80.8778 28.2456 82.5261C15.8641 102.842 17.6203 116.741 35.4797 135.565C33.9998 137.236 32.39 139.233 30.6701 140.996C21.1157 151.695 15.7637 164.074 15.2493 178.573C14.3535 202.159 28.1121 223.115 51.6906 234.233C64.4979 240.293 77.9492 243.217 91.9597 243.725C125.838 244.819 156.141 236.566 181.898 216.316C183.293 215.365 184.578 214.182 185.973 213.231C186.213 213.138 186.628 213.116 187.874 213.048C187.066 219.135 184.462 224.128 182.033 229.19C179.844 234.16 177.24 239.152 174.681 244.54C179.429 245.871 182.089 244.295 184.244 241.951C186.594 239.119 189.294 236.427 190.768 233.246C194.626 224.607 198.374 215.735 201.421 206.584C203.689 199.383 200.442 195.823 193.079 196.463C177.459 197.952 161.773 199.603 146.088 201.253C143.532 201.552 141.197 202.315 138.031 203.124C140.649 207.752 143.97 207.571 147.051 207.483C158.325 206.708 169.404 206.422 181.489 206.16ZM74.7455 63.2117C68.6875 73.7996 58.1901 77.7119 47.1393 73.862C55.3803 68.4825 62.966 63.2182 74.7455 63.2117ZM79.9289 129.006C73.645 137.618 64.8224 139.372 54.5314 134.128C62.6028 130.189 69.9993 126.923 79.9289 129.006Z" fill="#400F92"/>
					</svg>
				</div>

				<div v-show="imageStep == 2" class="hidden sm:block">
					<img src="../../images/LoyaltySidebarOnboarding.png"  alt="Description" class="absolute right-0 top-1/2 transform -translate-y-1/2 h-auto max-w-none" style="width: auto; max-width: 90%;"/>

					<svg width="222" height="67" viewBox="0 0 222 67" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute z-20 hidden sm:block left-[-10%] top-[53%] transform -translate-x-1/2 -translate-y-1/2">
						<path d="M177.916 26.0528C169.889 21.3609 163.763 17.7354 157.848 14.3232C155.736 13.0436 153.623 11.9773 151.722 10.4844C148.976 8.56499 145.174 6.43228 147.286 2.38022C149.398 -1.45857 153.412 0.247557 156.369 1.31389C159.538 2.16695 162.706 3.87311 165.664 5.36597C179.394 11.9772 193.125 18.8018 206.644 25.6263C210.235 27.5457 214.038 29.2518 217.206 31.811C223.966 36.9294 223.543 42.0478 216.15 45.8866C206.222 51.005 196.082 55.6969 185.943 60.3887C181.718 62.5214 177.282 64.6541 172.846 66.1469C170.311 67 167.354 66.7867 164.608 67C163.763 61.0285 167.143 59.7489 170.1 58.2561C177.493 54.204 185.098 50.3652 192.491 46.3131C195.026 44.8203 197.772 43.3274 200.307 39.9151C196.505 39.4886 192.914 38.8488 189.111 38.6356C160.594 37.1427 132.288 35.6498 103.771 34.157C62.3678 32.0243 21.1761 29.8916 -20.2268 27.5457C-23.6066 27.3325 -26.9865 26.0528 -31 24.9865C-27.1977 19.2283 -22.7616 20.5079 -19.1706 20.7212C1.10839 21.1477 21.5986 22.0008 41.8776 22.6406C85.6041 24.1334 129.331 25.6263 173.057 27.1191C174.113 26.9059 174.747 26.6926 177.916 26.0528Z" fill="#400F92"/>
					</svg>
				</div>

				<div v-show="imageStep == 3 || imageStep == 4" class="hidden sm:block items-center justify-center h-full">
					<img src="../../images/EnableRaleon.gif" class="rounded-lg w-[90%] sm:w-[100%] sm:ml-4 sm:mt-[40%]">
				</div>

				<div v-show="imageStep == 5 || imageStep == 6" class="hidden sm:block items-center justify-center h-full">
					<img src="../../images/CustomerAccountEnable.gif" class="rounded-lg w-[40%] sm:w-[90%] sm:ml-4 sm:mt-[40%]">
				</div>
			</div>
		</transition>
	</div>
</template>


<script>
import { getCurrentOrg } from '../../services/organization';
import * as Utils from '../../utils/utils';
import enableRaleonImage from '../../images/enable_raleon.jpg';
import enableCustomersImage from '../../images/enable_customers.jpg';
import launcherOnboarding from '../../images/LoyaltyLauncherOnboarding.png';
import LightSecondaryButton from '../../components/LightSecondaryButton.ts.vue';
import { customerIOTrackEvent } from '../../services/customerio.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'OnboardingStart',
	props: ['orgExternalDomain'],
	components: {
		LightSecondaryButton,
	},
	computed: {
		currentImage() {
			return this.stepImages[this.currentStep]; // Assumes step starts at 1 for images
		},
		onboardImage() {
			return launcherOnboarding;
		}
	},
	data() {
		return {
			currentStep: 0,
			imageStep: 0,
			buttonText: 'Get Started Free',
			feedback: '',
			stepImages: [enableRaleonImage, enableCustomersImage, launcherOnboarding],
			enable_raleon: false,
			enable_customers: false,
			store_name: '',
			external_domain: '',
			currentTitle: 'Welcome to Raleon<br>Let\'s Get Started',
			startText: 'Welcome to Raleon<br>Let\'s Get Started',
			enableRaleonText: 'Enable Raleon in Your<br> Shopify Store',
			enableCustomersText: 'Enable Customer Accounts<br> in Your Shopify Store',
			customizeText: 'Customizing Raleon to<br>Your Store',
			currentMessageIndex: 0,
			messages: [
				'Setting up your branding...',
				'Building the best loyalty program for you...',
				'Hmm this is really interesting, looking at your products...',
				'Starting to understand your users current loyalty...'
			],
			messageInterval: null,
		};
	},
	created() {
		this.preloadStepImages();
	},
	async mounted() {
		await this.getOrgDetails();

		const descriptionGeneratorRes = await fetch(`${URL_DOMAIN}${'/onboard/description/generate'}`, {
			method: 'POST',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
			body: JSON.stringify({
				url: `${this.external_domain}`
			}),
		});

		// Only call onboard/begin if it hasn't been called before
		if (!localStorage.getItem('onboard_begin_called' + localStorage.getItem('userOrgId'))) {
			localStorage.setItem('onboard_begin_called' + localStorage.getItem('userOrgId'), 'true');

			let onboardStartURL = `/onboard/begin`;
			const res = await fetch(`${URL_DOMAIN}${onboardStartURL}`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					externalDomain: this.external_domain
				}),
			});

			// Unset the flag after successful call
			if (!res.ok) {
				localStorage.removeItem('onboard_begin_called' + localStorage.getItem('userOrgId'));
			} else {
				await this.getOrgDetails();
			}
		}

		let data = await res.json();

		//Setup default branding
		await this.setDefaultBranding();

		console.log(data);

		this.startMessageCycle();
	},
	beforeUnmount() {
		clearInterval(this.messageInterval);
	},
	methods: {
		getStarted() {
			console.log("Get Started button clicked!");
			this.$router.push('/onboarding/work?sidebar=false');
		},
		async getOrgDetails() {
			const org = await getCurrentOrg();
			console.log(org);
			if (org) {
				this.store_name = org?.externalDomain?.split('.myshopify.com')[0];
				this.external_domain = org?.externalDomain;
				if (!this.external_domain) {
					console.log(`No external domain ::: found for org ${org.id}`);
				}
				this.messages.push(`We are learning about your store, ${this.store_name}...`)
			}
		},
		preloadStepImages() {
			this.stepImages.forEach(image => {
				const img = new Image();
				img.src = image;
			});
		},
		async handleButtonClick() {
			// Logic to handle the button click based on the current step
			console.log("CURRENT STEP: " + this.currentStep);
			switch (this.currentStep) {
				case 0: //Welcome
					customerIOTrackEvent('Onboarding: Welcome');
					this.currentStep++;
					this.imageStep++;

					if (this.feedback === '') {
						this.feedback = 'No feedback provided';
					}
					await fetch(`${Utils.URL_DOMAIN}/onboard/adventure/update`, {
						method: "post",
						withCreditentials: true,
						credentials: 'omit',
						headers: {
							'Authorization': `Bearer ${localStorage.getItem('token')}`,
							"accept": "application/json",
							"Content-Type": "application/json"
						},
						body: JSON.stringify({
							chosenAdventure: this.feedback
						}),
					}).catch();

					break;
				case 1: //Launcher
					customerIOTrackEvent('Onboarding: Launcher');
					this.currentStep++;
					this.imageStep++;
					break;
				case 2: //Sidebar
					customerIOTrackEvent('Onboarding: Sidebar');
					this.currentStep++;
					this.imageStep++;
					break;
				case 3: //Enable Raleon Step1
					customerIOTrackEvent('Onboarding: Enable Raleon - step1');
					this.currentStep++;
					this.imageStep++;
					const snippetId = localStorage.getItem('shopify_snippet_id');
					window.open(`https://${this.external_domain}/admin/themes/current/editor?context=apps&activateAppId=${snippetId}/app-embed`, '_blank');
					break;
				case 4: //Enable Raleon Step2
					customerIOTrackEvent('Onboarding: Enable Raleon - step2');
					this.currentStep++;
					this.imageStep++;
					break;
				case 5: //Enable CA Step 1
					customerIOTrackEvent('Onboarding: Enable Customer Account - step1');
					this.currentStep++;
					this.imageStep++;
					window.open(`https://admin.shopify.com/store/${this.store_name}/settings/customer_accounts`, '_blank');
					this.buttonText = 'Next';
					break;
				case 6: //Enable CA step 2
					customerIOTrackEvent('Onboarding: Enable Customer Account - step2');
					this.currentStep++;
					this.imageStep++;
					this.$router.push('/loyalty/quickstart');
					break;
			}
		},
		startMessageCycle() {
			this.messageInterval = setInterval(() => {
				this.currentMessageIndex = (this.currentMessageIndex + 1) % this.messages.length;
			}, 3000);
		},
		async setDefaultBranding() {
			let defaultBranding = {
				useDefaults: true,
				isDefaultTemplate: true,
				logoUrl: '',
				header: 'Smart Shoes Loyalty Program',
				defaults: {
					colors: {
						useBackgroundColor: true,
						backgroundColor: '#000000', // can also be { from / to } gradient
						textColor: '#ffffff',
						buttonTextColor: '#000000',
						buttonBackgroundColor: '#ffffff',
						linkColor: '#aaaaaa',
						accentColor: {
							from: '#f0abfc',
							to: '#f87171',
						},
						secondaryColor: '#BBF7D0',
						warningColor: '#EFC030'
					}
				},
				colors: {
					useBackgroundColor: true,
					backgroundColor: '#000000',
					textColor: '#ffffff',
					buttonTextColor: '#000000',
					buttonBackgroundColor: '#ffffff',
					linkColor: '#aaaaaa',
					accentColor: {
						from: '#f0abfc',
						to: '#f87171',
					},
					secondaryColor: '#BBF7D0',
					warningColor: '#EFC030'
				},
				launcher: {
					launcherPosition: 'left',
					callToAction: 'JOIN OUR LOYALTY PROGRAM',
					styling: {
						textColor: '#ffffff',
						backgroundColor: '#000000',
					}
				},
				guest: {
					heroImageUrl: 'https://d3q4ufbgs1i4ak.cloudfront.net/upload-left-arrow.png',
					content: {
						title: 'JOIN COMMUNITY',
						subtitle: 'You receive rewards by completing ways to earn and making purchases. Collect XP to move across levels or spend it on extra rewards!',
						benefitsTitle: 'Member Benefits',
						benefitsList: [] // Reward ids, eventually, probably
					}
				},
				member: {
					content: {
						rewardsTitle: 'Your Rewards',
						wteTitle: 'Ways to Earn',
						rewardShopTitle: 'Available Rewards',
					}
				}
			}

			const responseBranding = await fetch(`${URL_DOMAIN}/branding`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
					'ngrok-skip-browser-warning': true,
				}
			});

			if (!responseBranding.ok || responseBranding.status < 200 || responseBranding.status >= 300) {
				throw new Error('Failed to retrieve branding');
			}

			const currentBranding = await responseBranding.json();
			console.log('HAS BRANDING',  currentBranding);

			if(!currentBranding || currentBranding == {}) {
				const response = await fetch(`${URL_DOMAIN}/branding`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					body: JSON.stringify(defaultBranding),
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					}
				});

				if (!response.ok || response.status < 200 || response.status >= 300) {
					throw new Error('Failed to save branding');
				}
			}
		}
	},
}
</script>

<style scoped>
.onboarding-background {
	background-image: url('../../images/onboarding_background_5.jpg');
	background-size: cover;
	background-position: center top;
	height: 100vh;
	width: 100vw;
	max-width: 100vw;
	display: flex;
	justify-content: center;
	overflow: hidden;
	position: fixed;
	flex-direction: column;
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

.onboarding-background::before,
.onboarding-background::after {
	content: '';
	position: absolute;
	z-index: 1;
	/* Make sure it doesn't overlap content */
}

.onboarding-background::before {
	top: 0;
	left: 0;
	width: 35%;
	height: 100%;
	background: linear-gradient(90deg, #222141 5%, rgba(0, 0, 0, 0) 100%);
}

.onboarding-background::after {
	top: 0;
	left: 0;
	width: 100%;
	height: 45%;
	background: linear-gradient(180deg, #222141 0%, rgba(0, 0, 0, 0) 100%);
}
.text-header {
	height: 175px;

	margin-top: 40px;

	font-family: 'Inter';
	font-style: normal;
	font-weight: 700;
	line-height: 76px;
	text-align: center;
	letter-spacing: -0.04em;

	color: #ffff;
}

@media (max-width: 840px) {
	.text-header {
		line-height: 38px;
		height: 116px;
	}

	.text-description {
		font-size: 16px;
		/* Adjust font size for smaller screens */
		line-height: 22.4px;
		margin-top: 0px;
		/* Adjust line height accordingly */
	}
}

.button-text {
	font-family: 'Inter';
	font-style: normal;
	font-weight: 400;
	font-size: 16px;
	line-height: 20px;
	text-align: center;
	letter-spacing: -0.04em;
	cursor: pointer;

	color: #392A85;

	flex: none;
	order: 0;
	flex-grow: 0;
}

.button-background {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	padding: 2px 22px;
	gap: 10px;
	margin-top: 2vh;
	margin-left: auto;
	margin-right: auto;
	cursor: pointer;

	width: 216px;
	height: 56px;

	background: linear-gradient(360deg, #E2E3F8 0%, #FFFFFF 111%);
	box-shadow: 0px 40px 40px rgba(0, 0, 0, 0.25);
	border-radius: 144px;
	transition: transform 0.3s ease;
}

.button-background:hover {
	transform: scale(1.05);
	/* Scale the element slightly */
	transition: transform 0.3s ease;
	/* Smooth transition effect */
}

.button-background:active {
	transform: scale(0.95);
	background: linear-gradient(225deg, #3F2E9C 0%, #4738D8 100%);
}

.circle-container {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 20px;
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 10;
	padding-bottom: 20px;

}

.circle-unselected {
	width: 19px;
	height: 19px;
	background: linear-gradient(360deg, #E2E3F8 0%, #FFFFFF 111.61%);
	box-shadow: 0px 6px 40px rgba(0, 0, 0, 0.48);
	border-radius: 144px;
	/* This will make it a circle if the width and height are equal */
	flex: none;
	order: 0;
	flex-grow: 0;
	cursor: pointer;
}

.circle-selected {
	width: 28px;
	height: 28px;
	background: linear-gradient(360deg, #E2E3F8 0%, #FFFFFF 111.61%);
	box-shadow: 0px 6px 40px rgba(0, 0, 0, 0.48);
	border-radius: 144px;
	/* This will make it a circle if the width and height are equal */
	flex: none;
	order: 0;
	flex-grow: 0;
	cursor: pointer;
}

.dynamic-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	/* Add relative positioning */
	height: 40vh;
	/* Set a fixed height */
	width: 100%;
	/* Ensure it spans the width of its container */
}

.dynamic-section img {
	transition: opacity 0.5s ease, visibility 0.5s ease;
}

.feedback-box {
	width: 80%;
	height: 10vh;
	border-radius: 8px;
	resize: none;
	padding: 10px;
	box-sizing: border-box;
	border: 1px solid #ccc;
	outline: none;
	overflow: auto;
	margin: 0 auto;
	display: block;
}

.margin-bottom {
	margin-bottom: 40px;
}

.scene-fade-enter-active,
.scene-fade-leave-active {
	transition: opacity 0.5s;
}

.scene-fade-enter,
.scene-fade-leave-to {
	opacity: 0;
}

.text-clickable {
	cursor: pointer;
	color: #fefeff;
	font-family: 'Inter';
	font-weight: 400;
	font-size: 12px;
	line-height: 28.8px;
	text-align: center;
	text-decoration: underline;
}
</style>
