<template>
	<div class="mb-6 text-center">
		<h1 class="text-xl font-semibold">What do we do here?</h1>
		<p class="text-md mt-2">Step 3 of 3</p>
	</div>

	<!-- Card -->
	<div class="rounded-lg shadow-lg bg-white p-6 w-full md:max-w-3xl">

		<!-- For mobile view -->
		<!-- <div class="block md:hidden mb-4">
			<img src="../../images/EnableCustomerAccounts.gif" alt="logo" class="w-full" />
		</div> -->

		<div class="flex flex-col md:flex-row justify-between items-start">

			<!-- GIF (desktop view) -->
			<!-- <div class="hidden md:block mr-6 flex-shrink-0 w-3/5">
				<img src="../../images/EnableCustomerAccounts.gif" alt="logo" class="w-full" />
			</div> -->

			<!-- Text and Button -->
			<div class="flex-1">
				<h2 class="text-xl mb-2 font-bold">Hands Off!</h2>
				<p class="text-sm mb-4">You're on autopilot now! Click the button below and we'll create everything for your first loyalty program. Brb.</p>
				<button
					class="text-xs font-semibold mb-4 inline-block text-white px-4 py-2 rounded bg-ralsuccess-dark"
					@click="autopilot()">
					Create Loyalty Program
				</button>
			</div>
		</div>
	</div>
</template>

<script>
import { getCurrentOrg } from '../../services/organization';
import * as Utils from '../../utils/utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		name: 'AutoPilot',
		props: ['orgExternalDomain'],
		computed: {
			customerAccountsUrl() {
				const subdomain = this.orgExternalDomain?.replace('.myshopify.com', '');
				return `https://admin.shopify.com/store/${subdomain}/settings/customer_accounts`
			}
		},
		methods: {
			nextStep() {
				this.$emit('next-step');
			},
			async autopilot() {
				console.log("Kick off autopilot")

				console.log("Create Program"); //How do we prevent this if already onboarded
				try {
					let programId = await this.createLoyaltyProgram();
					let currencyId = await this.createDefaultPoints(programId);
					let campaignId = await this.createDefaultLoyaltyCampaign(programId);
					let earnId = await this.createDefaultLoyaltyEarn(campaignId);
					let earnConditionId = await this.createDefaultLoyaltyEarnCondition(earnId);
					let earnEffectId = await this.createDefaultLoyaltyEarnEffect(earnId, currencyId);
					let redemptionItemId = await this.createDefaultRedemptionItem(programId);
					let rewardCoupon = await this.createDefaultRewardCoupon(redemptionItemId);
					let redemptionShopItemId = await this.createLoyaltyRedemptionShopItem(campaignId, redemptionItemId);
					await this.createLoginQuest();

					this.$router.push(`/loyalty/programs/${programId}`);
				}
				catch (err) {
					console.log(err);
				}

				console.log("Finished creating program for autopilot")
			},
			async createLoyaltyProgram() {
				var payload = {
					name: "Copilot Recommended Program",
					active: true
				}

				let url = `/loyalty-programs`;
				const response = await fetch(`${URL_DOMAIN}${url}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				let data = await response.json();
				console.log(data);
				return data.id;
			},
			async createDefaultPoints(programId) {
				var payload = {
					name: "Points",
					conversionToUSD: 100
				}
				let url = `/loyalty-programs/${programId}/loyalty-currencies`;
				const response = await fetch(`${URL_DOMAIN}${url}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				let data = await response.json();
				console.log(data);
				return data.id;
			},
			async createDefaultLoyaltyCampaign(programId) {
				var payload = {
					name: "Copilot Recommended Campaign",
					active: true,
					evergreen: true,
				}
				let url = `/loyalty-programs/${programId}/loyalty-campaigns`;
				const response = await fetch(`${URL_DOMAIN}${url}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				let data = await response.json();
				console.log(data);
				return data.id;
			},
			async createDefaultLoyaltyEarn(loyaltyCampaignId) {
				var payload = {
					name: "Autopilot Earn",
					quest_def_id: "uuid here"
				}
				let url = `/loyalty-campaigns/${loyaltyCampaignId}/loyalty-earns`;
				const response = await fetch(`${URL_DOMAIN}${url}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				let data = await response.json();
				console.log(data);
				return data.id;
			},
			async createDefaultLoyaltyEarnCondition(loyaltyEarnId) {
				var payload = {
					type: 'triggered',
					triggeredEvent: 'ORDER_CREATED'
				}

				let url = `/loyalty-earns/${loyaltyEarnId}/earn-conditions`;
				const response = await fetch(`${URL_DOMAIN}${url}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				let data = await response.json();
				console.log(data);
				return data.id;
			},
			async createDefaultLoyaltyEarnEffect(loyaltyEarnId, currencyId) {
				var payload = {
					pointsPerDollar: 100,
					loyaltyCurrencyId: currencyId
				}

				let url = `/loyalty-earns/${loyaltyEarnId}/earn-effects`;
				const response = await fetch(`${URL_DOMAIN}${url}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				let data = await response.json();
				console.log(data);
				return data.id;
			},
			async createDefaultRewardCoupon(rewardDefinitionId) {
				let url = `/loyalty-reward-definitions/${rewardDefinitionId}/reward-coupon`;
				let payload = {
					name: 'Default Reward Coupon',
					amount: 1000,
					amountType: '$',
					loyaltyRewardDefinitionId: rewardDefinitionId,
				}
				const response = await fetch(`${URL_DOMAIN}${url}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});
				let data = await response.json();
				console.log(data);
				return data.id;

			},
			async createDefaultRedemptionItem(loyaltyProgramId) {
				var payload = {
					daysToRedeem: 10,
					redeemable: true,
					grantable: false,
					startingInventory: 1000,
					maxUserGrants: 0,
					maxUserRedemptions: 3,
					price: 1000,
				}

				let url = `/loyalty-programs/${loyaltyProgramId}/loyalty-reward-definitions`;
				const response = await fetch(`${URL_DOMAIN}${url}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				let data = await response.json();
				console.log(data);
				return data.id;
			},
			async createLoyaltyRedemptionShopItem(loyaltyCampaignId, loyaltyRewardDefinitionId) {
				var payload = {
					name: "Fake Redemption Item",
					price: 1000,
					loyaltyRewardDefinitionId: loyaltyRewardDefinitionId
				}

				let url = `/loyalty-campaigns/${loyaltyCampaignId}/loyalty-redemption-shop-items`;
				const response = await fetch(`${URL_DOMAIN}${url}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				let data = await response.json();
				console.log(data);
				return data.id;
			},
			async createLoginQuest() {
				const currentDate = new Date();
				const endDate = new Date(currentDate)
				endDate.setFullYear(currentDate.getFullYear() + 5);

				const organization = await getCurrentOrg();
				if (!organization || !organization?.externalDomain) {
					console.error(`No organization found with an external domain`);
					return;
				}
				const externalDomain = organization.externalDomain;
				var payload = {
					id: Utils.uuidv4(),
					chatGraph: `[{"type":"config-state","data":{"persist":false}},{"type":"simple-button","id":405232,"data":{"message":"Welcome! We have a great loyalty program that rewards you for being a valued customer. All you have to do is log in to get started!","sender":"Raleon Demo","buttons":[{"text":"Login","url":"https://${externalDomain}/account/login","onClick":656786,"target":"_top"}]},"next":{"condition":"event"}}]`,
					name: 'Login Quest',
					segmentId: -1,
					startDate: currentDate,
					endDate: endDate,
					description: 'Default Login Quest',
					isLoginQuest: true,
				}

				let loginQuestsUrl = `/campaigns/${localStorage.getItem('raleon_org_id')}/login`;
				const res = await fetch(`${URL_DOMAIN}${loginQuestsUrl}`, {
					method: 'GET',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
				});

				if (res.status === 200) {
					const data = await res.json();
					if (!data || !data.id) {
						console.error(`Login quest already exists for organization`);
						return;
					}
				}

				let url = `/chat-graph`;
				const response = await fetch(`${URL_DOMAIN}${url}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				let data = await response.json();
				console.log(data);
				return data.id;
			},
		},
	}
</script>
