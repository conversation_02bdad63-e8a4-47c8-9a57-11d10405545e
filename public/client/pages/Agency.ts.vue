<template>
  <StatusMessage :message="status.message" :status="status.type" @resetStatus="status.type = 'nope'" />

  <!-- Header Section -->
  <div class="flex flex-col p-6 bg-white shadow-sm sm:flex-row items-center justify-between">
    <div>
      <div class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary">
        Agency Brands
      </div>
      <p class="mt-1 text-gray-500">Manage and switch between your brand accounts</p>
    </div>
    <div class="mt-4 sm:mt-0">
      <button
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-button-primary hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200"
        @click="openAddModal"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        Add Brand
      </button>
    </div>
  </div>

  <div class="min-h-screen bg-[#F5F5F5] p-4 pt-3">
    <!-- Information Banner -->
    <div class="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
      <div class="flex items-start">
        <div class="flex-shrink-0 mr-3">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        </div>
        <div>
          <h3 class="text-sm font-medium text-blue-700">When to Add Brands</h3>
          <p class="text-sm text-blue-600 mt-1">
            Only add brands here that <strong>haven't installed the Raleon app on Shopify</strong> and don't already have a Raleon connection.
            Most brands should connect through the Shopify App Store instead.
          </p>
        </div>
      </div>
    </div>

    <!-- Loading skeleton -->
    <div v-if="isLoading" class="space-y-4">
      <div v-for="i in 3" :key="i" class="bg-white rounded-lg p-6 border border-gray-200 animate-pulse">
        <div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div class="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div class="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    </div>

    <!-- Brands list -->
    <div v-else-if="filteredUserOrgs.length > 0" class="space-y-4">
      <div v-for="org in filteredUserOrgs" :key="org.id" class="w-full">
        <!-- Brand Card -->
        <div class="p-6 mb-2 rounded-lg border bg-white border-gray-200 hover:border-purple-200 hover:shadow-md transition-all duration-200 group relative overflow-hidden">
          <!-- Header Section -->
          <div class="w-full text-left flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center mb-2">
                <h3 class="text-lg font-medium text-gray-900">{{ org.name }}</h3>
                <div class="ml-3 flex items-center">
                  <span
                    v-if="currentOrgId && currentOrgId === org.id.toString()"
                    class="px-2.5 py-1 rounded-full bg-green-50 text-green-600 text-xs font-medium"
                  >
                    Current Brand
                  </span>
                  <span
                    v-else
                    class="px-2.5 py-1 rounded-full bg-gray-50 text-gray-600 text-xs font-medium"
                  >
                    Available
                  </span>
                </div>
              </div>
            </div>

            <div class="flex items-center">
              <!-- Login Button -->
              <button
                v-if="currentOrgId && currentOrgId === org.id.toString()"
                class="inline-flex items-center px-4 py-2 border border-green-300 rounded-md shadow-sm text-sm font-medium text-green-700 bg-green-50 cursor-default"
                disabled
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Current Brand
              </button>

              <button
                v-else
                @click.stop="loginToOrg(org)"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
                Switch to Brand
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty state -->
    <div v-else class="text-center py-12 bg-white rounded-lg shadow">
      <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No brands found</h3>
      <p class="mt-1 text-sm text-gray-500">
        Get started by adding your first brand
      </p>
      <div class="mt-6">
        <button
          @click="openAddModal"
          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gradient-button-primary hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Brand
        </button>
      </div>
    </div>
  </div>

  <!-- Add Brand Modal -->
  <div v-if="isAddModalOpen"
       class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
       tabindex="-1"
       role="dialog"
       @click="isAddModalOpen = false">
    <!-- Modal Content -->
    <div class="bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-auto animate-fadeIn"
         role="document"
         @click.stop>
      <!-- Modal Header -->
      <div class="sticky top-0 bg-white p-6 border-b z-10 flex justify-between items-center">
        <h3 class="text-lg font-semibold">Add New Brand</h3>
        <button @click="isAddModalOpen = false" class="text-gray-400 hover:text-gray-600 transition">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="p-6">
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="brand-url">
              Brand Website URL
            </label>
            <div class="relative">
              <input
                id="brand-url"
                v-model="newBrandUrl"
                type="text"
                placeholder="https://example.com"
                class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
            </div>
            <p class="mt-2 text-sm text-gray-500">
              Enter the website URL for your brand. We'll automatically extract the brand name from the domain.
            </p>
          </div>

          <!-- Information Box -->
          <div class="p-4 bg-amber-50 rounded-lg border border-amber-200">
            <div class="flex items-start">
              <div class="flex-shrink-0 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-amber-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div>
                <h4 class="text-sm font-medium text-amber-700">Important Note</h4>
                <p class="text-sm text-amber-600 mt-1">
                  Only add brands that haven't installed the Raleon Shopify app. Most brands should connect through the App Store instead.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="flex justify-end space-x-3 p-6 pt-4 border-t bg-gray-50">
        <button
          @click="isAddModalOpen = false"
          class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-150"
        >
          Cancel
        </button>
        <button
          @click="showPricingConfirmation"
          class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-150"
        >
          Continue
        </button>
      </div>
    </div>
  </div>

  <!-- Pricing Confirmation Modal -->
  <div v-if="isPricingModalOpen"
       class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
       tabindex="-1"
       role="dialog"
       @click="isPricingModalOpen = false">
    <!-- Modal Content -->
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-auto animate-fadeIn"
         role="document"
         @click.stop>
      <!-- Modal Header -->
      <div class="sticky top-0 bg-white p-6 border-b z-10 flex justify-between items-center">
        <h3 class="text-lg font-semibold">Confirm Brand Addition</h3>
        <button @click="isPricingModalOpen = false" class="text-gray-400 hover:text-gray-600 transition">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="p-6">
        <div v-if="pricingPreview" class="space-y-6">
          <!-- Plan Information -->
          <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <h4 class="text-sm font-medium text-blue-900 mb-2">Current Plan</h4>
            <p class="text-blue-800">{{ pricingPreview.currentPlan.name }}</p>
            <p class="text-sm text-blue-600">Includes {{ pricingPreview.brandsIncludedLimit }} brands</p>
          </div>

          <!-- Brand Count Changes -->
          <div class="grid grid-cols-2 gap-4">
            <div class="bg-gray-50 rounded-lg p-4">
              <h4 class="text-sm font-medium text-gray-900 mb-1">Current Brands</h4>
              <p class="text-2xl font-bold text-gray-800">{{ pricingPreview.currentBrandCount }}</p>
            </div>
            <div class="bg-green-50 rounded-lg p-4">
              <h4 class="text-sm font-medium text-green-900 mb-1">After Adding Brand</h4>
              <p class="text-2xl font-bold text-green-800">{{ pricingPreview.newBrandCount }}</p>
            </div>
          </div>

          <!-- Pricing Breakdown -->
          <div class="border rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Pricing Breakdown</h4>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-600">Base Plan (includes {{ pricingPreview.brandsIncludedLimit }} brands)</span>
                <span class="font-medium">${{ pricingPreview.currentPlan.price.toFixed(2) }}/month</span>
              </div>
              <div v-if="pricingPreview.extraBrandsAfterAdd > 0" class="flex justify-between">
                <span class="text-gray-600">Extra Brands ({{ pricingPreview.extraBrandsAfterAdd }} × ${{ pricingPreview.extraBrandPrice.toFixed(2) }})</span>
                <span class="font-medium">${{ (pricingPreview.extraBrandsAfterAdd * pricingPreview.extraBrandPrice).toFixed(2) }}/month</span>
              </div>
              <div class="border-t pt-2 flex justify-between font-semibold">
                <span>Total Monthly Cost</span>
                <span>${{ pricingPreview.newPrice.toFixed(2) }}/month</span>
              </div>
            </div>
          </div>

          <!-- Price Change Summary -->
          <!-- <div v-if="pricingPreview.newPrice !== pricingPreview.currentPrice"
               class="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
            <div class="flex items-start">
              <div class="flex-shrink-0 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div>
                <h4 class="text-sm font-medium text-yellow-800">Subscription Price Change</h4>
                <p class="text-sm text-yellow-700 mt-1">
                  Your monthly subscription will increase from ${{ pricingPreview.currentPrice.toFixed(2) }} to ${{ pricingPreview.newPrice.toFixed(2) }}
                  (an increase of ${{ (pricingPreview.newPrice - pricingPreview.currentPrice).toFixed(2) }}/month).
                </p>
              </div>
            </div>
          </div> -->

          <div v-if="pricingPreview.newPrice === pricingPreview.currentPrice" class="bg-green-50 rounded-lg p-4 border border-green-200">
            <div class="flex items-start">
              <div class="flex-shrink-0 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h4 class="text-sm font-medium text-green-800">No Additional Cost</h4>
                <p class="text-sm text-green-700 mt-1">
                  This brand is included in your current plan. No additional charges will apply.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="flex items-center justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          <span class="ml-3 text-gray-600">Loading pricing information...</span>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="flex justify-end space-x-3 p-6 pt-4 border-t bg-gray-50">
        <button
          @click="isPricingModalOpen = false"
          class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-150"
        >
          Cancel
        </button>
        <button
          @click="confirmCreateBrand"
          :disabled="!pricingPreview"
          class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Confirm & Add Brand
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { onMounted, ref, computed } from 'vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import ModalBasic from '../components/ModalBasic.ts.vue';
import * as Utils from '../../client-old/utils/Utils';
import * as userService from '../services/user.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
  components: { StatusMessage, ModalBasic },
  setup() {
    const userOrgs = ref([]);
    const isAddModalOpen = ref(false);
    const isPricingModalOpen = ref(false);
    const newBrandUrl = ref('');
    const status = ref({ type: 'nope', message: '' });
    const isLoading = ref(true);
    const currentOrgId = ref(null);
    const pricingPreview = ref(null);

    // Get current organization ID from localStorage (same as sidebar logic)
    const getCurrentOrgId = () => {
      if (typeof window !== 'undefined') {
        return localStorage.getItem('userOrgId');
      }
      return null;
    };

    const fetchOrgs = async () => {
      isLoading.value = true;
      try {
        const response = await fetch(`${URL_DOMAIN}/user/orgs`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        userOrgs.value = await response.json();
        // Update current org ID
        currentOrgId.value = getCurrentOrgId();
      } catch (err) {
        console.error('Error fetching organizations:', err);
      } finally {
        isLoading.value = false;
      }
    };

    const loginToOrg = async org => {
      try {
        const loginRequest = await fetch(`${URL_DOMAIN}/users/login/${org.id}`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (!loginRequest.ok) {
          const errorData = await loginRequest.json();
          throw new Error(errorData.error?.message || 'Login failed');
        }

        const loginResult = await loginRequest.json();
        if (loginResult.token) {
          localStorage.setItem('token', loginResult.token);
          let userInfo = await userService.getUserInfo();
          await userService.setUserInfoSignin(userInfo);
          window.location.reload();
        }
      } catch (err) {
        console.error('Failed to login to org', err);
        status.value = {
          type: 'fail',
          message: err.message || 'Failed to switch to brand. Please try again.'
        };
      }
    };

    const leaveOrg = async org => {
      try {
        if (localStorage.getItem('userOrgId') !== org.id.toString()) {
          await loginToOrg(org);
        }
        const leaveRequest = await fetch(`${URL_DOMAIN}/user`, {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        await leaveRequest.json();
        await fetchOrgs();
      } catch (err) {
        console.error('Failed to leave org', err);
      }
    };

    const showPricingConfirmation = async () => {
      if (!newBrandUrl.value.trim()) {
        status.value = { type: 'fail', message: 'Please enter a brand URL' };
        return;
      }

      try {
        // Get pricing preview
        const response = await fetch(`${URL_DOMAIN}/onboard/agency/pricing-preview`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          pricingPreview.value = await response.json();
          isAddModalOpen.value = false;
          isPricingModalOpen.value = true;
        } else {
          status.value = { type: 'fail', message: 'Failed to get pricing information' };
        }
      } catch (err) {
        console.error('Error getting pricing preview', err);
        status.value = { type: 'fail', message: 'Error getting pricing information' };
      }
    };

    const confirmCreateBrand = async () => {
      try {
        const domain = newBrandUrl.value.replace(/^https?:\/\//, '').replace(/\/$/, '');
        const name = domain.split('.')[0] || domain;
        const response = await fetch(`${URL_DOMAIN}/onboard/self-service-additional`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ organizationName: name, externalDomain: domain })
        });
        if (response.ok) {
          status.value = { type: 'success', message: 'Brand added successfully' };
          await fetchOrgs();
          isPricingModalOpen.value = false;
          newBrandUrl.value = '';
          pricingPreview.value = null;
        } else {
          status.value = { type: 'fail', message: 'Failed to add brand' };
        }
      } catch (err) {
        console.error('Error creating brand', err);
        status.value = { type: 'fail', message: 'Error adding brand' };
      }
    };

    const openAddModal = () => {
      newBrandUrl.value = '';
      isAddModalOpen.value = true;
    };

    // Computed property to filter organizations to show only current org and truly linked orgs
    const filteredUserOrgs = computed(() => {
      const currentId = parseInt(currentOrgId.value || '0');

      // For non-admin users, use the original filtering logic
      return userOrgs.value.filter(org => {
        // Always show the current org
        if (org.id === currentId) {
          return true;
        }
        // Show orgs that have parentId set to the current org (truly linked)
        if (org.parentId === currentId) {
          return true;
        }
        // Hide all other orgs (including those only linked by user accounts)
        return false;
      }).filter(org => org.orgType !== 'agency'); // Also filter out agency type orgs
    });

    onMounted(fetchOrgs);

    return {
      userOrgs,
      filteredUserOrgs,
      isAddModalOpen,
      isPricingModalOpen,
      newBrandUrl,
      status,
      isLoading,
      currentOrgId,
      pricingPreview,
      openAddModal,
      showPricingConfirmation,
      confirmCreateBrand,
      loginToOrg,
      leaveOrg
    };
  }
};
</script>

<style scoped>
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom button hover effects */
.group:hover button {
  transform: translateY(-1px);
}

/* Enhanced focus states */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}
</style>

