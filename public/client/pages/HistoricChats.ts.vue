<template>
  <div class="min-h-screen bg-[#f8f8fb]">
    <!-- Header -->
    <div class="flex items-center justify-between p-6 bg-white">
      <h1 class="text-2xl font-medium text-gray-800">Chat History</h1>
      <router-link
        to="/chat"
        class="flex items-center px-4 py-2 bg-[#9333EA] text-white rounded-md hover:bg-[#7E22CE] transition-colors duration-200"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
        New Chat
      </router-link>
    </div>

    <!-- Filter Section -->
    <div class="flex flex-wrap items-center justify-between px-6 py-4 bg-white border-t border-gray-100">
      <div class="flex space-x-4 mb-2 sm:mb-0">
        <!-- Date Range Dropdown -->
        <!-- <div class="relative date-range-dropdown">
          <button
            @click="toggleDateRangeDropdown"
            class="flex items-center px-4 py-2 border border-gray-200 rounded-md bg-white text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Date Range
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          <div v-if="showDateRangeDropdown" class="absolute z-10 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg">
            <div class="py-1">
              <button @click="setDateRange('all')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">All Time</button>
              <button @click="setDateRange('today')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Today</button>
              <button @click="setDateRange('week')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">This Week</button>
              <button @click="setDateRange('month')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">This Month</button>
            </div>
          </div>
        </div> -->

        <!-- Chat Type Dropdown -->
        <div class="relative chat-type-dropdown">
          <button
            @click="toggleChatTypeDropdown"
            class="flex items-center px-4 py-2 border border-gray-200 rounded-md bg-white text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            {{ chatTypeFilter === 'brief' ? 'Brief Chats' : 'All Chats' }}
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          <!-- Chat Type Dropdown Menu -->
          <div v-if="showChatTypeDropdown" class="absolute z-10 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg">
            <div class="py-1">
              <button @click="setChatTypeFilter('all')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">All Chats</button>
              <button @click="setChatTypeFilter('brief')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Brief Chats</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Search Field -->
      <!-- <div class="relative w-full sm:w-auto">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="Search conversations..."
          class="w-full sm:w-64 px-4 py-2 pl-10 border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500"
          @input="handleSearch"
        />
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute left-3 top-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div> -->
    </div>

    <!-- Main Content -->
    <div class="p-6">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#9333EA]"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center py-12 bg-white rounded-xl shadow-sm">
        <div class="text-red-500 mb-4">{{ error }}</div>
        <button
          @click="fetchConversations"
          class="px-5 py-2.5 bg-[#6e3ff2] text-white rounded-lg hover:bg-[#5a32d4] transition-colors duration-200 font-medium"
        >
          Try Again
        </button>
      </div>

      <!-- Empty State -->
      <div v-else-if="conversations.length === 0" class="text-center py-16 bg-white rounded-xl shadow-sm">
        <div class="mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
          </svg>
        </div>
        <div class="text-gray-500 mb-6 text-lg">You don't have any chat conversations yet.</div>
        <router-link
          to="/chat"
          class="px-5 py-2.5 bg-[#6e3ff2] text-white rounded-lg hover:bg-[#5a32d4] transition-colors duration-200 inline-block font-medium"
        >
          Start a New Chat
        </router-link>
      </div>

      <!-- Conversations List -->
      <div v-else class="space-y-1">
        <div
          v-for="conversation in filteredConversations"
          :key="conversation.id"
          class="bg-white border-b border-gray-100 hover:bg-gray-50 transition-all duration-200"
        >
          <!-- Conversation Row -->
          <div class="flex items-center px-4 py-3 cursor-pointer" @click="toggleExpanded(conversation)">
            <!-- Expand/Collapse Icon -->
            <div class="mr-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 text-gray-400 transition-transform duration-200"
                :class="{ 'transform rotate-90': expandedConversations.includes(conversation.id) }"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>

            <!-- Chat Type Indicator -->
            <div class="mr-3 relative group">
              <div v-if="conversation.taskId" class="flex items-center text-orange-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div v-else class="flex items-center text-blue-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
              </div>
              <div class="absolute left-0 -mt-8 w-24 px-2 py-1 bg-gray-800 rounded-md text-center text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
                {{ conversation.taskId ? 'Brief Chat' : 'Regular Chat' }}
              </div>
            </div>

            <!-- Title -->
            <div class="flex-1">
              <div class="flex items-center">
                <h3
                  v-if="!editingConversation || editingConversation.id !== conversation.id"
                  class="font-medium text-gray-900 cursor-pointer"
                  @click.stop="navigateToChat(conversation.id)"
                >
                  {{ conversation.name || 'Untitled Conversation' }}
                </h3>
                <input
                  v-else
                  type="text"
                  v-model="editingConversation.name"
                  class="w-full font-medium text-gray-900 border-b border-gray-300 focus:border-purple-500 focus:outline-none"
                  @click.stop
                  @keyup.enter="saveTitle"
                  @blur="saveTitle"
                  ref="titleInput"
                />
              </div>
            </div>

            <!-- Dates and Message Count -->
            <div class="text-sm text-gray-500 flex items-center flex-wrap gap-x-2">
              <span>Created {{ formatDate(conversation.createdAt) }}</span>
              <span v-if="conversation.createdByUser" class="mx-1">•</span>
              <span v-if="conversation.createdByUser">by {{ formatUserName(conversation.createdByUser) }}</span>
              <span class="mx-1">•</span>
              <span>Updated {{ formatDate(conversation.updatedAt) }}</span>
              <span class="mx-1">•</span>
              <span>{{ conversation.messageCount || 0 }} messages</span>
            </div>

            <!-- Options Menu -->
            <div class="relative ml-4">
              <button
                @click.stop="toggleOptionsMenu(conversation.id)"
                class="text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                </svg>
              </button>

              <!-- Dropdown Menu -->
              <div
                v-if="activeOptionsMenu === conversation.id"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-20 py-1 border border-gray-200"
              >
                <button
                  @click.stop="startEditing(conversation)"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Rename
                </button>
                <button
                  v-if="!conversation.taskId"
                  @click.stop="deleteConversation(conversation)"
                  class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>

          <!-- Expanded Content -->
          <div
            v-if="expandedConversations.includes(conversation.id)"
            class="px-12 py-3 border-t border-gray-100 bg-gray-50"
          >
            <div class="text-gray-600 mb-3 preview-text">
              {{ getPreviewText(conversation) }}
            </div>
            <button
              @click.stop="navigateToChat(conversation.id)"
              class="text-purple-600 hover:text-purple-800 text-sm font-medium flex items-center"
            >
              Open Chat
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex justify-between items-center mt-6 py-4 px-6 border-t border-gray-100">
        <button
          @click="prevPage"
          :disabled="currentPage === 1"
          :class="[
            'px-3 py-1 rounded flex items-center text-sm font-medium transition-colors duration-200',
            currentPage === 1
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          ]"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          Previous
        </button>
        <div class="flex items-center">
          <span class="text-sm text-gray-600">
            Page {{ currentPage }} of {{ totalPages }}
          </span>
        </div>
        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          :class="[
            'px-3 py-1 rounded flex items-center text-sm font-medium transition-colors duration-200',
            currentPage === totalPages
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          ]"
        >
          Next
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <!-- No Results Message -->
      <div v-if="filteredConversations.length === 0 && !isLoading && !error" class="text-center py-8">
        <div class="text-gray-500">No conversations match your search criteria.</div>
      </div>
    </div>
  </div>

  <!-- Confirm Delete Modal -->
  <ConfirmModal
    v-model="showDeleteModal"
    title="Delete Conversation"
    message="Are you sure you want to delete this conversation? This action cannot be undone."
    confirm-text="Delete"
    cancel-text="Cancel"
    @confirm="confirmDelete"
  />
</template>

<script lang="ts">
import { defineComponent } from '@vue/runtime-core';
import { useRouter } from 'vue-router';
import * as Utils from '../../client-old/utils/Utils';
import ConfirmModal from '../components/common/ConfirmModal.vue';

const URL_DOMAIN = Utils.URL_DOMAIN;

// Define the Conversation interface based on the model
interface Conversation {
  id: number;
  name: string;
  updatedAt: string;
  createdAt: string;
  // isArchived property removed as archive functionality is not available
  messageCount?: number;
  firstMessage?: string;
  lastMessage?: string;
  taskId?: number;
  campaignId?: number;
  createdByUser?: {
    id: number;
    email: string;
    firstName?: string;
    lastName?: string;
  };
  messages?: Array<{
    id: number;
    role: string;
    content: string;
    status: string;
    createdAt: string;
    updatedAt: string;
  }>;
}

export default defineComponent({
  name: 'HistoricChats',
  components: {
    ConfirmModal
  },
  setup() {
    const router = useRouter();
    return { router };
  },
  data() {
    return {
      conversations: [] as Conversation[],
      filteredConversations: [] as Conversation[],
      isLoading: true,
      error: null as string | null,
      currentPage: 1,
      limit: 10,
      total: 0,
      editingConversation: null as Conversation | null,
      titleInput: null as HTMLInputElement | null,

      // Filter properties
      searchQuery: '',
      dateRangeFilter: 'all', // 'all', 'today', 'week', 'month'
      chatTypeFilter: 'all', // 'all', 'brief'
      showDateRangeDropdown: false,
      showChatTypeDropdown: false,

      // Options menu
      activeOptionsMenu: null as number | null,

      // Expanded conversations
      expandedConversations: [] as number[],

      // Delete confirmation modal
      showDeleteModal: false,
      conversationToDelete: null as Conversation | null
    };
  },
  computed: {
    totalPages(): number {
      return Math.ceil(this.total / this.limit);
    }
  },
  mounted() {
    this.fetchConversations();

    // Close dropdowns when clicking outside
    document.addEventListener('click', this.handleOutsideClick);
  },

  beforeUnmount() {
    // Clean up event listener
    document.removeEventListener('click', this.handleOutsideClick);
  },
  methods: {
    formatUserName(user?: { firstName?: string; lastName?: string; email: string }): string {
      if (!user) return 'Unknown User';

      const firstName = user.firstName?.trim();
      const lastName = user.lastName?.trim();

      if (firstName && lastName) {
        return `${firstName} ${lastName}`;
      } else if (firstName) {
        return firstName;
      } else if (lastName) {
        return lastName;
      } else {
        // Fallback to email if no name is available
        return user.email;
      }
    },

    handleOutsideClick(event: MouseEvent): void {
      // Close dropdowns when clicking outside
      const target = event.target as HTMLElement;

      if (this.showDateRangeDropdown && !target.closest('.date-range-dropdown')) {
        this.showDateRangeDropdown = false;
      }

      if (this.showChatTypeDropdown && !target.closest('.chat-type-dropdown')) {
        this.showChatTypeDropdown = false;
      }

      // Close options menu when clicking outside
      if (this.activeOptionsMenu !== null && !target.closest('.relative')) {
        this.activeOptionsMenu = null;
      }

      // Handle unfocusing the title input when editing
      if (this.editingConversation) {
        const titleInputRef = this.$refs.titleInput as HTMLInputElement | null;
        // If the input is being edited and the click target is outside the input field
        if (titleInputRef && target !== titleInputRef && !titleInputRef.contains(target as Node)) {
          // The @click.stop on elements that call startEditing() prevents this
          // from being triggered by the same click that initiated the edit.
          titleInputRef.blur();
        }
      }
    },

    async fetchConversations(): Promise<void> {
      this.isLoading = true;
      this.error = null;

      try {
        const offset = (this.currentPage - 1) * this.limit;
        const token = localStorage.getItem('token');

        const response = await fetch(
          `${URL_DOMAIN}/chat/conversations?limit=${this.limit}&offset=${offset}&sortField=updatedAt&sortOrder=DESC&include=firstMessage,lastMessage`,
          {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (!response.ok) {
          throw new Error('Failed to fetch conversations');
        }

        const data = await response.json();
        this.conversations = data.data;
        this.filteredConversations = [...this.conversations]; // Initialize filtered conversations
        this.total = data.meta.total;

        // Apply any active filters
        if (this.searchQuery || this.dateRangeFilter !== 'all' || this.chatTypeFilter !== 'all') {
          this.applyFilters();
        }
      } catch (error) {
        console.error('Error fetching conversations:', error);
        this.error = 'Failed to load conversations. Please try again.';
      } finally {
        this.isLoading = false;
      }
    },

    navigateToChat(conversationId: number): void {
      // Find the conversation in our list
      const conversation = this.conversations.find((c: Conversation) => c.id === conversationId);

      if (conversation?.taskId) {
        // If the conversation has a taskId, navigate to the task page
        this.router.push(`/ai-strategist/tasks/${conversation.taskId}`);
      } else {
        // Otherwise navigate to the regular chat page
        this.router.push(`/chat?conversation_id=${conversationId}`);
      }
    },

    // Archive functionality removed as it's not currently available

    formatDate(dateString: string): string {
      const date = new Date(dateString);
      const now = new Date();

      // Compare calendar days rather than 24h periods to avoid timezone issues
      const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const startOfDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const diffDays = Math.floor((startOfToday.getTime() - startOfDate.getTime()) / (1000 * 60 * 60 * 24));

      if (diffDays === 0) {
        // Today - show time
        return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      } else if (diffDays === 1) {
        // Yesterday - show time as well to help with clarity
        return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      } else if (diffDays < 7) {
        // Within a week
        return `${date.toLocaleDateString([], { weekday: 'long' })} at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      } else {
        // Older
        return date.toLocaleDateString([], {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
    },

    getPreviewText(conversation: Conversation): string {
      let text = '';

      // Try to get the last message content
      if (conversation.lastMessage) {
        text = conversation.lastMessage;
      }
      // If no last message, try to get from messages array
      else if (conversation.messages && conversation.messages.length > 0) {
        const lastMessage = conversation.messages[conversation.messages.length - 1];
        text = lastMessage.content || 'Empty message';
      }
      // If no messages at all
      else {
        return 'No messages';
      }

      // Truncate text to approximately 1-2 lines (around 100-150 characters)
      if (text.length > 120) {
        return text.substring(0, 120) + '...';
      }

      return text;
    },

    getLastMessage(conversation: Conversation): string {
      if (!conversation.messages || conversation.messages.length === 0) {
        return 'No messages';
      }

      // Get the last message
      const lastMessage = conversation.messages[conversation.messages.length - 1];
      return lastMessage.content || 'Empty message';
    },

    prevPage(): void {
      if (this.currentPage > 1) {
        this.currentPage--;
        this.fetchConversations();
      }
    },

    nextPage(): void {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
        this.fetchConversations();
      }
    },

    startEditing(conversation: Conversation): void {
      // Create a copy of the conversation to edit
      this.editingConversation = { ...conversation };

      // Focus the input field after the DOM updates
      this.$nextTick(() => {
        const input = this.$refs.titleInput as HTMLInputElement;
        if (input) {
          input.focus();
          input.select();
        }
      });
    },

    async saveTitle(): Promise<void> {
      if (!this.editingConversation) return;

      try {
        const token = localStorage.getItem('token');
        const response = await fetch(
          `${URL_DOMAIN}/chat/conversations/${this.editingConversation.id}/name`,
          {
            method: 'PATCH',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ name: this.editingConversation.name })
          }
        );

        if (!response.ok) {
          throw new Error('Failed to update conversation title');
        }

        // Update the conversation in the list
        const index = this.conversations.findIndex((c: Conversation) => c.id === this.editingConversation!.id);
        if (index !== -1) {
          this.conversations[index].name = this.editingConversation.name;
        }

        // Also update in filtered conversations if present
        const filteredIndex = this.filteredConversations.findIndex((c: Conversation) => c.id === this.editingConversation!.id);
        if (filteredIndex !== -1) {
          this.filteredConversations[filteredIndex].name = this.editingConversation.name;
        }
      } catch (error) {
        console.error('Error updating conversation title:', error);
        // Optionally show an error message to the user
      } finally {
        this.editingConversation = null;
      }
    },

    deleteConversation(conversation: Conversation): void {
      this.conversationToDelete = conversation;
      this.showDeleteModal = true;
      this.activeOptionsMenu = null;
    },

    async confirmDelete(): Promise<void> {
      if (!this.conversationToDelete) return;

      try {
        const token = localStorage.getItem('token');
        const response = await fetch(
          `${URL_DOMAIN}/chat/conversations/${this.conversationToDelete.id}`,
          {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (!response.ok) {
          throw new Error('Failed to delete conversation');
        }

        this.conversations = this.conversations.filter(c => c.id !== this.conversationToDelete!.id);
        this.filteredConversations = this.filteredConversations.filter(c => c.id !== this.conversationToDelete!.id);
        this.total = this.total - 1;
      } catch (error) {
        console.error('Error deleting conversation:', error);
      } finally {
        this.conversationToDelete = null;
      }
    },

    // Filter methods
    toggleDateRangeDropdown(): void {
      this.showDateRangeDropdown = !this.showDateRangeDropdown;
      if (this.showDateRangeDropdown) {
        this.showChatTypeDropdown = false;
      }
    },

    toggleChatTypeDropdown(): void {
      this.showChatTypeDropdown = !this.showChatTypeDropdown;
      if (this.showChatTypeDropdown) {
        this.showDateRangeDropdown = false;
      }
    },

    setDateRange(range: string): void {
      this.dateRangeFilter = range;
      this.showDateRangeDropdown = false;
      this.applyFilters();
    },

    setChatTypeFilter(type: string): void {
      this.chatTypeFilter = type;
      this.showChatTypeDropdown = false;
      this.applyFilters();
    },

    handleSearch(): void {
      this.applyFilters();
    },

    toggleOptionsMenu(conversationId: number): void {
      if (this.activeOptionsMenu === conversationId) {
        this.activeOptionsMenu = null;
      } else {
        this.activeOptionsMenu = conversationId;
      }
    },

    toggleExpanded(conversation: Conversation): void {
      const index = this.expandedConversations.indexOf(conversation.id);
      if (index === -1) {
        // Add to expanded list
        this.expandedConversations.push(conversation.id);
      } else {
        // Remove from expanded list
        this.expandedConversations.splice(index, 1);
      }
    },

    applyFilters(): void {
      // Start with all conversations
      let filtered = [...this.conversations];

      // Apply chat type filter
      if (this.chatTypeFilter === 'brief') {
        filtered = filtered.filter(c => c.taskId);
      }

      // Apply date range filter
      if (this.dateRangeFilter !== 'all') {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        filtered = filtered.filter(c => {
          const date = new Date(c.updatedAt);

          if (this.dateRangeFilter === 'today') {
            return date >= today;
          } else if (this.dateRangeFilter === 'week') {
            const weekStart = new Date(today);
            weekStart.setDate(today.getDate() - today.getDay());
            return date >= weekStart;
          } else if (this.dateRangeFilter === 'month') {
            const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
            return date >= monthStart;
          }

          return true;
        });
      }

      // Apply search query
      if (this.searchQuery.trim()) {
        const query = this.searchQuery.toLowerCase().trim();
        filtered = filtered.filter(c => {
          // Search in name
          if (c.name && c.name.toLowerCase().includes(query)) {
            return true;
          }

          // Search in last message
          if (c.lastMessage && c.lastMessage.toLowerCase().includes(query)) {
            return true;
          }

          // Search in first message
          if (c.firstMessage && c.firstMessage.toLowerCase().includes(query)) {
            return true;
          }

          return false;
        });
      }

      this.filteredConversations = filtered;
    }
  }
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.preview-text {
  line-height: 1.5;
  max-height: 4.5rem; /* Approximately 2-3 lines */
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
