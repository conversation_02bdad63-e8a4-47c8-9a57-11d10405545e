<template>


	<div class="flex flex-wrap test-container">
		<div class="test">
			<NumberChart title="Test" number="69,420" change="+4.20%" />
		</div>
		<div class="test">
			<NumberChart title="Test" number="69,420" change="+4.20%" />
		</div>
		<div class="test">
			<NumberChart title="Test" number="69,420" change="+4.20%" />
		</div>
		<div class="test">
			<NumberChart title="Test" number="69,420" change="+4.20%" />
		</div>
	</div>
</template>

<script>
	import NumberChart from '../components/NumberChart.ts.vue';

	export default {
		components: {
			NumberChart
		},
		mounted() {
			console.log('test');
		}
	}
</script>
<style scoped>
.test-container {
	margin: 0.5em;
}
.test {
	width: 50%;
	padding: 0em 0.4em;
}

@media all and (max-width: 768px) {
	.test {
		width: 100%;
	}
}
</style>

