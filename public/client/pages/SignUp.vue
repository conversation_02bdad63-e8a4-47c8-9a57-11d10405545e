<template>
	<main class="bg-white">
		<div class="relative flex">
			<!-- Content -->
			<div class="w-full md:w-1/2">
				<div class="min-h-screen h-full flex flex-col after:flex-1 z-50">
					<div class="flex-1">
						<div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
							<!-- Logo -->
							<router-link class="block" to="/">
								<svg width="120" height="25" viewBox="0 0 147 24" fill="none"
									xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" clip-rule="evenodd"
										d="M25.1863 15.7711L12.6781 0L0.170035 15.7711L2.49477 18.1962L0 21.2107V24H5.34493L12.6781 14.8335L20.0114 24H25.3563V21.2107L22.8615 18.1962L25.1863 15.7711ZM12.6781 5.89126L3.74663 16.6835L2.75569 15.6498L12.6781 3.13888L22.6006 15.6498L21.6097 16.6835L12.6781 5.89126ZM1.95048 21.9131L12.6781 8.95052L23.4058 21.9131V22.0495H20.9488L12.6781 11.7112L4.40748 22.0495H1.95048V21.9131Z"
										fill="url(#paint0_linear_2481_141825)" />
									<path
										d="M18.133 24L12.6783 17.0577L7.22362 24H9.70415L12.6783 20.2147L15.6525 24H18.133Z"
										fill="url(#paint1_linear_2481_141825)" />
									<path
										d="M41.3555 17.8333V6.16667H46.1632C47.1982 6.16667 48.0829 6.33333 48.8174 6.66667C49.5631 7 50.1362 7.47778 50.5368 8.1C50.9375 8.72222 51.1378 9.46111 51.1378 10.3167C51.1378 11.1722 50.9375 11.9111 50.5368 12.5333C50.1362 13.1444 49.5631 13.6167 48.8174 13.95C48.0829 14.2722 47.1982 14.4333 46.1632 14.4333H42.5574L43.5256 13.45V17.8333H41.3555ZM49.0011 17.8333L46.0463 13.6H48.3667L51.3381 17.8333H49.0011ZM43.5256 13.6833L42.5574 12.65H46.063C47.0201 12.65 47.7379 12.4444 48.2165 12.0333C48.7061 11.6222 48.951 11.05 48.951 10.3167C48.951 9.57222 48.7061 9 48.2165 8.6C47.7379 8.2 47.0201 8 46.063 8H42.5574L43.5256 6.93333V13.6833Z"
										fill="#000000" />
									<path
										d="M58.9244 17.8333L64.1828 6.16667H66.3196L71.5947 17.8333H69.3244L64.8005 7.31667H65.6685L61.1613 17.8333H58.9244ZM61.3449 15.1333L61.9292 13.4333H68.2393L68.8236 15.1333H61.3449Z"
										fill="#000000" />
									<path d="M80.092 17.8333V6.16667H82.2622V16H88.372V17.8333H80.092Z"
										fill="#000000" />
									<path
										d="M99.0997 11.0167H104.909V12.8H99.0997V11.0167ZM99.2666 16.0167H105.861V17.8333H97.0965V6.16667H105.627V7.98333H99.2666V16.0167Z"
										fill="#000000" />
									<path
										d="M120.766 18C119.854 18 119.014 17.85 118.246 17.55C117.478 17.25 116.81 16.8333 116.243 16.3C115.675 15.7556 115.235 15.1222 114.924 14.4C114.612 13.6667 114.456 12.8667 114.456 12C114.456 11.1333 114.612 10.3389 114.924 9.61667C115.235 8.88333 115.675 8.25 116.243 7.71667C116.81 7.17222 117.478 6.75 118.246 6.45C119.014 6.15 119.848 6 120.75 6C121.662 6 122.497 6.15 123.254 6.45C124.022 6.75 124.689 7.17222 125.257 7.71667C125.825 8.25 126.264 8.88333 126.576 9.61667C126.887 10.3389 127.043 11.1333 127.043 12C127.043 12.8667 126.887 13.6667 126.576 14.4C126.264 15.1333 125.825 15.7667 125.257 16.3C124.689 16.8333 124.022 17.25 123.254 17.55C122.497 17.85 121.668 18 120.766 18ZM120.75 16.1C121.34 16.1 121.885 16 122.386 15.8C122.887 15.6 123.321 15.3167 123.688 14.95C124.055 14.5722 124.339 14.1389 124.539 13.65C124.751 13.15 124.856 12.6 124.856 12C124.856 11.4 124.751 10.8556 124.539 10.3667C124.339 9.86667 124.055 9.43333 123.688 9.06667C123.321 8.68889 122.887 8.4 122.386 8.2C121.885 8 121.34 7.9 120.75 7.9C120.16 7.9 119.615 8 119.114 8.2C118.624 8.4 118.19 8.68889 117.812 9.06667C117.444 9.43333 117.155 9.86667 116.944 10.3667C116.743 10.8556 116.643 11.4 116.643 12C116.643 12.5889 116.743 13.1333 116.944 13.6333C117.155 14.1333 117.444 14.5722 117.812 14.95C118.179 15.3167 118.613 15.6 119.114 15.8C119.615 16 120.16 16.1 120.75 16.1Z"
										fill="#000000" />
									<path
										d="M136.355 17.8333V6.16667H138.141L145.469 15.15H144.585V6.16667H146.738V17.8333H144.952L137.623 8.85H138.508V17.8333H136.355Z"
										fill="#000000" />
									<defs>
										<linearGradient id="paint0_linear_2481_141825" x1="-0.25" y1="15" x2="26.9315"
											y2="13.755" gradientUnits="userSpaceOnUse">
											<stop stop-color="#6536E2" />
											<stop offset="1" stop-color="#1E90DB" />
										</linearGradient>
										<linearGradient id="paint1_linear_2481_141825" x1="-0.25" y1="15" x2="26.9315"
											y2="13.755" gradientUnits="userSpaceOnUse">
											<stop stop-color="#6536E2" />
											<stop offset="1" stop-color="#1E90DB" />
										</linearGradient>
									</defs>
								</svg>
							</router-link>
						</div>
					</div>
					<div class="mx-auto px-8 py-8" style="min-width: 500px;">
						<!-- Title section -->
						<div class="mb-8">
							<h1 class="text-3xl text-slate-800 font-bold mb-2">Create Your Account</h1>
							<p class="text-gray-600">Get started with your free trial of Raleon AI</p>
						</div>

						<!-- Sign up form -->
						<div class="space-y-6">
							<!-- Error Message -->
							<div v-if="error"
								class="flex items-center p-4 w-full text-gray-500 bg-red-100 border border-red-400 rounded-lg shadow">
								<div
									class="inline-flex flex-shrink-0 justify-center items-center w-8 h-8 text-red-500 bg-red-100 rounded-lg text-red-700">
									<svg aria-hidden="true" class="w-5 h-5" fill="#f56565" viewBox="0 0 20 20">
										<path fill-rule="evenodd"
											d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
											clip-rule="evenodd"></path>
									</svg>
								</div>
								<div class="ml-3 text-sm font-normal">
								{{ error }}
								<span v-if="error && error.includes('already exists')">
									<router-link to="/signin" class="text-indigo-600 hover:text-indigo-800 font-medium underline ml-2">
										Go to Sign In
									</router-link>
								</span>
							</div>
							</div>

							<form @submit.prevent="handleSignUp">
								<div class="space-y-4">
									<div>
										<label class="block text-sm font-medium mb-1" for="email">Email Address</label>
										<input id="email" class="form-input w-full" type="email"
											v-model="formData.email" required placeholder="<EMAIL>" />
									</div>
									<div>
										<label class="block text-sm font-medium mb-1" for="password">Password</label>
										<input id="password" class="form-input w-full" type="password"
											v-model="formData.password" required placeholder="8+ characters" />
									</div>
									<div>
										<label class="block text-sm font-medium mb-1" for="brandUrl">
											Website URL
											<span class="text-xs text-gray-500 ml-1">(optional - helps us customize your experience)</span>
										</label>
										<input id="brandUrl" class="form-input w-full" type="text"
											v-model="formData.externalDomain" placeholder="example.com" />
									</div>
								</div>
								<div class="mt-6">
									<button class="btn bg-indigo-500 hover:bg-indigo-600 text-white w-full"
										type="submit" :disabled="loading">
										<span v-if="loading">Creating Account...</span>
										<span v-else>Create Account</span>
									</button>
								</div>
							</form>

							<!-- Google Sign Up -->
							<div class="pt-5 border-t border-slate-200">
								<button @click="signUpWithGoogle"
									class="btn bg-gray-50 border border-gray-500 text-gray-700 w-full inline-flex items-center justify-center space-x-2 py-2 px-4 hover:bg-gray-100 shadow-sm hover:shadow-md transition-shadow duration-200">
									<svg class="w-5 h-5" viewBox="0 0 24 24">
										<path fill="#4285F4"
											d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
										<path fill="#34A853"
											d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
										<path fill="#FBBC05"
											d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
										<path fill="#EA4335"
											d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
									</svg>
									<span>Sign up with Google</span>
								</button>
							</div>

							<!-- Sign In Link -->
							<div class="text-center mt-6">
								<p class="text-sm text-gray-600">
									Already have an account?
									<router-link to="/signin" class="text-indigo-500 hover:text-indigo-600 font-medium">
										Sign in here
									</router-link>
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Futuristic Loading Modal -->
		<div v-if="showLoadingModal" class="fixed inset-0 z-50 flex items-center justify-center"
			style="background: rgba(0, 0, 0, 0.8); backdrop-filter: blur(8px);">
			<div
				class="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-8 rounded-2xl shadow-2xl border border-purple-500/30 max-w-md w-full mx-4 relative overflow-hidden">
				<!-- Animated background grid -->
				<div class="absolute inset-0 opacity-20">
					<div class="grid-pattern animate-pulse"></div>
				</div>

				<!-- Content -->
				<div class="relative z-10">
					<!-- Logo/Title -->
					<div class="text-center mb-8">
						<div
							class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full flex items-center justify-center">
							<svg class="w-8 h-8 text-white animate-spin" fill="none" viewBox="0 0 24 24">
								<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
									stroke-width="4"></circle>
								<path class="opacity-75" fill="currentColor"
									d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
								</path>
							</svg>
						</div>
						<h2 class="text-2xl font-bold text-white mb-2">Initializing Raleon AI</h2>
						<p class="text-purple-200 text-sm">Setting up your personalized experience</p>
					</div>

					<!-- Loading Steps -->
					<div class="space-y-4">
						<div v-for="(step, index) in loadingSteps" :key="index" class="flex items-center space-x-3">
							<div :class="[
								'w-6 h-6 rounded-full flex items-center justify-center transition-all duration-500',
								currentLoadingStep > index
									? 'bg-green-500 text-white'
									: currentLoadingStep === index
										? 'bg-purple-500 text-white animate-pulse'
										: 'bg-gray-600 text-gray-400'
							]">
								<svg v-if="currentLoadingStep > index" class="w-4 h-4" fill="currentColor"
									viewBox="0 0 20 20">
									<path fill-rule="evenodd"
										d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
										clip-rule="evenodd"></path>
								</svg>
								<div v-else-if="currentLoadingStep === index"
									class="w-2 h-2 bg-white rounded-full animate-ping"></div>
								<span v-else class="text-xs font-mono">{{ index + 1 }}</span>
							</div>
							<div :class="[
								'flex-1 transition-all duration-500',
								currentLoadingStep >= index ? 'text-white' : 'text-gray-400'
							]">
								<p class="font-medium">{{ step.title }}</p>
								<p class="text-xs opacity-75">{{ step.description }}</p>
							</div>
						</div>
					</div>

					<!-- Progress Bar -->
					<div class="mt-6">
						<div class="w-full bg-gray-700 rounded-full h-2 overflow-hidden">
							<div class="h-full bg-gradient-to-r from-purple-500 to-indigo-500 transition-all duration-1000 ease-out"
								:style="{width: loadingProgress + '%'}"></div>
						</div>
						<p class="text-center text-purple-200 text-xs mt-2">{{ Math.round(loadingProgress) }}% Complete
						</p>
					</div>
				</div>
			</div>
		</div>

		<!-- Background Image -->
		<div class="hidden md:block absolute top-0 bottom-0 right-0 md:w-1/2" aria-hidden="true">
			<img class="object-cover object-center w-full h-full" src="../images/r-login-bg.png" width="760"
				height="1024" alt="Authentication" />
		</div>

	</main>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';
import * as userService from '../services/user.js'
import { useSessionStore } from '../pages/useSessionStore.ts';
import { userLogin, getUserInfo, setUserInfo } from '../services/user';

const URL_DOMAIN = Utils.URL_DOMAIN;
const GOOGLE_CLIENT_ID = Utils.GOOGLE_CLIENT_ID;

export default {
	name: 'SignUp',
	data() {
		const sessionStore = useSessionStore();
		return {
			formData: {
				email: '',
				password: '',
				externalDomain: '',
				createToken: true
			},
			loading: false,
			error: null,
			sessionStore,
			showLoadingModal: false,
			currentLoadingStep: 0,
			loadingProgress: 0,
			loadingSteps: [
				{
					title: 'Gathering initial data from your website',
					description: 'Analyzing your brand presence and content'
				},
				{
					title: 'Fine Tuning Starting Model',
					description: 'Customizing AI algorithms for your needs'
				},
				{
					title: 'Understanding Brand Language',
					description: 'Learning your unique voice and tone'
				}
			]
		};
	},
	methods: {
		async handleSignUp() {
			if (!this.formData.email || !this.formData.password) {
				this.error = 'Please fill in all required fields';
				return;
			}

			if (this.formData.password.length < 8) {
				this.error = 'Password must be at least 8 characters long';
				return;
			}

			this.error = null;
			this.loading = true;

			try {
				// First check if user exists before showing any loading modal
				const checkResponse = await fetch(`${URL_DOMAIN}/users/check-email`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({ email: this.formData.email })
				});

				if (checkResponse.ok) {
					const checkResult = await checkResponse.json();
					if (checkResult.exists) {
						// User exists - show error, no loading modal
						this.error = checkResult.message;
						this.loading = false;
						return;
					}
				}

				// User doesn't exist, proceed with account creation
				// Show the futuristic loading modal
				this.showLoadingModal = true;
				this.currentLoadingStep = 0;
				this.loadingProgress = 0;

				// Start account creation in background while animation plays
				const accountCreationPromise = this.createMinimalAccount();

				// Animate through loading steps (15 seconds)
				await this.animateLoadingSteps();

				// Wait for account creation to complete
				await accountCreationPromise;

				// Store that user needs to complete onboarding and current form data
				localStorage.setItem('needs_onboarding_completion', 'true');
				localStorage.setItem('signup_form_data', JSON.stringify({
					externalDomain: this.formData.externalDomain || ''
				}));

				// Hide modal and navigate to chat with onboarding parameter
				this.showLoadingModal = false;
				this.$router.push('/chat?onboarding=true');
			} catch (err) {
				console.error('Signup error:', err);
				this.error = err.message || 'Failed to create account. Please try again.';
				this.showLoadingModal = false;
			} finally {
				this.loading = false;
			}
		},

		async createMinimalAccount() {

			// Set default values for minimal account creation
			const accountData = {
				email: this.formData.email,
				password: this.formData.password,
				firstName: this.formData.email.split('@')[0], // Use email prefix as default name
				organizationName: this.formData.email.split('@')[0] + "'s Organization", // Default org name
				orgType: 'individual', // Default to individual, can be changed later
				externalDomain: this.formData.externalDomain || '', // Use provided URL or empty
				createToken: true,
				isSignup: true // Mark this as a signup request
			};

			// Call the self-service endpoint
			const response = await fetch(`${URL_DOMAIN}/onboard/agency/self-service`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(accountData)
			});

			if (!response.ok) {
				const errorData = await response.json().catch(() => null);
				throw new Error(errorData?.error?.message || 'Failed to create account');
			}

			const data = await response.json();
			localStorage.setItem('token', data.accessToken);

			const loginResult = await userLogin(this.formData.email, this.formData.password);
			if (loginResult.token) {
				localStorage.setItem('token', loginResult.token);
				let userInfo = await getUserInfo();
				await setUserInfo(userInfo);

				// If user provided a URL, kick off onboarding immediately
				if (this.formData.externalDomain) {
					try {
						const onboardResponse = await fetch(`${URL_DOMAIN}/onboard/begin`, {
							method: 'POST',
							credentials: 'omit',
							mode: 'cors',
							headers: {
								'Content-Type': 'application/json',
								Authorization: `Bearer ${localStorage.getItem('token')}`,
							},
							body: JSON.stringify({
								externalDomain: this.formData.externalDomain
							}),
						});

						if (!onboardResponse.ok) {
							console.log("Error starting onboarding process");
						}
						localStorage.setItem('onboard_begin_called' + localStorage.getItem('userOrgId'), 'true');
					} catch (error) {
						console.warn('Failed to start onboarding process:', error);
					}
				}

				this.sessionStore.logIn();
			} else {
				throw new Error('Failed to log in after account creation');
			}
		},

		async signUpWithGoogle() {
			if (!window.google) {
				await new Promise(resolve => {
					const script = document.createElement('script');
					script.src = 'https://accounts.google.com/gsi/client';
					script.onload = resolve;
					document.head.appendChild(script);
				});
			}

			// Use OAuth2 with popup - larger modal with account selection
			const tokenClient = window.google.accounts.oauth2.initTokenClient({
				client_id: GOOGLE_CLIENT_ID,
				scope: 'email profile',
				hint: '',
				hosted_domain: '',
				callback: async (tokenResponse) => {
					if (tokenResponse && tokenResponse.access_token) {
						try {
							// Get user info from Google
							const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
								headers: {
									'Authorization': `Bearer ${tokenResponse.access_token}`
								}
							});
							const userInfo = await userResponse.json();

							// Populate form with Google data
							this.formData.email = userInfo.email;
							this.formData.firstName = userInfo.given_name || userInfo.name || '';
							this.googleIdToken = tokenResponse.access_token;

							// Clear any previous errors
							this.error = null;

							// Try to create account directly with Google info
							try {
								await this.createGoogleAccount(userInfo, tokenResponse.access_token);
							} catch (accountError) {
								// If account creation fails, just go to step 2 for manual completion
								console.warn('Direct Google account creation failed, continuing with form:', accountError);
								this.currentStep = 2;
							}
						} catch (error) {
							console.error('Google sign-up error:', error);
							this.error = 'An error occurred during Google sign-up. Please try again or use email signup.';
						}
					}
				}
			});

			// Request token with prompt for account selection
			tokenClient.requestAccessToken({
				prompt: 'select_account'
			});
		},

		async createGoogleAccount(userInfo, accessToken) {
			// Check if user already exists first
			const checkResponse = await fetch(`${URL_DOMAIN}/users/google-login`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					email: userInfo.email,
					name: userInfo.name,
					access_token: accessToken
				})
			});

			if (checkResponse.ok) {
				// User already exists, show error
				this.error = 'Account already exists with this Google email. Please sign in instead.';
				return;
			}

			// User doesn't exist, create new account
			// Set up account data with Google info
			const accountData = {
				email: userInfo.email,
				password: 'google_' + Math.random().toString(36), // Random password for Google users
				firstName: userInfo.given_name || userInfo.name || userInfo.email.split('@')[0],
				organizationName: (userInfo.given_name || userInfo.name || userInfo.email.split('@')[0]) + "'s Organization",
				orgType: 'individual', // Default for Google signup
				externalDomain: '', // Will be filled in later if needed
				createToken: true,
				isSignup: true // Mark this as a signup request
			};

			// Show loading modal
			this.showLoadingModal = true;
			this.currentLoadingStep = 0;
			this.loadingProgress = 0;

			try {
				// Create account
				const response = await fetch(`${URL_DOMAIN}/onboard/agency/self-service`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify(accountData)
				});

				if (!response.ok) {
					throw new Error('Failed to create account');
				}

				// Animate loading steps
				await this.animateLoadingSteps();

				const data = await response.json();
				localStorage.setItem('token', data.accessToken);

				// Log in the user
				const loginResult = await userLogin(accountData.email, accountData.password);
				if (loginResult.token) {
					localStorage.setItem('token', loginResult.token);
					let userInfo = await getUserInfo();
					await setUserInfo(userInfo);
					this.sessionStore.logIn();

					// Hide modal and redirect
					this.showLoadingModal = false;
					this.$router.push('/chat?onboarding=true');
				} else {
					throw new Error('Failed to log in after account creation');
				}
			} catch (error) {
				this.showLoadingModal = false;
				throw error; // Re-throw to be caught by calling function
			}
		},

		async animateLoadingSteps() {
			return new Promise((resolve) => {
				const totalSteps = this.loadingSteps.length;
				const stepDuration = 15000 / totalSteps; // 15 seconds total
				let currentStep = 0;

				const interval = setInterval(() => {
					this.currentLoadingStep = currentStep;
					this.loadingProgress = ((currentStep + 1) / totalSteps) * 100;

					currentStep++;

					if (currentStep >= totalSteps) {
						clearInterval(interval);
						// Wait a bit more to show completion
						setTimeout(() => {
							resolve();
						}, 1000);
					}
				}, stepDuration);
			});
		}
	}
};
</script>

<style scoped>
.form-input {
	@apply appearance-none rounded-lg block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500;
}

.btn {
	@apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.grid-pattern {
	background-image:
		linear-gradient(rgba(139, 92, 246, 0.1) 1px, transparent 1px),
		linear-gradient(90deg, rgba(139, 92, 246, 0.1) 1px, transparent 1px);
	background-size: 20px 20px;
	width: 100%;
	height: 100%;
}
</style>
