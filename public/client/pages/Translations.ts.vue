<template>
	<div class="flex h-screen overflow-hidden bg-gray-50">
		<!-- Content area -->
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<ToastStatus :status="status" :text="statusText" @clear-status="clearStatus()" />
			<main>
				<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<!-- Page header -->
					<div class="mb-8 flex">
						<h1 class="text-3xl md:text-4xl text-gray-900 font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent">Translation Settings</h1>
					</div>

					<!-- Content -->
					<div class="bg-white shadow-xl rounded-2xl mb-8 border border-gray-100">
						<div class="flex flex-col md:flex-row md:-mr-px">
							<SettingsSidebar />

							<div class="grow">
								<!-- Panel body -->
								<div class="p-8 space-y-8">
									<!-- Header Section -->
									<div class="mb-6">
										<h2 class="text-2xl font-bold text-gray-900 mb-2">Manage Translations</h2>
										<p class="text-gray-600">Customize text and labels displayed to your customers in their preferred language.</p>
									</div>

									<!-- Search Section -->
									<div class="mb-6">
										<form class="relative" @submit.prevent="onSearch">
											<label for="action-search" class="sr-only">Search translations</label>
											<div class="relative">
												<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
													<svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
													</svg>
												</div>
												<input 
													id="action-search"
													v-model="translationSearchTerm"
													class="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200"
													type="search" 
													placeholder="Search translation keys, labels, or values..." 
												/>
											</div>
										</form>
									</div>

									<!-- Info Box -->
									<div class="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-6">
										<div class="flex items-start">
											<svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
												<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
											</svg>
											<div>
												<h3 class="text-blue-900 text-base font-semibold mb-1">How to edit translations</h3>
												<p class="text-blue-800 text-sm">Click on any row to edit the translation value. Your custom translations will be marked as overrides and take precedence over default values.</p>
											</div>
										</div>
									</div>

									<!-- Translations Table -->
									<div v-if="!isLoading" class="border border-gray-100 rounded-xl overflow-hidden">
										<RaleonTable 
											:column-headers="translationColumnHeaders" 
											:row-data="filteredTranslationData"
											:clickable-rows="true"
											:auto-wrap="true"
											@row-clicked="handleRowClick" 
										/>
									</div>

									<!-- Loading State -->
									<div v-if="isLoading" class="flex items-center justify-center py-12">
										<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
										<span class="ml-3 text-gray-600">Loading translations...</span>
									</div>

									<!-- Empty State -->
									<div v-if="!isLoading && filteredTranslationData.length === 0" class="text-center py-12">
										<svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
										</svg>
										<h3 class="text-lg font-medium text-gray-900 mb-2">No translations found</h3>
										<p class="text-gray-500">
											{{ translationSearchTerm ? 'Try adjusting your search terms.' : 'No translation strings available.' }}
										</p>
									</div>

									<!-- Summary Stats -->
									<div v-if="!isLoading && translationData.length > 0" class="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t border-gray-100">
										<div class="bg-gray-50 rounded-xl p-4 text-center">
											<div class="text-2xl font-bold text-gray-900">{{ translationData.length }}</div>
											<div class="text-sm text-gray-600">Total Translations</div>
										</div>
										<div class="bg-purple-50 rounded-xl p-4 text-center">
											<div class="text-2xl font-bold text-purple-600">{{ translationData.filter(t => t.isOverride === 'YES').length }}</div>
											<div class="text-sm text-gray-600">Custom Overrides</div>
										</div>
										<div class="bg-green-50 rounded-xl p-4 text-center">
											<div class="text-2xl font-bold text-green-600">{{ filteredTranslationData.length }}</div>
											<div class="text-sm text-gray-600">Showing Results</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</main>
		</div>
	</div>
</template>

<script>
import RaleonTable from '../components/RaleonTable.ts.vue';
import * as Utils from '../../client-old/utils/Utils';
import Header from '../../client-old/partials/Header.vue'
import SettingsSidebar from '../../client-old/partials/settings/SettingsSidebar.vue'
import ToastStatus from '../../client-old/pages/component/ToastStatus.vue'
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue'
import CampaignTableSkeletonLoader from '../components/CampaignTableSkeletonLoader.ts.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		Header,
		SettingsSidebar,
		ToastStatus,
		RaleonTable,
		LightSecondaryButton,
		CampaignTableSkeletonLoader
	},
	data() {
		return {
			allShoppers: [],
			displayShoppers: [],
			searchedShoppers: [],
			currentPage: 1,
			totalPages: 0,
			totalCustomers: 0,
			nextPageNum: null,
			limit: 15,
			isLoading: true,
			translationData: [],
			translationSearchTerm: null,
			filteredTranslationData: [],
			translationColumnHeaders: [
				{
					name: 'Translation Key',
					tooltip: 'Translation Key',
					value: 'label',  // This will correspond to the combined name field
				},
				{
					name: 'Translated Value',
					tooltip: 'Translation',
					value: 'value',
				},
				{
					name: 'Is Override',
					tooltip: 'Translation',
					value: 'isOverride',
				},
			],
		};
	},
	watch: {
		translationSearchTerm: function (val) {
			this.filteredTranslationData = this.translationData.filter(x => !val || x.key.includes(val.toLowerCase()) || x.label.includes(val.toLowerCase()) || x.value.toLowerCase().includes(val.toLowerCase()));
		}
	},
	async mounted() {
		customerIOTrackEvent('Translation Workbench');
		const translationsResponse = await fetch(`${URL_DOMAIN}/loyalty-translations/${localStorage.getItem('userOrgId')}`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});

		const translationsResult = await translationsResponse.json();
		this.translationData = translationsResult.translations.map(x => ({
			...x,
			isOverride: x.orgId > 0 ? 'YES' : '',
			label: x.key.replace(/_/g, ' ')
		})).sort((a, b) => {
			if (a.isOverride === 'YES' && b.isOverride !== 'YES') {
				return -1;
			}
			if (a.isOverride !== 'YES' && b.isOverride === 'YES') {
				return 1;
			}

			return a.key.localeCompare(b.key);
		});

		this.filteredTranslationData = [...this.translationData];
		this.isLoading = false;
	},
	methods: {
		async handleRowClick(row) {
			const override = prompt('Enter the translation value', row.value);
			if (override) {
				const entry = this.translationData.find(x => x.key === row.key);
				entry.value = override;

				const translationResponse = await fetch(`${URL_DOMAIN}/translation-overrides`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					body: JSON.stringify([entry]),
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					}
				});

				if (!translationResponse.ok || translationResponse.status < 200 || translationResponse.status >= 300) {
					this.status = 'error';
					this.statusText = 'Error updating translation.';
					throw new Error('Failed to save translations');
				}

			}
		},
	}
};
</script>

<style>
.pagination {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 20px;
}
</style>
