# Raleon Web App - Claude Development Guide

## Project Overview

Raleon is a B2B SaaS platform that provides AI-powered retention and marketing tools for ecommerce and retail brands. The platform offers three core features:

1. **AI Segmentation** - Smart customer segmentation using AI
2. **Email Generation** - AI-powered email campaign creation
3. **Loyalty Programs** - Comprehensive loyalty and rewards management

The application serves both in-house teams at ecommerce brands and agencies working with those brands.

## Architecture Overview

### Backend (LoopBack 4)

The backend is built on **LoopBack 4**, a TypeScript-based Node.js framework that provides:

- **RESTful API** with auto-generated OpenAPI documentation
- **PostgreSQL** database with repository pattern
- **JWT Authentication** with multiple strategies (API keys, Shopify customer auth, user access tokens)
- **Modular architecture** with controllers, models, repositories, and services

#### Key Backend Components:

- **`src/application.ts`** - Main application configuration
- **`src/controllers/`** - REST API endpoints organized by domain
- **`src/models/`** - Database models with decorators
- **`src/repositories/`** - Data access layer
- **`src/services/`** - Business logic and external integrations
- **`src/datasources/`** - Database connection configuration

### Frontend (Vue 3 + Vite)

The frontend is a **Vue 3** single-page application with:

- **TypeScript** for type safety
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Pinia** for state management
- **Vue Router** for navigation

#### Key Frontend Structure:

- **`public/client/`** - Main Vue application
- **`public/client/pages/`** - Vue page components
- **`public/client/components/`** - Reusable Vue components
- **`public/client/services/`** - API communication and business logic
- **`public/client-old/`** - Legacy Vue components (being phased out)

## Tech Stack

### Core Technologies
- **Backend**: Node.js, LoopBack 4, TypeScript
- **Frontend**: Vue 3, TypeScript, Vite
- **Database**: PostgreSQL
- **Styling**: Tailwind CSS, SCSS
- **Authentication**: JWT, API Keys
- **Testing**: Vitest (frontend), Mocha (backend), Cypress (E2E)

### Key Dependencies
- **AI Integration**: OpenAI GPT models, Anthropic Claude
- **Email**: AWS SES, Unlayer email editor
- **Payments**: Stripe
- **Image Processing**: Sharp, Canvas
- **Ecommerce**: Shopify API integration
- **Charts**: Chart.js, Highcharts
- **Blockchain**: Ethers.js, Web3.js (for NFT features)

## Domain Model

### Core Entities

- **Organization** - Business entity using the platform
- **User** - Platform users (employees/agency staff)
- **Raleon User** - End customers of the organization
- **Campaign** - Marketing campaigns
- **Segment** - Customer groups defined by criteria
- **Loyalty Program** - Rewards and points system
- **Plan** - AI-generated marketing plans
- **Task** - Work items within campaigns

### Chat/AI System

- **Conversation** - Chat sessions with AI
- **Message** - Individual messages in conversations
- **Prompt Template** - Reusable AI prompt structures

## Development Workflow

### Local Development

```bash
# Install dependencies
npm install

# Start backend (builds and runs on port 3000)
npm start

# Start frontend dev server (port 3030)
npm run dev:ui

# Build frontend for production
npm run build:ui

# Run tests
npm test              # Backend tests
npm run test:client   # Frontend tests
```

### Database Operations

```bash
# Run database migrations
npm run migrate

# Build and migrate (production)
npm run migrate-prod
```

### Code Quality

```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run prettier:fix
```

## API Architecture

### RESTful Endpoints

The application uses LoopBack's automatic CRUD REST generation with custom controllers for business logic. Key endpoint patterns:

- `/api/organizations/{id}` - Organization management
- `/api/campaigns` - Campaign operations
- `/api/segments` - Customer segmentation
- `/api/loyalty-programs` - Loyalty management
- `/api/chat` - AI chat interface
- `/api/plans` - Marketing plan generation

### Authentication Strategies

1. **JWT Authentication** - Standard user authentication
2. **API Key Authentication** - For external integrations
3. **Shopify Customer Auth** - For Shopify-specific operations
4. **User Access Token** - For specific user-scoped operations

## Frontend Architecture

### Component Organization

- **Pages** - Route-level components in `pages/`
- **Components** - Reusable UI components in `components/`
- **Services** - API communication and business logic
- **Composables** - Vue 3 composition utilities
- **Types** - TypeScript type definitions

### State Management

Using Pinia for:
- User session management
- Organization settings
- Chat state
- Feature flags

### Styling Guidelines

Following brand guidelines with:
- **Primary Color**: Dark purple (#6E41FF to #8A4FFF)
- **Secondary Colors**: Light purple, green (#B8F4B8), light blue (#E6F0FF)
- **Typography**: Clean sans-serif with gradient effects
- **UI Elements**: Rounded corners, pill-shaped buttons, minimal design

## Key Integrations

### External Services

- **OpenAI/Anthropic** - AI content generation
- **Shopify** - Ecommerce platform integration
- **Klaviyo** - Email marketing platform
- **Stripe** - Payment processing
- **AWS SES** - Email delivery
- **Unsplash** - Stock images

### Email Marketing

- **Unlayer Editor** - Visual email design
- **Template System** - Reusable email templates
- **Campaign Management** - Email automation and scheduling

## Testing Strategy

### Backend Testing
- **Unit Tests**: Model and service testing with Mocha
- **Integration Tests**: API endpoint testing
- **Test Location**: `src/__tests__/`

### Frontend Testing
- **Unit Tests**: Component testing with Vitest
- **E2E Tests**: User flow testing with Cypress
- **Test Locations**: `public/client/tests/` and `cypress/`

## Deployment

### Build Process
```bash
npm run build     # Builds both frontend and backend
npm run rebuild   # Clean build
```

### Docker Support
```bash
npm run docker:build  # Build Docker image
npm run docker:run    # Run in container
```

### Environment Configuration
- Uses `.env` files for configuration
- Environment variables exported via `scripts/export-environment.js`
- Supports development, staging, and production environments

## Development Guidelines

### Code Style
1. **Use fetch instead of axios** for HTTP requests
2. **Use URL_DOMAIN for API calls** - Import URL_DOMAIN from utils and use `${URL_DOMAIN}/endpoint` instead of `/api/endpoint` for all API calls
3. **TypeScript required** for all new code
4. **Follow LoopBack 4 patterns** for backend development
5. **Use Vue 3 Composition API** for new components
6. **Maintain ubiquitous language** as defined in `ubiquitous-language.md`

### File Naming
- **Backend**: kebab-case for files, PascalCase for classes
- **Frontend**: PascalCase for components, camelCase for utilities
- **TypeScript extensions**: Use `.ts.vue` for TypeScript Vue components

### Security Considerations
- JWT tokens for authentication
- API rate limiting and guards
- Input validation on all endpoints
- CORS configuration for frontend communication

## Common Development Tasks

### Adding a New Model
1. Create model in `src/models/`
2. Create repository in `src/repositories/`
3. Add CRUD controller or use auto-generation
4. Run migration to update database

### Creating New API Endpoints
1. Add controller in `src/controllers/`
2. Implement business logic in `src/services/`
3. Add authentication decorators as needed
4. Update OpenAPI documentation

### Adding Frontend Features
1. Create page component in `public/client/pages/`
2. Add route in `public/client/router.ts`
3. Create supporting components in `public/client/components/`
4. Add API service calls in `public/client/services/`

### Working with AI Features
1. **Chat Integration**: Use `chatService.ts` for AI conversations
2. **Tool Handlers**: Extend `toolHandlerService.ts` for new AI tools
3. **Prompt Templates**: Define in `src/services/prompt/`
4. **Message Parsing**: Use `messageParserService.ts` for response handling

## Performance Considerations

- **Database**: Use indexes and optimize queries
- **Frontend**: Lazy load components and implement virtual scrolling
- **API**: Implement pagination for large datasets
- **Caching**: Use memcached for frequently accessed data
- **Images**: Optimize with Sharp for processing

## Troubleshooting

### Common Issues
1. **Build Failures**: Check TypeScript errors and dependencies
2. **Database Connections**: Verify PostgreSQL configuration
3. **Authentication Errors**: Check JWT secret and token validity
4. **CORS Issues**: Verify frontend/backend URL configuration
5. **Migration Problems**: Check model changes and database state

### Debug Tools
- **Backend**: Use `--inspect` flag for Node.js debugging
- **Frontend**: Vue DevTools for component inspection
- **API**: `/explorer` endpoint for OpenAPI documentation
- **Logs**: Winston logging with CloudWatch integration

This guide provides a foundation for working with the Raleon Web App codebase. Refer to the individual README files in each directory for more specific implementation details.