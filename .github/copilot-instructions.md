We are building an AI retention agent (agentic retention). We have Segmentation, Email Generation and Loyalty.

The app is a b2b saas app that is used by in-house teams at ecommerce and retail brands, as well as agencies for those brands.

If designing or making UI please use the following style guide, example of our design:
Brand Guidelines
Colors

Primary Brand Color: Dark purple (#6E41FF to #8A4FFF)
Secondary Colors:

Light purple/lavender for highlights and accents
Green (#B8F4B8) for positive states and "AI Managed" indicators
Light blue (#E6F0FF) for neutral states and signals
White for cards and content areas
Light gray (#F8F8F8) for backgrounds

Typography

Clean, modern sans-serif font family
Clear hierarchy with varying weights
Gradient text effects for key headings (purple-to-blue)
Dark gray (#4A4A4A) for primary text
Lighter gray for supporting text and descriptions

UI Elements

Rounded corners on all containers (cards, buttons, input fields)
Pill-shaped buttons for filters, signals, and tags
Consistent padding (~16-24px) between elements
White cards with subtle shadows on light backgrounds
Progress indicators using purple gradient fills
Clean, minimal empty states with helpful messaging

Visual Style

Apple-esque minimalism with ample white space
Sleek, modern interface with clean lines
Data visualization using brand purple for primary metrics
Subtle use of pastels for categorization and signals
Grid-based layouts with clear visual hierarchy
Icon + text combinations for improved scannability

Tone & Messaging

Clear, concise explanatory text
Professional but approachable language
Focus on metrics and actionable insights
Simplified complex data concepts

#coding rules
1. Don't use axios, use fetch instead
