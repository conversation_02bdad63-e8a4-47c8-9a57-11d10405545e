export function setNestedValue(obj: any, path : string, value: string) {
    // Split the path by dots
    const keys = path.split('.');
    let current = obj;

    // Navigate through all but the last key
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];

      // Handle array access (e.g., "rows[0]")
      if (key.includes('[') && key.includes(']')) {
        const arrayName = key.substring(0, key.indexOf('['));
        const index = parseInt(key.substring(key.indexOf('[') + 1, key.indexOf(']')));

        // Create the array property if it doesn't exist
        if (!current[arrayName]) {
          current[arrayName] = [];
        }

        // Create the array element if it doesn't exist
        if (!current[arrayName][index]) {
          current[arrayName][index] = {};
        }

        current = current[arrayName][index];
      } else {
        // Create the property if it doesn't exist
        if (!current[key]) {
          current[key] = {};
        }

        current = current[key];
      }
    }

    // Set the value on the last key
    const lastKey = keys[keys.length - 1];

    // Handle array access for the last key
    if (lastKey.includes('[') && lastKey.includes(']')) {
      const arrayName = lastKey.substring(0, lastKey.indexOf('['));
      const index = parseInt(lastKey.substring(lastKey.indexOf('[') + 1, lastKey.indexOf(']')));

      // Create the array property if it doesn't exist
      if (!current[arrayName]) {
        current[arrayName] = [];
      }

      current[arrayName][index] = value;
    } else {
      current[lastKey] = value;
    }
}
