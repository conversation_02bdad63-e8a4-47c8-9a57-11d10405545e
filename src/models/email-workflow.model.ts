import {Entity, model, property, belongsTo, hasMany} from '@loopback/repository';
import {Task} from './task.model';
import {EmailWorkflowGeneration} from './email-workflow-generation.model';

export enum EmailWorkflowStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  TIMEOUT = 'timeout'
}

@model({
  settings: {
    postgresql: {
      table: 'email_workflow'
    }
  }
})
export class EmailWorkflow extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
    required: true,
  })
  taskId: number;

  @property({
    type: 'object',
    required: true,
    postgresql: {
      dataType: 'jsonb'
    }
  })
  briefData: {
    subjectLine: string;
    previewText: string;
    briefText: string;
  };

  @property({
    type: 'string',
    required: true,
    default: EmailWorkflowStatus.PENDING,
  })
  status: EmailWorkflowStatus;

  @property({
    type: 'number',
    required: true,
    default: 3,
  })
  iterationCount: number;

  @property({
    type: 'number',
    default: 0,
  })
  completedIterations: number;

  @property({
    type: 'date',
    required: true,
    default: () => new Date(),
  })
  createdAt: Date;

  @property({
    type: 'date',
  })
  startedAt?: Date;

  @property({
    type: 'date',
  })
  completedAt?: Date;

  @property({
    type: 'date',
    required: true,
  })
  timeoutAt: Date;

  @property({
    type: 'string',
  })
  error?: string;

  @property({
    type: 'object',
    postgresql: {
      dataType: 'jsonb'
    }
  })
  metadata?: {
    campaignId?: string;
    organizationId?: number;
    userId?: number;
    [key: string]: any;
  };

  // Relations
  @belongsTo(() => Task, {keyTo: 'id'}, {type: 'number'})
  task?: Task;

  @hasMany(() => EmailWorkflowGeneration, {keyTo: 'workflowId'})
  generations?: EmailWorkflowGeneration[];

  constructor(data?: Partial<EmailWorkflow>) {
    super(data);
  }
}

export interface EmailWorkflowRelations {
  task?: Task;
  generations?: EmailWorkflowGeneration[];
}

export type EmailWorkflowWithRelations = EmailWorkflow & EmailWorkflowRelations;