import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Organization} from './organization.model';

@model()
export class MessageCredit extends Entity {
  @property({type: 'number', id: true, generated: true})
  id?: number;

  @belongsTo(() => Organization)
  organizationId: number;

  @property({type: 'number', default: 0})
  credits: number;

  @property({type: 'string'})
  type?: string;

  @property({type: 'date', default: () => new Date()})
  createdAt?: string;

  @property({type: 'date', default: () => new Date()})
  updatedAt?: string;

  constructor(data?: Partial<MessageCredit>) {
    super(data);
  }
}

export interface MessageCreditRelations {
  organization?: Organization;
}

export type MessageCreditWithRelations = MessageCredit & MessageCreditRelations;
