import {
  Entity,
  model,
  property,
  belongsTo,
  hasMany,
} from '@loopback/repository';
import {OrganizationPlannerPlan} from './organization-planner-plan.model';
import {RaleonUser} from './raleon-user.model';
import {User} from './user-management/user.model';

@model()
export class PlanComment extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  content: string;

  @property({
    type: 'boolean',
    default: false,
  })
  resolved?: boolean;

  @property({
    type: 'boolean',
    default: false,
  })
  ignored?: boolean;

  @property({
    type: 'date',
    default: () => new Date(),
  })
  createdAt: Date;

  @belongsTo(() => OrganizationPlannerPlan)
  organizationPlannerPlanId: number;

  @belongsTo(() => RaleonUser)
  raleonUserId?: number;

  @belongsTo(() => User)
  userId?: number;

  @belongsTo(() => PlanComment, {name: 'parent'})
  parentId?: number;

  @hasMany(() => PlanComment, {keyTo: 'parentId', name: 'replies'})
  replies: PlanComment[];

  constructor(data?: Partial<PlanComment>) {
    super(data);
  }
}

export interface PlanCommentRelations {
  // describe navigational properties here
}

export type PlanCommentWithRelations = PlanComment & PlanCommentRelations;
