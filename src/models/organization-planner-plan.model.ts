import {Entity, model, property, hasMany} from '@loopback/repository';
import {PlannerPlanVersion} from './planner-plan-version.model';

@model()
export class OrganizationPlannerPlan extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	//This is true when the plan is set to be worked "Make it happen"
	@property({
		type: 'boolean',
		default: false,
	})
	active?: boolean;

	@property({
		type: 'boolean',
		default: false,
	})
	archived?: boolean;

	@property({
		type: 'boolean',
		default: false,
	})
	inProgress?: boolean;

	@property({
		type: 'string',
	})
	name?: string;

	@property({
		type: 'string',
	})
	description?: string;

	@property({
		type: 'string',
	})
	businessGoal?: string;

	@property({
		type: 'date',
	})
	startdate?: string;

	@property({
		type: 'date',
	})
	enddate?: string;

	@property({
		type: 'date',
	})
	createdDate?: Date;

	// New field for tracking the generation status
	@property({
		type: 'string',
		default: 'processing',
	})
	generationStatus?: string;

	// New field to store the progress percentage as a number
	@property({
		type: 'number',
		default: 0,
	})
	generationProgress?: number;


	@property({
		type: 'boolean',
		default: false
	})
	hiddenToUsers?: boolean;

	@hasMany(() => PlannerPlanVersion)
	plannerPlanVersions: PlannerPlanVersion[];

	@property({
		type: 'number',
	})
	organizationId?: number;

	constructor(data?: Partial<OrganizationPlannerPlan>) {
		super(data);
	}
}

export interface OrganizationPlannerPlanRelations {
	// describe navigational properties here
}

export type OrganizationPlannerPlanWithRelations = OrganizationPlannerPlan & OrganizationPlannerPlanRelations;
