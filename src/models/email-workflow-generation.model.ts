import {Entity, model, property, belongsTo, hasMany} from '@loopback/repository';
import {EmailWorkflow} from './email-workflow.model';
import {EmailGeneration} from './email-generation.model';
import {EmailWorkflowLog} from './email-workflow-log.model';

export enum EmailWorkflowGenerationStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  TIMEOUT = 'timeout'
}

@model({
  settings: {
    postgresql: {
      table: 'email_workflow_generation'
    }
  }
})
export class EmailWorkflowGeneration extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
    required: true,
  })
  workflowId: number;

  @property({
    type: 'number',
    required: true,
  })
  iterationNumber: number;

  @property({
    type: 'string',
    required: true,
    default: EmailWorkflowGenerationStatus.PENDING,
  })
  status: EmailWorkflowGenerationStatus;

  @property({
    type: 'number',
  })
  emailGenerationId?: number;

  @property({
    type: 'object',
    postgresql: {
      dataType: 'jsonb'
    }
  })
  variationPrompt?: {
    tone?: string;
    style?: string;
    focus?: string;
    customInstructions?: string;
    tasteProfile?: string;
  };

  @property({
    type: 'object',
    postgresql: {
      dataType: 'jsonb'
    }
  })
  imageProcessingData?: {
    sectionsNeedingImages?: Array<{
      sectionId: string;
      imageType: string;
      searchQuery: string;
    }>;
    processedImages?: Array<{
      originalUrl: string;
      editedUrl?: string;
      edits?: string[];
    }>;
  };

  @property({
    type: 'string',
    postgresql: {
      dataType: 'text'
    }
  })
  finalHtml?: string;

  @property({
    type: 'object',
    postgresql: {
      dataType: 'jsonb'
    }
  })
  emailDesignJson?: object;

  @property({
    type: 'date',
    required: true,
    default: () => new Date(),
  })
  createdAt: Date;

  @property({
    type: 'date',
  })
  startedAt?: Date;

  @property({
    type: 'date',
  })
  completedAt?: Date;

  @property({
    type: 'string',
  })
  error?: string;

  // Relations
  @belongsTo(() => EmailWorkflow, {keyTo: 'id'}, {type: 'number'})
  workflow?: EmailWorkflow;

  @belongsTo(() => EmailGeneration, {name: 'emailGeneration', keyTo: 'id'}, {type: 'number'})
  emailGeneration?: EmailGeneration;

  @hasMany(() => EmailWorkflowLog, {keyTo: 'generationId'})
  logs?: EmailWorkflowLog[];

  constructor(data?: Partial<EmailWorkflowGeneration>) {
    super(data);
  }
}

export interface EmailWorkflowGenerationRelations {
  workflow?: EmailWorkflow;
  emailGeneration?: EmailGeneration;
  logs?: EmailWorkflowLog[];
}

export type EmailWorkflowGenerationWithRelations = EmailWorkflowGeneration & EmailWorkflowGenerationRelations;