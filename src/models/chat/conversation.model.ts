import {Entity, model, property, belongsTo, hasMany} from '@loopback/repository';
import {Message} from './message.model';
import {Organization} from '../organization.model';
import {User} from '../user-management/user.model';

@model({
	settings: {
		indexes: {
			uniqueConversationId: {
				keys: {
					id: 1,
					organizationId: 1,
				},
				options: {
					unique: true,
				},
			},
		},
	},
})
export class Conversation extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: false,
		jsonSchema: {
			description: 'Auto-generated or user-provided name for the conversation',
		},
	})
	name?: string;

	@property({
		type: 'number',
		required: false,
	})
	promptTemplateId?: number;

	@property({
		type: 'boolean',
		required: true,
		default: false,
	})
	isArchived: boolean;

	@property({
		type: 'date',
		required: true,
	})
	createdAt: Date;

	@property({
		type: 'date',
		required: true,
	})
	updatedAt: Date;

	@property({
		type: 'number',
		required: false,
		postgresql: {
			dataType: 'double precision'
		},
		jsonSchema: {
			description: 'Total cost of all messages in the conversation in USD',
		},
	})
	totalCost?: number;

	@property({
		type: 'object',
		required: false,
		jsonSchema: {
			description: 'Aggregated token usage for the entire conversation',
			properties: {
				promptTokens: { type: 'number' },
				completionTokens: { type: 'number' },
				totalTokens: { type: 'number' }
			}
		},
	})
	totalTokenUsage?: {
		promptTokens: number;
		completionTokens: number;
		totalTokens: number;
	};

	@property({
		type: 'number',
		required: false,
		jsonSchema: {
			description: 'Total number of messages in the conversation',
		},
	})
	messageCount?: number;

	@property({
		type: 'number',
		required: false,
		postgresql: {
			dataType: 'bigint'
		},
		jsonSchema: {
			description: 'Average response time across all assistant messages (milliseconds)',
		},
	})
	averageResponseTime?: number;

	@property({
		type: 'date',
		required: false,
		jsonSchema: {
			description: 'Timestamp of the last user message',
		},
	})
	lastUserMessageAt?: Date;

	@property({
		type: 'date',
		required: false,
		jsonSchema: {
			description: 'Timestamp of the last assistant message',
		},
	})
	lastAssistantMessageAt?: Date;

	@property({
		type: 'number',
		required: false,
		jsonSchema: {
			description: 'Associated task ID for this conversation',
		},
	})
	taskId?: number;

	@property({
		type: 'number',
		required: false,
		jsonSchema: {
			description: 'Associated campaign ID for this conversation',
		},
	})
	campaignId?: number;

	@property({
		type: 'array',
		itemType: 'object',
		required: false,
		jsonSchema: {
			description: 'Product URLs captured from chat tool results',
			items: {
				type: 'object',
				properties: {
					name: { type: 'string' },
					productUrl: { type: 'string' },
					imageUrl: { type: 'string' },
					productId: { type: 'string' },
					price: { type: 'number' },
					salesData: {
						type: 'object',
						properties: {
							quantitySold: { type: 'number' },
							totalRevenue: { type: 'number' },
							rank: { type: 'number' }
						}
					},
					source: { type: 'string' },
					timestamp: { type: 'string' }
				}
			}
		},
	})
	productUrls?: Array<{
		name: string;
		productUrl?: string;
		imageUrl?: string;
		productId?: string;
		price?: number;
		salesData?: {
			quantitySold?: number;
			totalRevenue?: number;
			rank?: number;
		};
		source?: string;
		timestamp?: string;
	}>;

	@hasMany(() => Message)
	messages: Message[];

	@belongsTo(() => Organization)
	organizationId: number;

	@belongsTo(() => User)
	createdByUserId?: number;

	constructor(data?: Partial<Conversation>) {
		super(data);
	}
}

export interface ConversationRelations {
	organization: Organization;
	createdByUser?: User;
}

export type ConversationWithRelations = Conversation & ConversationRelations;
