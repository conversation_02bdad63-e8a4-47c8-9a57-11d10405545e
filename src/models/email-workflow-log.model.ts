import {Entity, model, property, belongsTo} from '@loopback/repository';
import {EmailWorkflow} from './email-workflow.model';
import {EmailWorkflowGeneration} from './email-workflow-generation.model';

export enum EmailWorkflowLogLevel {
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  DEBUG = 'debug'
}

@model({
  settings: {
    postgresql: {
      table: 'email_workflow_log'
    }
  }
})
export class EmailWorkflowLog extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
    required: true,
  })
  workflowId: number;

  @property({
    type: 'number',
  })
  generationId?: number;

  @property({
    type: 'date',
    required: true,
    default: () => new Date(),
  })
  timestamp: Date;

  @property({
    type: 'string',
    required: true,
    default: EmailWorkflowLogLevel.INFO,
  })
  level: EmailWorkflowLogLevel;

  @property({
    type: 'string',
    required: true,
  })
  message: string;

  @property({
    type: 'string',
  })
  step?: string;

  @property({
    type: 'object',
    postgresql: {
      dataType: 'jsonb'
    }
  })
  data?: any;

  @property({
    type: 'number',
  })
  duration?: number; // Duration in milliseconds for performance tracking

  // Relations
  @belongsTo(() => EmailWorkflow, {keyTo: 'id'}, {type: 'number'})
  workflow?: EmailWorkflow;

  @belongsTo(() => EmailWorkflowGeneration, {name: 'generation', keyTo: 'id'}, {type: 'number'})
  generation?: EmailWorkflowGeneration;

  constructor(data?: Partial<EmailWorkflowLog>) {
    super(data);
  }
}

export interface EmailWorkflowLogRelations {
  workflow?: EmailWorkflow;
  generation?: EmailWorkflowGeneration;
}

export type EmailWorkflowLogWithRelations = EmailWorkflowLog & EmailWorkflowLogRelations;