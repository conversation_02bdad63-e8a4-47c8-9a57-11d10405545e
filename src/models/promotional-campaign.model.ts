import {Entity, model, property, hasMany} from '@loopback/repository';
import {PromotionalCampaignDetails} from './promotional-campaign-details.model';

@model({settings: {strict: false}})
export class PromotionalCampaign extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
    required: true,
  })
  orgId: number;

  @property({
    type: 'string',
    required: true,
  })
  startDate: string;

  @property({
    type: 'string',
  })
  endDate?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  active?: boolean;

  @property({
    type: 'string',
  })
  loyaltySegment?: string;

  @property({
    type: 'string',
  })
  loyaltySegmentType?: string;

  @hasMany(() => PromotionalCampaignDetails)
  promotionalCampaignDetails: PromotionalCampaignDetails[];
  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<PromotionalCampaign>) {
    super(data);
  }
}

export interface PromotionalCampaignRelations {
  // describe navigational properties here
}

export type PromotionalCampaignWithRelations = PromotionalCampaign & PromotionalCampaignRelations;
