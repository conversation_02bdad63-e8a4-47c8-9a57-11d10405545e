import {inject} from '@loopback/core';
import {post, getJsonSchema, api} from '@loopback/rest';
import {CleanupService} from '../services/cleanup.service';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class CleanupController {
	constructor(
		@inject('services.CleanupService')
		private cleanupService: CleanupService,
	) { }

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-admin'],
		voters: [basicAuthorization],
	})
	@post('/api/cleanup/stuck-jobs', {
		responses: {
			200: {
				description: 'Manually trigger cleanup of stuck jobs',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								message: {type: 'string'},
								cleanedJobsCount: {type: 'number'}
							}
						}
					}
				}
			}
		}
	})
	async cleanupStuckJobs(): Promise<{message: string, cleanedJobsCount: number}> {
		await this.cleanupService.manualCleanup();
		return {
			message: 'Cleanup completed successfully',
			cleanedJobsCount: 0 // You could modify the service to return the count
		};
	}
}
