import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {api, del, get, patch, post, param, requestBody} from '@loopback/rest';
import {repository} from '@loopback/repository';
import {
  OrganizationPlannerPlanRepository,
  PlannerCampaignRepository,
  PlannerPlanVersionRepository,
  TaskRepository,
  OrganizationRepository,
  CampaignSegmentRepository,
} from '../repositories';
import {
  guardStrategy,
  skipGuardCheck,
  injectUserOrgId,
  OrgGuardPropertyStrategy,
} from '../interceptors';
import {basicAuthorization} from '../services';

interface Campaign {
  id: number;
  name: string;
  type: 'Promotion' | 'Education' | 'Awareness';
  description: string;
  targetSegment: string;
  scheduledDate: string;
  businessGoal: string;
  whyText: string;
  taskType: 'Email' | 'SMS' | 'Loyalty';
  klaviyoSegmentId?: string;
  segmentType?: 'raleon' | 'klaviyo';
  promotion: {
    type: string;
    discount: string;
    details: string;
    reason?: string;
  };
}

@api({basePath: '/api/v1'})
@guardStrategy(
  new OrgGuardPropertyStrategy<any>({
    orgIdModelPropertyName: 'organizationId',
    repositoryClass: OrganizationRepository,
  }),
)
export class PlannerCampaignController {
  constructor(
    @repository(PlannerCampaignRepository)
    private plannerCampaignRepository: PlannerCampaignRepository,
    @repository(PlannerPlanVersionRepository)
    private plannerPlanVersionRepository: PlannerPlanVersionRepository,
    @repository(OrganizationPlannerPlanRepository)
    private organizationPlannerPlanRepository: OrganizationPlannerPlanRepository,
    @repository(TaskRepository)
    private taskRepository: TaskRepository,
    @repository(CampaignSegmentRepository)
    private campaignSegmentRepository: CampaignSegmentRepository,
  ) {}

  @patch('/planner/campaign/{campaignId}')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async updateCampaign(
    @param.path.number('campaignId') campaignId: number,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              name: {type: 'string'},
              description: {type: 'string'},
              scheduledDate: {type: 'string', format: 'date'},
              promotion: {type: 'string'},
              targetSegment: {type: 'string'},
              klaviyoSegmentId: {type: 'string'},
              segmentType: {type: 'string', enum: ['raleon', 'klaviyo']},
            },
          },
        },
      },
    })
    updates: Partial<Campaign>,
    @injectUserOrgId() orgId: number,
  ): Promise<any> {
    try {
      const campaign = await this.plannerCampaignRepository.findById(campaignId);
      if (!campaign) {
        return {status: 404, message: 'Campaign not found'};
      }

      const version = await this.plannerPlanVersionRepository.findById(
        campaign.plannerPlanVersionId,
      );
      const plan = await this.organizationPlannerPlanRepository.findById(
        version.organizationPlannerPlanId,
      );
      if (plan.organizationId !== orgId) {
        return {
          status: 403,
          message: 'You do not have permission to update this campaign',
        };
      }

      // Handle Klaviyo segment updates
      if (updates.klaviyoSegmentId && updates.segmentType === 'klaviyo') {
        // Find existing campaign-segment relationships
        const existingSegments = await this.campaignSegmentRepository.find({
          where: { campaignId: campaignId }
        });

        // Remove existing segments
        for (const segment of existingSegments) {
          await this.campaignSegmentRepository.deleteById(segment.id);
        }

        // Create new Klaviyo segment relationship
        await this.campaignSegmentRepository.create({
          campaignId: campaignId,
          segmentId: -1, // Use -1 as placeholder for Klaviyo segments
          klaviyoSegmentId: updates.klaviyoSegmentId,
          segmentType: 'klaviyo'
        });

        // Remove Klaviyo-specific fields from updates before updating campaign
        const campaignUpdates = { ...updates };
        delete campaignUpdates.klaviyoSegmentId;
        delete campaignUpdates.segmentType;

        await this.plannerCampaignRepository.updateById(campaignId, campaignUpdates);
      } else {
        await this.plannerCampaignRepository.updateById(campaignId, updates);
      }

      return {status: 200, message: 'Campaign updated successfully'};
    } catch (error) {
      console.error('Failed to update campaign:', error);
      return {status: 500, message: 'Failed to update campaign: ' + error.message};
    }
  }

  @get('/planner/campaign/{campaignId}')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async getCampaign(
    @param.path.number('campaignId') campaignId: number,
    @injectUserOrgId() orgId: number,
  ): Promise<any> {
    try {
      const campaign = await this.plannerCampaignRepository.findById(campaignId, {
        include: [
          {
            relation: 'task',
            scope: {include: [{relation: 'taskSteps'}]},
          },
        ],
      });
      if (!campaign || campaign.isDeleted) {
        return {status: 404, message: 'Campaign not found'};
      }

      const version = await this.plannerPlanVersionRepository.findById(
        campaign.plannerPlanVersionId,
      );
      const plan = await this.organizationPlannerPlanRepository.findById(
        version.organizationPlannerPlanId,
      );
      if (plan.organizationId !== orgId) {
        return {
          status: 403,
          message: 'You do not have permission to view this campaign',
        };
      }

      return {status: 200, data: campaign};
    } catch (error) {
      console.error('Failed to get campaign:', error);
      return {status: 500, message: 'Failed to get campaign: ' + error.message};
    }
  }

  @get('/planner/campaigns')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async getAllCampaigns(
    @param.query.string('filter') filterStr: string = '',
    @injectUserOrgId() orgId: number,
  ): Promise<any> {
    try {
      let includeArchived = false;
      if (filterStr) {
        try {
          const filter = JSON.parse(filterStr);
          includeArchived = filter?.includeArchived === true;
        } catch (e) {
          console.error('Invalid filter JSON:', e);
        }
      }

      const planWhereCondition: any = {
        organizationId: orgId,
        or: [{isDeleted: false}, {isDeleted: {eq: null}}],
      };

      if (!includeArchived) {
        planWhereCondition.and = [{or: [{archived: false}, {archived: {eq: null}}]}];
      }

      const orgPlannerPlans = await this.organizationPlannerPlanRepository.find({
        where: planWhereCondition,
        include: [
          {
            relation: 'plannerPlanVersions',
            scope: {
              include: [
                {
                  relation: 'plannerCampaigns',
                  scope: {
                    where: {or: [{isDeleted: false}, {isDeleted: {eq: null}}]},
                    include: [{relation: 'task'}],
                  },
                },
              ],
            },
          },
        ],
      });

      const campaigns = [] as any[];
      for (const plan of orgPlannerPlans) {
        for (const version of plan?.plannerPlanVersions || []) {
          for (const campaign of version?.plannerCampaigns || []) {
            // Skip campaigns with Archive status when not including archived
            if (!includeArchived && campaign.task?.status === 'Archive') {
              continue;
            }
            campaigns.push({
              ...campaign,
              planName: plan.name,
              planId: plan.id,
              versionId: version.id,
              archived: plan.archived || false,
            });
          }
        }
      }

      campaigns.sort(
        (a, b) => new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime(),
      );

      return {status: 200, data: campaigns};
    } catch (error) {
      console.error('Failed to get campaigns:', error);
      return {status: 500, message: 'Failed to get campaigns: ' + error.message};
    }
  }

  @patch('/planner/campaign/{campaignId}/status')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async updateCampaignStatus(
    @param.path.number('campaignId') campaignId: number,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['status'],
            properties: {status: {type: 'string'}},
          },
        },
      },
    })
    data: {status: string},
    @injectUserOrgId() orgId: number,
  ): Promise<any> {
    try {
      const campaign = await this.plannerCampaignRepository.findById(campaignId);
      if (!campaign) {
        return {status: 404, message: 'Campaign not found'};
      }

      const plannerPlanVersion = await this.plannerPlanVersionRepository.findById(
        campaign.plannerPlanVersionId,
      );
      if (!plannerPlanVersion) {
        return {status: 404, message: 'PlannerPlanVersion not found'};
      }

      const organizationPlannerPlan = await this.organizationPlannerPlanRepository.findById(
        plannerPlanVersion.organizationPlannerPlanId,
      );
      if (!organizationPlannerPlan || organizationPlannerPlan.organizationId !== orgId) {
        return {
          status: 404,
          message: 'Campaign not found or you do not have permission to update it',
        };
      }

      const task = await this.taskRepository.findOne({where: {plannerCampaignId: campaignId}});
      if (task) {
        await this.taskRepository.updateById(task.id, {status: data.status});
      }

      return {status: 200, message: 'Campaign status updated successfully'};
    } catch (error) {
      console.error('Failed to update campaign status:', error);
      return {status: 500, message: 'Failed to update campaign status: ' + error.message};
    }
  }

  @del('/planner/campaign/{campaignId}')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async deleteCampaign(
    @param.path.number('campaignId') campaignId: number,
    @injectUserOrgId() orgId: number,
  ): Promise<any> {
    try {
      const campaign = await this.plannerCampaignRepository.findById(campaignId);
      if (!campaign) {
        return {status: 404, message: 'Campaign not found'};
      }

      const plannerPlanVersion = await this.plannerPlanVersionRepository.findById(
        campaign.plannerPlanVersionId,
      );
      if (!plannerPlanVersion) {
        return {status: 404, message: 'PlannerPlanVersion not found'};
      }

      const organizationPlannerPlan = await this.organizationPlannerPlanRepository.findById(
        plannerPlanVersion.organizationPlannerPlanId,
      );
      if (!organizationPlannerPlan || organizationPlannerPlan.organizationId !== orgId) {
        return {
          status: 404,
          message: 'Campaign not found or you do not have permission to delete it',
        };
      }

      await this.plannerCampaignRepository.updateById(campaignId, {isDeleted: true});
      return {status: 200, message: 'Campaign deleted successfully'};
    } catch (error) {
      console.error('Failed to delete campaign:', error);
      return {status: 500, message: 'Failed to delete campaign: ' + error.message};
    }
  }

  @post('/planner/campaigns/bulk-delete')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async bulkDeleteCampaigns(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['campaignIds'],
            properties: {
              campaignIds: {type: 'array', items: {type: 'number'}},
            },
          },
        },
      },
    })
    data: {campaignIds: number[]},
    @injectUserOrgId() orgId: number,
  ): Promise<any> {
    try {
      if (!data.campaignIds || data.campaignIds.length === 0) {
        return {status: 400, message: 'No campaign IDs provided'};
      }

      const results = {success: 0, failed: 0, errors: [] as string[]};
      for (const campaignId of data.campaignIds) {
        try {
          const campaign = await this.plannerCampaignRepository.findById(campaignId);
          if (!campaign) {
            results.failed++;
            results.errors.push(`Campaign ID ${campaignId} not found`);
            continue;
          }

          const plannerPlanVersion = await this.plannerPlanVersionRepository.findById(
            campaign.plannerPlanVersionId,
          );
          if (!plannerPlanVersion) {
            results.failed++;
            results.errors.push(`PlannerPlanVersion not found for campaign ID ${campaignId}`);
            continue;
          }

          const organizationPlannerPlan = await this.organizationPlannerPlanRepository.findById(
            plannerPlanVersion.organizationPlannerPlanId,
          );
          if (!organizationPlannerPlan || organizationPlannerPlan.organizationId !== orgId) {
            results.failed++;
            results.errors.push(`No permission to delete campaign ID ${campaignId}`);
            continue;
          }

          await this.plannerCampaignRepository.updateById(campaignId, {isDeleted: true});
          results.success++;
        } catch (error) {
          results.failed++;
          results.errors.push(`Error processing campaign ID ${campaignId}: ${error.message}`);
        }
      }

      return {
        status: 200,
        message: `Bulk delete completed: ${results.success} succeeded, ${results.failed} failed`,
        details: results,
      };
    } catch (error) {
      console.error('Failed to bulk delete campaigns:', error);
      return {status: 500, message: 'Failed to bulk delete campaigns: ' + error.message};
    }
  }
}
