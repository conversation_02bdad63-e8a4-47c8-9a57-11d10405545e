import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {service} from '@loopback/core';
import {get, api, param} from '@loopback/rest';
import {injectUserOrgId, skipGuardCheck, GuardSkipStrategy, guardStrategy} from '../interceptors';
import {PromptContextService} from '../services/prompt/prompt-context.service';
import {basicAuthorization} from '../services';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class PromptTagController {
  constructor(
    @service(PromptContextService)
    private promptContextService: PromptContextService,
  ) {}

  @get('/prompt-tags')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async getTagValues(
    @param.query.boolean('skipCache') skipCache: boolean = false,
	@injectUserOrgId() orgId: number,
  ): Promise<Record<string, string>> {
    return this.promptContextService.getAllTagValues(orgId, skipCache);
  }
}
