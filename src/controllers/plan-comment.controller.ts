import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {repository} from '@loopback/repository';
import {api, get, post, patch, param, requestBody} from '@loopback/rest';
import {PlanComment} from '../models';
import {
  PlanCommentRepository,
  OrganizationPlannerPlanRepository,
  RaleonUserRepository,
  UserRepository,
} from '../repositories';
import {
  skipGuardCheck,
  OrgGuardPropertyStrategy,
  guardStrategy,
} from '../interceptors';
import {basicAuthorization} from '../services';

@api({basePath: '/api/v1'})
@guardStrategy(
  new OrgGuardPropertyStrategy<PlanComment>({
    orgIdModelPropertyName: 'organizationPlannerPlanId',
    repositoryClass: PlanCommentRepository,
  }),
)
export class PlanCommentController {
  constructor(
    @repository(PlanCommentRepository)
    private planCommentRepository: PlanCommentRepository,
    @repository(OrganizationPlannerPlanRepository)
    private planRepository: OrganizationPlannerPlanRepository,
    @repository(RaleonUserRepository)
    private raleonUserRepository: RaleonUserRepository,
    @repository(UserRepository)
    private userRepository: UserRepository,
  ) {}

  @get('/planner/plan/{planId}/comments')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'customer', 'support'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async findByPlan(
    @param.path.number('planId') planId: number,
  ): Promise<PlanComment[]> {
    return this.planCommentRepository.find({
      where: {organizationPlannerPlanId: planId},
      include: ['replies', 'raleonUser', 'user'],
    });
  }

  @post('/planner/plan/{planId}/comments')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'customer', 'support'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async createComment(
    @param.path.number('planId') planId: number,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['content'],
            properties: {
              content: {type: 'string'},
              parentId: {type: 'number'},
              raleonUserId: {type: 'number'},
              userId: {type: 'number'},
            },
          },
        },
      },
    })
    body: {content: string; parentId?: number; raleonUserId?: number; userId?: number},
  ): Promise<PlanComment> {
    const comment = new PlanComment({
      content: body.content,
      parentId: body.parentId,
      organizationPlannerPlanId: planId,
    });

    // Support both userId and raleonUserId for backward compatibility
    if (body.userId) {
      comment.userId = body.userId;
    } else if (body.raleonUserId) {
      comment.raleonUserId = body.raleonUserId;
    }

    return this.planCommentRepository.create(comment);
  }

  @patch('/planner/comments/{id}')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'customer', 'support'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async updateCommentStatus(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              resolved: {type: 'boolean'},
              ignored: {type: 'boolean'},
            },
          },
        },
      },
    })
    body: Partial<Pick<PlanComment, 'resolved' | 'ignored'>>,
  ): Promise<void> {
    await this.planCommentRepository.updateById(id, body);
  }
}
