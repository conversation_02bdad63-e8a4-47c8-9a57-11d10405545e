import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {
	Filter,
	repository,
} from '@loopback/repository';
import {
	api,
	del,
	get,
	getModelSchemaRef,
	HttpErrors,
	param,
	patch,
	post,
	requestBody,
} from '@loopback/rest';
import {injectGuardedFilter, OrgGuardPropertyStrategy, guardStrategy, CrudGuardInterceptor, restrictReadsWithGuard, skipGuardCheck, injectUserOrgId, modelIdForGuard} from '../interceptors'
import {OrganizationPlannerPlanRepository, OrganizationRepository, OrganizationSegmentRepository, PlannerCampaignRepository, PlannerPlanVersionRepository, ProjectRepository, PromptTemplateRepository, UnlayerComponentRepository, EmailGenerationRepository, ImageRepository, ConversationRepository} from '../repositories';
import {inject, service} from '@loopback/core';
import { v4 as uuidv4 } from 'uuid';
import {basicAuthorization} from '../services';
import { LLMRouterService } from '../services/chat/llm-router.service';
import {PlannerCampaign, PlannerPlanVersion, PromptTemplate, TaskWithRelations} from '../models';
import { CompletionMessage, RouterParams, RouterResponse } from '../services/chat/types';
import {TaskService}from '../services/tasks/task.service';
import {OrganizationSegmentController}from './organization-segment.controller';
import {KlaviyoService}from '../services/integrations/klaviyo.service';
import {EcommerceMetricController} from './ecommerce-metric.controller';
import {KlaviyoMetricsService} from '../services/metrics/klaviyo-metrics.service';
import {PromptContextService}from '../services/prompt/prompt-context.service';
import { jsonrepair } from 'jsonrepair';
import { handleClaudeResponse } from '../services/chat/utils/campaign-parser';
import { setNestedValue } from '../utils/object-utils';
import { PromptLogService } from '../services/prompt/prompt-log.service';


interface Campaign {
	id: number;
	name: string;
	type: 'Promotion' | 'Education' | 'Awareness';
	description: string;
	targetSegment: string;
	scheduledDate: string;
	businessGoal: string;
	whyText: string;
	taskType: 'Email' | 'SMS' | 'Loyalty';
	promotion: {
		type: string;
		discount: string;
		details: string;
		reason?: string;
	};
}

interface CampaignResponse {
	campaigns: Campaign[];
	metadata: {
		totalCampaigns: number;
		name: string;
		description: string;
		dataSummary: string;
		businessGoal: string;
		timeframeCovered: {
			startDate: string;
			endDate: string;
		};
	};
}

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<any>({
    orgIdModelPropertyName: 'organizationId',
    repositoryClass: OrganizationRepository
}))
export class PlannerController {
    constructor(
		@repository(OrganizationRepository) private organizationRepository: OrganizationRepository,
		@inject('datasources.dev_db') private devDbDataSource: any,
		@repository(OrganizationSegmentRepository) private organizationSegmentRepository: OrganizationSegmentRepository,
		@service(LLMRouterService) private llmRouter: LLMRouterService,
		@repository(OrganizationPlannerPlanRepository) private organizationPlannerPlanRepository: OrganizationPlannerPlanRepository,
		@repository(PlannerPlanVersionRepository) private plannerPlanVersionRepository: PlannerPlanVersionRepository,
		@repository(PlannerCampaignRepository) private plannerCampaignRepository: PlannerCampaignRepository,
		@service(TaskService) private taskService: TaskService,
		@repository(PromptTemplateRepository) private promptTemplateRepository: PromptTemplateRepository,
		@service(KlaviyoService) private klaviyoService: KlaviyoService,
		@inject('controllers.OrganizationSegmentController') private organizationSegmentController: OrganizationSegmentController,
		@inject('controllers.EcommerceMetricController') private ecommerceMetricController: EcommerceMetricController,
		@repository(UnlayerComponentRepository) private unlayerComponentRepository: UnlayerComponentRepository,
		@repository(EmailGenerationRepository) public emailGenerationRepository: EmailGenerationRepository,
		@repository(ImageRepository) private imageRepository: ImageRepository,
		@service(PromptContextService) private promptContextService: PromptContextService,
		@service(KlaviyoMetricsService) private klaviyoMetricsService: KlaviyoMetricsService,
		@service(PromptLogService) private promptLogService: PromptLogService,
		@repository(ConversationRepository) private conversationRepository: ConversationRepository,
	) { }

	private async buildPlannerPrompt(orgId: number, userPrompt: string): Promise<string> {
		const promptTemplate = await this.promptTemplateRepository.findOne({
			where: {
				type: 'Plan',
				isActive: true
			}
		});

		if (!promptTemplate) {
			throw new Error('No active prompt template found for "Planner"');
		}

		return this.replacePromptTags(promptTemplate.content, orgId, userPrompt);
	}

	private async buildPromptWithTemplate(orgId: number, userPrompt: string, templateName: string): Promise<string> {
		const promptTemplate = await this.promptTemplateRepository.findOne({
			where: {
				type: templateName,
				isActive: true
			}
		});

		if (!promptTemplate) {
			throw new Error(`No active prompt template found for "${templateName}"`);
		}

		return this.replacePromptTags(promptTemplate.content, orgId, userPrompt);
	}

	private parseBulkPlanIdeas(completion: string): {
		ideas: Array<{ idea: string; date?: string | null; target?: string | null }>;
		name?: string;
		description?: string;
		goal?: string;
		startDate?: string;
		endDate?: string;
		dataSummary?: string;
	} {
		try {
			// Try to parse the JSON response
			let cleaned = completion.replace(/^[\s`]*json[\s`]*|^[\s`]*|[\s`]*```[\s`]*$/gmi, '');
			const firstBrace = cleaned.indexOf('{');
			const lastBrace = cleaned.lastIndexOf('}');
			if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
				cleaned = cleaned.substring(firstBrace, lastBrace + 1);
			}
			const repairedJsonString = jsonrepair(cleaned);
			const parsed = JSON.parse(repairedJsonString);

			// Validate the structure
			if (!parsed.campaigns || !Array.isArray(parsed.campaigns)) {
				throw new Error('Invalid format: "campaigns" array is missing or not an array');
			}

			// Ensure each idea has the required structure
			parsed.ideas = parsed.campaigns.map((idea: any, index: number) => {
				// If the idea is just a string, convert to object format
				if (typeof idea === 'string') {
					return { idea, date: null, target: null };
				}

				// If it's an object, ensure it has the idea field
				if (!idea.idea) {
					throw new Error(`Idea at index ${index} is missing the "idea" field`);
				}

				// Validate date format if present
				if (idea.date && !/^\d{4}-\d{2}-\d{2}$/.test(idea.date)) {
					console.warn(`Invalid date format for idea at index ${index}, using null instead`);
					idea.date = null;
				}

				return {
					idea: idea.idea,
					date: idea.date || null,
					target: idea.target || null
				};
			});

			return {
				ideas: parsed.ideas,
				name: parsed.name,
				description: parsed.description,
				goal: parsed.goal,
				startDate: parsed.startDate,
				endDate: parsed.endDate,
				dataSummary: parsed.dataSummary
			};
		} catch (error) {
			console.error('Error parsing bulk plan ideas:', error);
			// Try to recover by doing basic regex parsing for ideas
			try {
				const ideasRegex = /(\d+\.\s*(.*?)(?=\n\d+\.|\n*$))/gs;
				const matches = [...completion.matchAll(ideasRegex)];

				if (matches.length > 0) {
					return {
						ideas: matches.map(match => ({
							idea: match[2].trim(),
							date: null,
							target: null
						}))
					};
				}
			} catch (fallbackError) {
				console.error('Fallback parsing also failed:', fallbackError);
			}

			throw new Error('Failed to parse bulk plan ideas: ' + (error instanceof Error ? error.message : 'Unknown error'));
		}
	}

	private async replacePromptTags(prompt: string, orgId: number, userPrompt?: string, campaign?: PlannerCampaign, task?: TaskWithRelations): Promise<string> {
		let metricsData = {};

		return this.promptContextService.replacePromptTags(prompt, orgId, userPrompt, campaign, task, metricsData);
	}


	@post('/planner/plan')
	@authenticate('jwt')
	@restrictReadsWithGuard({plural: true})
	@skipGuardCheck()
	async create(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						required: ['user_prompt', 'additional_data'],
						properties: {
							user_prompt: {
								type: 'string',
							},
							additional_data: {
								type: 'string',
							},
							starttime: {
								type: 'string',
								format: 'date-time',
							},
							endtime: {
								type: 'string',
								format: 'date-time',
							},
							template_name: {
								type: 'string',
							},
							name: {
								type: 'string',
							},
						},
					},
				},
			},
		}) data: {
			user_prompt: string;
			additional_data: string;
			starttime?: Date;
			endtime?: Date;
			template_name?: string;
			name?: string;
		},
		@injectUserOrgId() orgId: number
	): Promise<any> {
		const maxRetries = 5; // Maximum number of retries
		const initialDelay = 1000; // Initial delay in milliseconds (1 second)
		let retryCount = 0;

		while (retryCount < maxRetries) {
			try {
				// Step 1: Build the complete prompt
				const prompt = await this.buildPlannerPrompt(orgId, data.user_prompt);

				// Step 2: Call the LLM Router service to get the completion
				const messages: CompletionMessage[] = [{ role: 'user', content: prompt }];
				const params: RouterParams = {
					models: ["anthropic/claude-3.5-sonnet", "anthropic/claude-3.7-sonnet"],
					maxTokens: 4096,
					temperature: 0.7
				};
				const response: RouterResponse = await this.llmRouter.executeCompletion(messages, params);
				const completion = response.content;
				console.log('Completion:', completion);

				// Step 3: Parse and validate the response
				const campaigns = await handleClaudeResponse(completion);
				console.log('Parsed campaigns:', campaigns);

				if(!campaigns || !campaigns.campaigns || campaigns.campaigns.length === 0) {
					throw new Error('No campaigns found in response');
				}

				await this.promptLogService.logPromptData(orgId, 'PlannerPlan', prompt, completion);

				// Step 4: Create the organization plan
				const orgPlan = await this.organizationRepository.organizationPlannerPlans(orgId).create({
					name: campaigns.metadata.name,
					active: false,
					archived: false,
					description: campaigns.metadata.description,
					businessGoal: campaigns.metadata.businessGoal,
					startdate: campaigns.metadata.timeframeCovered.startDate,
					enddate: campaigns.metadata.timeframeCovered.endDate,
					createdDate: new Date(), // Set the creation date
					generationStatus: 'complete', // Set generation status to complete for new plans
					generationProgress: 100 // Set generation progress to 100% for new plans
				});

				// Step 5: Create the plan version
				const planVersion = await this.organizationPlannerPlanRepository.plannerPlanVersions(orgPlan.id).create({
					prompt: prompt,
					userPrompt: data.user_prompt,
					active: false,
					dataSummary: campaigns.metadata.dataSummary
				});

				// Step 6: Create the campaigns
				for (const campaign of campaigns.campaigns) {
					await this.plannerPlanVersionRepository.plannerCampaigns(planVersion.id).create({
						name: campaign.name,
						type: campaign.type,
						taskType: campaign.taskType,
						description: campaign.description,
						targetSegment: campaign.targetSegment,
						scheduledDate: campaign.scheduledDate,
						businessGoal: campaign.businessGoal,
						promotionTitle: campaign.promotion.discount,
						promotionDescription: campaign.promotion.details,
						whyText: campaign.whyText,
						promotionType: campaign.promotion.type,
						promotionSuggestionReason: campaign.promotion.reason || `${campaign.promotion.type} promotion with ${campaign.promotion.discount} based on campaign analysis`
					});
				}

				// Step 7: Return success response
				return {
					status: 200,
					data: {
						planId: orgPlan.id,
						versionId: planVersion.id,
						campaigns: campaigns.campaigns
					}
				};
			} catch (error) {
				retryCount++;
				if (retryCount >= maxRetries) {
					console.error('Failed to create planner plan after maximum retries:', error);
					return {
						status: 500,
						message: 'Failed to create planner plan after maximum retries: ' + error.message
					};
				}

				// Calculate exponential backoff delay
				const delay = initialDelay * Math.pow(2, retryCount - 1);
				console.warn(`Attempt ${retryCount} failed. Retrying in ${delay}ms...`);

				// Wait before retrying
				await new Promise(resolve => setTimeout(resolve, delay));
			}
		}
	}

	@post('/planner/bulk-plan')
	@authenticate('jwt')
	@restrictReadsWithGuard({plural: true})
	@skipGuardCheck()
	async createBulkPlan(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						required: ['user_prompt', 'additional_data', 'template_name'],
						properties: {
							user_prompt: {
								type: 'string',
							},
							additional_data: {
								type: 'string',
							},
							template_name: {
								type: 'string',
							},
							name: {
								type: 'string',
							},
							starttime: {
								type: 'string',
								format: 'date-time',
							},
							endtime: {
								type: 'string',
								format: 'date-time',
							},
						},
					},
				},
			},
		}) data: {
			user_prompt: string;
			additional_data: string;
			template_name: string;
			name?: string;
			starttime?: Date;
			endtime?: Date;
		},
		@injectUserOrgId() orgId: number
	): Promise<any> {
		const maxRetries = 5; // Maximum number of retries
		const initialDelay = 1000; // Initial delay in milliseconds (1 second)
		let retryCount = 0;

		while (retryCount < maxRetries) {
			try {
				// PHASE 1: Get campaign ideas using BulkPlan template
				console.log('Phase 1: Generating bulk campaign ideas');

				// Build the prompt using the BulkPlan template
				const bulkPrompt = await this.buildPromptWithTemplate(orgId, data.user_prompt, 'BulkPlan');

				// Call the AI service to get campaign ideas
				const bulkMessages: CompletionMessage[] = [{ role: 'user', content: bulkPrompt }];
				const bulkParams: RouterParams = {
					models: ["anthropic/claude-3.5-sonnet", "anthropic/claude-3.7-sonnet"],
					maxTokens: 4096,
					temperature: 0.7
				};
				const bulkResponse: RouterResponse = await this.llmRouter.executeCompletion(bulkMessages, bulkParams);
				const bulkCompletion = bulkResponse.content;

				console.log('Bulk ideas completion:', bulkCompletion);
				await this.promptLogService.logPromptData(orgId, 'BulkPlan', bulkPrompt, bulkCompletion);

				// Parse the bulk ideas (should be in JSON format with ideas and dates)
				const bulkIdeas = this.parseBulkPlanIdeas(bulkCompletion);

				if (!bulkIdeas || !bulkIdeas.ideas || bulkIdeas.ideas.length === 0) {
					throw new Error('No campaign ideas found in bulk plan response');
				}

				console.log(`Generated ${bulkIdeas.ideas.length} campaign ideas`);

				// Create the plan that will contain all generated campaigns
				const planName =  bulkIdeas.name || 'Monthly Email Plan';
				const orgPlan = await this.organizationRepository.organizationPlannerPlans(orgId).create({
					name: planName,
					active: false,
					archived: false,
					description: bulkIdeas.description || 'Generated monthly email plan',
					businessGoal: bulkIdeas.goal || 'Increase customer engagement',
					startdate: bulkIdeas.startDate || new Date().toISOString().split('T')[0],
					enddate: bulkIdeas.endDate || new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString().split('T')[0],
					createdDate: new Date(), // Set the creation date
					generationStatus: 'processing', // Set generation status to processing initially
					generationProgress: 0 // Start with 0% progress
				});

				// Create the plan version
				const planVersion = await this.organizationPlannerPlanRepository.plannerPlanVersions(orgPlan.id).create({
					prompt: bulkPrompt,
					userPrompt: data.user_prompt,
					active: false,
					dataSummary: bulkIdeas.dataSummary
				});

				// PHASE 2: Generate detailed campaigns for each idea
				console.log('Phase 2: Generating detailed campaigns for each idea');

				const allCampaigns = [];

				// Process each idea from the bulk plan
				const totalIdeas = bulkIdeas.ideas.length;
				for (let i = 0; i < totalIdeas; i++) {
					const idea = bulkIdeas.ideas[i];
					try {
						// Create an enhanced user prompt that includes the idea details
						const enhancedUserPrompt = `Campaign Idea: ${idea.idea}${
							idea.date ? `\nScheduled Date: ${idea.date}` : ''
						}${
							idea.target ? `\nTarget Audience: ${idea.target}` : ''
						}`;

						// Build a prompt using the SinglePlan template with the enhanced user prompt
						const singlePrompt = await this.buildPromptWithTemplate(
							orgId,
							enhancedUserPrompt,
							'SinglePlan'
						);

						// Call the AI service for this specific idea
						const singleMessages: CompletionMessage[] = [{ role: 'user', content: singlePrompt }];
						const singleParams: RouterParams = {
							models: ["anthropic/claude-3.5-sonnet"],
							maxTokens: 4096,
							temperature: 0.7
						};
						const singleResponse: RouterResponse = await this.llmRouter.executeCompletion(singleMessages, singleParams);
						const singleCompletion = singleResponse.content;

						//clean up the response
						singleCompletion.replace(/```json/g, '').replace(/```/g, '');

						await this.promptLogService.logPromptData(orgId, 'SinglePlan', singlePrompt, singleCompletion);

						// Parse the response
						const campaignDetail = await handleClaudeResponse(singleCompletion);

						if (!campaignDetail || !campaignDetail.campaigns || campaignDetail.campaigns.length === 0) {
							console.warn(`No campaign details found for idea: ${idea.idea}`);
							continue;
						}

						// Use the scheduled date from the bulk ideas if available
						if (idea.date) {
							for (const campaign of campaignDetail.campaigns) {
								campaign.scheduledDate = idea.date;
							}
						}

						// Create the campaigns in the database
						for (const campaign of campaignDetail.campaigns) {
							const newCampaign = await this.plannerPlanVersionRepository.plannerCampaigns(planVersion.id).create({
								name: campaign.name,
								type: campaign.type,
								taskType: campaign.taskType || 'Email',
								description: campaign.description,
								targetSegment: campaign.targetSegment,
								scheduledDate: campaign.scheduledDate,
								businessGoal: campaign.businessGoal,
								promotionTitle: campaign.promotion?.discount || '',
								promotionDescription: campaign.promotion?.details || '',
								whyText: campaign.whyText || '',
								promotionType: campaign.promotion?.type || 'None',
								promotionSuggestionReason: campaign.promotion?.reason || ''
							});

							// Add to our collection for the response
							allCampaigns.push(campaign);
						}

						// Update the progress after each idea is processed
						const progress = Math.round(((i + 1) / totalIdeas) * 100);
						await this.organizationPlannerPlanRepository.updateById(orgPlan.id, {
							generationProgress: progress
						});

					} catch (ideaError) {
						console.error(`Error processing idea "${idea.idea}":`, ideaError);
						// Continue to the next idea even if one fails
					}
				}

				if (allCampaigns.length === 0) {
					throw new Error('Failed to generate any campaigns from the ideas');
				}

				// Update the plan's generationStatus to 'complete' now that all campaigns are generated
				await this.organizationPlannerPlanRepository.updateById(orgPlan.id, {
					generationStatus: 'complete',
					generationProgress: 100
				});

				// Return success response
				return {
					status: 200,
					data: {
						planId: orgPlan.id,
						versionId: planVersion.id,
						campaigns: allCampaigns
					}
				};

			} catch (error) {
				retryCount++;
				if (retryCount >= maxRetries) {
					console.error('Failed to create bulk plan after maximum retries:', error);
					return {
						status: 500,
						message: 'Failed to create bulk plan after maximum retries: ' + error.message
					};
				}

				// Calculate exponential backoff delay
				const delay = initialDelay * Math.pow(2, retryCount - 1);
				console.log(`Retrying after ${delay}ms...`);
				await new Promise(resolve => setTimeout(resolve, delay));
			}
		}
	}

	@post('/planner/email-generator')
	@authenticate('jwt')
	@restrictReadsWithGuard({plural: true})
	@skipGuardCheck()
	async createEmailCampaign(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						required: ['user_prompt', 'additional_data', 'template_name'],
						properties: {
							user_prompt: {
								type: 'string',
							},
							additional_data: {
								type: 'string',
							},
							template_name: {
								type: 'string',
							},
							name: {
								type: 'string',
							},
							starttime: {
								type: 'string',
								format: 'date-time',
							},
							endtime: {
								type: 'string',
								format: 'date',
							},
						},
					},
				},
			},
		}) data: {
			user_prompt: string;
			additional_data: string;
			template_name: string;
			name?: string;
			starttime?: Date;
			endtime?: Date;
		},
		@injectUserOrgId() orgId: number
	): Promise<any> {
		const maxRetries = 5; // Maximum number of retries
		const initialDelay = 1000; // Initial delay in milliseconds (1 second)
		let retryCount = 0;

		while (retryCount < maxRetries) {
			try {
				// Step 1: Build the complete prompt using the Email Generator template
				const prompt = await this.buildPromptWithTemplate(orgId, data.user_prompt, data.template_name);

				// Step 2: Call the LLM Router service to get the completion
				const messages: CompletionMessage[] = [{ role: 'user', content: prompt }];
				const params: RouterParams = {
					models: ["anthropic/claude-3.5-sonnet", "anthropic/claude-3.7-sonnet"],
					maxTokens: 4096,
					temperature: 0.7
				};
				const response: RouterResponse = await this.llmRouter.executeCompletion(messages, params);
				const completion = response.content;
				console.log('Email Generator Completion:', completion);

				// Step 3: Parse and validate the response
				const campaigns = await handleClaudeResponse(completion);
				console.log('Parsed email campaigns:', campaigns);

				if(!campaigns || !campaigns.campaigns || campaigns.campaigns.length === 0) {
					throw new Error('No campaigns found in email generator response');
				}

				await this.promptLogService.logPromptData(orgId, data.template_name, prompt, completion);

				// Step 4: Create the organization plan
				const planName = data.name || campaigns.metadata.name || 'Email Campaign';
				const orgPlan = await this.organizationRepository.organizationPlannerPlans(orgId).create({
					name: planName,
					active: false,
					archived: false,
					description: campaigns.metadata.description,
					businessGoal: campaigns.metadata.businessGoal,
					startdate: campaigns.metadata.timeframeCovered.startDate,
					enddate: campaigns.metadata.timeframeCovered.endDate,
					createdDate: new Date(), // Set the creation date
					generationStatus: 'complete', // Set generation status to complete for new plans
					generationProgress: 100 // Set generation progress to 100% for new plans
				});

				// Step 5: Create the plan version
				const planVersion = await this.organizationPlannerPlanRepository.plannerPlanVersions(orgPlan.id).create({
					prompt: prompt,
					userPrompt: data.user_prompt,
					active: true,
					dataSummary: campaigns.metadata.dataSummary
				});

				// Step 6: Create the campaigns
				for (const campaign of campaigns.campaigns) {
					await this.plannerPlanVersionRepository.plannerCampaigns(planVersion.id).create({
						name: campaign.name,
						type: campaign.type,
						taskType: campaign.taskType,
						description: campaign.description,
						targetSegment: campaign.targetSegment,
						scheduledDate: campaign.scheduledDate,
						businessGoal: campaign.businessGoal,
						promotionTitle: campaign.promotion.discount,
						promotionDescription: campaign.promotion.details,
						whyText: campaign.whyText,
						promotionType: campaign.promotion.type,
						promotionSuggestionReason: campaign.promotion.reason || `${campaign.promotion.type} promotion with ${campaign.promotion.discount} based on campaign analysis`
					});
				}

				// Step 7: Return success response
				return {
					status: 200,
					data: {
						planId: orgPlan.id,
						versionId: planVersion.id,
						campaigns: campaigns.campaigns
					}
				};
			} catch (error) {
				retryCount++;
				if (retryCount >= maxRetries) {
					console.error('Failed to create email campaign after maximum retries:', error);
					return {
						status: 500,
						message: 'Failed to create email campaign after maximum retries: ' + error.message
					};
				}

				// Calculate exponential backoff delay
				const delay = initialDelay * Math.pow(2, retryCount - 1);
				console.log(`Retrying after ${delay}ms...`);
				await new Promise(resolve => setTimeout(resolve, delay));
			}
		}
	}


	@post('/planner/version/{id}/campaign')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async addCampaign(
		@param.path.number('id') versionId: number,
		@injectUserOrgId() orgId: number
	): Promise<any> {
		const maxRetries = 5; // Maximum number of retries
		const initialDelay = 1000; // Initial delay in milliseconds (1 second)
		let retryCount = 0;

		while (retryCount < maxRetries) {
			try {
				// Step 1: Verify ownership of the version
				const version = await this.plannerPlanVersionRepository.findById(versionId);
				if (!version) {
					throw new Error('Version not found');
				}

				// Step 2: Verify ownership of the plan
				const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);
				if (!plan) {
					throw new Error('Plan not found');
				}

				// Step 3: Verify that the plan belongs to the organization
				if (plan.organizationId !== orgId) {
					throw new Error('You do not have permission to add campaigns to this plan');
				}

				// Step 4: Get existing campaigns
				const existingCampaigns = await this.plannerPlanVersionRepository.plannerCampaigns(versionId).find();
				const campaignContext = existingCampaigns.map(c =>
					`Campaign: ${c.name}\nType: ${c.type}\nDescription: ${c.description}\nScheduled: ${c.scheduledDate}\n`
				).join('\n');

				// Step 5: Build AI context with original prompt and existing campaigns
				const aiPrompt = `
					${version.prompt}

					EXISTING CAMPAIGNS:
					${campaignContext}

					Please provide a single new campaign that complements the existing ones,  but with only one campaign in the array.

					USE this JSON structure
					 Please provide your response in the following JSON structure:

					{
					"campaigns": [
						{
						"id": "integer",
						"name": "string",
						"type": "enum(Promotion|Education|Awareness)",
						"taskType\: "enum(Email|SMS|Loyalty)",
						"description": "string",
						"targetSegment": "string",
						"scheduledDate": "YYYY-MM-DD",
						"businessGoal": "string",
						"whyText": "string",
						"promotion": {
							"type": "enum(Dollars Off|Percent Off|Free Shipping|null)",
							"discount": "string",
							"details": "string",
							"reason": "string"
						}
						}
					],
					"metadata": {
						"totalCampaigns": "integer",
						"name": "",
						"description": "",
						"businessGoal": "",
						"timeframeCovered": {
						"startDate": "",
						"endDate": ""
						}
					}
					}

					Please pick a date that fits within a gap of the other campaigns.
				`;

				// Step 6: Get AI completion
				const messages: CompletionMessage[] = [{ role: 'user', content: aiPrompt }];
				const params: RouterParams = {
					models: ["anthropic/claude-3.5-sonnet"],
					maxTokens: 4096,
					temperature: 0.7
				};
				const response: RouterResponse = await this.llmRouter.executeCompletion(messages, params);
				const completion = response.content;

				//clean up the response
				completion.replace(/```json/g, '').replace(/```/g, '');


				// Step 7: Parse response
				let finalData = await handleClaudeResponse(completion);
				const newCampaign = finalData.campaigns[0];

				// Step 8: Create campaign
				const plannerCampaign = await this.plannerPlanVersionRepository.plannerCampaigns(versionId).create({
					name: newCampaign.name,
					type: newCampaign.type,
					description: newCampaign.description,
					targetSegment: newCampaign.targetSegment,
					scheduledDate: newCampaign.scheduledDate,
					businessGoal: newCampaign.businessGoal,
					promotionTitle: newCampaign.promotion.discount,
					promotionDescription: newCampaign.promotion.details,
					whyText: newCampaign.whyText || 'No reason provided',
					taskType: newCampaign.taskType,
					promotionType: newCampaign.promotion.type,
					promotionSuggestionReason: newCampaign.promotion.reason || `${newCampaign.promotion.type} promotion with ${newCampaign.promotion.discount} based on campaign analysis`
				});

				return {
					status: 200,
					data: plannerCampaign
				};
			} catch (error) {
				retryCount++;
				if (retryCount >= maxRetries) {
					console.error('Failed to add campaign after maximum retries:', error);
					throw new Error('Failed to add campaign after maximum retries: ' + error.message);
				}

				// Calculate exponential backoff delay
				const delay = initialDelay * Math.pow(2, retryCount - 1);
				console.warn(`Attempt ${retryCount} failed. Retrying in ${delay}ms...`);

				// Wait before retrying
				await new Promise(resolve => setTimeout(resolve, delay));
			}
		}
	}


	@patch('/organization-planner-plans/{id}/unarchive')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async unarchivePlan(
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number
	): Promise<any> {
		try {
			// Step 1: Find the OrganizationPlannerPlan
			const plan = await this.organizationPlannerPlanRepository.findById(id);
			if (!plan) {
				return {
					status: 404,
					message: 'Plan not found'
				};
			}

			// Step 2: Verify ownership
			if (plan.organizationId !== orgId) {
				return {
					status: 403,
					message: 'You do not have permission to unarchive this plan'
				};
			}

			// Step 3: Update the archived field
			await this.organizationPlannerPlanRepository.updateById(id, {archived: false});

			return {
				status: 200,
				message: 'Plan has been unarchived'
			};
		} catch (error) {
			console.error('Failed to unarchive plan:', error);
			return {
				status: 500,
				message: 'Failed to unarchive plan: ' + error.message
			};
		}
	}

	@patch('/organization-planner-plans/{id}/archive')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async archiveOrganizationPlannerPlan(
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number
	): Promise<any> {
		try {
			// Step 1: Find the OrganizationPlannerPlan
			const organizationPlannerPlan = await this.organizationPlannerPlanRepository.findById(id);
			if (!organizationPlannerPlan) {
				return {
					status: 404,
					message: 'OrganizationPlannerPlan not found'
				};
			}

			// Step 2: Verify ownership
			if (organizationPlannerPlan.organizationId !== orgId) {
				return {
					status: 403,
					message: 'You do not have permission to archive this plan'
				};
			}

			// Step 3: Update the archived field
			await this.organizationPlannerPlanRepository.updateById(id, {archived: true});

			return {
				status: 200,
				message: 'OrganizationPlannerPlan archived successfully'
			};
		} catch (error) {
			console.error('Failed to archive OrganizationPlannerPlan:', error);
			return {
				status: 500,
				message: 'Failed to archive OrganizationPlannerPlan: ' + error.message
			};
		}
	}

	@patch('/organization-planner-plans/{id}/start-work')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async startWorkOnPlan(
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number
	): Promise<any> {
		try {
			// Step 1: Find the OrganizationPlannerPlan
			const plan = await this.organizationPlannerPlanRepository.findById(id);
			if (!plan) {
				return {
					status: 404,
					message: 'Plan not found'
				};
			}

			// Step 2: Verify ownership
			if (plan.organizationId !== orgId) {
				return {
					status: 403,
					message: 'You do not have permission to start work on this plan'
				};
			}

			// Step 3: Update the inProgress field and set generationStatus to 'processing'
			await this.organizationPlannerPlanRepository.updateById(id, {
				inProgress: true,
				generationStatus: 'generating',
				generationProgress: 0
			});

			// Get plan version
			let version = await this.plannerPlanVersionRepository.findOne({
				where: {
					organizationPlannerPlanId: id
				},
				include: [
					{
						relation: 'plannerCampaigns'
					}
				]
			});

			if (!version) {
				throw new HttpErrors.NotFound('No active version found for this plan');
			}

			// create task per each campaign of the plan
			for (const campaign of version?.plannerCampaigns) {
				const task = await this.taskService.createTaskForPlannerCampaign(campaign);

				// CALLING MOVED postProcessEmailContent, this needs to be moved to taskService
				this.taskService.postProcessEmailContent(task).catch((error) => console.error('Failed to post-process email content:', error));
			}

			// Update generationStatus to 'complete' after all tasks are created
			await this.organizationPlannerPlanRepository.updateById(id, {
				generationStatus: 'complete',
				generationProgress: 100
			});

			return {
				status: 200,
				message: 'Plan is now in progress'
			};
		} catch (error) {
			console.error('Failed to start work on plan:', error);
			return {
				status: 500,
				message: 'Failed to start work on plan: ' + error.message
			};
		}
	}

	@get('/organization-planner-plans/{id}')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getPlan(
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number
	): Promise<any> {
		try {
			// Step 1: Find the OrganizationPlannerPlan
			const plan = await this.organizationPlannerPlanRepository.findById(id);
			if (!plan) {
				return {
					status: 404,
					message: 'Plan not found'
				};
			}

			// Step 2: Verify ownership
			if (plan.organizationId !== orgId) {
				return {
					status: 403,
					message: 'You do not have permission to view this plan'
				};
			}

			// Step 3: Get the plan versions
			const versions = await this.organizationPlannerPlanRepository.plannerPlanVersions(id).find();

			return {
				status: 200,
				data: {
					plan,
					versions
				}
			};
		} catch (error) {
			console.error('Failed to get plan:', error);
			return {
				status: 500,
				message: 'Failed to get plan: ' + error.message
			};
		}
	}

	@post('/planner/generate-email-from-component-list')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async generateEmailFromComponentList(
		@requestBody({
			content: {
			  'application/json': {
				schema: {
				  type: 'object',
				  properties: {
					components: {
					  type: 'array',
					  items: {
						type: 'object',
						properties: {
						  name: {
							type: 'string'
						  },
						  fields: {
							type: 'array',
							items: {
							  type: 'object',
							  properties: {
								name: {
								  type: 'string'
								},
								value: {
								  type: 'string'
								}
							  },
							  required: ['name', 'value']
							}
						  }
						},
						required: ['name', 'fields']
					  }
					}
				  },
				  required: ['components']
				}
			  }
			}
		  }) data: { components: any[] },
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		try {
			// Find the EmailBase component
			let emailBaseComponent = await this.unlayerComponentRepository.findOne({
				where: {
					name: 'EmailBase',
					orgId: orgId
				}
			});

			if (!emailBaseComponent) {
				emailBaseComponent = await this.unlayerComponentRepository.findOne({
					where: {
						name: 'EmailBase',
						orgId: {eq: null as any}
					}
				});
			}

			// Lets assemble the email
			let emailJSON = emailBaseComponent?.json;
			let emailJSONFinal = emailJSON ? JSON.parse(emailJSON) : null;
			let emailComponents : any[] = []

			//For each item in emailDesign we need to look for the component and append
			for(const item of data.components)
				{
					let component = await this.unlayerComponentRepository.findOne({
						where: {
							name: item.name,
							orgId: orgId
						}
					});

					if (!component) {
						component = await this.unlayerComponentRepository.findOne({
							where: {
								name: item.name,
								orgId: {eq: null as any}
							}
						});
					}
					emailComponents.push(component);
				}

			//Go through each component and add it to the emailJSON
			for(var i = 0; i < emailComponents.length; i++)
			{
				let emailBrandingComponent = data.components[i];
				let componentJSON = emailComponents[i]?.json;
				let componentObject = componentJSON ? JSON.parse(componentJSON) : null;


				//lets see if we have any editable fields
				if(emailBrandingComponent?.fields && emailBrandingComponent?.fields.length > 0)
				{
					for(const field of emailBrandingComponent.fields) {
						if(componentObject) {
						  // Replace direct assignment with nested setter
						  setNestedValue(componentObject, field.name, field.value);
						}
					}
				}

				//Add all body.rows to the emailJSONFinal body.rows array
				if(!emailJSONFinal.body.rows) {
					emailJSONFinal.body.rows = [];
				}

				if(componentObject?.body?.rows) {
					emailJSONFinal.body.rows = emailJSONFinal.body.rows.concat(componentObject.body.rows);
				}
			}

			return emailJSONFinal;
		} catch (error) {
			console.error('Failed to generate email design:', error);
			throw new Error('Failed to generate email design: ' + error.message);
		}
	}
}
