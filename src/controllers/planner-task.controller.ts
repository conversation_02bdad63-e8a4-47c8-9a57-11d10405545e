import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {
	repository,
} from '@loopback/repository';
import {
	api,
	get,
	HttpErrors,
	param,
	patch,
	post,
	requestBody,
} from '@loopback/rest';
import {skipGuardCheck, injectUserOrgId, OrgGuardPropertyStrategy, guardStrategy} from '../interceptors';
import {OrganizationPlannerPlanRepository, PlannerCampaignRepository, TaskRepository, PlannerPlanVersionRepository, OrganizationSegmentRepository, EmailGenerationRepository, PromptTemplateRepository, UnlayerComponentRepository, ConversationRepository, OrganizationRepository, CampaignSegmentRepository} from '../repositories';
import {inject, service} from '@loopback/core';
import {basicAuthorization} from '../services';
import {Task, TaskWithRelations, PlannerCampaign} from '../models';
import {TaskService} from '../services/tasks/task.service';
import {handleClaudeResponse} from '../services/chat/utils/campaign-parser'
import {OrganizationSegmentController} from './organization-segment.controller'

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<any>({
    orgIdModelPropertyName: 'organizationId',
    repositoryClass: OrganizationRepository
}))
export class PlannerTaskController {
	constructor(
		@repository(TaskRepository) private taskRepository: TaskRepository,
		@repository(OrganizationPlannerPlanRepository) private organizationPlannerPlanRepository: OrganizationPlannerPlanRepository,
		@repository(PlannerCampaignRepository) private plannerCampaignRepository: PlannerCampaignRepository,
		@repository(PlannerPlanVersionRepository) private plannerPlanVersionRepository: PlannerPlanVersionRepository,
		@repository(OrganizationSegmentRepository) private organizationSegmentRepository: OrganizationSegmentRepository,
		@repository(EmailGenerationRepository) private emailGenerationRepository: EmailGenerationRepository,
		@repository(PromptTemplateRepository) private promptTemplateRepository: PromptTemplateRepository,
		@repository(UnlayerComponentRepository) private unlayerComponentRepository: UnlayerComponentRepository,
		@repository(ConversationRepository) private conversationRepository: ConversationRepository,
		@repository(CampaignSegmentRepository) private campaignSegmentRepository: CampaignSegmentRepository,
		@inject('datasources.dev_db') private devDbDataSource: any,
		@service(TaskService) private taskService: TaskService,
		@inject('controllers.OrganizationSegmentController') private organizationSegmentController: OrganizationSegmentController,
	) {}


	@get('/planner/tasks')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
        async getTasks(
          @param.query.boolean('includeArchived') includeArchived = false,
          @injectUserOrgId() orgId: number
        ): Promise<Task[]> {
	  const orgPlannerPlans = await this.organizationPlannerPlanRepository.find({
	    where: {
	      organizationId: orgId
	    },
	    include: [
	      {
	        relation: 'plannerPlanVersions',
	        scope: {
	          include: [
	            {
	              relation: 'plannerCampaigns',
	              scope: {
	                where: { or: [{ isDeleted: false }, { isDeleted: { eq: null } }] },
	                include: [
	                  {
	                    relation: 'task'
	                  }
	                ]
	              }
	            }
	          ]
	        }
	      }
	    ]
	  });

	  const tasks: Task[] = [];
	  for (const plan of orgPlannerPlans) {
	    for (const version of (plan?.plannerPlanVersions || [])) {
              for (const campaign of (version?.plannerCampaigns || [])) {
                if (campaign.task) {
                  if (!includeArchived && campaign.task.status === 'Archive') {
                    continue;
                  }
                  tasks.push({
                  ...campaign.task,
                    plannerCampaign: campaign,
                    plan,
                    plannerPlanVersionId: version.id
                  } as any);
                }
              }
            }
          }

	  tasks.sort((a, b) => new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime());

	  return tasks;
	}

	@patch('/planner/task/{taskId}')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async updateTask(
		@param.path.number('taskId') taskId: number,
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							status: {
								type: 'string'
							}
						},

					},
				},
			},
		}) updates: Partial<Task>,
		@injectUserOrgId() orgId: number
	): Promise<any> {
		try {
			// Find the task
			const task = await this.taskRepository.findById(taskId);
			if (!task) {
				throw new HttpErrors.NotFound('Task not found');
			}

			// Verify ownership through campaign -> version -> plan -> org
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
			const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
			const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);

			if (plan.organizationId !== orgId) {
				throw new HttpErrors.Forbidden('You do not have permission to update this task');
			}

			// Validate emailDesign: only allow valid JSON design objects
			if ('emailDesign' in updates) {
				const design = updates.emailDesign;
				// Check 1: Must be a non-null object and not an array.
				// Check 2: Must contain a 'body' key (heuristic for a valid design object).
				// Check 3: Should not *only* contain an 'html' key (heuristic for exported HTML object).
				const isObject = typeof design === 'object' && design !== null && !Array.isArray(design);
				const hasBodyKey = isObject && Object.prototype.hasOwnProperty.call(design, 'body');
				const onlyHtmlKey = isObject && Object.keys(design).length === 1 && Object.prototype.hasOwnProperty.call(design, 'html');

				if (!isObject || !hasBodyKey || onlyHtmlKey) {
					// If it fails any check (not object, no body, or only html key), ignore the update for this field.
					console.warn(`Ignoring invalid or non-design object format for emailDesign update in task ${taskId}. Type: ${typeof design}, Keys: ${isObject ? Object.keys(design).join(', ') : 'N/A'}`);
					delete updates.emailDesign;
				}
				// If it passes all checks, it's considered a valid design object and will be saved.
			}
			// Update the task
			await this.taskRepository.updateById(taskId, updates);

			return {
				status: 200,
				message: 'Task updated successfully'
			};
		} catch (error) {
			if (error instanceof HttpErrors.HttpError) {
				throw error;
			}
			throw new HttpErrors.InternalServerError('Error updating task');
		}
	}

	@post('/planner/task/{taskId}/regenerate')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async regenerateTaskContent(
		@param.path.number('taskId') taskId: number,
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							userPrompt: { type: 'string' },
						},
					},
				},
			},
			required: false,
		}) body?: { userPrompt?: string } | undefined,
		@injectUserOrgId() orgId?: number
	): Promise<any> {
		if (orgId === undefined) {
			throw new Error('orgId was not injected properly');
		}
		const userPrompt = body?.userPrompt; // Extract userPrompt if body exists
		try {
			// Find the task
			const task = await this.taskRepository.findById(taskId, {
				include: [
					{
						relation: 'taskSteps'
					}
				]
			});

			if (!task) {
				throw new HttpErrors.NotFound('Task not found');
			}

			// Verify ownership through campaign -> version -> plan -> org
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
			const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
			const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);

			if (plan.organizationId !== orgId) {
				throw new HttpErrors.Forbidden('You do not have permission to regenerate this task content');
			}

			// Call postProcessEmailContent to regenerate the content
			await this.taskService.postProcessEmailContent(task, userPrompt);

			return {
				status: 200,
				message: 'Task content regenerated successfully'
			};
		} catch (error) {
			if (error instanceof HttpErrors.HttpError) {
				throw error;
			}
			throw new HttpErrors.InternalServerError('Error regenerating task content');
		}
	}

	@get('/planner/task/{taskId}')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getTask(
		@param.path.number('taskId') taskId: number,
		@injectUserOrgId() orgId: number
	): Promise<Task & { campaign: PlannerCampaign }> {
		const task = await this.taskRepository.findById(taskId, {
			include: [
				{
					relation: 'taskSteps'
				}
			]
		});

		if (!task) {
			throw new HttpErrors.NotFound('Task not found');
		}

		const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
		const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
		const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);
		if (!plan) {
			throw new HttpErrors.NotFound('Plan not found');
		}
		if (plan.organizationId !== orgId) {
			throw new HttpErrors.Forbidden('You do not have permission to view this task');
		}

		// Look up the conversation associated with this task
		console.log('Looking up conversation for task:', taskId);

		try {
			// Find the conversation with this taskId
			const conversation = await this.conversationRepository.findOne({
				where: {
					taskId: taskId
				}
			});

			console.log('Conversation lookup result:', conversation);

			if (conversation && conversation.id) {
				console.log('Found conversation ID:', conversation.id);

				// Update the task with the conversationId
				task.conversationId = conversation.id;
				await this.taskRepository.updateById(taskId, {
					conversationId: conversation.id
				});
			} else {
				console.log('No conversation found for task:', taskId);

				// Try a direct SQL query as a fallback
				const query = `SELECT id FROM conversation WHERE "taskid" = ${taskId}`;
				console.log('Executing fallback query:', query);

				const result = await this.devDbDataSource.execute(query);
				console.log('Fallback query result:', result);

				if (result && result.length > 0) {
					const conversationId = result[0].id;
					console.log('Found conversation ID from fallback:', conversationId);

					// Update the task with the conversationId
					task.conversationId = conversationId;
					await this.taskRepository.updateById(taskId, {
						conversationId: conversationId
					});
				}
			}
		} catch (error: any) {
			console.error('Error looking up conversation:', error);
		}

		const segmentName = campaign.targetSegment;

		let segmentData;
		try {
			// Check if this campaign uses a Klaviyo segment
			const campaignSegments = await this.campaignSegmentRepository.find({
				where: { campaignId: campaign.id }
			});

			const klaviyoSegment = campaignSegments.find(cs => cs.segmentType === 'klaviyo' && cs.klaviyoSegmentId);
			let isKlaviyoSegment = !!klaviyoSegment;

			if (isKlaviyoSegment && klaviyoSegment) {
				// Create a mock segment object for Klaviyo segments
				segmentData = {
					id: -1, // Use -1 for Klaviyo segments
					name: segmentName,
					description: 'Klaviyo segment used directly',
					segmentType: 'klaviyo',
					klaviyoSegmentId: klaviyoSegment.klaviyoSegmentId,
					externalId: klaviyoSegment.klaviyoSegmentId, // Mark as "synced"
					aggregates: {
						totalCount: 'Available in Klaviyo',
						averageLtv: 0
					},
					organizationSegmentDetails: []
				};
			} else {
				// Fetch the regular Raleon segment details
				const segment = await this.organizationSegmentRepository.findOne({
					where: {
						orgId: orgId,
						name: segmentName
					}
				});
				if (segment && segment.id) {
					segmentData = await this.organizationSegmentController.getSegmentById(segment.id, 1, orgId, undefined, undefined, undefined, false);
				} else {
					console.warn(`Segment not found for name: ${segmentName} and orgId: ${orgId}`);
					segmentData = {
						name: segmentName,
						description: 'Segment not found',
						aggregates: {
							totalCount: 0,
							averageLtv: 0
						},
						organizationSegmentDetails: []
					};
				}
			}
		} catch (error) {
			console.error('Failed to fetch segment data:', error);
			segmentData = {
				name: segmentName,
				description: 'Segment description not available'
			};
		}

		return {
			...task,
			campaign,
			plan,
			segment: segmentData
		} as any;
	}

	@post('/planner/task/{taskId}/create-klaviyo-campaign')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async createKlaviyoCampaign(
		@param.path.number('taskId') taskId: number,
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							html: {
								type: 'string'
							}
						}
					}
				}
			}
		}) data: {
			html?: string
		},
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		try {
			// Create a record to track the creation status
			await this.emailGenerationRepository.create({
				taskId: taskId,
				status: 'pending',
				startTime: new Date().toISOString(),
				operationType: 'klaviyo_campaign'
			});

			// Start async campaign creation process
			this.taskService.createKlaviyoCampaignAsync(taskId, orgId, data.html).catch(error => {
				console.error('Failed to create Klaviyo campaign:', error);
				// Update status to failed in case of error
				this.emailGenerationRepository.updateAll({
					status: 'failed',
					error: error.message
				}, {
					taskId: taskId,
					operationType: 'klaviyo_campaign'
				}).catch(e => console.error('Failed to update status:', e));
			});

			return {
				status: 200,
				message: 'Klaviyo campaign creation initiated',
				data: {
					status: 'pending'
				}
			};
		} catch (error) {
			console.error('Failed to initiate Klaviyo campaign creation:', error);
			return {
				status: 500,
				message: 'Failed to initiate Klaviyo campaign creation: ' + error.message
			};
		}
	}

	@post('/planner/task/{taskId}/create-klaviyo-campaign-html-only')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async createKlaviyoCampaignHtmlOnly(
		@param.path.number('taskId') taskId: number,
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							html: {
								type: 'string'
							}
						}
					}
				}
			}
		}) data: {
			html?: string
		},
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		try {
			// Create a record to track the creation status
			await this.emailGenerationRepository.create({
				taskId: taskId,
				status: 'pending',
				startTime: new Date().toISOString(),
				operationType: 'klaviyo_campaign_html_only'
			});

			// Start async campaign creation process (HTML-only)
			this.taskService.createKlaviyoCampaignHtmlOnlyAsync(taskId, orgId, data.html).catch(error => {
				console.error('Failed to create Klaviyo campaign (HTML-only):', error);
				// Update status to failed in case of error
				this.emailGenerationRepository.updateAll({
					status: 'failed',
					error: error.message
				}, {
					taskId: taskId,
					operationType: 'klaviyo_campaign_html_only'
				}).catch(e => console.error('Failed to update status:', e));
			});

			return {
				status: 200,
				message: 'Klaviyo campaign creation (HTML-only) initiated',
				data: {
					status: 'pending'
				}
			};
		} catch (error) {
			console.error('Failed to initiate Klaviyo campaign creation (HTML-only):', error);
			return {
				status: 500,
				message: 'Failed to initiate Klaviyo campaign creation (HTML-only): ' + error.message
			};
		}
	}

	@post('/planner/task/{taskId}/resync-klaviyo-campaign-html-only')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async resyncKlaviyoCampaignHtmlOnly(
		@param.path.number('taskId') taskId: number,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		try {
			await this.emailGenerationRepository.updateAll({
				status: 'pending',
				startTime: new Date().toISOString()
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Start async campaign resync process (HTML-only)
			this.taskService.resyncKlaviyoCampaignHtmlOnlyAsync(taskId, orgId).catch(error => {
				console.error('Failed to resync Klaviyo campaign (HTML-only):', error);
				// Update status to failed in case of error
				this.emailGenerationRepository.updateAll({
					status: 'failed',
					error: error.message
				}, {
					taskId: taskId,
					operationType: 'klaviyo_campaign_html_only'
				}).catch(e => console.error('Failed to update status:', e));
			});

			return {
				status: 200,
				message: 'Klaviyo campaign resync (HTML-only) initiated',
				data: {
					status: 'pending'
				}
			};
		} catch (error) {
			console.error('Failed to initiate Klaviyo campaign resync (HTML-only):', error);
			return {
				status: 500,
				message: 'Failed to initiate Klaviyo campaign resync (HTML-only): ' + error.message
			};
		}
	}

	@post('/planner/task/{taskId}/resync-klaviyo-campaign')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async resyncKlaviyoCampaign(
		@param.path.number('taskId') taskId: number,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		try {
			// Create a record to track the resync status
			// await this.emailGenerationRepository.create({
			// 	taskId: taskId,
			// 	status: 'pending',
			// 	startTime: new Date().toISOString(),
			// 	operationType: 'klaviyo_resync'
			// });

			await this.emailGenerationRepository.updateAll({
				status: 'pending',
				startTime: new Date().toISOString()
			}, {
				taskId: taskId
			});

			// Start async campaign resync process
			this.taskService.resyncKlaviyoCampaignAsync(taskId, orgId).catch(error => {
				console.error('Failed to resync Klaviyo campaign:', error);
				// Update status to failed in case of error
				this.emailGenerationRepository.updateAll({
					status: 'failed',
					error: error.message
				}, {
					taskId: taskId,
					operationType: 'klaviyo_resync'
				}).catch(e => console.error('Failed to update status:', e));
			});

			return {
				status: 200,
				message: 'Klaviyo campaign resync initiated',
				data: {
					status: 'pending'
				}
			};
		} catch (error) {
			console.error('Failed to initiate Klaviyo campaign resync:', error);
			return {
				status: 500,
				message: 'Failed to initiate Klaviyo campaign resync: ' + error.message
			};
		}
	}

	@post('/planner/task/{taskId}/email-content')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async generateEmailContent(
		@param.path.number('taskId') taskId: number,
		@injectUserOrgId() orgId: number
	): Promise<any> {
		try {
			// Create generation record with explicit operationType
			await this.emailGenerationRepository.create({
				taskId: taskId,
				status: 'pending',
				startTime: new Date().toISOString(),
				operationType: 'email'  // Explicitly set the operationType
			});

			// Start async generation process
			console.log('Initiating email content generation for task:', taskId);
			this.taskService.generateEmailContentAsync(taskId, orgId).catch(error => {
				console.error('Failed to generate email content:', error);
			});

			return {
				status: 200,
				message: 'Email content generation initiated'
			};
		} catch (error) {
			console.error('Failed to initiate email generation:', error);
			return {
				status: 500,
				message: 'Failed to initiate email generation: ' + error.message
			};
		}
	}

	@get('/planner/task/{taskId}/email-status')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getEmailGenerationStatus(
		@param.path.number('taskId') taskId: number,
	): Promise<object> {
		// First try to find records with operationType='email'
		let generation = await this.emailGenerationRepository.findOne({
			where: {
				taskId: taskId,
				operationType: 'email'
			},
			order: ['startTime DESC']
		});

		// If no record found with operationType='email', try to find records with no operationType (for backward compatibility)
		if (!generation) {
			generation = await this.emailGenerationRepository.findOne({
				where: {
					taskId: taskId,
					operationType: { exists: false }
				},
				order: ['startTime DESC']
			});
		}

		if (!generation) {
			return {
				status: 'none'
			};
		}

		console.log('Found email generation status:', generation.status, 'for task:', taskId);
		return {
			status: generation.status,
			startTime: generation.startTime,
			design: generation.design
		};
	}

	@get('/planner/task/{taskId}/klaviyo-status')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getKlaviyoCampaignStatus(
		@param.path.number('taskId') taskId: number,
	): Promise<object> {

		const resync = await this.emailGenerationRepository.findOne({
			where: {
				taskId: taskId,
				operationType: 'klaviyo_resync'
			},
			order: ['startTime DESC']
		});

		if (resync) {
			return {
				status: resync.status,
				startTime: resync.startTime,
				step: resync.step,
				data: resync.data,
				error: resync.error
			};
		}

		// Check for HTML-only campaign first (most recent)
		const htmlOnlyGeneration = await this.emailGenerationRepository.findOne({
			where: {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			},
			order: ['startTime DESC']
		});

		if (htmlOnlyGeneration) {
			return {
				status: htmlOnlyGeneration.status,
				startTime: htmlOnlyGeneration.startTime,
				step: htmlOnlyGeneration.step,
				data: htmlOnlyGeneration.data,
				error: htmlOnlyGeneration.error
			};
		}

		// Check for regular hybrid campaign
		const generation = await this.emailGenerationRepository.findOne({
			where: {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			},
			order: ['startTime DESC']
		});

		if (!generation) {
			return {
				status: 'none'
			};
		}

		return {
			status: generation.status,
			startTime: generation.startTime,
			step: generation.step,
			data: generation.data,
			error: generation.error
		};
	}

	@patch('/planner/task/{taskId}/taskstep/{taskStepId}')
	@authenticate('jwt')
	@skipGuardCheck()
	async updateTaskStep(
		@param.path.number('taskId') taskId: number,
		@param.path.number('taskStepId') taskStepId: number,
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							data: {
								type: 'string'
							}
						},
						required: ['data']
					},
				},
			},
		}) updates: { data: string },
		@injectUserOrgId() orgId: number
	): Promise<any> {
		try {
			// Find the task
			const task = await this.taskRepository.findById(taskId);
			if (!task) {
				throw new HttpErrors.NotFound('Task not found');
			}

			// Verify ownership through campaign -> version -> plan -> org
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
			const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
			const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);

			if (plan.organizationId !== orgId) {
				throw new HttpErrors.Forbidden('You do not have permission to update this task');
			}

			// Update the task step
			await this.taskRepository.taskSteps(taskId).patch(updates, {
				id: taskStepId
			});

			return {
				status: 200,
				message: 'Task step updated successfully'
			};
		} catch (error) {
			if (error instanceof HttpErrors.HttpError) {
				throw error;
			}
			throw new HttpErrors.InternalServerError('Error updating task step');
		}
	}

	@post('/planner/task/{taskId}/save-email-html')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async saveEmailHtml(
		@param.path.number('taskId') taskId: number,
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						required: ['html'],
						properties: {
							html: {
								type: 'string'
							}
						}
					}
				}
			}
		}) data: {
			html: string
		},
		@injectUserOrgId() orgId: number
	): Promise<any> {
		try {
			// Find the task
			const task = await this.taskRepository.findById(taskId);
			if (!task) {
				throw new HttpErrors.NotFound('Task not found');
			}

			// Verify ownership
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
			const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
			const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);

			if (plan.organizationId !== orgId) {
				throw new HttpErrors.Forbidden('You do not have permission to update this task');
			}

			// Save the HTML
			await this.taskRepository.updateById(taskId, {
				emailHtml: data.html
			});

			return {
				status: 200,
				message: 'Email HTML saved successfully'
			};
		} catch (error) {
			if (error instanceof HttpErrors.HttpError) {
				throw error;
			}
			throw new HttpErrors.InternalServerError('Error saving email HTML: ' + error.message);
		}
	}
}
