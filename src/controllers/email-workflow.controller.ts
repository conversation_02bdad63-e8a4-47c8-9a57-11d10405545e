import {repository} from '@loopback/repository';
import {
  api,
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  requestBody,
  response,
} from '@loopback/rest';
import {inject, service} from '@loopback/core';
import {
  EmailWorkflow,
  EmailWorkflowGeneration,
  EmailWorkflowLog,
  EmailWorkflowStatus,
  EmailWorkflowGenerationStatus,
  EmailWorkflowLogLevel
} from '../models';
import {
  EmailWorkflowRepository,
  EmailWorkflowGenerationRepository,
  EmailWorkflowLogRepository,
  TaskRepository
} from '../repositories';
import {EmailWorkflowService, EmailWorkflowOptions, BriefData} from '../services/email-workflow.service';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {SecurityBindings, UserProfile} from '@loopback/security';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck, injectUserOrgId} from '../interceptors';
import {basicAuthorization} from '../services';

@guardStrategy(new GuardSkipStrategy())
@api({basePath: '/api/v1'})
export class EmailWorkflowController {
  constructor(
    @repository(EmailWorkflowRepository)
    public emailWorkflowRepository: EmailWorkflowRepository,
    @repository(EmailWorkflowGenerationRepository)
    public emailWorkflowGenerationRepository: EmailWorkflowGenerationRepository,
    @repository(EmailWorkflowLogRepository)
    public emailWorkflowLogRepository: EmailWorkflowLogRepository,
    @repository(TaskRepository)
    public taskRepository: TaskRepository,
    @service(EmailWorkflowService)
    public emailWorkflowService: EmailWorkflowService,
    @inject(SecurityBindings.USER)
    public currentUserProfile: UserProfile,
  ) {}

  @post('/workflows/email')
  @response(200, {
    description: 'Create a new email workflow',
    content: {
      'application/json': {
        schema: getModelSchemaRef(EmailWorkflow, {includeRelations: true}),
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async createEmailWorkflow(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['taskId', 'briefData'],
            properties: {
              taskId: {type: 'number'},
              briefData: {
                type: 'object',
                required: ['subjectLine', 'previewText', 'briefText'],
                properties: {
                  subjectLine: {type: 'string'},
                  previewText: {type: 'string'},
                  briefText: {type: 'string'},
                },
              },
              options: {
                type: 'object',
                properties: {
                  iterationCount: {type: 'number', default: 3},
                  timeoutMinutes: {type: 'number', default: 30},
                  variations: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        tone: {type: 'string'},
                        style: {type: 'string'},
                        focus: {type: 'string'},
                        customInstructions: {type: 'string'},
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    })
    request: {
      taskId: number;
      briefData: BriefData;
      options?: EmailWorkflowOptions;
    },
    @injectUserOrgId() orgId: number,
  ): Promise<EmailWorkflow> {
    // Verify task exists and user has access
    const task = await this.taskRepository.findById(request.taskId);
    if (!task) {
      throw new Error(`Task ${request.taskId} not found`);
    }

    // Validate task ownership - this will be implemented based on your task-organization relationship
    // For now, we'll skip this validation since the exact relationship isn't clear
    // TODO: Add proper task ownership validation based on organization

    // Create workflow
    const workflow = await this.emailWorkflowService.createWorkflow(
      request.taskId,
      request.briefData,
      request.options
    );

    // Start execution
    await this.emailWorkflowService.executeWorkflow(workflow.id!);

    return workflow;
  }

  @get('/workflows/task/{taskId}')
  @response(200, {
    description: 'Get all email workflows for a task',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EmailWorkflow, {includeRelations: true}),
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async getWorkflowsByTaskId(
    @param.path.number('taskId') taskId: number,
    @injectUserOrgId() orgId: number,
  ): Promise<EmailWorkflow[]> {
    // Verify task exists and user has access
    const task = await this.taskRepository.findById(taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }

    // TODO: Add task ownership validation

    return this.emailWorkflowService.getWorkflowsByTaskId(taskId);
  }

  @get('/workflows/{id}')
  @response(200, {
    description: 'Get workflow by ID',
    content: {
      'application/json': {
        schema: getModelSchemaRef(EmailWorkflow, {includeRelations: true}),
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async getWorkflowById(
    @param.path.number('id') id: number,
    @injectUserOrgId() orgId: number,
  ): Promise<EmailWorkflow> {
    const workflow = await this.emailWorkflowService.getWorkflowById(id);
    if (!workflow) {
      throw new Error(`Workflow ${id} not found`);
    }
    // TODO: Add workflow ownership validation
    return workflow;
  }

  @get('/workflows/{id}/status')
  @response(200, {
    description: 'Get workflow status and progress',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            id: {type: 'number'},
            status: {type: 'string'},
            progress: {
              type: 'object',
              properties: {
                completedIterations: {type: 'number'},
                totalIterations: {type: 'number'},
                currentIteration: {type: 'number'},
                percentage: {type: 'number'},
              },
            },
            timeRemaining: {type: 'number'},
            estimatedCompletion: {type: 'string'},
            generations: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: {type: 'number'},
                  iterationNumber: {type: 'number'},
                  status: {type: 'string'},
                  hasEmailDesign: {type: 'boolean'},
                  hasHtml: {type: 'boolean'},
                  error: {type: 'string'},
                },
              },
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async getWorkflowStatus(
    @param.path.number('id') id: number,
    @injectUserOrgId() orgId: number,
  ): Promise<any> {
    const workflow = await this.emailWorkflowService.getWorkflowById(id);
    if (!workflow) {
      throw new Error(`Workflow ${id} not found`);
    }
    // TODO: Add workflow ownership validation

    const generations = await this.emailWorkflowGenerationRepository.findByWorkflowId(id);

    // Calculate progress
    const completedIterations = generations.filter(g => g.status === EmailWorkflowGenerationStatus.COMPLETED).length;
    const processingIteration = generations.find(g => g.status === EmailWorkflowGenerationStatus.PROCESSING);
    const currentIteration = processingIteration ? processingIteration.iterationNumber : 0;
    const percentage = (completedIterations / workflow.iterationCount) * 100;

    // Calculate time remaining
    let timeRemaining = 0;
    let estimatedCompletion = null;

    if (workflow.status === 'processing' && workflow.startedAt) {
      const timeoutTime = workflow.timeoutAt.getTime();
      timeRemaining = Math.max(0, timeoutTime - Date.now());
      estimatedCompletion = new Date(timeoutTime).toISOString();
    }

    return {
      id: workflow.id,
      status: workflow.status,
      progress: {
        completedIterations,
        totalIterations: workflow.iterationCount,
        currentIteration,
        percentage,
      },
      timeRemaining,
      estimatedCompletion,
      generations: generations.map(g => ({
        id: g.id,
        iterationNumber: g.iterationNumber,
        status: g.status,
        hasEmailDesign: !!g.emailDesignJson,
        hasHtml: !!g.finalHtml,
        error: g.error,
      })),
    };
  }

  @get('/workflows/{id}/logs')
  @response(200, {
    description: 'Get workflow execution logs',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EmailWorkflowLog),
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async getWorkflowLogs(
    @param.path.number('id') id: number,
    @param.query.string('level') level: string = 'info',
    @param.query.number('limit') limit: number = 50,
    @injectUserOrgId() orgId: number,
  ): Promise<EmailWorkflowLog[]> {
    const workflow = await this.emailWorkflowRepository.findById(id);
    if (!workflow) {
      throw new Error(`Workflow ${id} not found`);
    }
    // TODO: Add workflow ownership validation

    if (level) {
      return this.emailWorkflowLogRepository.findByWorkflowAndLevel(id, level as EmailWorkflowLogLevel);
    }

    if (limit) {
      return this.emailWorkflowLogRepository.findRecentLogs(id, limit);
    }

    return this.emailWorkflowService.getWorkflowLogs(id);
  }

  @get('/workflows/{id}/variations')
  @response(200, {
    description: 'Get generated email variations',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: {type: 'number'},
              iterationNumber: {type: 'number'},
              status: {type: 'string'},
              variationPrompt: {type: 'object'},
              emailDesignJson: {type: 'object'},
              finalHtml: {type: 'string'},
              imageProcessingData: {type: 'object'},
              completedAt: {type: 'string'},
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async getWorkflowVariations(
    @param.path.number('id') id: number,
    @injectUserOrgId() orgId: number,
  ): Promise<any[]> {
    const workflow = await this.emailWorkflowRepository.findById(id);
    if (!workflow) {
      throw new Error(`Workflow ${id} not found`);
    }
    // TODO: Add workflow ownership validation

    const completedGenerations = await this.emailWorkflowGenerationRepository.findCompletedGenerations(id);

    return completedGenerations.map(gen => ({
      id: gen.id,
      iterationNumber: gen.iterationNumber,
      status: gen.status,
      variationPrompt: gen.variationPrompt,
      emailDesignJson: gen.emailDesignJson,
      finalHtml: gen.finalHtml,
      imageProcessingData: gen.imageProcessingData,
      completedAt: gen.completedAt,
    }));
  }

  @post('/workflows/{id}/retry')
  @response(200, {
    description: 'Retry a failed workflow',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            message: {type: 'string'},
            workflowId: {type: 'number'},
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async retryWorkflow(
    @param.path.number('id') id: number,
    @injectUserOrgId() orgId: number,
  ): Promise<{message: string; workflowId: number}> {
    // TODO: Add workflow ownership validation
    await this.emailWorkflowService.retryWorkflow(id);
    return {
      message: 'Workflow retry initiated',
      workflowId: id,
    };
  }

  @get('/workflows/{id}/generation/{generationId}')
  @response(200, {
    description: 'Get specific generation details',
    content: {
      'application/json': {
        schema: getModelSchemaRef(EmailWorkflowGeneration, {includeRelations: true}),
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async getGenerationById(
    @param.path.number('id') workflowId: number,
    @param.path.number('generationId') generationId: number,
    @injectUserOrgId() orgId: number,
  ): Promise<EmailWorkflowGeneration> {
    const generation = await this.emailWorkflowGenerationRepository.findByIdWithLogs(generationId);
    if (!generation || generation.workflowId !== workflowId) {
      throw new Error(`Generation ${generationId} not found in workflow ${workflowId}`);
    }
    // TODO: Add workflow ownership validation
    return generation;
  }

  @get('/workflows/{id}/generation/{generationId}/logs')
  @response(200, {
    description: 'Get logs for a specific generation',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EmailWorkflowLog),
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async getGenerationLogs(
    @param.path.number('id') workflowId: number,
    @param.path.number('generationId') generationId: number,
    @injectUserOrgId() orgId: number,
  ): Promise<EmailWorkflowLog[]> {
    const generation = await this.emailWorkflowGenerationRepository.findById(generationId);
    if (!generation || generation.workflowId !== workflowId) {
      throw new Error(`Generation ${generationId} not found in workflow ${workflowId}`);
    }
    // TODO: Add workflow ownership validation

    return this.emailWorkflowLogRepository.findByGenerationId(generationId);
  }

  @get('/workflows/active')
  @response(200, {
    description: 'Get all active workflows',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EmailWorkflow),
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async getActiveWorkflows(
    @injectUserOrgId() orgId: number,
  ): Promise<EmailWorkflow[]> {
    // TODO: Filter by organization
    return this.emailWorkflowService.getActiveWorkflows();
  }

  @patch('/workflows/{id}/cancel')
  @response(200, {
    description: 'Cancel a running workflow',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            message: {type: 'string'},
            workflowId: {type: 'number'},
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async cancelWorkflow(
    @param.path.number('id') id: number,
    @injectUserOrgId() orgId: number,
  ): Promise<{message: string; workflowId: number}> {
    const workflow = await this.emailWorkflowRepository.findById(id);
    if (!workflow) {
      throw new Error(`Workflow ${id} not found`);
    }
    // TODO: Add workflow ownership validation

    if (workflow.status !== 'processing' && workflow.status !== 'pending') {
      throw new Error(`Cannot cancel workflow ${id} with status ${workflow.status}`);
    }

    // Update workflow status
    await this.emailWorkflowRepository.updateById(id, {
      status: EmailWorkflowStatus.FAILED,
      completedAt: new Date(),
      error: 'Workflow cancelled by user',
    });

    // Cancel any processing generations
    const processingGenerations = await this.emailWorkflowGenerationRepository.find({
      where: {
        workflowId: id,
        status: EmailWorkflowGenerationStatus.PROCESSING,
      },
    });

    for (const generation of processingGenerations) {
      await this.emailWorkflowGenerationRepository.updateById(generation.id!, {
        status: EmailWorkflowGenerationStatus.FAILED,
        completedAt: new Date(),
        error: 'Cancelled by user',
      });
    }

    // Log cancellation
    await this.emailWorkflowLogRepository.createLog({
      workflowId: id,
      level: EmailWorkflowLogLevel.INFO,
      message: 'Workflow cancelled by user',
      step: 'workflow_cancellation',
      data: {
        cancelledGenerations: processingGenerations.length,
      },
    });

    return {
      message: 'Workflow cancelled successfully',
      workflowId: id,
    };
  }
}

