import {inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {EmailGenerationRepository} from '../repositories';
import {CronJob} from 'cron';

@injectable()
export class CleanupService {
  private stuckJobCleanupJob: CronJob;

  constructor(
    @repository(EmailGenerationRepository)
    private emailGenerationRepository: EmailGenerationRepository,
  ) {
    // Different intervals for dev vs production
    const isProduction = process.env.NODE_ENV === 'production';
    const cronPattern = isProduction ? '0 */30 * * * *' : '0 */10 * * * *'; // 30min prod, 10min dev
    
    this.stuckJobCleanupJob = new CronJob(cronPattern, () => {
      this.cleanupStuckJobs();
    });
  }

  start() {
    const isProduction = process.env.NODE_ENV === 'production';
    const interval = isProduction ? '30 minutes' : '10 minutes';
    
    this.stuckJobCleanupJob.start();
    console.log(`Cleanup service started - will check for stuck jobs every ${interval}`);
  }

  stop() {
    this.stuckJobCleanupJob.stop();
    console.log('Cleanup service stopped');
  }

  async cleanupStuckJobs() {
    try {
      // Check memory usage before running cleanup
      const memUsage = process.memoryUsage();
      const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
      
      // Skip cleanup if memory usage is high (over 1.5GB)
      if (heapUsedMB > 1536) {
        console.warn(`[Cleanup] Skipping cleanup - high memory usage: ${heapUsedMB}MB`);
        return;
      }
      
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
      
      // Find stuck jobs with limit to prevent large result sets
      const stuckJobs = await this.emailGenerationRepository.find({
        where: {
          status: 'processing',
          startTime: {
            lt: thirtyMinutesAgo.toISOString()
          }
        },
        limit: 100 // Process max 100 jobs at a time
      });

      if (stuckJobs.length > 0) {
        console.log(`[Cleanup] Found ${stuckJobs.length} stuck jobs, cleaning up... (Heap: ${heapUsedMB}MB)`);
        
        // Update stuck jobs to failed status
        await this.emailGenerationRepository.updateAll(
          {
            status: 'failed',
            error: 'Job automatically reset - was stuck in processing state for over 30 minutes',
            step: undefined
          },
          {
            status: 'processing',
            startTime: {
              lt: thirtyMinutesAgo.toISOString()
            }
          }
        );

        console.log(`[Cleanup] Successfully cleaned up ${stuckJobs.length} stuck jobs`);
      }
    } catch (error) {
      console.error('[Cleanup] Error during stuck job cleanup:', error);
    }
  }

  // Manual cleanup method for immediate use
  async manualCleanup() {
    await this.cleanupStuckJobs();
  }
}