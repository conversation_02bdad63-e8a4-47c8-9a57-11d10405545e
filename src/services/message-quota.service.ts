import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {ConversationRepository, MessageRepository, MessageCreditRepository, OrganizationPlanRepository, PlanFeatureRepository, OrganizationRepository} from '../repositories';
import {MessageRole} from '../models/chat/message.model';

@injectable({scope: BindingScope.TRANSIENT})
export class MessageQuotaService {
  constructor(
    @repository(ConversationRepository) private conversationRepository: ConversationRepository,
    @repository(MessageRepository) private messageRepository: MessageRepository,
    @repository(MessageCreditRepository) private creditRepository: MessageCreditRepository,
    @repository(OrganizationPlanRepository) private orgPlanRepository: OrganizationPlanRepository,
    @repository(PlanFeatureRepository) private planFeatureRepository: PlanFeatureRepository,
    @repository(OrganizationRepository) private organizationRepository: OrganizationRepository,
  ) {}

  private async getDailyLimit(orgId: number): Promise<number> {
    // Fetch the organization details to check for parent org
    const org = await this.organizationRepository.findById(orgId);

    // Determine which org to check for plan - use parent org if it exists
    const planOrgId = org.parentOrgId || orgId;

    // Look up the organization's active plan to determine daily limit
    const activeOrgPlan = await this.orgPlanRepository.findOne({
      where: {
        orgId: planOrgId,
        status: 'ACTIVE'
      },
      include: [{relation: 'plan'}]
    });

    // Check if user is on trial (no active plan or no subscriptionId)
    const isTrialUser = !activeOrgPlan || !activeOrgPlan.subscriptionId;

    if (isTrialUser) {
      // Trial users get 10 messages per day
	  if(activeOrgPlan && (activeOrgPlan.planId === 16 || activeOrgPlan.planId === 17)) {
		return await this.ensureDailyLimitRecord(orgId, 50);
	  }
      return await this.ensureDailyLimitRecord(orgId, 10);
    }

    // For non-trial users, get the ai-strategist feature limit
    const aiStrategistFeature = await this.planFeatureRepository.findOne({
      where: {
        planId: activeOrgPlan.planId,
        featureId: 'ai-strategist'
      }
    });

    let featureBasedLimit = 5; // Default fallback
    if (aiStrategistFeature && aiStrategistFeature.limit) {
      featureBasedLimit = aiStrategistFeature.limit;
    }

    return await this.ensureDailyLimitRecord(orgId, featureBasedLimit);
  }

  private async ensureDailyLimitRecord(orgId: number, targetLimit: number): Promise<number> {
    // Look for existing daily limit record
    let dailyLimitRecord = await this.creditRepository.findOne({
      where: {
        organizationId: orgId,
        type: 'dailyLimit'
      }
    });

    // If no record exists or the limit has changed, create/update with new limit
    if (!dailyLimitRecord) {
      dailyLimitRecord = await this.creditRepository.create({
        organizationId: orgId,
        type: 'dailyLimit',
        credits: targetLimit
      });
    } else if (dailyLimitRecord.credits !== targetLimit) {
      // Update existing record if limit has changed
      dailyLimitRecord.credits = targetLimit;
      await this.creditRepository.updateById(dailyLimitRecord.id, dailyLimitRecord);
    }

    return dailyLimitRecord.credits || targetLimit;
  }

  async getUsageToday(orgId: number): Promise<number> {
    const start = new Date();
    start.setUTCHours(0,0,0,0);
    const conversations = await this.conversationRepository.find({where: {organizationId: orgId, updatedAt: {gte: start}}});
    const convIds = conversations.map(c => c.id).filter(id => id !== undefined) as number[];
    let userMessageCount = 0;
    if (convIds.length) {
      // Only count USER messages, not assistant/system/tool messages
      userMessageCount = (await this.messageRepository.count({
        conversationId: {inq: convIds},
        createdAt: {gte: start},
        role: MessageRole.USER // This is the key fix!
      })).count;
    }
    return userMessageCount;
  }

  async getPremiumCredits(orgId: number): Promise<number> {
    const credits = await this.creditRepository.find({
      where: {
        organizationId: orgId,
        type: {nin: ['daily', 'dailyLimit']} // Exclude daily reset credits and daily limit records
      }
    });
    return credits.reduce((sum, c) => sum + (c.credits || 0), 0);
  }

  async getQuotaStatus(orgId: number) {
    const [dailyLimit, usedToday, premium] = await Promise.all([
      this.getDailyLimit(orgId),
      this.getUsageToday(orgId),
      this.getPremiumCredits(orgId),
    ]);
    return {dailyLimit, dailyUsed: usedToday, premium};
  }

  async consume(orgId: number, count = 1): Promise<void> {
    const status = await this.getQuotaStatus(orgId);
    const dailyRemaining = Math.max(0, status.dailyLimit - status.dailyUsed);
    const totalAvailable = dailyRemaining + status.premium;

    if (totalAvailable < count) {
      throw new Error('No messages remaining');
    }

    // If daily quota covers it, no need to deduct premium
    if (dailyRemaining >= count) {
      return; // Usage automatically tracked via conversation/message creation
    }

    // Need to use premium credits
    const needed = count - dailyRemaining;
    const credits = await this.creditRepository.find({
      where: {
        organizationId: orgId,
        type: {nin: ['daily', 'dailyLimit']} // Exclude daily reset credits and daily limit records
      },
      order: ['createdAt ASC'] // Use oldest first
    });

    let toDeduct = needed;
    for (const credit of credits) {
      if (credit.credits > 0) {
        const use = Math.min(credit.credits, toDeduct);
        credit.credits -= use;
        await this.creditRepository.updateById(credit.id, credit);
        toDeduct -= use;
        if (toDeduct <= 0) break;
      }
    }
  }
}
