import {jsonrepair} from 'jsonrepair';

interface Campaign {
    id: number;
    name: string;
    type: 'Promotion' | 'Education' | 'Awareness';
    description: string;
    targetSegment: string;
    scheduledDate: string;
    businessGoal: string;
    whyText: string;
    taskType: 'Email' | 'SMS' | 'Loyalty';
    promotion: {
        type: string;
        discount: string;
        details: string;
        reason?: string;
    };
}

interface CampaignResponse {
    campaigns: Campaign[];
    metadata: {
        totalCampaigns: number;
        name: string;
        description: string;
        dataSummary: string;
        businessGoal: string;
        timeframeCovered: {
            startDate: string;
            endDate: string;
        };
    };
}

// Helper method to parse and validate the Claude response
export function parseCampaignResponse(rawResponse: string): CampaignResponse {
    try {
        // First, try to parse the JSON
        let cleaned = rawResponse.replace(/^[\s`]*json[\s`]*|^[\s`]*|[\s`]*```[\s`]*$/gmi, '');
        const firstBrace = cleaned.indexOf('{');
        const lastBrace = cleaned.lastIndexOf('}');
        if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
            cleaned = cleaned.substring(firstBrace, lastBrace + 1);
        }
        const repairedJsonString = jsonrepair(cleaned);
        const parsed = JSON.parse(repairedJsonString);

        // Basic validation
        if (!parsed.campaigns || !Array.isArray(parsed.campaigns)) {
            throw new Error('Invalid response format: campaigns array is missing');
        }

        if (!parsed.metadata || !parsed.metadata.totalCampaigns || !parsed.metadata.timeframeCovered) {
            throw new Error('Invalid response format: metadata is missing or incomplete');
        }

        // Validate each campaign
        parsed.campaigns.forEach((campaign: Campaign, index: number) => {
            if (!campaign.id || typeof campaign.id !== 'number') {
                throw new Error(`Invalid campaign id at index ${index}`);
            }
            if (!campaign.type || !['Promotion', 'Education', 'Awareness'].includes(campaign.type)) {
                campaign.type = 'Promotion';
                console.log('Invalid campaign type at index', index, 'setting to default:', campaign.type);
            }
            if (!campaign.taskType || !['Email', 'SMS', 'Loyalty'].includes(campaign.taskType)) {
                campaign.taskType = 'Email';
                console.log('Invalid task type at index', index, 'setting to default:', campaign.taskType);
            }
            if (!campaign.scheduledDate || !/^\d{4}-\d{2}-\d{2}$/.test(campaign.scheduledDate)) {
                campaign.scheduledDate = new Date().toISOString().split('T')[0];
                console.log('Invalid scheduled date at index', index, 'setting to default:', campaign.scheduledDate);
            }
        });

        // Validate metadata dates
        const {startDate, endDate} = parsed.metadata.timeframeCovered;
        if (!startDate || !endDate ||
            !/^\d{4}-\d{2}-\d{2}$/.test(startDate) ||
            !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
            throw new Error('Invalid date format in metadata');
        }

        return parsed as CampaignResponse;
    } catch (error) {
        if (error instanceof Error) {
            throw new Error(`Failed to parse campaign response: ${error.message}`);
        }
        throw new Error('Failed to parse campaign response');
    }
}

export async function handleClaudeResponse(completion: string): Promise<CampaignResponse> {
    try {
        const campaigns = parseCampaignResponse(completion);
        return campaigns;
    } catch (error) {
        console.error('Error parsing Claude response:', error);
        throw new Error('Failed to process campaign recommendations');
    }
}
