import {RouterTarget, RouterParams, CompletionMessage, StreamingCallback, RouterResponse, ToolCall, ToolResult} from './types';

export abstract class BaseRouterTarget implements RouterTarget {
  abstract id: string;
  abstract systemId: string;
  abstract supportedModels: string[];
  abstract supportedProviders: string[];

  protected matchesWildcard(pattern: string, value: string): boolean {
    if (pattern === '*') return true;
    if (pattern.endsWith('/*')) {
      const prefix = pattern.slice(0, -2); // Remove /* from the end
      return value.startsWith(prefix + '/');
    }
    return pattern === value;
  }

  canHandle(params: RouterParams): boolean {
    // By default, no provider supports image generation/editing except those that override
    if (params.imageGeneration || params.imageEditing) {
      return false;
    }

    // Check system match if systems are specified
    if (params.systems?.length) {
      const systemMatch = params.systems.includes('*') || params.systems.includes(this.systemId);
      if (!systemMatch) return false;
    }

    // Check model match if models are specified
    if (params.models?.length) {
      const modelMatch = params.models.some(requestedModel =>
        this.supportedModels.some(supportedPattern => {
          // For exact matches, both should match exactly
          if (!supportedPattern.includes('*')) {
            return supportedPattern === requestedModel;
          }
          // For wildcard patterns, ensure proper prefix matching
          return this.matchesWildcard(supportedPattern, requestedModel);
        })
      );
      if (!modelMatch) return false;
    }

    // Check provider match if providers are specified
    if (params.providers?.length) {
      const providerMatch = params.providers.some(provider =>
        this.supportedProviders.some(supported => this.matchesWildcard(supported, provider))
      );
      if (!providerMatch) return false;
    }

    return true;
  }

  protected async processToolCalls(toolCalls: ToolCall[], params: RouterParams): Promise<ToolResult[]> {
    if (!params.tools?.length || !toolCalls?.length) return [];

    // Combine fragmented tool calls
    const combinedToolCalls: ToolCall[] = [];
    let currentToolCall: ToolCall | null = null;

    for (const tc of toolCalls) {
      if (tc.id) {
        // This is a new tool call with an ID
        if (currentToolCall) {
          combinedToolCalls.push(currentToolCall);
        }
        currentToolCall = {...tc};
      } else if (currentToolCall && tc.function?.arguments) {
        // This is a fragment of the current tool call
        currentToolCall.function.arguments = (currentToolCall.function.arguments || '') + tc.function.arguments;
      } else if (!currentToolCall && tc.function?.arguments) {
        // Fragment without a preceding tool call with ID - create a placeholder
        currentToolCall = {
          type: 'function',
          id: `generated_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
          function: {
            name: tc.function?.name || 'unknown',
            arguments: tc.function.arguments
          }
        };
      }
    }

    // Add the last tool call if it exists
    if (currentToolCall) {
      combinedToolCalls.push(currentToolCall);
    }

    console.log('Combined tool calls:', JSON.stringify(combinedToolCalls, null, 2));

    const results: ToolResult[] = [];
    for (const toolCall of combinedToolCalls) {
      if (toolCall.type === 'function') {
        const tool = params.tools.find(t => t.function.name === toolCall.function.name);
        if (!tool) {
          console.warn(`Tool ${toolCall.function.name} not found in provided tools`);
          results.push({
            toolCallId: toolCall.id,
            output: JSON.stringify({ error: `Tool ${toolCall.function.name} not found in provided tools` })
          });
          continue;
        }

        try {
          // Look for a tool executor in the router params
          const executor = params.toolExecutors?.[toolCall.function.name];
          console.log(`Tool executor for ${toolCall.function.name}:`, executor ? 'Found' : 'Not found');
          if (!executor) {
            console.warn(`No executor found for tool ${toolCall.function.name}`);
            results.push({
              toolCallId: toolCall.id,
              output: JSON.stringify({ error: `No executor found for tool ${toolCall.function.name}` })
            });
            continue;
          }

          // Parse arguments with better error handling
          let args;
          try {
            // Handle empty arguments by providing an empty object
            if (!toolCall.function.arguments || toolCall.function.arguments.trim() === '') {
              args = {};
              console.warn(`Empty arguments for tool ${toolCall.function.name}, using empty object`);
            } else {
              args = JSON.parse(toolCall.function.arguments);
            }
          } catch (parseError) {
            console.error(`Error parsing arguments for tool ${toolCall.function.name}:`, parseError);
            console.error(`Raw arguments: ${toolCall.function.arguments}`);
            results.push({
              toolCallId: toolCall.id,
              output: JSON.stringify({
                error: `Invalid JSON in tool arguments: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`,
                raw_arguments: toolCall.function.arguments
              })
            });
            continue;
          }

          // Execute the tool function with the provided arguments
          console.log(`Executing tool ${toolCall.function.name} with args:`, args);
          const result = await executor(args);
          console.log(`Tool ${toolCall.function.name} result:`, result);

          results.push({
            toolCallId: toolCall.id,
            output: JSON.stringify(result)
          });
        } catch (error) {
          console.error(`Error executing tool ${toolCall.function.name}:`, error);
          results.push({
            toolCallId: toolCall.id,
            output: JSON.stringify({
              error: error instanceof Error ? error.message : 'Tool execution failed',
              details: error instanceof Error ? error.stack : 'No stack trace available'
            })
          });
        }
      }
    }
    return results;
  }

  // This is now the main entry point that handles streaming
  async execute(
    messages: CompletionMessage[],
    params: RouterParams,
    onChunk?: StreamingCallback
  ): Promise<RouterResponse> {
    try {
      const result = await this.executeCompletion(messages, params, onChunk);

      // Process any tool calls
      const toolResults = await this.processToolCalls(result.toolCalls || [], params);

      // If there are tool results and we have streaming enabled, send them
      if (toolResults.length > 0 && onChunk) {
        await onChunk({
          content: '',
          done: false,
          toolResults
        });
      }

      // If there are tool results, send them back to the LLM for further processing
      let finalResult = result;

      // Extract image results from additional data with type checking
      const generatedImages = finalResult.additional?.generatedImages as Array<{url: string; revised_prompt?: string}> | undefined;
      const editedImages = finalResult.additional?.editedImages as Array<{url: string}> | undefined;

      const response: RouterResponse = {
        content: finalResult.content,
        system: this.systemId,
        provider: finalResult.provider || params.providers?.[0] || this.supportedProviders[0],
        model: finalResult.model || params.models?.[0] || this.supportedModels[0],
        totalCost: finalResult.totalCost,
        promptTokens: finalResult.promptTokens,
        completionTokens: finalResult.completionTokens,
        totalTokens: finalResult.totalTokens,
        tokenRatePerSec: finalResult.tokenRatePerSec,
        firstTokenMs: finalResult.firstTokenMs,
        latencyMs: finalResult.latencyMs,
        generationTimeMs: finalResult.generationTimeMs,
        finishReason: finalResult.finishReason,
        additional: finalResult.additional,
        toolCalls: result.toolCalls, // Keep the original tool calls
        toolResults, // Keep the original tool results
        generatedImages,
        editedImages
      };

      // If we have a streaming callback, send the full response with metadata
      if (!this.supportsStreaming() && onChunk) {
        await onChunk({
          content: finalResult.content,
          done: true,
          metadata: {
            system: response.system,
            provider: response.provider,
            model: response.model,
            totalCost: response.totalCost,
            promptTokens: response.promptTokens,
            completionTokens: response.completionTokens,
            totalTokens: response.totalTokens,
            tokenRatePerSec: response.tokenRatePerSec,
            firstTokenMs: response.firstTokenMs,
            latencyMs: response.latencyMs,
            generationTimeMs: response.generationTimeMs,
            finishReason: response.finishReason,
            additional: response.additional
          },
          toolCalls: result.toolCalls,
          toolResults
        });
      }

      return response;
    } catch (error) {
      // Ensure we mark the stream as done even if there's an error
      if (onChunk) {
        await onChunk({
          content: error instanceof Error ? error.message : 'An error occurred',
          done: true,
          error: error instanceof Error ? error : new Error('An error occurred')
        });
      }
      throw error;
    }
  }

  protected abstract supportsStreaming(): boolean;

  // This is what concrete implementations should override - now returns metadata
  protected abstract executeCompletion(
    messages: CompletionMessage[],
    params: RouterParams,
    onChunk?: StreamingCallback
  ): Promise<{
    content: string;
    provider: string;
    model: string;
    totalCost?: number;
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
    tokenRatePerSec?: number;
    firstTokenMs?: number;
    latencyMs?: number;
    generationTimeMs?: number;
    finishReason?: string;
    additional?: Record<string, unknown>;
    toolCalls?: ToolCall[];
  }>;
}
