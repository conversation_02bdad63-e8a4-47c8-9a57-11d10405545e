import {Message, MessageRole} from '../../models';

export interface ChatOptions {
  promptTemplateId?: number;
  campaignId?: number;
  taskId?: number;
  organizationId: number;
  routerParams: RouterParams;
  tasteProfile?: string;
}

export interface RouterParams {
  systems?: string[];
  models?: string[];
  providers?: string[];
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  tools?: ToolDefinition[];
  isToolResponseFollowUp?: boolean;
  toolExecutors?: {
    [functionName: string]: (args: any) => Promise<any>;
  };
  messages?: CompletionMessage[];
  imageGeneration?: {
    prompt: string;
    quality?: 'standard' | 'high';
    style?: 'vivid' | 'natural';
    size?: '1024x1024' | '1024x1536' | '1536x1024';
    n?: number;
  };
  imageEditing?: {
    imageDataUrls: string[];
    maskDataUrl?: string;
	quality?: 'standard' | 'high';
    prompt: string;
    size?: '1024x1024' | '1024x1536' | '1536x1024';
    n?: number;
    model?: 'gpt-image-1';
  };
}

export interface LLMMetadata {
  system: string;
  provider: string;
  model: string;
  additional?: Record<string, unknown>;
}

export interface StreamingChunk {
  content: string;
  done: boolean;
  error?: Error;
  metadata?: {
    system: string;
    provider: string;
    model: string;
    totalCost?: number;
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
    tokenRatePerSec?: number;
    firstTokenMs?: number;
    latencyMs?: number;
    generationTimeMs?: number;
    finishReason?: string;
    // For any provider-specific or non-standard metadata
    additional?: Record<string, unknown>;
  };
  toolCalls?: ToolCall[];
  toolResults?: ToolResult[];
}

export type StreamingCallback = (chunk: StreamingChunk) => Promise<void>;

export interface RouterTarget {
  id: string;
  systemId: string;
  supportedModels: string[];
  supportedProviders: string[];
  canHandle(params: RouterParams): boolean;
  execute(
    messages: CompletionMessage[],
    params: RouterParams,
    onChunk?: StreamingCallback
  ): Promise<RouterResponse>;
}

export interface RouterResponse {
  content: string;
  system: string;
  provider: string;
  model: string;
  totalCost?: number;
  promptTokens?: number;
  completionTokens?: number;
  totalTokens?: number;
  tokenRatePerSec?: number;
  firstTokenMs?: number;
  latencyMs?: number;
  generationTimeMs?: number;
  finishReason?: string;
  additional?: Record<string, unknown>;
  toolCalls?: ToolCall[];
  toolResults?: ToolResult[];
  generatedImages?: Array<{url: string; revised_prompt?: string}>;
  editedImages?: Array<{url: string}>;
}

export interface ImageUrlContent {
  type: 'image_url';
  image_url: {
    url: string;
    detail?: 'low' | 'high' | 'auto';
  };
  cache_control?: {
    type: 'ephemeral';
  };
}

export interface Base64ImageContent {
  type: 'image';
  source: {
    type: 'base64';
    media_type: 'image/jpeg' | 'image/png' | 'image/gif' | 'image/webp';
    data: string;
  };
}

export interface TextContent {
  type: 'text';
  text: string;
  cache_control?: {
    type: 'ephemeral';
  };
}

export type MessageContent = TextContent | ImageUrlContent;

export interface CompletionMessage {
  role: "user" | "assistant" | "system" | "tool" | string;
  content: string | MessageContent[];
  toolCallId?: string; // For tool messages
  name?: string; // For tool messages (function name)
  tool_calls?: ToolCall[]; // For assistant messages with tool calls
}

// Update OpenRouterParams to support image content and tool calls
export interface OpenRouterParams {
  model: string;
  messages: {
    role: string;
    content: string | MessageContent[];
    toolCallId?: string; // For tool messages
    name?: string; // For tool messages (function name)
    tool_calls?: ToolCall[]; // For assistant messages with tool calls
  }[];
}

export interface ToolDefinition {
  type: string;
  function: {
    name: string;
    description: string;
    parameters: {
      type: string;
      properties?: Record<string, {
        type: string;
        description?: string;
        enum?: string[];
      }>;
      required?: string[];
    };
  };
}

export interface ToolCall {
  type: 'function';
  id: string;
  function: {
    name: string;
    arguments: string;
  };
}

export interface ToolResult {
  toolCallId: string;
  output: string;
}
