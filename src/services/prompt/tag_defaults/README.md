# Tag Defaults

This directory contains default content for prompt tags used in the system. These are primarily used for providing AI agents with instructions on how to use various tools and features.

## Adding a New Tool Default

To add a new tool default, follow these steps:

1. **Create the default file**: Copy `TOOL_TEMPLATE.ts.example` and rename it (e.g., `TOOL_MY_NEW_TOOL.ts`)

2. **Update the content**: 
   - Replace `DEFAULT_TOOL_[NAME]` with your actual tool name (e.g., `DEFAULT_TOOL_MY_NEW_TOOL`)
   - Write clear instructions including usage rules, parameters, and examples
   - Follow the existing format for consistency

3. **Export from index**: Add your export to `index.ts`:
   ```typescript
   export * from './TOOL_MY_NEW_TOOL';
   ```

4. **Add to tags list**: Update `/src/services/prompt/tags.ts` to include your new tag:
   ```typescript
   'TOOL_MY_NEW_TOOL'
   ```

5. **Add replacement logic**: In `/src/services/prompt/prompt-context.service.ts`:
   - Import your default at the top:
     ```typescript
     import { DEFAULT_TOOL_MY_NEW_TOOL } from './tag_defaults';
     ```
   - Add the replacement in `replacePromptTags` method:
     ```typescript
     .replace(/{TOOL_MY_NEW_TOOL}/g, DEFAULT_TOOL_MY_NEW_TOOL)
     ```

## Existing Tag Defaults

- **PLAN_STRATEGY**: Default planning strategy for AI agents
- **TOOL_DATA_LOOKUP**: Instructions for using the data lookup tool to query Athena tables

## Best Practices

1. **Be specific**: Include concrete examples and clear parameter descriptions
2. **Security first**: Always include security guidelines (e.g., "Never inject malicious SQL")
3. **Version control**: Document any breaking changes to tool interfaces
4. **Test thoroughly**: Ensure your instructions work correctly with the AI agents
5. **Keep it concise**: While being thorough, avoid unnecessary verbosity