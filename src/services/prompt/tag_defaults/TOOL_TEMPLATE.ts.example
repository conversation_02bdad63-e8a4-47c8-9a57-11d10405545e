// Example template for adding new tool defaults
// To add a new tool:
// 1. Copy this file and rename it to TOOL_[NAME].ts
// 2. Update the export name and content
// 3. Add the export to index.ts
// 4. Add the tag to tags.ts
// 5. Add the replacement in prompt-context.service.ts

export const DEFAULT_TOOL_[NAME] = `## [Tool Name]
Brief description of what this tool does.

### Usage Rules
1. When to use this tool
2. Important considerations
3. Security guidelines

### Parameters
- \`param1\`: (Required/Optional) description
- \`param2\`: (Optional) description

### Examples

#### Example 1 Name
\`\`\`
tool_name({
  "param1": "value",
  "param2": "value"
})
\`\`\`

#### Example 2 Name
\`\`\`
tool_name({
  "param1": "different value"
})
\`\`\`

Additional notes or best practices for using this tool.`;