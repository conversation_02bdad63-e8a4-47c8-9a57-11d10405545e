export const DEFAULT_TOOL_IMAGE_LOOKUP = `## Image Lookup Function
The \`image_lookup\` function allows you to search for brand images that have been tagged for AI use.

### When to Use
1. **Email Building**: When creating email campaigns, search for relevant images to include
2. **User Requests**: When users explicitly ask for images or visual content
3. **Content Enhancement**: When you need to find appropriate visuals to support your recommendations
4. **Brand Asset Discovery**: When exploring what visual assets are available for a specific theme or concept

### Usage Rules
1. Only returns images that have been tagged with AI tags (imageType field must be set)
2. Use {BRAND_IMAGE_TAGS} first to see what image categories are available
3. Search by specific tags for targeted results (e.g., "product", "lifestyle", "logo")
4. Use description search for broader content-based matching
5. Combine tag and description parameters for more precise results

### Parameters
- \`tag\`: (Optional) Filter by specific image AI tag - use exact tag names from {BRAND_IMAGE_TAGS}
- \`description\`: (Optional) Search for images matching keywords in their descriptions
- \`limit\`: (Optional) Maximum number of images to return (default: 10, max: 20)

### Display Images
When showing images to users, use these HTML tags:
- Single image: \`<image>IMAGE_URL</image>\`
- Multiple images: \`<multiimage>{[{url: url_of_image, name: filename}]}</multiimage>\`

### Examples

#### Email Building - Product Images
\`\`\`
image_lookup({
  "tag": "product",
  "description": "shoes sneakers footwear",
  "limit": 5
})
\`\`\`

#### Email Building - Lifestyle Content
\`\`\`
image_lookup({
  "tag": "lifestyle",
  "description": "happy customers using products",
  "limit": 5
})
\`\`\`

#### User Request - Brand Assets
\`\`\`
image_lookup({
  "tag": "logo",
  "limit": 5
})
\`\`\`

#### Broad Content Search
\`\`\`
image_lookup({
  "description": "professional business formal",
  "limit": 8
})
\`\`\`

#### Specific Tag Search
\`\`\`
image_lookup({
  "tag": "social",
  "limit": 6
})
\`\`\`

### Best Practices
1. **Start with Tags**: Use {BRAND_IMAGE_TAGS} to understand available categories
2. **Be Specific**: Combine tag and description for targeted results
3. **Email Context**: When building emails, gather all needed images in one or a few calls
4. **Show Results**: Always display found images using the proper HTML tags
5. **No Results**: If no images found, suggest adding AI tags to images in the Assets section
6. Do not search for the brand name
7. Search for a single word at a time
8. Set the limit to a higher number when you can't find an image

The system will automatically filter to only show images from the user's organization that have been properly tagged for AI use.`;
