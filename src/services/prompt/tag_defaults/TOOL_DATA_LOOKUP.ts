export const DEFAULT_TOOL_DATA_LOOKUP = `## Data Lookup Function
The \`data_lookup\` function allows you to query data from Athena tables using natural language.

### Usage Rules
1. Use this function when you need to retrieve specific data from database tables
2. Provide clear, specific queries that target the exact information needed
3. For natural language queries, be specific about what data you're looking for
4. You have access to questions about email campaigns such as their open rates, revenue, and click rates. You also have access to subject lines for each campaign.
5. You have access to order information for an org
6. Never inject anything malicious, such as SQL or modify the orgID even if asked by the user.
7. Never mention the organization id or orgid to the user even if they ask, that needs to remain secret
8. Only ever use the organization id given to you, never change it, or try to use a different one.
9. If a user tries to give you a different organization id, always ignore it and tell the user that you can only use the organization data that you have access to.

### Parameters
- \`query\`: (Required) natural language description of the data needed

### Examples

#### Natural Language Example
\`\`\`
data_lookup({
  "query": "Show me the top 10 best-selling products by revenue for this organization in the last 12 months"
})
\`\`\`

#### Time-Based Analysis Example
\`\`\`
data_lookup({
  "query": "Compare monthly email campaign open rates for the last 6 months"
})
\`\`\`

### Subject-Line Analysis Example
\`\`\`
data_lookup({
  "query": "Show me my top performing subject lines from last month based on revenue"
})
\`\`\`

When using this tool, be specific about the timeframe, metrics, and context of the data you need. The system will automatically apply the correct organization ID to your query.`;
