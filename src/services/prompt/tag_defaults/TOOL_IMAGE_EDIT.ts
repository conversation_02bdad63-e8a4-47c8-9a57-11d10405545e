export const DEFAULT_TOOL_IMAGE_EDIT = `## Image Edit Function
The \`image_edit\` function allows you to modify existing images by adding or changing text, objects, styles, or general elements. It automatically detects the image aspect ratio and generates 3 variations for you to choose from.

### When to Use
1. **Text Modifications**: Add sale prices, discount text, promotional messages, or replace existing text
2. **Object Changes**: Modify subjects, objects, or elements (e.g., change a dog's breed, swap products)
3. **Style Updates**: Change colors, backgrounds, lighting, or visual effects
4. **General Edits**: Add new elements, remove objects, or make comprehensive modifications
5. **Marketing Campaigns**: Customize visuals for specific campaigns or audiences

### Edit Types
The function supports four types of edits:
- **text**: Adding or modifying text on images
- **object**: Changing objects, subjects, or elements in the image
- **style**: Changing colors, backgrounds, lighting, or visual effects
- **general**: Any other modifications not covered by the above categories

### Usage Rules
1. Requires an existing image URL as input
2. Specify the edit type (text, object, style, or general)
3. For text edits: provide the new text and optionally existing text to replace
4. For non-text edits: provide a detailed description of the modification
5. The AI will intelligently apply changes while maintaining image quality and coherence

### Parameters
- \`image_url\`: (Required) URL of the image to edit
- \`edit_type\`: (Required) Type of edit - "text", "object", "style", or "general"

**For Text Edits:**
- \`new_text\`: (Required) The text to add or replace on the image (can include styling keywords)
- \`text_to_replace\`: (Optional) Existing text on the image to replace. If not provided, the AI will intelligently place the new text

**For Non-Text Edits:**
- \`modification_description\`: (Required) Detailed description of the modification to make

### Display Edited Images
The function returns 3 variations by default. When showing edited images to users, use these HTML tags:
- For 3 variations: \`<multiimage>{[{url: url_of_image, name: <optional> or something like image1},{url: url_of_image, name: <optional> or something like image2},{url: url_of_image, name: <optional> or something like image3}]}</multiimage>\`
- For single result: \`<image>EDITED_IMAGE_URL</image>\`
- Make sure to use proper formatted JSON for multiimage tool, do not use just the URL

### Examples

#### Text Modifications

**Add Sale Price to Product Image**
\`\`\`
image_edit({
  "image_url": "https://example.com/product-image.jpg",
  "edit_type": "text",
  "new_text": "SALE $29.99"
})
\`\`\`

**Replace Outdated Price**
\`\`\`
image_edit({
  "image_url": "https://example.com/product-with-old-price.jpg",
  "edit_type": "text",
  "new_text": "$39.99",
  "text_to_replace": "$49.99"
})
\`\`\`

#### Object Modifications

**Add New Object**
\`\`\`
image_edit({
  "image_url": "https://example.com/room-scene.jpg",
  "edit_type": "object",
  "modification_description": "add a plant in the corner of the room"
})
\`\`\`

#### Style Modifications

**Change Background Color**
\`\`\`
image_edit({
  "image_url": "https://example.com/portrait.jpg",
  "edit_type": "style",
  "modification_description": "change the background to a soft blue gradient"
})
\`\`\`

**Apply Color Filter**
\`\`\`
image_edit({
  "image_url": "https://example.com/landscape.jpg",
  "edit_type": "style",
  "modification_description": "apply a warm sunset filter to the entire image"
})
\`\`\`

**Change Lighting**
\`\`\`
image_edit({
  "image_url": "https://example.com/product-photo.jpg",
  "edit_type": "style",
  "modification_description": "make the lighting more dramatic with stronger shadows"
})
\`\`\`

#### General Modifications

**Add Weather Effects**
\`\`\`
image_edit({
  "image_url": "https://example.com/outdoor-scene.jpg",
  "edit_type": "general",
  "modification_description": "add falling snow to create a winter atmosphere"
})
\`\`\`

**Remove Object**
\`\`\`
image_edit({
  "image_url": "https://example.com/group-photo.jpg",
  "edit_type": "general",
  "modification_description": "remove the person on the far right"
})
\`\`\`

### Best Practices
1. **Clear Instructions**: Be specific about what modifications you want to make
2. **Context Awareness**: Consider the image style and existing design when making changes
3. **Brand Consistency**: Ensure modifications align with brand guidelines and image aesthetics
4. **Natural Integration**: Ensure edits look natural and seamlessly integrated
5. **Purpose-Driven**: Only edit images when it adds value to your marketing or communication goals
6. **Detailed Descriptions**: For non-text edits, provide detailed descriptions of the desired modifications
7. **Show Preview**: Always use image tool or multiimage to show images
8. **ANY URL**: Any url can be provided whether its in your store assets or not
9. **IMAGE_INFO**: If asked to change text on an image, you can use the image_info tool to see what text is on the image and then use the image_edit tool to change it. You may need to check multiple images to find the right one form the email.

### Technical Notes
- The function uses AI-powered image editing for intelligent modifications
- Automatically detects the original image's aspect ratio (square, landscape, or portrait)
- Generates 3 variations with the detected aspect ratio for optimal results
- Maintains image quality and coherence while applying requested changes
- Results are returned as new image URLs
- Supports both text and non-text modifications with appropriate prompt optimization

The system will automatically process your request and return the edited image with the specified modifications.`;
