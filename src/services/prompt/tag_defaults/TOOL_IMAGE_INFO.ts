export const DEFAULT_TOOL_IMAGE_INFO = `## Image Info Function
The \`image_info\` function allows you to analyze images and extract readable text content using AI vision capabilities.

### When to Use
1. **Content Analysis**: When you need to understand what text appears in screenshots, UI mockups, or marketing materials
2. **Accessibility Review**: When analyzing interfaces to understand text hierarchy and button labels
3. **Content Extraction**: When users upload images and want to know what text content they contain
4. **UI/UX Analysis**: When reviewing website screenshots or app interfaces to understand navigation and messaging
5. **Marketing Material Review**: When analyzing promotional images to understand their text content
6. **EMAIL EDIT**: When asked to change text in an email it probably means in an image, you can use this tool to see what text is on the image and then use the image_edit tool to change it. You may need to check multiple images to find the right one form the email.

### Usage Rules
1. Only provide image URLs that are accessible and valid
2. Focus on extracting meaningful, readable text content
3. The tool will automatically ignore small product labels, copyright text, and watermarks
4. Results are structured into categories: main text, button text, navigation, and other text
5. Always includes a summary of what type of content the image appears to be

### Parameters
- \`url\`: (Required) URL of the image to analyze - must be a valid, accessible image URL

### What Gets Extracted
**INCLUDED:**
- Large headings and prominent text
- Button text and clickable elements
- Navigation menu items
- Form labels and important UI text
- Main messaging and calls-to-action

**IGNORED:**
- Small product SKUs or model numbers
- Copyright notices and legal disclaimers
- Watermarks (unless they are primary content)
- Very small or illegible text
- Background decorative text

### Response Format
The tool returns structured JSON with:
- \`mainText\`: Primary headings and prominent messages
- \`buttonText\`: Button labels and clickable element text
- \`navigationText\`: Menu items and navigation elements
- \`otherText\`: Other significant readable text
- \`summary\`: Description of the content type and purpose

### Examples

#### Analyzing a Website Screenshot
\`\`\`
image_info({
  "url": "https://example.com/screenshot.png"
})
\`\`\`

#### Analyzing Marketing Material
\`\`\`
image_info({
  "url": "https://cdn.example.com/promo-banner.jpg"
})
\`\`\`

#### Analyzing App Interface
\`\`\`
image_info({
  "url": "https://assets.example.com/app-mockup.png"
})
\`\`\`

### Best Practices
1. **Valid URLs**: Ensure the image URL is accessible and returns a valid image
2. **Image Quality**: Higher quality images with clear text will yield better results
3. **Context Awareness**: The summary helps understand the overall purpose and context
4. **Structured Output**: Use the categorized text arrays to understand content hierarchy
5. **Error Handling**: If analysis fails, the tool will provide meaningful error messages

### Common Use Cases
- **Content Audits**: Understanding what text appears across marketing materials
- **Accessibility Reviews**: Checking button labels and navigation text
- **Competitive Analysis**: Analyzing competitor website screenshots
- **Content Planning**: Understanding text hierarchy in design mockups
- **Quality Assurance**: Verifying text content in various image assets

The tool uses GPT-4o-mini's advanced vision capabilities to provide accurate, structured text extraction focused on the most important and readable content in images.`;
