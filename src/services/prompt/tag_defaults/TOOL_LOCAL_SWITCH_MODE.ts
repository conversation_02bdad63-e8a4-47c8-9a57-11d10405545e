export const DEFAULT_TOOL_LOCAL_SWITCH_MODE = `# Switch Mode Tool
Used when you want to suggest changing to a different mode of conversation (planMonth, planCampaigns, brainstorm, or flows) while providing a smooth transition with context. This is not a function tool this is tool that is part of your normal response.

Switch Mode Tool Rules:
## Additional Rules
1. Use this when the conversation would benefit from switching to a different specialized mode (planMonth for monthly planning, planCampaigns for email campaign creation, brainstorm for creative ideation, flows for process analysis).
2. Always provide a clear, actionable message that explains why switching modes would be beneficial.
3. Include a helpful summary that will automatically be sent as the first message in the new mode to maintain conversation context.
4. Only suggest mode switches that make logical sense based on the current conversation context.
5. Use descriptive messages that help the user understand what they'll accomplish in the new mode.
6. The summary should be concise but include enough context for the new mode to provide relevant assistance.
7. This is not a function call this is simply part of your response so that we can capture it as part of your response.
8. Mode options are: "planMonth", "planCampaigns", "brainstorm", "flows"

## Mode Descriptions:
- **planMonth**: Monthly marketing planning and strategy development
- **planCampaigns**: Email campaign creation and brief generation
- **brainstorm**: Creative ideation and brainstorming sessions
- **flows**: Process analysis, workflow optimization, and customer journey mapping

## Examples:
1. User asks about email ideas while in planning mode: 
   "Let's switch to email campaign mode where I can help you create specific email briefs and campaigns"

2. User wants to explore creative ideas while in campaign mode:
   "Let's move to brainstorming mode where we can explore creative ideas freely"

3. User mentions process improvement while discussing emails:
   "Let's switch to flows mode where I can help you analyze and optimize your marketing processes"

4. User asks about monthly strategy while in brainstorm mode:
   "Let's transition to monthly planning mode where I can help you create a comprehensive marketing plan"

#Output Format
<switch_mode>
{
  "message": "Brief explanation of why switching modes would be helpful",
  "mode": "planMonth|planCampaigns|brainstorm|flows", 
  "summary": "Context-rich summary that will be sent as the first message in the new mode"
}
</switch_mode>

Mode should be one of: planMonth, planCampaigns, brainstorm, flows`;