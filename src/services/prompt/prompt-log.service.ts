import {injectable, /* inject, */ BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {PromptLogRepository} from '../../repositories';
import {PromptLog} from '../../models';
import * as AWS from 'aws-sdk';
import { v4 as uuidv4 } from 'uuid';

@injectable({scope: BindingScope.TRANSIENT})
export class PromptLogService {
  constructor(
    @repository(PromptLogRepository) private promptLogRepository: PromptLogRepository,
  ) {}

  /**
   * Uploads prompt and response data to an S3 bucket.
   * @param orgId The organization ID.
   * @param promptType The type of prompt (e.g., 'Plan', 'Brief').
   * @param prompt The prompt content.
   * @param response The LLM response content.
   * @returns The S3 path where the log is stored.
   * @throws Error if AWS credentials are not configured or if S3 upload fails.
   */
  async uploadPromptLogToS3(orgId: number, promptType: string, prompt: string, response: string): Promise<string> {
    const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
    const AWS_SECRET_KEY = process.env.API_SECRET_KEY;

    if (!AWS_ACCESS_KEY || !AWS_SECRET_KEY) {
      console.error('AWS credentials not found in environment variables.');
      throw new Error('AWS credentials not configured for S3 prompt logging.');
    }

    const logId = uuidv4();
    // Sanitize promptType for filename safety (replace non-alphanumeric with '-')
    const safePromptType = promptType.replace(/[^a-zA-Z0-9]/g, '-');
    const filename = `orgId-${orgId}-${safePromptType}-${logId}.json`;
    const bucketName = 'raleon-prompt-logs';
    const s3Path = `s3://${bucketName}/${filename}`; // Full S3 path

    const s3 = new AWS.S3({
      accessKeyId: AWS_ACCESS_KEY,
      secretAccessKey: AWS_SECRET_KEY,
      region: 'us-east-1' // Ensure this matches your bucket region
    });

    // Structure the content as required: [{prompt: "..."}, {response: "..."}]
    const logContent = JSON.stringify([{ prompt: prompt }, { response: response }]);

    try {
      await s3.putObject({
        Bucket: bucketName,
        Key: filename,
        Body: logContent,
        ContentType: 'application/json'
      }).promise(); // Use promise() for async/await

      console.log(`Successfully uploaded prompt log to ${s3Path}`);
      return s3Path; // Return the full S3 path upon success
    } catch (err: any) {
      console.error(`Error uploading prompt log to S3: ${err}`);
      throw new Error(`Failed to upload prompt log to S3: ${err.message}`);
    }
  }

  /**
   * Logs prompt and response data to both S3 and the database.
   * @param orgId The organization ID.
   * @param promptType The type of prompt (e.g., 'Plan', 'Brief').
   * @param prompt The prompt content.
   * @param response The LLM response content.
   */
  async logPromptData(orgId: number, promptType: string, prompt: string, response: string): Promise<void> {
    try {
      // Step 1: Upload to S3
      const s3Path = await this.uploadPromptLogToS3(orgId, promptType, prompt, response);

      // Step 2: Create PromptLog entry
      const logEntry = new PromptLog({
        orgId: orgId,
        type: promptType, // Use the determined prompt type/template name
        prompt: s3Path, // Store the S3 path returned by the upload function
        date: new Date().toISOString(), // Current timestamp as ISO string
      });

      // Step 3: Save to Database
      await this.promptLogRepository.create(logEntry);
      console.log(`Successfully logged prompt data to S3 (${s3Path}) and DB for type: ${promptType}, orgId: ${orgId}`);

    } catch (logError: any) {
      // Log the error but don't let logging failures break the main functionality
      console.error(`Failed to log prompt data (type: ${promptType}, orgId: ${orgId}): ${logError.message}`);
    }
  }
}
