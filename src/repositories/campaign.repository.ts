import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, repository, HasManyThroughRepositoryFactory, HasOneRepository, HasOneRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Quest, CampaignRelations, Segment, CampaignSegment, Campaign} from '../models';
import {QuestRepository} from './quest.repository';
import {CampaignSegmentRepository} from './campaign-segment.repository';
import {SegmentRepository} from './segment.repository';
import { ImageRepository } from './image.repository';
import { Image } from '../models/image.model';

export class CampaignRepository extends DefaultCrudRepository<
	Campaign,
	typeof Campaign.prototype.id,
	CampaignRelations
> {

	public readonly quests: HasManyRepositoryFactory<Quest, typeof Campaign.prototype.id>;

	public readonly segments: HasManyThroughRepositoryFactory<Segment, typeof Segment.prototype.id,
		CampaignSegment,
		typeof Campaign.prototype.id
	>;

	public readonly image: HasOneRepositoryFactory<Image, typeof Campaign.prototype.id>;

	constructor(
		@inject('datasources.dev_db') dataSource: DevDbDataSource,
		@repository.getter('QuestRepository') protected questRepositoryGetter: Getter<QuestRepository>,
		@repository.getter('CampaignSegmentRepository') protected campaignSegmentRepositoryGetter: Getter<CampaignSegmentRepository>,
		@repository.getter('SegmentRepository') protected segmentRepositoryGetter: Getter<SegmentRepository>,
		@repository.getter('CampaignRepository') protected campaignRepositoryGetter: Getter<CampaignRepository>,
		@repository.getter('ImageRepository') protected imageRepositoryGetter: Getter<ImageRepository>,
	) {
		super(Campaign, dataSource);
		this.segments = this.createHasManyThroughRepositoryFactoryFor('segments', segmentRepositoryGetter, campaignSegmentRepositoryGetter);
		this.registerInclusionResolver('segments', this.segments.inclusionResolver);
		this.quests = this.createHasManyRepositoryFactoryFor('quests', questRepositoryGetter);
		this.registerInclusionResolver('quests', this.quests.inclusionResolver);
		this.image = this.createHasOneRepositoryFactoryFor('image', imageRepositoryGetter);
		this.registerInclusionResolver('image', this.image.inclusionResolver);

		(this.modelClass as any).observe('before save', async (ctx: any) => {
			if (ctx.instance && ctx.instance.__unknownProperties) {
				ctx.instance.__unknownProperties = [];
			}
		});
	}

	async cascadeDeleteCampaign(campaignId: number) {
		const quests = await this.quests(campaignId).find();
		const questRepository = await this.questRepositoryGetter();
		for (const quest of quests) {
			await questRepository.cascadeDelete(quest.id!);
		}
		await this.segments(campaignId).unlinkAll();
		await this.deleteById(campaignId);
	}
}
