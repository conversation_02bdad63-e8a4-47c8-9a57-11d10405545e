import {DefaultCrudRepository, repository, BelongsToAccessor, juggler} from '@loopback/repository';
import {MessageCredit, MessageCreditWithRelations, Organization} from '../models';
import {Getter, inject} from '@loopback/core';
import {OrganizationRepository} from './organization.repository';

export class MessageCreditRepository extends DefaultCrudRepository<
  MessageCredit,
  typeof MessageCredit.prototype.id,
  MessageCreditWithRelations
> {
  public readonly organization: BelongsToAccessor<Organization, typeof MessageCredit.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: juggler.DataSource,
    @repository.getter('OrganizationRepository') protected organizationRepositoryGetter: Getter<OrganizationRepository>,
  ) {
    super(MessageCredit, dataSource);
    this.organization = this.createBelongsToAccessorFor('organization', organizationRepositoryGetter);
    this.registerInclusionResolver('organization', this.organization.inclusionResolver);
  }
}
