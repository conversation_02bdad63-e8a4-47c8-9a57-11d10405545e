import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {OrganizationPlannerPlan, OrganizationPlannerPlanRelations, PlannerPlanVersion} from '../models';
import {PlannerPlanVersionRepository} from './planner-plan-version.repository';

export class OrganizationPlannerPlanRepository extends DefaultCrudRepository<
  OrganizationPlannerPlan,
  typeof OrganizationPlannerPlan.prototype.id,
  OrganizationPlannerPlanRelations
> {

  public readonly plannerPlanVersions: HasManyRepositoryFactory<PlannerPlanVersion, typeof OrganizationPlannerPlan.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('PlannerPlanVersionRepository') protected plannerPlanVersionRepositoryGetter: Getter<PlannerPlanVersionRepository>,
  ) {
    super(OrganizationPlannerPlan, dataSource);
    this.plannerPlanVersions = this.createHasManyRepositoryFactoryFor('plannerPlanVersions', plannerPlanVersionRepositoryGetter,);
    this.registerInclusionResolver('plannerPlanVersions', this.plannerPlanVersions.inclusionResolver);
  }
}
