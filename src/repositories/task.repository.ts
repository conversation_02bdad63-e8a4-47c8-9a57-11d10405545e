import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Task, TaskRelations, TaskStep} from '../models';
import {TaskStepRepository} from './task-step.repository';

export class TaskRepository extends DefaultCrudRepository<
  Task,
  typeof Task.prototype.id,
  TaskRelations
> {

  public readonly taskSteps: HasManyRepositoryFactory<TaskStep, typeof Task.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('TaskStepRepository') protected taskStepRepositoryGetter: Getter<TaskStepRepository>,
  ) {
    super(Task, dataSource);
    this.taskSteps = this.createHasManyRepositoryFactoryFor('taskSteps', taskStepRepositoryGetter,);
    this.registerInclusionResolver('taskSteps', this.taskSteps.inclusionResolver);
  }
}
