import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {EmailWorkflowLog, EmailWorkflowLogRelations, EmailWorkflow, EmailWorkflowGeneration, EmailWorkflowLogLevel} from '../models';
import {DevDbDataSource} from '../datasources';
import {inject, Getter} from '@loopback/core';
import {EmailWorkflowRepository} from './email-workflow.repository';
import {EmailWorkflowGenerationRepository} from './email-workflow-generation.repository';

export class EmailWorkflowLogRepository extends DefaultCrudRepository<
  EmailWorkflowLog,
  typeof EmailWorkflowLog.prototype.id,
  EmailWorkflowLogRelations
> {
  public readonly workflow: BelongsToAccessor<EmailWorkflow, typeof EmailWorkflowLog.prototype.id>;
  public readonly generation: BelongsToAccessor<EmailWorkflowGeneration, typeof EmailWorkflowLog.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
    @repository.getter('EmailWorkflowRepository')
    protected emailWorkflowRepositoryGetter: Getter<EmailWorkflowRepository>,
    @repository.getter('EmailWorkflowGenerationRepository')
    protected emailWorkflowGenerationRepositoryGetter: Getter<EmailWorkflowGenerationRepository>,
  ) {
    super(EmailWorkflowLog, dataSource);
    
    this.generation = this.createBelongsToAccessorFor('generation', emailWorkflowGenerationRepositoryGetter);
    this.registerInclusionResolver('generation', this.generation.inclusionResolver);
    
    this.workflow = this.createBelongsToAccessorFor('workflow', emailWorkflowRepositoryGetter);
    this.registerInclusionResolver('workflow', this.workflow.inclusionResolver);
  }

  /**
   * Find logs by workflow ID
   */
  async findByWorkflowId(workflowId: number): Promise<EmailWorkflowLog[]> {
    return this.find({
      where: {workflowId},
      order: ['timestamp ASC']
    });
  }

  /**
   * Find logs by generation ID
   */
  async findByGenerationId(generationId: number): Promise<EmailWorkflowLog[]> {
    return this.find({
      where: {generationId},
      order: ['timestamp ASC']
    });
  }

  /**
   * Find logs by workflow and level
   */
  async findByWorkflowAndLevel(workflowId: number, level: EmailWorkflowLogLevel): Promise<EmailWorkflowLog[]> {
    return this.find({
      where: {
        workflowId,
        level
      },
      order: ['timestamp ASC']
    });
  }

  /**
   * Find recent logs for a workflow
   */
  async findRecentLogs(workflowId: number, limit: number = 50): Promise<EmailWorkflowLog[]> {
    return this.find({
      where: {workflowId},
      order: ['timestamp DESC'],
      limit
    });
  }

  /**
   * Create a log entry with standard fields
   */
  async createLog(data: {
    workflowId: number;
    generationId?: number;
    level: EmailWorkflowLogLevel;
    message: string;
    step?: string;
    data?: any;
    duration?: number;
  }): Promise<EmailWorkflowLog> {
    return this.create({
      ...data,
      timestamp: new Date()
    });
  }
}