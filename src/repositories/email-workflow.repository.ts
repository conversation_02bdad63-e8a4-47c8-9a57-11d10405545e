import {DefaultCrudRepository, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {EmailWorkflow, EmailWorkflowRelations, Task, EmailWorkflowGeneration, EmailWorkflowStatus} from '../models';
import {DevDbDataSource} from '../datasources';
import {inject, Getter} from '@loopback/core';
import {TaskRepository} from './task.repository';
import {EmailWorkflowGenerationRepository} from './email-workflow-generation.repository';

export class EmailWorkflowRepository extends DefaultCrudRepository<
  EmailWorkflow,
  typeof EmailWorkflow.prototype.id,
  EmailWorkflowRelations
> {
  public readonly task: BelongsToAccessor<Task, typeof EmailWorkflow.prototype.id>;
  public readonly generations: HasManyRepositoryFactory<EmailWorkflowGeneration, typeof EmailWorkflow.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
    @repository.getter('TaskRepository')
    protected taskRepositoryGetter: Getter<TaskRepository>,
    @repository.getter('EmailWorkflowGenerationRepository')
    protected emailWorkflowGenerationRepositoryGetter: Getter<EmailWorkflowGenerationRepository>,
  ) {
    super(EmailWorkflow, dataSource);
    
    this.generations = this.createHasManyRepositoryFactoryFor('generations', emailWorkflowGenerationRepositoryGetter);
    this.registerInclusionResolver('generations', this.generations.inclusionResolver);
    
    this.task = this.createBelongsToAccessorFor('task', taskRepositoryGetter);
    this.registerInclusionResolver('task', this.task.inclusionResolver);
  }

  /**
   * Find workflows by task ID
   */
  async findByTaskId(taskId: number): Promise<EmailWorkflow[]> {
    return this.find({
      where: {taskId},
      order: ['createdAt DESC'],
      include: ['generations']
    });
  }

  /**
   * Find workflows that have exceeded their timeout
   */
  async findTimedOutWorkflows(): Promise<EmailWorkflow[]> {
    return this.find({
      where: {
        status: {
          inq: [EmailWorkflowStatus.PENDING, EmailWorkflowStatus.PROCESSING]
        },
        timeoutAt: {
          lt: new Date()
        }
      }
    });
  }

  /**
   * Find active workflows (pending or processing)
   */
  async findActiveWorkflows(): Promise<EmailWorkflow[]> {
    return this.find({
      where: {
        status: {
          inq: [EmailWorkflowStatus.PENDING, EmailWorkflowStatus.PROCESSING]
        }
      }
    });
  }

  /**
   * Get workflow with all related data
   */
  async findByIdWithRelations(id: number): Promise<EmailWorkflow | null> {
    return this.findById(id, {
      include: [
        'task',
        {
          relation: 'generations',
          scope: {
            include: ['logs', 'emailGeneration']
          }
        }
      ]
    });
  }
}