import {inject, Getter} from '@loopback/core';
import {
  DefaultCrudRepository,
  repository,
  BelongsToAccessor,
  HasManyRepositoryFactory,
} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {
  PlanComment,
  PlanCommentRelations,
  OrganizationPlannerPlan,
  RaleonUser,
  User,
} from '../models';
import {OrganizationPlannerPlanRepository} from './organization-planner-plan.repository';
import {RaleonUserRepository} from './raleon-user.repository';
import {UserRepository} from './user.repository';

export class PlanCommentRepository extends DefaultCrudRepository<
  PlanComment,
  typeof PlanComment.prototype.id,
  PlanCommentRelations
> {
  public readonly organizationPlannerPlan: BelongsToAccessor<
    OrganizationPlannerPlan,
    typeof PlanComment.prototype.id
  >;
  public readonly raleonUser: BelongsToAccessor<
    RaleonUser,
    typeof PlanComment.prototype.id
  >;
  public readonly user: <PERSON>ongsToAccessor<
    User,
    typeof PlanComment.prototype.id
  >;
  public readonly replies: HasManyRepositoryFactory<
    PlanComment,
    typeof PlanComment.prototype.id
  >;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
    @repository.getter('OrganizationPlannerPlanRepository')
    protected planRepositoryGetter: Getter<OrganizationPlannerPlanRepository>,
    @repository.getter('RaleonUserRepository')
    protected raleonUserRepositoryGetter: Getter<RaleonUserRepository>,
    @repository.getter('UserRepository')
    protected userRepositoryGetter: Getter<UserRepository>,
    @repository.getter('PlanCommentRepository')
    protected commentRepositoryGetter: Getter<PlanCommentRepository>,
  ) {
    super(PlanComment, dataSource);
    this.replies = this.createHasManyRepositoryFactoryFor(
      'replies',
      commentRepositoryGetter,
    );
    this.registerInclusionResolver('replies', this.replies.inclusionResolver);
    this.organizationPlannerPlan = this.createBelongsToAccessorFor(
      'organizationPlannerPlan',
      planRepositoryGetter,
    );
    this.registerInclusionResolver(
      'organizationPlannerPlan',
      this.organizationPlannerPlan.inclusionResolver,
    );
    this.raleonUser = this.createBelongsToAccessorFor(
      'raleonUser',
      raleonUserRepositoryGetter,
    );
    this.registerInclusionResolver(
      'raleonUser',
      this.raleonUser.inclusionResolver,
    );

    this.user = this.createBelongsToAccessorFor(
      'user',
      userRepositoryGetter,
    );
    this.registerInclusionResolver(
      'user',
      this.user.inclusionResolver,
    );
  }
}
