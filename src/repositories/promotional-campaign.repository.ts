import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {PromotionalCampaign, PromotionalCampaignRelations, PromotionalCampaignDetails} from '../models';
import {PromotionalCampaignDetailsRepository} from './promotional-campaign-details.repository';

export class PromotionalCampaignRepository extends DefaultCrudRepository<
  PromotionalCampaign,
  typeof PromotionalCampaign.prototype.id,
  PromotionalCampaignRelations
> {

  public readonly promotionalCampaignDetails: HasManyRepositoryFactory<PromotionalCampaignDetails, typeof PromotionalCampaign.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
    @repository.getter('PromotionalCampaignDetailsRepository') protected promotionalCampaignDetailsRepositoryGetter: Getter<PromotionalCampaignDetailsRepository>,
  ) {
    super(PromotionalCampaign, dataSource);

    this.promotionalCampaignDetails = this.createHasManyRepositoryFactoryFor(
      'promotionalCampaignDetails',
      promotionalCampaignDetailsRepositoryGetter,
    );

    // Register the inclusion resolver for the relation
    this.registerInclusionResolver('promotionalCampaignDetails', this.promotionalCampaignDetails.inclusionResolver);
  }
}
