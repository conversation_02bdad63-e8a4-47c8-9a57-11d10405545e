import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasOneRepositoryFactory, HasManyRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {PlannerCampaign, PlannerCampaignRelations, Task, PlanCampaignContent, PlannerCampaignImage} from '../models';
import {TaskRepository} from './task.repository';
import {PlanCampaignContentRepository} from './plan-campaign-content.repository';
import {PlannerCampaignImageRepository} from './planner-campaign-image.repository';

export class PlannerCampaignRepository extends DefaultCrudRepository<
  PlannerCampaign,
  typeof PlannerCampaign.prototype.id,
  PlannerCampaignRelations
> {

  public readonly task: HasOneRepositoryFactory<Task, typeof PlannerCampaign.prototype.id>;

  public readonly planCampaignContent: HasOneRepositoryFactory<PlanCampaignContent, typeof PlannerCampaign.prototype.id>;

  public readonly plannerCampaignImages: HasManyRepositoryFactory<PlannerCampaignImage, typeof PlannerCampaign.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('TaskRepository') protected taskRepositoryGetter: Getter<TaskRepository>, @repository.getter('PlanCampaignContentRepository') protected planCampaignContentRepositoryGetter: Getter<PlanCampaignContentRepository>, @repository.getter('PlannerCampaignImageRepository') protected plannerCampaignImageRepositoryGetter: Getter<PlannerCampaignImageRepository>,
  ) {
    super(PlannerCampaign, dataSource);
    this.plannerCampaignImages = this.createHasManyRepositoryFactoryFor('plannerCampaignImages', plannerCampaignImageRepositoryGetter,);
    this.registerInclusionResolver('plannerCampaignImages', this.plannerCampaignImages.inclusionResolver);
    this.planCampaignContent = this.createHasOneRepositoryFactoryFor('planCampaignContent', planCampaignContentRepositoryGetter);
    this.registerInclusionResolver('planCampaignContent', this.planCampaignContent.inclusionResolver);
    this.task = this.createHasOneRepositoryFactoryFor('task', taskRepositoryGetter);
    this.registerInclusionResolver('task', this.task.inclusionResolver);
  }
}
