import {DefaultCrudRepository, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {EmailWorkflowGeneration, EmailWorkflowGenerationRelations, EmailWorkflow, EmailGeneration, EmailWorkflowLog, EmailWorkflowGenerationStatus} from '../models';
import {DevDbDataSource} from '../datasources';
import {inject, Getter} from '@loopback/core';
import {EmailWorkflowRepository} from './email-workflow.repository';
import {EmailGenerationRepository} from './email-generation.repository';
import {EmailWorkflowLogRepository} from './email-workflow-log.repository';

export class EmailWorkflowGenerationRepository extends DefaultCrudRepository<
  EmailWorkflowGeneration,
  typeof EmailWorkflowGeneration.prototype.id,
  EmailWorkflowGenerationRelations
> {
  public readonly workflow: BelongsToAccessor<EmailWorkflow, typeof EmailWorkflowGeneration.prototype.id>;
  public readonly emailGeneration: BelongsToAccessor<EmailGeneration, typeof EmailWorkflowGeneration.prototype.id>;
  public readonly logs: HasManyRepositoryFactory<EmailWorkflowLog, typeof EmailWorkflowGeneration.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
    @repository.getter('EmailWorkflowRepository')
    protected emailWorkflowRepositoryGetter: Getter<EmailWorkflowRepository>,
    @repository.getter('EmailGenerationRepository')
    protected emailGenerationRepositoryGetter: Getter<EmailGenerationRepository>,
    @repository.getter('EmailWorkflowLogRepository')
    protected emailWorkflowLogRepositoryGetter: Getter<EmailWorkflowLogRepository>,
  ) {
    super(EmailWorkflowGeneration, dataSource);
    
    this.logs = this.createHasManyRepositoryFactoryFor('logs', emailWorkflowLogRepositoryGetter);
    this.registerInclusionResolver('logs', this.logs.inclusionResolver);
    
    this.emailGeneration = this.createBelongsToAccessorFor('emailGeneration', emailGenerationRepositoryGetter);
    this.registerInclusionResolver('emailGeneration', this.emailGeneration.inclusionResolver);
    
    this.workflow = this.createBelongsToAccessorFor('workflow', emailWorkflowRepositoryGetter);
    this.registerInclusionResolver('workflow', this.workflow.inclusionResolver);
  }

  /**
   * Find generations by workflow ID
   */
  async findByWorkflowId(workflowId: number): Promise<EmailWorkflowGeneration[]> {
    return this.find({
      where: {workflowId},
      order: ['iterationNumber ASC'],
      include: ['logs', 'emailGeneration']
    });
  }

  /**
   * Find completed generations with email design
   */
  async findCompletedGenerations(workflowId: number): Promise<EmailWorkflowGeneration[]> {
    return this.find({
      where: {
        workflowId,
        status: EmailWorkflowGenerationStatus.COMPLETED
      },
      order: ['iterationNumber ASC'],
      include: ['emailGeneration']
    });
  }

  /**
   * Find generation by workflow and iteration
   */
  async findByWorkflowAndIteration(workflowId: number, iterationNumber: number): Promise<EmailWorkflowGeneration | null> {
    return this.findOne({
      where: {
        workflowId,
        iterationNumber
      },
      include: ['logs', 'emailGeneration']
    });
  }

  /**
   * Get generation with all logs
   */
  async findByIdWithLogs(id: number): Promise<EmailWorkflowGeneration | null> {
    return this.findById(id, {
      include: [
        'workflow',
        'emailGeneration',
        {
          relation: 'logs',
          scope: {
            order: ['timestamp ASC']
          }
        }
      ]
    });
  }
}