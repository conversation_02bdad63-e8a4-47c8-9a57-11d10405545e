import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Conversation, ConversationRelations, Organization, Message, User} from '../models';
import {OrganizationRepository} from './organization.repository';
import {MessageRepository} from './message.repository';
import {UserRepository} from './user.repository';

export class ConversationRepository extends DefaultCrudRepository<
	Conversation,
	typeof Conversation.prototype.id,
	ConversationRelations
> {

	public readonly organization: BelongsToAccessor<Organization, typeof Conversation.prototype.id>;

	public readonly messages: HasManyRepositoryFactory<Message, typeof Conversation.prototype.id>;

	public readonly createdByUser: BelongsToAccessor<User, typeof Conversation.prototype.id>;

	constructor(
		@inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('OrganizationRepository') protected organizationRepositoryGetter: Getter<OrganizationRepository>, @repository.getter('MessageRepository') protected messageRepositoryGetter: Getter<MessageRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
	) {
		super(Conversation, dataSource);
		this.messages = this.createHasManyRepositoryFactoryFor('messages', messageRepositoryGetter,);
		this.registerInclusionResolver('messages', this.messages.inclusionResolver);
		this.organization = this.createBelongsToAccessorFor('organization', organizationRepositoryGetter,);
		this.registerInclusionResolver('organization', this.organization.inclusionResolver);
		this.createdByUser = this.createBelongsToAccessorFor('createdByUser', userRepositoryGetter,);
		this.registerInclusionResolver('createdByUser', this.createdByUser.inclusionResolver);
	}
}
