# AGENTS.md - Development Guide for AI Coding Agents

## Build/Test Commands

- `npm run build` - Build frontend and backend
- `npm run lint` - Run ESLint and Prettier checks
- `npm run lint:fix` - Fix linting and formatting issues
- `npm test` - Run backend tests (Node.js test runner)
- `npm run test:client` - Run frontend tests (Vitest)
- `npm run test:dev` - Run LoopBack tests with console logs
- `npm run dev:ui` - Start frontend dev server (port 3030)
- `npm start` - Start backend server (port 3000)
- `npm run migrate` - Run database migrations

## Code Style Guidelines

- **HTTP Requests**: Use `fetch()` instead of axios (per .github/copilot-instructions.md)
- **Formatting**: Prettier config - single quotes, no bracket spacing, trailing commas, 80 char width
- **TypeScript**: Required for all new code, experimentalDecorators enabled
- **Imports**: Use absolute imports, group by external/internal, destructure when possible
- **Naming**: camelCase for variables/functions, PascalCase for classes/components, kebab-case for files
- **Backend**: LoopBack 4 patterns - decorators, dependency injection, repository pattern
- **Frontend**: Vue 3 Composition API, TypeScript, Tailwind CSS
- **Error Handling**: Use HttpErrors for backend, proper try/catch blocks
- **Authentication**: JWT tokens, @authenticate decorators, guard strategies for authorization
